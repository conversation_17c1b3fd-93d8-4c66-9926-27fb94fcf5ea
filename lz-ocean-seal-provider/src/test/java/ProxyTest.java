import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.ocean.seal.api.GameProxyService;
import fm.lizhi.ocean.seal.api.GameReportService;
import fm.lizhi.ocean.seal.protocol.GameProxyServiceProto;
import fm.lizhi.ocean.seal.protocol.GameReportServiceProto;
import org.junit.Test;

import java.util.List;

public class ProxyTest {

    public static final GameProxyService service = ProxyBuilderContainer.PROXY_BUILDER.buildProxy(GameProxyService.class);

    @Test
    public void getCode() throws Exception {
        /*Result<GameProxyServiceProto.ResponseInvokeTarget> result = service.invokeTarget(GameProxyServiceProto.InvokeTargetParams.newBuilder().setAppId("6518293")
                .setGameId("5206662980335600255").setInterface("/game/leave").setType(2)
                .setParams("{\"gameId\":5206662980335600255,\"extra\":\"{\"userId\":5236517478397381164}\",\"roomId\":\"5256349446939122815\"}").build());
        System.out.println("dddd:rCod=" + result.rCode());*/
    }
}
