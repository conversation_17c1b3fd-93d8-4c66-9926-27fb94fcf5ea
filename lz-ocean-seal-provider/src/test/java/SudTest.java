import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.ocean.seal.api.GameAuthService;
import fm.lizhi.ocean.seal.api.GameProxyService;
import fm.lizhi.ocean.seal.constant.GameChannel;
import fm.lizhi.ocean.seal.protocol.GameAuthServiceProto;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import tech.sud.mgp.auth.api.SudCode;
import tech.sud.mgp.auth.api.SudMGPAuth;
import tech.sud.mgp.auth.api.SudSSToken;

/**
 * <AUTHOR>
 */
@Slf4j
public class SudTest {

    static {
//        -Dmetadata.region=cn
//                -Dmetadata.deploy.env=test
//                -Dmetadata.business.env=lizhi
//                -Dmetadata.service.name=lz_ocean_seal
//                -Dconf.env=office
//                -Dconf.key=lz_ocean_seal
//                -Dapp.name=lz_ocean_seal
//                -Dregion=cn
//                -DbusinessEnv=lizhi
//                -DCAT_HOME=/tmp
        System.setProperty("metadata.region", "cn");
        System.setProperty("metadata.deploy.env", "test");
        System.setProperty("metadata.service.name", "lz_ocean_seal_local");
        System.setProperty("conf.env", "cn");
        System.setProperty("region", "cn");
        System.setProperty("businessEnv", "lizhi");

    }

    public static final GameAuthService gameAuthService = ProxyBuilderContainer.PROXY_BUILDER.buildProxy(GameAuthService.class);

    @Test
    public void getSstoken() throws Exception {

        String code = "xxx";

        Result<GameAuthServiceProto.ResponseGetServerToken> ssTokenResult = gameAuthService.getServerToken(code, GameChannel.SUD, "13521078");
        System.out.println("rcode:"+ssTokenResult.rCode());
        if (ssTokenResult.rCode() != 0) {
            throw new RuntimeException(ssTokenResult.getAttachment());
        }
        GameAuthServiceProto.ServerToken sudSSToken = ssTokenResult.target().getToken();
        if (sudSSToken.getErrorCode() != 0) {
            log.error("获取token失败：{}", sudSSToken);
            throw new RuntimeException(ssTokenResult.getAttachment());
        }
        log.info("结果token：{}", sudSSToken.getToken());
    }



    @Test
    public void getCode() throws Exception {

        SudMGPAuth sudMGPAuth = new SudMGPAuth("1922112172167983104", "uHA7ZWxa6CWmPZvK7LDHXg4nVKgqbKqO");
        SudCode code = sudMGPAuth.getCode("1258483457768698626");
        log.info("结果code：{}", code.getCode());
    }

    @Test
    public void getSudSstoken() throws Exception {

        SudMGPAuth sudMGPAuth = new SudMGPAuth("1922112172167983104", "uHA7ZWxa6CWmPZvK7LDHXg4nVKgqbKqO");
        SudSSToken ssToken = sudMGPAuth.getSSToken("1258483457768698626");
        log.info("结果code：{}", ssToken.getToken());
    }
}
