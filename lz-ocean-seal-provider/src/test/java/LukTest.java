import cn.hutool.core.map.MapUtil;
import fm.lizhi.commons.config.util.JsonUtil;
import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.ocean.seal.api.GameAuthService;
import fm.lizhi.ocean.seal.constant.GameChannel;
import fm.lizhi.ocean.seal.constant.LukControlEventType;
import fm.lizhi.ocean.seal.protocol.GameAuthServiceProto;
import io.github.cfgametech.Response;
import io.github.cfgametech.SDK;
import io.github.cfgametech.beans.*;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import tech.sud.mgp.auth.api.SudCode;
import tech.sud.mgp.auth.api.SudMGPAuth;

import java.util.Map;

/**
 */
@Slf4j
public class LukTest {

    static {
//        -Dmetadata.region=cn
//                -Dmetadata.deploy.env=test
//                -Dmetadata.business.env=lizhi
//                -Dmetadata.service.name=lz_ocean_seal
//                -Dconf.env=office
//                -Dconf.key=lz_ocean_seal
//                -Dapp.name=lz_ocean_seal
//                -Dregion=cn
//                -DbusinessEnv=lizhi
//                -DCAT_HOME=/tmp
        System.setProperty("metadata.region", "cn");
        System.setProperty("metadata.deploy.env", "test");
        System.setProperty("metadata.service.name", "lz_ocean_seal_local");
        System.setProperty("conf.env", "cn");
        System.setProperty("region", "cn");
        System.setProperty("businessEnv", "lizhi");

    }

    public static final GameAuthService gameAuthService = ProxyBuilderContainer.PROXY_BUILDER.buildProxy(GameAuthService.class);

    public static final int APP_ID = 1045111;
    public static final String APP_SECRET = "55428f6455ef4b1810c9ecb8be6a";
    public static final String DOMAIN = "https://pre.luk.live";


    @Test
    public void getLukSsToken() throws Exception {

        SDK sdk = new SDK(APP_SECRET, DOMAIN);

        // 来自 SDK 请求的参数结构
        GetChannelTokenRequest request = new GetChannelTokenRequest();

        request.setChannelId(APP_ID);
        request.setUserId("1234567890123");
        request.setTimestamp(System.currentTimeMillis());
        request.setSign(sdk.generateSignature(request));

        // 处理请求
        Response<GetChannelTokenResponse> resp = sdk.getChannelToken(request, getChannelTokenRequest -> {
            // 业务逻辑
            GetChannelTokenResponse response = new GetChannelTokenResponse();
            // 生成 Token
            response.setToken("token");
            // 设置 Token 过期时间
            response.setLeftTime(7200);

            return response;
        });

        // 将 resp 作为 JSON 写入 HTTP 响应
        System.out.println(resp.getCode());
        System.out.println(resp.getMessage());
        System.out.println(resp.getData().getToken());
    }

    @Test
    public void getGameServiceList() throws Exception {
        SDK sdk = new SDK(APP_SECRET, DOMAIN);
        GetGameServiceListRequest request = new GetGameServiceListRequest.Builder()
                .setChannelId(APP_ID)
                .setTimestamp(System.currentTimeMillis())
                .build();
        Response<GetGameServiceListResponse> response = sdk.GetGameServiceList(request);

        log.info("response: {}", JsonUtil.dumps(response.getData()));
    }

    @Test
    public void queryPropStatus() throws Exception {
        SDK sdk = new SDK(APP_SECRET, DOMAIN);
        Map<Object, Object> data = MapUtil.builder().put("unique_id", "950969d9-fdff-4649-a99a-e37cedae8379").build();
        PublishControlEventRequest.Builder builder = new PublishControlEventRequest.Builder();
        builder.setAppId(APP_ID);
        builder.setGameId(102);
        builder.setTimestamp(System.currentTimeMillis());
        builder.setType(LukControlEventType.QUERY_PROP_STATUS.getType());
        builder.setData(JsonUtil.dumps(data));

        Response<GetGameServiceListResponse> response = sdk.PublishControlEvent(builder.build());

        log.info("response: {}", JsonUtil.dumps(response.getData()));
    }

    @Test
    public void getUserProps() throws Exception {
        SDK sdk = new SDK(APP_SECRET, DOMAIN);
        Map<Object, Object> data = MapUtil.builder().put("user_id", "1431178837848312194").build();
        PublishControlEventRequest.Builder builder = new PublishControlEventRequest.Builder();
        builder.setAppId(APP_ID);
        builder.setGameId(102);
        builder.setTimestamp(System.currentTimeMillis());
        builder.setType(LukControlEventType.GET_USER_PROPS.getType());
        builder.setData(JsonUtil.dumps(data));

        Response<GetGameServiceListResponse> response = sdk.PublishControlEvent(builder.build());

        log.info("response: {}", JsonUtil.dumps(response.getData()));
    }

}
