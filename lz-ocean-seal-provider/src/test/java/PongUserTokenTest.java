import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.ocean.seal.api.GameReportService;
import fm.lizhi.ocean.seal.protocol.GameReportServiceProto;
import fm.lizhi.pongpong.middle.user.api.token.PongTokenService;
import fm.lizhi.pongpong.middle.user.protocol.token.PongTokenProto;
import org.junit.Test;

import javax.inject.Inject;
import java.util.ArrayList;
import java.util.List;

public class PongUserTokenTest {

    public static final  PongTokenService  pongTokenService= ProxyBuilderContainer.PROXY_BUILDER.buildProxy(PongTokenService.class);

    @Test
    public void getCode() throws Exception {
        Long userId = 5037627506050466348L;
        ArrayList<Long> userIds = new ArrayList();
        userIds.add(5118181354067984428L);
        userIds.add(5122948306057562668L);
        userIds.add(5133165736902001196L);
        userIds.add(5120142962268834860L);
        userIds.add(5120142962268833836L);
        ArrayList<String> tokens = new ArrayList();
        int expire = 3600 * 60 * 24;
        for (int i = 0; i < userIds.size(); i++) {
            PongTokenProto.GenerateTokenParam.Builder builder = PongTokenProto.GenerateTokenParam.newBuilder();
            builder.setCustomerId(userIds.get(i));
            builder.setExpire(expire);
            Result<PongTokenProto.ResponseGenerateToken> responseGenerateTokenResult = pongTokenService.generateToken(builder.build());
            tokens.add(responseGenerateTokenResult.target().getToken() + " --> " + userIds.get(i));
            Thread.sleep(1000);
        }

        for (int j = 0; j < tokens.size(); j++) {
            System.out.println(tokens.get(j));
        }
    }
}
