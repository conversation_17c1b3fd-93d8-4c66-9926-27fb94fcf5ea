/**
 * <AUTHOR>
 * @date 2025/4/21 下午6:22
 * @description
 */

import cn.hutool.crypto.digest.HMac;
import cn.hutool.crypto.digest.HmacAlgorithm;
import org.apache.commons.codec.binary.Hex;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;
import org.springframework.web.client.RestTemplate;

import javax.crypto.Cipher;
import javax.crypto.Mac;
import javax.crypto.spec.IvParameterSpec;
import javax.crypto.spec.SecretKeySpec;
import java.nio.charset.StandardCharsets;
import java.security.InvalidKeyException;
import java.security.NoSuchAlgorithmException;
import java.util.Base64;
import java.util.UUID;

public class SudBillTest {

    public static void main(String[] args) {

        // 准备参数
        String code = "d74532f0ac7c3afe95ac0f2778c3bdb8";
        String timestamp = System.currentTimeMillis() / 1000 + "";
        String trace = UUID.randomUUID().toString();
        String body = "{\"month\":\"2025-04\"}";
        String hmacSecret = "718607e87d12cdbc5194b7b6f0a81d27";

        // 构造待签名原始内容
        String plainText = code + "\n" + timestamp + "\n" + trace + "\n" + body + "\n";

        // 签名算法
        String algorithm = "HmacSHA256";
        String signature = generateHmacSignature(algorithm, hmacSecret, plainText);

        // 输出结果
        System.out.println("待签名原始内容 (plain_text):");
        System.out.println(plainText);
        System.out.println("签名结果 (signature): " + signature);


        // 创建 RestTemplate 实例
        RestTemplate restTemplate = new RestTemplate();
        // 设置请求头
        HttpHeaders headers = new HttpHeaders();
        headers.set("content-type", "application/json");
        headers.set("x-sud-code", code);
        headers.set("x-sud-timestamp", timestamp);
        headers.set("x-sud-trace", trace);
        headers.set("x-sud-signature", signature);

        // 设置请求体
        HttpEntity<String> request = new HttpEntity<>(body, headers);

        // 发送 POST 请求
        ResponseEntity<String> response = restTemplate.exchange(
                "https://console-api.sud.ltd/application/bill/v20250305",
                HttpMethod.POST,
                request,
                String.class
        );

        System.out.println("响应结果 (statusCode): " + response.getStatusCode());
        System.out.println("响应结果 (signature): " + response.getBody());
        System.out.println("响应头 (headers): " + response.getHeaders().get("x-sud-signature"));

        String aesSecretBase64 = "7O90igSLdBNzKza9iKNtH/0V5NVeV4ePvVLWZz/vnS0="; // AES 密钥（Base64 格式）
        String responseBody = response.getBody(); // 加密后的响应体
//        String signatureFromHeader = "3a7bd3e2360a3d29eea436fcfb7e44c735d117c7d8b8f6e8b9b8a7c6d5e4f3a2"; // 响应头中的签名
        String signatureFromHeader = response.getHeaders().get("x-sud-signature").get(0);// 响应头中的签名

        try {
            // 解密响应体
            String decryptedPlainText = decryptResponse(aesSecretBase64, responseBody);
            System.out.println("解密后的明文: " + decryptedPlainText);

            // 验证签名
            boolean isValidSignature = verifySignature(code, timestamp, trace, responseBody, hmacSecret, signatureFromHeader);
            if (isValidSignature) {
                System.out.println("签名验证成功！");
            } else {
                System.out.println("签名验证失败！");
            }

        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * 使用 HMAC 算法生成签名
     *
     * @param algorithm  算法名称，例如 "HmacSHA256"
     * @param secret     HMAC 密钥
     * @param plainText  待签名的原始内容
     * @return 十六进制编码的签名结果
     */
    public static String generateHmacSignature(String algorithm, String secret, String plainText) {
        try {
            // 创建 Mac 实例
            Mac mac = Mac.getInstance(algorithm);

            // 初始化密钥
            SecretKeySpec secretKeySpec = new SecretKeySpec(secret.getBytes(StandardCharsets.UTF_8), algorithm);
            mac.init(secretKeySpec);

            // 计算签名
            byte[] digest = mac.doFinal(plainText.getBytes(StandardCharsets.UTF_8));

            // 将字节数组转换为十六进制字符串
            return Hex.encodeHexString(digest);
        } catch (NoSuchAlgorithmException | InvalidKeyException e) {
            throw new RuntimeException("签名生成失败", e);
        }
    }


    /**
     * 解密响应内容
     *
     * @param aesSecretBase64 AES 密钥（Base64 格式）
     * @param responseBody    加密后的响应体
     * @return 解密后的明文
     * @throws Exception 解密过程中可能抛出的异常
     */
    private static String decryptResponse(String aesSecretBase64, String responseBody) throws Exception {
        // 分割响应体
        String[] parts = responseBody.split("\\.");
        if (parts.length != 2) {
            throw new IllegalArgumentException("响应体格式错误");
        }
        String aesIvBase64 = parts[0]; // Base64 编码的 IV
        String cipherTextBase64 = parts[1]; // Base64 编码的密文

        // 解码 AES 密钥和 IV
        byte[] aesSecret = Base64.getDecoder().decode(aesSecretBase64);
        byte[] ivBytes = Base64.getDecoder().decode(aesIvBase64);

        // 初始化 Cipher
        Cipher cipher = Cipher.getInstance("AES/CBC/PKCS5Padding");
        SecretKeySpec secretKeySpec = new SecretKeySpec(aesSecret, "AES");
        IvParameterSpec ivParameterSpec = new IvParameterSpec(ivBytes);
        cipher.init(Cipher.DECRYPT_MODE, secretKeySpec, ivParameterSpec);

        // 解密并返回明文
        byte[] plainTextBytes = cipher.doFinal(Base64.getDecoder().decode(cipherTextBase64));
        return new String(plainTextBytes, StandardCharsets.UTF_8);
    }


    private static boolean verifySignature(String code, String timestamp, String trace, String responseBody,
                                           String hmacSecret, String signatureFromHeader) throws Exception  {
        // 构造待签名原始内容
        String plainText = code + "\n" + timestamp + "\n" + trace + "\n" + responseBody + "\n";

        // 计算签名
        String algorithm = "HmacSHA256";
        String computedSignature = generateHmacSignature(algorithm, hmacSecret, plainText);

        // 比较签名值
        return computedSignature.equals(signatureFromHeader);
    }
}