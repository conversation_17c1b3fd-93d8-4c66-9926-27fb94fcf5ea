import fm.lizhi.commons.config.config.ConfigBean;
import fm.lizhi.commons.service.client.codec.DataCenterServiceConsumerCodec;
import fm.lizhi.commons.service.client.proxy.ProxyBuilder;

public class ProxyBuilderContainer {

	public static final ProxyBuilder PROXY_BUILDER;
	static {
		PROXY_BUILDER = new ProxyBuilder();
		try {
			PROXY_BUILDER.init(new DataCenterServiceConsumerCodec(), null, "dc_proxy_office", 1, ConfigBean.TEST_OFFICE, ProxyBuilder.DEFAULT_CLOSE_CONN_AFTER_NOT_RESP_HB_SECONDS, ProxyBuilder.DEFAULT_READ_IDLE_INTERVAL_SECONDS);
		} catch (Exception e) {
			throw new RuntimeException(e);
		}
	}

}
