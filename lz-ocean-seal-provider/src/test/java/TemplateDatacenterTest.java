import fm.lizhi.common.jsonparser.JsonUtils;
import fm.lizhi.commons.config.util.JsonUtil;
import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.commons.service.client.rcode.GeneralRCode;
import fm.lizhi.ocean.seal.api.GameReportService;
import fm.lizhi.ocean.seal.api.GameService;
import fm.lizhi.ocean.seal.api.SealAuthService;
import fm.lizhi.ocean.seal.api.SealTokenService;
import fm.lizhi.ocean.seal.protocol.GameReportServiceProto;
import fm.lizhi.ocean.seal.protocol.GameServiceProto;
import fm.lizhi.ocean.seal.protocol.SealTokenServiceProto;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;

import java.util.ArrayList;
import java.util.List;

@Slf4j
public class TemplateDatacenterTest {

    public static final GameReportService service = ProxyBuilderContainer.PROXY_BUILDER.buildProxy(GameReportService.class);

    public static final SealTokenService sealToken = ProxyBuilderContainer.PROXY_BUILDER.buildProxy(SealTokenService.class);
    public static final GameService gameService = ProxyBuilderContainer.PROXY_BUILDER.buildProxy(GameService.class);

    @Test
    public void getGameVersion(){
        List list= new ArrayList();
        list.add(GameServiceProto.GameVersionParams.newBuilder().setGameId("5246914438883181184").setGameVersionCode(1).build());
        Result<GameServiceProto.ResponseGetGameVersionInfo> result = gameService.getGameVersionInfo(
                "6518293", 1, 300, list, 345L);
        if(result.rCode() == GeneralRCode.GENERAL_RCODE_SUCCESS){
            System.out.println("请求正常");
            System.out.println(result.target().getGameVersionInfosList().size());
        }
    }

    @Test
    public void getSealToken() throws Exception{
        Result<SealTokenServiceProto.ResponseGetSealHashingToken> result
                = sealToken.getSealHashingToken(SealTokenServiceProto.SealHashingTokenParam.newBuilder()
                .setNotVerifySign(true).setAppId("1045111")
                .setUserId(1234567890123L).setCurrentTimeMillis(1750833561707L).build());
        System.out.println("rCode=" + result.rCode());
        if (result.rCode() == 0) {
            System.out.println(result.target().getSealToken());
        }
    }

    @Test
    public void getCode() throws Exception {
        Result<GameReportServiceProto.ResponseGetGameSettleReport> gameSettleReport = service.getGameSettleReport("cuw9kdrp2rsp-cehorlmy01pq-cwqp0km3p0vd", "sud", "123");
        System.out.println("rCode=" + gameSettleReport.rCode());
        if (gameSettleReport.rCode() == 0) {
            GameReportServiceProto.GameSettleResult result = gameSettleReport.target().getResult();
            System.out.println(result);
        }
        Result<GameReportServiceProto.ResponseGetRoomGameReportList> roomGameReportList = service.getRoomGameReportList("8001", "sud", 1, 10, "123");
        System.out.println("rCode=" + roomGameReportList.rCode());
        if (roomGameReportList.rCode() == 0) {
            List<GameReportServiceProto.GameResult> result = roomGameReportList.target().getResultList();
            System.out.println(result);
        }
    }
}
