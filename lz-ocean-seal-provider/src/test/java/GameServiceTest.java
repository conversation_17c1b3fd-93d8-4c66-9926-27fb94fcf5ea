import fm.lizhi.commons.config.util.JsonUtil;
import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.ocean.seal.api.GameAuthService;
import fm.lizhi.ocean.seal.api.GameService;
import fm.lizhi.ocean.seal.constant.GameChannel;
import fm.lizhi.ocean.seal.protocol.GameAuthServiceProto;
import fm.lizhi.ocean.seal.protocol.GameServiceProto;
import io.github.cfgametech.Response;
import io.github.cfgametech.SDK;
import io.github.cfgametech.beans.GetChannelTokenRequest;
import io.github.cfgametech.beans.GetChannelTokenResponse;
import io.github.cfgametech.beans.GetGameServiceListRequest;
import io.github.cfgametech.beans.GetGameServiceListResponse;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import tech.sud.mgp.auth.api.SudCode;
import tech.sud.mgp.auth.api.SudMGPAuth;

/**
 */
@Slf4j
public class GameServiceTest {

    static {
//        -Dmetadata.region=cn
//                -Dmetadata.deploy.env=test
//                -Dmetadata.business.env=lizhi
//                -Dmetadata.service.name=lz_ocean_seal
//                -Dconf.env=office
//                -Dconf.key=lz_ocean_seal
//                -Dapp.name=lz_ocean_seal
//                -Dregion=cn
//                -DbusinessEnv=lizhi
//                -DCAT_HOME=/tmp
        System.setProperty("metadata.region", "cn");
        System.setProperty("metadata.deploy.env", "test");
        System.setProperty("metadata.service.name", "lz_ocean_seal_local");
        System.setProperty("conf.env", "cn");
        System.setProperty("region", "cn");
        System.setProperty("businessEnv", "lizhi");

    }

    public static final GameService gameService = ProxyBuilderContainer.PROXY_BUILDER.buildProxy(GameService.class);


    @Test
    public void getGameDetail() throws Exception {
        GameServiceProto.GetGameLoadingConfigParam.Builder builder = GameServiceProto.GetGameLoadingConfigParam.newBuilder();
        builder.setAppId("1895023737905397760");
        builder.setGameId("102");

        Result<GameServiceProto.ResponseGetGameLoadingConfig> result = gameService.getGameLoadingConfig(builder.build());
        System.out.println("rCode=" + result.rCode());
        if (result.rCode() == 0) {
            GameServiceProto.GameLoadingConfig target = result.target().getGameLoadingConfig();
            System.out.println(target);
        }
    }
}
