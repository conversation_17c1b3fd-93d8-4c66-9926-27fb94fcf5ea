package fm.lizhi.ocean.seal.manager;

import com.google.inject.Inject;
import com.netflix.governator.annotations.AutoBindSingleton;
import fm.lizhi.common.datastore.redis.client.RedisClient;
import fm.lizhi.ocean.seal.conf.DataStoreConfig;
import lombok.extern.slf4j.Slf4j;
import redis.clients.jedis.params.SetParams;

@Slf4j
@AutoBindSingleton
public class RedisManager {
    @Inject
    @com.google.inject.name.Named(DataStoreConfig.REDIS_NAME_SPACE)
    private RedisClient redisClient;

    private final static String OK = "OK";

    public RedisClient getRedisClient(){
        return redisClient;
    }

    public boolean setNx(String key, String value, long expiredTime){
        SetParams params = new SetParams();
        params.nx();
        params.px(expiredTime);
        return OK.equals(getRedisClient().set(key, value, params));
    }
}
