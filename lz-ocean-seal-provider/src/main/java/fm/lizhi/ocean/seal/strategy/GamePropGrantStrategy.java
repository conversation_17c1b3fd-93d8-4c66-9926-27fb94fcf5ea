package fm.lizhi.ocean.seal.strategy;

import fm.lizhi.ocean.seal.dao.bean.GameBizGameBean;
import fm.lizhi.ocean.seal.dao.bean.GameChannelBean;
import fm.lizhi.ocean.seal.dao.bean.GameInfoBean;
import fm.lizhi.ocean.seal.dao.bean.GamePropBean;
import fm.lizhi.ocean.seal.pojo.GamePropGrantResult;
import fm.lizhi.ocean.seal.protocol.GamePropServiceProto.GrantGamePropParam;

/**
 * 游戏道具发放策略接口
 * <AUTHOR>
 */
public interface GamePropGrantStrategy {

    /**
     * 发放道具
     * @param param 发放参数
     * @param gameProp 道具信息
     * @param bizGameBean 业务游戏信息
     * @param gameInfoBean 游戏信息
     * @param gameChannelBean 渠道信息
     * @return 发放结果
     */
    GamePropGrantResult grantProp(GrantGamePropParam param,
                                  GamePropBean gameProp,
                                  GameBizGameBean bizGameBean,
                                  GameInfoBean gameInfoBean,
                                  GameChannelBean gameChannelBean);

    /**
     * 发放道具（策略内部处理数据查询）
     * @param param 发放参数
     * @param gameProp 道具信息
     * @return 发放结果
     */
    GamePropGrantResult grantProp(GrantGamePropParam param, GamePropBean gameProp);

    /**
     * 是否支持该渠道
     * @param channel 渠道名称
     * @return 是否支持
     */
    boolean supports(String channel);


    int GRANT_SUCCESS = 0;
    int GRANT_FAIL = 1;
}
