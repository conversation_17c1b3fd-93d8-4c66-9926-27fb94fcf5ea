package fm.lizhi.ocean.seal.pojo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 基础响应体
 *
 * @param <T>
 * <AUTHOR>
 */
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Data
public class BusinessResp<T> {

    /**
     * 响应码
     */
    private int rCode;

    /**
     * 响应消息
     */
    private String msg;

    /**
     * 响应数据
     */
    private T data;

    public boolean isSuccess() {
        return rCode == 0;
    }

}
