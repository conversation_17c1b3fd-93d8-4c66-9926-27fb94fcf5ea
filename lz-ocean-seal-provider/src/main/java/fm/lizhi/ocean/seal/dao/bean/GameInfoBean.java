package fm.lizhi.ocean.seal.dao.bean;

import java.util.Date;
import javax.persistence.*;

/**
 * This class was generated by DataStore MyBatis Generator.
 *
 * 游戏信息表
 *
 * @date 2022-05-19 08:06:23
 */
@Table(name = "`game_info`")
public class GameInfoBean {
    @Id
    @GeneratedValue
    @Column(name= "`id`")
    private Long id;

    /**
     * 渠道游戏ID
     */
    @Column(name= "`channel_game_id`")
    private Long channelGameId;

    /**
     * String类型的渠道游戏ID，为了不影响旧业务，新增是比较合适的方式
     */
    @Column(name= "`channel_game_id_str`")
    private String channelGameIdStr;

    /**
     * 游戏名称
     */
    @Column(name= "`name`")
    private String name;

    /**
     * 游戏描述
     */
    @Column(name= "`desc`")
    private String desc;

    /**
     * 扩展参数
     */
    @Column(name= "`extra_json`")
    private String extraJson;

    /**
     * 1-启用 0-禁用
     */
    @Column(name= "`status`")
    private Integer status;

    /**
     * 客户端渲染类型，0：WEB，1：NATIVE
     */
    @Column(name= "`render_type`")
    private Integer renderType;

    /**
     * 是否需要队长，1：是，0：否
     */
    @Column(name= "`captain`")
    private Integer captain;

    /**
     * 最低消费：0没有 1有
     */
    @Column(name= "`low_expense`")
    private Integer lowExpense;

    /**
     * 最低消费值
     */
    @Column(name= "`low_expense_value`")
    private String lowExpenseValue;

    /**
     * 接入方式 0 接入 1待接入
     */
    @Column(name= "`access_type`")
    private Integer accessType;

    /**
     * 付费方式：1, "月MAU付费"，2, "包月付费"，3,"季MAU付费"，4,"包季付费"，5, "半年MAU付费"，6, "包半年付费"，7, "年MAU付费"，8, "包年付费"，9, "总请求量付费"
     */
    @Column(name= "`pay_way_code`")
    private Integer payWayCode;

    /**
     * 计费规则
     */
    @Column(name= "`expense_rule`")
    private String expenseRule;

    /**
     * 音频地址，使用逗号分隔
     */
    @Column(name= "`game_video`")
    private String gameVideo;

    /**
     * 图片url,使用逗号分隔
     */
    @Column(name= "`game_image`")
    private String gameImage;

    /**
     * 游戏来源渠道
     */
    @Column(name= "`channel`")
    private String channel;

    /**
     * 操作者
     */
    @Column(name= "`operator`")
    private String operator;

    @Column(name= "`create_time`")
    private Date createTime;

    @Column(name= "`modify_time`")
    private Date modifyTime;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getChannelGameId() {
        return channelGameId;
    }

    public void setChannelGameId(Long channelGameId) {
        this.channelGameId = channelGameId;
    }

    public String getChannelGameIdStr() {
        return channelGameIdStr;
    }

    public void setChannelGameIdStr(String channelGameIdStr) {
        this.channelGameIdStr = channelGameIdStr == null ? null : channelGameIdStr.trim();
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name == null ? null : name.trim();
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc == null ? null : desc.trim();
    }

    public String getExtraJson() {
        return extraJson;
    }

    public void setExtraJson(String extraJson) {
        this.extraJson = extraJson == null ? null : extraJson.trim();
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public Integer getRenderType() {
        return renderType;
    }

    public void setRenderType(Integer renderType) {
        this.renderType = renderType;
    }

    public Integer getCaptain() {
        return captain;
    }

    public void setCaptain(Integer captain) {
        this.captain = captain;
    }

    public Integer getLowExpense() {
        return lowExpense;
    }

    public void setLowExpense(Integer lowExpense) {
        this.lowExpense = lowExpense;
    }

    public String getLowExpenseValue() {
        return lowExpenseValue;
    }

    public void setLowExpenseValue(String lowExpenseValue) {
        this.lowExpenseValue = lowExpenseValue == null ? null : lowExpenseValue.trim();
    }

    public Integer getAccessType() {
        return accessType;
    }

    public void setAccessType(Integer accessType) {
        this.accessType = accessType;
    }

    public Integer getPayWayCode() {
        return payWayCode;
    }

    public void setPayWayCode(Integer payWayCode) {
        this.payWayCode = payWayCode;
    }

    public String getExpenseRule() {
        return expenseRule;
    }

    public void setExpenseRule(String expenseRule) {
        this.expenseRule = expenseRule == null ? null : expenseRule.trim();
    }

    public String getGameVideo() {
        return gameVideo;
    }

    public void setGameVideo(String gameVideo) {
        this.gameVideo = gameVideo == null ? null : gameVideo.trim();
    }

    public String getGameImage() {
        return gameImage;
    }

    public void setGameImage(String gameImage) {
        this.gameImage = gameImage == null ? null : gameImage.trim();
    }

    public String getChannel() {
        return channel;
    }

    public void setChannel(String channel) {
        this.channel = channel == null ? null : channel.trim();
    }

    public String getOperator() {
        return operator;
    }

    public void setOperator(String operator) {
        this.operator = operator == null ? null : operator.trim();
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getModifyTime() {
        return modifyTime;
    }

    public void setModifyTime(Date modifyTime) {
        this.modifyTime = modifyTime;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", channelGameId=").append(channelGameId);
        sb.append(", channelGameIdStr=").append(channelGameIdStr);
        sb.append(", name=").append(name);
        sb.append(", desc=").append(desc);
        sb.append(", extraJson=").append(extraJson);
        sb.append(", status=").append(status);
        sb.append(", renderType=").append(renderType);
        sb.append(", captain=").append(captain);
        sb.append(", lowExpense=").append(lowExpense);
        sb.append(", lowExpenseValue=").append(lowExpenseValue);
        sb.append(", accessType=").append(accessType);
        sb.append(", payWayCode=").append(payWayCode);
        sb.append(", expenseRule=").append(expenseRule);
        sb.append(", gameVideo=").append(gameVideo);
        sb.append(", gameImage=").append(gameImage);
        sb.append(", channel=").append(channel);
        sb.append(", operator=").append(operator);
        sb.append(", createTime=").append(createTime);
        sb.append(", modifyTime=").append(modifyTime);
        sb.append("]");
        return sb.toString();
    }
}