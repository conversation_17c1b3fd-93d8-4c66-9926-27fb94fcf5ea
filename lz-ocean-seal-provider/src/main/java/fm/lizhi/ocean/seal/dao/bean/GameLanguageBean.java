package fm.lizhi.ocean.seal.dao.bean;

import java.util.Date;
import javax.persistence.*;

/**
 * This class was generated by DataStore MyBatis Generator.
 *
 * 游戏语言元数据表
 *
 * @date 2022-04-22 05:21:24
 */
@Table(name = "`game_language`")
public class GameLanguageBean {
    @Id
    @Column(name= "`id`")
    private Long id;

    /**
     * 中文名称
     */
    @Column(name= "`name`")
    private String name;

    /**
     * 语言简称 zh en
     */
    @Column(name= "`abbreviation`")
    private String abbreviation;

    @Column(name= "`create_time`")
    private Date createTime;

    @Column(name= "`modify_time`")
    private Date modifyTime;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name == null ? null : name.trim();
    }

    public String getAbbreviation() {
        return abbreviation;
    }

    public void setAbbreviation(String abbreviation) {
        this.abbreviation = abbreviation == null ? null : abbreviation.trim();
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getModifyTime() {
        return modifyTime;
    }

    public void setModifyTime(Date modifyTime) {
        this.modifyTime = modifyTime;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", name=").append(name);
        sb.append(", abbreviation=").append(abbreviation);
        sb.append(", createTime=").append(createTime);
        sb.append(", modifyTime=").append(modifyTime);
        sb.append("]");
        return sb.toString();
    }
}