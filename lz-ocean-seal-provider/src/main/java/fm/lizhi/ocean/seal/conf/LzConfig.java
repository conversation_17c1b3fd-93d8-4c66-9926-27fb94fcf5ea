package fm.lizhi.ocean.seal.conf;

import lombok.Data;

@Data
public class LzConfig {
	/**
	 * 是否生产环境
	 */
	private boolean isProduct;
	/**
	 * 服务代理配置地址
	 */
	private String dcProxyConfigKey;

	/**
	 * 到服务代理连接数
	 */
	private int dcProxyConnectionCount;

	/**
	 * 工作线程数
	 */
	private int workerCount;

	/**
	 * 开启游戏数据分发环境
	 */
	private int dispatchGameReportSwitchEnv;

	/**
	 * 开启游戏数据分发处理任务(1开启)
	 */
	private int dispatchGameReportSwitch;

	/**
	 * appId对应的appName
	 */
	private String appIdAndNameMap;

	/**
	 * 大数据上报链接，默认测试
	 */
	private String bigDataServerUrl = "https://cnstat.lz310.com/sa?project=seal_server_test";
	/**
	 * Seal token 过期时间（秒），这个时间需要21600s（6h）
	 */
	private long hashingSealTokenExpire = 21600L;
	/**
	 * Seal token 生成jwt过期时间（秒），这个时间需要比sealTokenExpire的时间大，最好大几天，目前设置15天：
	 */
	private long jwtSealTokenExpire = 1296000L;
	/**
	 * 业务自定义游戏配置，默认过期时间10分钟
	 */
	private int bizGameConfigExpireTime = 10 * 60;
	/**
	 * 定时从声网获取游戏列表
	 */
	private boolean startTimerGetGameList = false;

	private String kafkaClusterNamespace = "public-kafka250-bootstrap-server";
	/**
	 * kafka key从最新消息消费: lz-commons-kafka-async-latest
	 */
	private String kafkaLastConfigKey = "lz-commons-kafka-async-latest";

	/**
	 * 新GroupId从历史最早的offset开始消费消息: lz-commons-kafka-async
	 */
	private String kafkaConfigKey = "lz-commons-kafka-async";

	/**
	 * 查询sud账单code
	 */
	private String sudBillCode = "d74532f0ac7c3afe95ac0f2778c3bdb8";
	/**
	 *  查询sud账单hmacSecret
	 */
	private String sudBillHmacSecret = "718607e87d12cdbc5194b7b6f0a81d27";
	/**
	 * 查询sud账单url
	 */
	private String sudBillUrl = "https://console-api.sud.ltd/application/bill/v20250305";
	/**
	 * 查询sud账单AES 密钥（Base64 格式）
	 */
	private String aesSecretBase64 = "7O90igSLdBNzKza9iKNtH/0V5NVeV4ePvVLWZz/vnS0=";

}
