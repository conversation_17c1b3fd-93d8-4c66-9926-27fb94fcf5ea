package fm.lizhi.ocean.seal.agora.token;

/**
 * Created in 2022-05-18 11:34.
 *
 * <AUTHOR>
 */
public class RtmTokenBuilder {
    public AccessToken mTokenCreator;

    public String buildToken(String appId, String appCertificate,
                             String uid, int privilegeTs) throws Exception {
        mTokenCreator = new AccessToken(appId, appCertificate, uid, "");
        mTokenCreator.addPrivilege(AccessToken.Privileges.kRtmLogin, privilegeTs);
        return mTokenCreator.build();
    }

    public void setPrivilege(AccessToken.Privileges privilege, int expireTs) {
        mTokenCreator.addPrivilege(privilege, expireTs);
    }

    public boolean initTokenBuilder(String originToken) {
        mTokenCreator.fromString(originToken);
        return true;
    }

    public enum Role {
        Rtm_User(1);

        int value;

        Role(int value) {
            this.value = value;
        }
    }
}