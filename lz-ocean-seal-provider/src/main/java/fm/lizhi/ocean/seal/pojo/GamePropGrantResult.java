package fm.lizhi.ocean.seal.pojo;

import lombok.Data;

/**
 * 游戏道具发放结果
 * <AUTHOR>
 */
@Data
public class GamePropGrantResult {

    /**
     * 是否成功
     */
    private boolean success;

    /**
     * 业务码
     */
    private int bizCode;

    /**
     * 消息
     */
    private String message;

    /**
     * 响应数据
     */
    private String data;

    public GamePropGrantResult() {
    }

    public GamePropGrantResult(boolean success, int bizCode, String message) {
        this.success = success;
        this.bizCode = bizCode;
        this.message = message;
    }

    public GamePropGrantResult(boolean success, int bizCode, String message, String data) {
        this.success = success;
        this.bizCode = bizCode;
        this.message = message;
        this.data = data;
    }

    /**
     * 创建成功结果
     */
    public static GamePropGrantResult success() {
        return new GamePropGrantResult(true, 0, "success");
    }

    /**
     * 创建成功结果
     */
    public static GamePropGrantResult success(String data) {
        return new GamePropGrantResult(true, 0, "success", data);
    }

    /**
     * 创建失败结果
     */
    public static GamePropGrantResult failure(int bizCode, String message) {
        return new GamePropGrantResult(false, bizCode, message);
    }
}
