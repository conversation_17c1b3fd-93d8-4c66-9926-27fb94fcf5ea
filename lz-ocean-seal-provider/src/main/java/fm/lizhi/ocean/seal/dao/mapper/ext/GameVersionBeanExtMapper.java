package fm.lizhi.ocean.seal.dao.mapper.ext;


import fm.lizhi.common.datastore.core.annotation.DataStore;
import fm.lizhi.ocean.seal.dao.bean.GameVersionBean;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

@DataStore(namespace = "mysql_ocean_oceanseal")
public interface GameVersionBeanExtMapper {
    /**
     * 查询 某个业务 关联的游戏 支持的seal sdk最低版本的最大游戏版本号。
     * 例如： funbox 关联 ludo游戏  嵌入的 sealSdk 版本为 1000 游戏版本为100
     *  现在存在游戏版本记录： funbox ludo游戏 sealSdk版本最低为999 另外游戏版本已经更新到102
     *  那么根据对比判断。将会查询到 funbox ludo minsealsdk 999 gameversion 102 的记录
     *  这里为什么需要引入appId。理论上是不需要考虑业务的，只需要考虑游戏。但是目前存在的情况是。不同业务定制了不同的seal sdk。而这里的版本又存在重叠。所以需要增加appId
     *
     * select * from game_version where `app_id` = '1' and `game_id` = 1 and `system_type` = 1 and `min_sdk_version` <= 123 and `game_version` > 1 order by `game_version` desc limit 1;
     *
     * @param appId      业务的appId
     * @param gameId      游戏Id
     * @param systemType  app 系统版本 0 未知 1 ios 2 android
     * @param sdkVersion  sdk版本
     * @param gameVersion 游戏版本
     * @return
     */
    @Select("select * from game_version where `app_id` = #{appId} and `game_id` = #{gameId} and `system_type` = #{systemType} and `min_sdk_version` <= #{sdkVersion} and `game_version` > #{gameVersion} order by `game_version` desc limit 1;")
    GameVersionBean selectLatestVersion(@Param("appId") String appId, @Param("gameId") Long gameId, @Param("systemType") int systemType, @Param("sdkVersion") long sdkVersion, @Param("gameVersion") long gameVersion);
}
