package fm.lizhi.ocean.seal.manager;

import com.google.inject.Inject;
import com.netflix.governator.annotations.AutoBindSingleton;
import fm.lizhi.common.datastore.mysql.mybatis.PageList;
import fm.lizhi.commons.util.GuidGenerator;
import fm.lizhi.ocean.seal.constant.StatusConstant;
import fm.lizhi.ocean.seal.dao.bean.GameChannelBean;
import fm.lizhi.ocean.seal.dao.mapper.GameChannelBeanMapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.util.CollectionUtils;

import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Optional;

@Slf4j
@AutoBindSingleton
public class GameChannelManager {
    @Inject
    private GameChannelBeanMapper gameChannelBeanMapper;
    @Inject
    private GuidGenerator guidGenerator;

    public List<GameChannelBean> getAllGameChannels() {
        GameChannelBean gameChannelBean = new GameChannelBean();
        gameChannelBean.setState(StatusConstant.ENABLE);
        return gameChannelBeanMapper.selectMany(gameChannelBean);
    }

    public PageList<GameChannelBean> pageSearchChannel(long channelId, String appId, int pageSize, int pageNumber){
        GameChannelBean bean = new GameChannelBean();
        if(channelId > 0L){
            bean.setId(channelId);
        }
        if(StringUtils.isNotBlank(appId)){
            bean.setAppId(appId);
        }

        return gameChannelBeanMapper.selectPage(bean, pageNumber, pageSize);
    }

    /**
     * 根据appId查询
     *
     * @param appId
     * @return
     */
    public GameChannelBean getChannelByAppId(String appId) {
        GameChannelBean bean = new GameChannelBean();
        bean.setAppId(appId);
        return this.gameChannelBeanMapper.selectOne(bean);
    }

    /**
     * 获取游戏渠道信息
     *
     * @param id
     * @return
     */
    public GameChannelBean getGameChannel(long id) {
        GameChannelBean bean = new GameChannelBean();
        bean.setId(id);
        return this.gameChannelBeanMapper.selectByPrimaryKey(bean);
    }

    public void delChannelById(long id) {
        GameChannelBean bean = new GameChannelBean();
        bean.setId(id);
        this.gameChannelBeanMapper.deleteByPrimaryKey(bean);
    }

    public void insertOrUpdateChannel(long id, String name, String channel
            , String appId, String appKey, String appSecret, String desc, String extConfig){
        boolean insert = id <= 0L;
        Date date = new Date();
        GameChannelBean bean = new GameChannelBean();
        if(!insert){
            bean = getGameChannel(id);
            if(bean == null){
                return;
            }
        }else {
            bean.setId(guidGenerator.genId());
            bean.setCreateTime(date);
        }

        bean.setName(name);
        bean.setChannel(channel);
        bean.setAppId(appId);
        bean.setAppKey(appKey);
        bean.setAppSecret(appSecret);
        bean.setExtConfig(Optional.ofNullable(extConfig).orElse(StringUtils.EMPTY));
        bean.setState(NumberUtils.INTEGER_ONE);
        bean.setDescription(desc);
        bean.setModifyTime(date);
        if(insert){
            gameChannelBeanMapper.insert(bean);
        }else {
            gameChannelBeanMapper.updateByPrimaryKey(bean);
        }

    }

    /**
     * 获取游戏渠道信息
     *
     * @param channel
     * @return
     */
    public List<GameChannelBean> getGameChannelByChannel(String channel) {
        GameChannelBean bean = new GameChannelBean();
        bean.setChannel(channel);
        bean.setState(StatusConstant.ENABLE);
        return gameChannelBeanMapper.selectMany(bean);
    }

}
