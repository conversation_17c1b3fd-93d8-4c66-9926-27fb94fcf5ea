package fm.lizhi.ocean.seal.http.helper;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * Created in 2019-08-19 14:46.
 *
 * <AUTHOR>
 */
public class LogHelper {
    private static Logger logger = LoggerFactory.getLogger(LogHelper.class);

    public static boolean getBooleanProperty(String key) {
        String property = System.getProperty(key, String.valueOf(false));
        return Boolean.parseBoolean(property);
    }

    /**
     * 是否开启统计日志
     *
     * @return
     */
    public static boolean isOpenStats() {
        return getBooleanProperty("commons.http.stats");
    }

    /**
     * 是否开启请求日志
     *
     * @return
     */
    public static boolean isOpenRequestLog() {
        return getBooleanProperty("commons.http.request.log");
    }
}