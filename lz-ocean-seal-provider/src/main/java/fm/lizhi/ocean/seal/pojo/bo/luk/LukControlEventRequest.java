package fm.lizhi.ocean.seal.pojo.bo.luk;

import fm.lizhi.ocean.seal.constant.LukControlEventType;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.Map;

/**
 * LUK 控制事件请求参数
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
public class LukControlEventRequest {

    /**
     * 应用ID
     */
    private String appId;

    /**
     * 游戏ID
     */
    private String gameId;

    /**
     * 房间ID（可选，全局事件不需要）
     */
    private String roomId;

    /**
     * 事件类型
     */
    private LukControlEventType eventType;

    /**
     * 事件数据
     */
    private Map<String, Object> eventData;

    /**
     * 时间戳（可选，默认当前时间）
     */
    private Long timestamp;

    /**
     * 创建房间事件请求
     */
    public static LukControlEventRequest createRoomEvent(String appId, String gameId, String roomId,
                                                        LukControlEventType eventType, Map<String, Object> eventData) {
        return new LukControlEventRequest()
                .setAppId(appId)
                .setGameId(gameId)
                .setRoomId(roomId)
                .setEventType(eventType)
                .setEventData(eventData)
                .setTimestamp(System.currentTimeMillis());
    }

    /**
     * 创建全局事件请求
     */
    public static LukControlEventRequest createGlobalEvent(String appId, String gameId,
                                                          LukControlEventType eventType, Map<String, Object> eventData) {
        return new LukControlEventRequest()
                .setAppId(appId)
                .setGameId(gameId)
                .setEventType(eventType)
                .setEventData(eventData)
                .setTimestamp(System.currentTimeMillis());
    }
}
