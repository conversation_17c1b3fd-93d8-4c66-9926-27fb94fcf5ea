package fm.lizhi.ocean.seal.dao.mapper.ext;


import fm.lizhi.common.datastore.core.annotation.DataStore;
import org.apache.ibatis.annotations.Delete;
import org.apache.ibatis.annotations.Param;

@DataStore(namespace = "mysql_ocean_oceanseal")
public interface GameTypeRelationExtMapper {

    @Delete("delete from game_type_relation where game_id = #{gameId} ")
    void delRelationByGameId(@Param("gameId") long gameId);
}
