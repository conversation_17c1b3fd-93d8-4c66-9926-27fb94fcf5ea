package fm.lizhi.ocean.seal.api.impl;

import com.google.inject.Inject;
import fm.lizhi.common.datastore.mysql.mybatis.PageList;
import fm.lizhi.commons.service.client.annotation.ServiceProvider;
import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.commons.service.client.rcode.GeneralRCode;
import fm.lizhi.ocean.seal.adapter.GameOrcaApiAdapter;
import fm.lizhi.ocean.seal.api.GameOrcaApiService;
import fm.lizhi.ocean.seal.dao.bean.*;
import fm.lizhi.ocean.seal.dao.mapper.ext.BizGameRelationOrcaResult;
import fm.lizhi.ocean.seal.manager.*;
import fm.lizhi.ocean.seal.protocol.GameOrcaApiProto;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.util.CollectionUtils;

import java.util.Collections;

/**
 * 这个类是为orca api 配置服务的。等orca上单表操作功能，可直接删除。
 */

@Slf4j
@ServiceProvider
public class GameOrcaApiServiceImpl implements GameOrcaApiService {
    @Inject
    private GameChannelManager gameChannelManager;
    @Inject
    private GameAppManager gameAppManager;
    @Inject
    private GameInfoManager gameInfoManager;
    @Inject
    private BizGameManager bizGameManager;
    @Inject
    private GameCallbackManager gameCallbackManager;
    @Inject
    private GameVersionManager gameVersionManager;

    @Override
    public Result<Void> addOrUpdateChannel(GameOrcaApiProto.GameChannelPB gameChannel) {
        gameChannelManager.insertOrUpdateChannel(gameChannel.getId(), gameChannel.getName(), gameChannel.getChannel()
                , gameChannel.getAppId(), gameChannel.getAppKey(), gameChannel.getAppSecret(), StringUtils.EMPTY, gameChannel.getExtConfig());
        return new Result<>(GeneralRCode.GENERAL_RCODE_SUCCESS, null);
    }

    @Override
    public Result<GameOrcaApiProto.ResponseGetChannelById> getChannelById(long channelId) {
        GameChannelBean bean = gameChannelManager.getGameChannel(channelId);
        if(null == bean){
            return new Result<>(GET_APP_INFO_BY_ID_NOT_EXISTS, null);
        }
        return new Result<>(GeneralRCode.GENERAL_RCODE_SUCCESS
                , GameOrcaApiProto.ResponseGetChannelById.newBuilder().setGameChannel(GameOrcaApiAdapter.convertChannelToPB(bean)).build());
    }

    @Override
    public Result<GameOrcaApiProto.ResponseQueryPageGameChannels> queryPageGameChannels(long channelId, String appId, int pageSize, int pageNumber) {
        pageSize = pageSize <= 0 ? 500 : pageSize;
        pageNumber = pageNumber <= 0 ? 1 : pageNumber;
        PageList<GameChannelBean> pageList = gameChannelManager.pageSearchChannel(channelId, appId, pageSize, pageNumber);
        if(CollectionUtils.isEmpty(pageList)){
            return new Result<>(GeneralRCode.GENERAL_RCODE_SUCCESS
                    , GameOrcaApiProto.ResponseQueryPageGameChannels.newBuilder()
                    .setPageNumber(pageNumber).setPageSize(pageSize).setTotal(NumberUtils.LONG_ZERO)
                    .addAllGameChannels(Collections.EMPTY_LIST).build());
        }
        return new Result<>(GeneralRCode.GENERAL_RCODE_SUCCESS
                , GameOrcaApiProto.ResponseQueryPageGameChannels.newBuilder()
                .setPageNumber(pageNumber).setPageSize(pageSize).setTotal(pageList.getTotal())
                .addAllGameChannels(GameOrcaApiAdapter.convertGameChannelPBs(pageList)).build());
    }

    @Override
    public Result<Void> delGameChannel(long id) {
        gameChannelManager.delChannelById(id);
        return new Result<>(GeneralRCode.GENERAL_RCODE_SUCCESS,null);
    }

    @Override
    public Result<GameOrcaApiProto.ResponseGetAppInfoById> getAppInfoById(long id) {
        GameAppBean bean = gameAppManager.getGameAppBeanById(id);
        if(null == bean){
            return new Result<>(GET_GAME_BY_ID_NOT_EXISTS, null);
        }
        return new Result<>(GeneralRCode.GENERAL_RCODE_SUCCESS
                , GameOrcaApiProto.ResponseGetAppInfoById.newBuilder().setGameAppInfo(GameOrcaApiAdapter.convertGameAppToPB(bean)).build());
    }

    @Override
    public Result<Void> addOrUpdateGameApp(GameOrcaApiProto.GameAppInfoPB gameAppInfo) {
        gameAppManager.insertOrUpdateGameApp(gameAppInfo.getId()
                , gameAppInfo.getAppId(), gameAppInfo.getAppSecret(), gameAppInfo.getAppName(), gameAppInfo.getAppAlias()
                , gameAppInfo.getAppTopic(), "orcaAdmin");
        return new Result<>(GeneralRCode.GENERAL_RCODE_SUCCESS,null);
    }

    @Override
    public Result<Void> delGameAppById(long id) {
        gameAppManager.delGameAppById(id);
        return new Result<>(GeneralRCode.GENERAL_RCODE_SUCCESS,null);
    }

    @Override
    public Result<GameOrcaApiProto.ResponseQueryPageGameApp> queryPageGameApp(long id, String appId, int pageSize, int pageNumber) {
        pageSize = pageSize <= 0 ? 500 : pageSize;
        pageNumber = pageNumber <= 0 ? 1 : pageNumber;
        PageList<GameAppBean> pageList = gameAppManager.pageSearchGameApp(id, appId, pageSize, pageNumber);
        if(CollectionUtils.isEmpty(pageList)){
            return new Result<>(GeneralRCode.GENERAL_RCODE_SUCCESS
                    , GameOrcaApiProto.ResponseQueryPageGameApp.newBuilder()
                    .setPageNumber(pageNumber).setPageSize(pageSize).setTotal(NumberUtils.LONG_ZERO)
                    .addAllGameAppInfos(Collections.EMPTY_LIST).build());
        }
        return new Result<>(GeneralRCode.GENERAL_RCODE_SUCCESS
                , GameOrcaApiProto.ResponseQueryPageGameApp.newBuilder()
                .setPageNumber(pageNumber).setPageSize(pageSize).setTotal(pageList.getTotal())
                .addAllGameAppInfos(GameOrcaApiAdapter.convertGameAppToPBs(pageList)).build());
    }

    @Override
    public Result<GameOrcaApiProto.ResponseGetGameById> getGameById(long id) {
        GameInfoBean bean = gameInfoManager.getGameInfoBeanById(id);
        if(null == bean){
            return new Result<>(GET_GAME_BY_ID_NOT_EXISTS, null);
        }
        return new Result<>(GeneralRCode.GENERAL_RCODE_SUCCESS
                , GameOrcaApiProto.ResponseGetGameById.newBuilder().setGameInfo(GameOrcaApiAdapter.convertGameInfoToPB(bean)).build());
    }

    @Override
    public Result<Void> addOrUpdateGame(GameOrcaApiProto.GameInfoPB gameInfo) {
        gameInfoManager.insertOrUpdateGameInfo(gameInfo.getId(), gameInfo.getName(), gameInfo.getChannelGameId()
                , gameInfo.hasCaptain() ? gameInfo.getCaptain() : 1, gameInfo.getRenderType()
                , gameInfo.getDesc(), gameInfo.getExtraJson(), gameInfo.getChannel());
        return new Result<>(GeneralRCode.GENERAL_RCODE_SUCCESS, null);
    }

    @Override
    public Result<Void> delGameById(long id) {
        gameInfoManager.delGameInfoById(id);
        return new Result<>(GeneralRCode.GENERAL_RCODE_SUCCESS, null);
    }

    @Override
    public Result<GameOrcaApiProto.ResponseQueryPageGame> queryPageGame(long id, String channel, String name, int pageSize, int pageNumber) {
        pageSize = pageSize <= 0 ? 500 : pageSize;
        pageNumber = pageNumber <= 0 ? 1 : pageNumber;
        PageList<GameInfoBean> pageList = gameInfoManager.pageSearch(id, channel, name, pageSize, pageNumber);
        if(CollectionUtils.isEmpty(pageList)){
            return new Result<>(GeneralRCode.GENERAL_RCODE_SUCCESS
                    , GameOrcaApiProto.ResponseQueryPageGame.newBuilder()
                    .setPageNumber(pageNumber).setPageSize(pageSize).setTotal(NumberUtils.LONG_ZERO)
                    .addAllGameInfoPBs(Collections.EMPTY_LIST).build());
        }
        return new Result<>(GeneralRCode.GENERAL_RCODE_SUCCESS
                , GameOrcaApiProto.ResponseQueryPageGame.newBuilder()
                .setPageNumber(pageNumber).setPageSize(pageSize).setTotal(pageList.getTotal())
                .addAllGameInfoPBs(GameOrcaApiAdapter.convertGameInfoToPBs(pageList)).build());
    }

    @Override
    public Result<GameOrcaApiProto.ResponseGetGameRelationById> getGameRelationById(long id) {
        BizGameRelationOrcaResult orcaResult = bizGameManager.getBizGameRelationOrcaResultById(id);
        if(null == orcaResult){
            return new Result<>(GET_GAME_RELATION_BY_ID_NOT_EXISTS, null);
        }
        return new Result<GameOrcaApiProto.ResponseGetGameRelationById>(GeneralRCode.GENERAL_RCODE_SUCCESS
                , GameOrcaApiProto.ResponseGetGameRelationById.newBuilder()
                .setGameRelation(GameOrcaApiAdapter.convertGameBizGameToPB(orcaResult)).build());
    }

    @Override
    public Result<Void> addOrUpdateGameRelation(GameOrcaApiProto.GameBizGamePB gameBizGame) {
        bizGameManager.insertOrUpdate(gameBizGame.getId(), gameBizGame.getGameAppId()
                , gameBizGame.getGameInfoId(), gameBizGame.getChannelId(), gameBizGame.getConfig(), gameBizGame.getAppId(), gameBizGame.getChannelGameId());
        return new Result<>(GeneralRCode.GENERAL_RCODE_SUCCESS, null);
    }

    @Override
    public Result<Void> delGameRelationById(long id) {
        bizGameManager.delBizGameRelationOrcaResultById(id);
        return new Result<>(GeneralRCode.GENERAL_RCODE_SUCCESS, null);
    }

    @Override
    public Result<GameOrcaApiProto.ResponseQueryPageGameRelation> queryPageGameRelation(long id, long gameAppId, long gameInfoId, long channelId, int pageSize, int pageNumber) {
        pageSize = pageSize <= 0 ? 500 : pageSize;
        pageNumber = pageNumber <= 0 ? 1 : pageNumber;
        PageList<BizGameRelationOrcaResult> pageList
                = bizGameManager.pageSearchBizOrca(id, gameAppId, gameInfoId, channelId, pageSize, pageNumber);
        if(CollectionUtils.isEmpty(pageList)){
            return new Result<>(GeneralRCode.GENERAL_RCODE_SUCCESS
                    , GameOrcaApiProto.ResponseQueryPageGameRelation.newBuilder()
                    .setPageNumber(pageNumber).setPageSize(pageSize).setTotal(NumberUtils.LONG_ZERO)
                    .addAllGameBizGamePBs(Collections.EMPTY_LIST).build());
        }
        return new Result<>(GeneralRCode.GENERAL_RCODE_SUCCESS
                , GameOrcaApiProto.ResponseQueryPageGameRelation.newBuilder()
                .setPageNumber(pageNumber).setPageSize(pageSize).setTotal(pageList.getTotal())
                .addAllGameBizGamePBs(GameOrcaApiAdapter.convertGameBizGameToPBs(pageList)).build());
    }

    @Override
    public Result<GameOrcaApiProto.ResponseGetGameCallbackById> getGameCallbackById(long id) {
        GameCallback gameCallback = gameCallbackManager.getGameCallbackById(id);
        if(null == gameCallback){
            return new Result<>(GET_GAME_CALLBACK_BY_ID_NOT_EXISTS, null);
        }
        return new Result<GameOrcaApiProto.ResponseGetGameCallbackById>(GeneralRCode.GENERAL_RCODE_SUCCESS
                , GameOrcaApiProto.ResponseGetGameCallbackById.newBuilder()
                .setGameCallbackPB(GameOrcaApiAdapter.convertGameCallbackToPB(gameCallback)).build());
    }

    @Override
    public Result<Void> addOrUpdateGameCallback(GameOrcaApiProto.GameCallbackPB gameCallbackPB) {
        gameCallbackManager.insertOrUpdate(gameCallbackPB.getId(), gameCallbackPB.getAppId()
                , gameCallbackPB.getCallbackKey(), gameCallbackPB.getType(), gameCallbackPB.getUrl());
        return new Result<>(GeneralRCode.GENERAL_RCODE_SUCCESS, null);
    }

    @Override
    public Result<Void> delGameCallBackById(long id) {
        gameCallbackManager.delCallback(id);
        return new Result<>(GeneralRCode.GENERAL_RCODE_SUCCESS, null);
    }

    @Override
    public Result<GameOrcaApiProto.ResponseQueryPageGameCallback> queryPageGameCallback(String appId, int pageSize, int pageNumber) {
        pageSize = pageSize <= 0 ? 500 : pageSize;
        pageNumber = pageNumber <= 0 ? 1 : pageNumber;
        PageList<GameCallback> pageList = gameCallbackManager.pageSearch(appId, pageSize, pageNumber);
        if(CollectionUtils.isEmpty(pageList)){
            return new Result<>(GeneralRCode.GENERAL_RCODE_SUCCESS
                    , GameOrcaApiProto.ResponseQueryPageGameCallback.newBuilder()
                    .setPageNumber(pageNumber).setPageSize(pageSize).setTotal(NumberUtils.LONG_ZERO)
                    .addAllGameCallbackPBs(Collections.EMPTY_LIST).build());
        }
        return new Result<>(GeneralRCode.GENERAL_RCODE_SUCCESS
                , GameOrcaApiProto.ResponseQueryPageGameCallback.newBuilder()
                .setPageNumber(pageNumber).setPageSize(pageSize).setTotal(pageList.getTotal())
                .addAllGameCallbackPBs(GameOrcaApiAdapter.convertGameCallbackToPBs(pageList)).build());
    }

    @Override
    public Result<GameOrcaApiProto.ResponseGetGameVersionById> getGameVersionById(long id) {
        GameVersionBean bean = gameVersionManager.getGameVersionBeanById(id);

        if(null == bean){
            return new Result<>(GET_GAME_VERSION_BY_ID_NOT_EXISTS, null);
        }
        return new Result<>(GeneralRCode.GENERAL_RCODE_SUCCESS
                , GameOrcaApiProto.ResponseGetGameVersionById.newBuilder()
                .setGameVersionPB(GameOrcaApiAdapter.convertGameVersionToPB(bean)).build());
    }

    @Override
    public Result<Void> addOrUpdateGameVersion(GameOrcaApiProto.GameVersionPB gameVersionPB) {
        gameVersionManager.insertOrUpdate(gameVersionPB.getId(), gameVersionPB.getAppId(), gameVersionPB.getGameId()
            , gameVersionPB.getSystemType(), gameVersionPB.getMinSdkVersion(), gameVersionPB.getGameVersion()
            , gameVersionPB.getForceUpdate(), gameVersionPB.getDownloadLink(), gameVersionPB.getGameFlow(), gameVersionPB.getExtConfig());
        return new Result<>(GeneralRCode.GENERAL_RCODE_SUCCESS, null);
    }

    @Override
    public Result<Void> delGameVersionById(long id) {
        gameVersionManager.delGameVersionBean(id);
        return new Result<>(GeneralRCode.GENERAL_RCODE_SUCCESS, null);
    }

    @Override
    public Result<GameOrcaApiProto.ResponseQueryPageGameVersion> queryPageGameVersion(GameOrcaApiProto.SearchVersionParam searchVersionParam) {
        int pageSize = searchVersionParam.getPageSize() <= 0 ? 500 : searchVersionParam.getPageSize();
        int pageNumber = searchVersionParam.getPageNumber() <= 0 ? 1 : searchVersionParam.getPageNumber();

        PageList<GameVersionBean> pageList = gameVersionManager.pageSearch(
                searchVersionParam.getAppId(), searchVersionParam.hasGameId() ? searchVersionParam.getGameId() : null,
                searchVersionParam.hasSystemType() ? searchVersionParam.getSystemType() : null,
                searchVersionParam.hasGameVersion() ? searchVersionParam.getGameVersion() : null
                , pageSize, pageNumber);
        if(CollectionUtils.isEmpty(pageList)){
            return new Result<>(GeneralRCode.GENERAL_RCODE_SUCCESS
                    , GameOrcaApiProto.ResponseQueryPageGameVersion.newBuilder()
                    .setPageNumber(pageNumber).setPageSize(pageSize).setTotal(NumberUtils.LONG_ZERO)
                    .addAllGameVersionPBs(Collections.EMPTY_LIST).build());
        }
        return new Result<>(GeneralRCode.GENERAL_RCODE_SUCCESS
                , GameOrcaApiProto.ResponseQueryPageGameVersion.newBuilder()
                .setPageNumber(pageNumber).setPageSize(pageSize).setTotal(pageList.getTotal())
                .addAllGameVersionPBs(GameOrcaApiAdapter.convertGameVersionToPBs(pageList)).build());
    }
}
