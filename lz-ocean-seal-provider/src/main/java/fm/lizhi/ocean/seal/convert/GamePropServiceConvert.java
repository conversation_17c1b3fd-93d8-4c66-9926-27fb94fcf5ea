package fm.lizhi.ocean.seal.convert;


import fm.lizhi.common.datastore.mysql.mybatis.PageList;
import fm.lizhi.ocean.seal.dao.bean.GamePropBean;
import fm.lizhi.ocean.seal.protocol.GamePropServiceProto;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.ReportingPolicy;
import org.mapstruct.factory.Mappers;

import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Mapper(unmappedTargetPolicy = ReportingPolicy.ERROR,
        imports = {}, uses = {CommonConvert.class}
)
public interface GamePropServiceConvert {

    GamePropServiceConvert I = Mappers.getMapper(GamePropServiceConvert.class);

    default Iterable<GamePropServiceProto.GameProp> convertGamePropBeansToGameProps(PageList<GamePropBean> propList){
        return propList.stream().map(this::convertGamePropBeanToGameProp).collect(Collectors.toList());
    }

    @Mapping(target = "channelPropIdBytes", ignore = true)
    @Mapping(target = "channelGameIdBytes", ignore = true)
    @Mapping(target = "unknownFields", ignore = true)
    @Mapping(target = "remarkBytes", ignore = true)
    @Mapping(target = "propDescBytes", ignore = true)
    @Mapping(target = "operatorBytes", ignore = true)
    @Mapping(target = "nameBytes", ignore = true)
    @Mapping(target = "mergeFrom", ignore = true)
    @Mapping(target = "iconUrlBytes", ignore = true)
    @Mapping(target = "defaultInstanceForType", ignore = true)
    @Mapping(target = "allFields", ignore = true)
    @Mapping(target = "mergeUnknownFields", ignore = true)
    @Mapping(target = "clearField", ignore = true)
    GamePropServiceProto.GameProp convertGamePropBeanToGameProp(GamePropBean gamePropBean);
}
