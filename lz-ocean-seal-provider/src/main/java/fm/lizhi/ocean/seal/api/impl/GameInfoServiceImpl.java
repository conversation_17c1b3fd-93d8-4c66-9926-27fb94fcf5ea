package fm.lizhi.ocean.seal.api.impl;

import com.google.common.base.Joiner;
import com.google.common.base.Splitter;
import com.google.inject.Inject;
import fm.lizhi.commons.service.client.annotation.ServiceProvider;
import fm.lizhi.commons.service.client.log.LogContext;
import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.commons.service.client.rcode.GeneralRCode;
import fm.lizhi.commons.util.GuidGenerator;
import fm.lizhi.ocean.seal.api.GameInfoService;
import fm.lizhi.ocean.seal.constant.SealRCode;
import fm.lizhi.ocean.seal.dao.bean.GameAppBean;
import fm.lizhi.ocean.seal.dao.bean.GameInfoBean;
import fm.lizhi.ocean.seal.dao.mapper.ext.BizGameInfoResult;
import fm.lizhi.ocean.seal.dao.mapper.ext.GameFieldNameConstant;
import fm.lizhi.ocean.seal.manager.*;
import fm.lizhi.ocean.seal.protocol.GameInfoServiceProto.GameInfoList;
import fm.lizhi.ocean.seal.protocol.GameInfoServiceProto.GetGameListParam;
import fm.lizhi.ocean.seal.protocol.GameInfoServiceProto.GameInfo;
import fm.lizhi.ocean.seal.protocol.GameInfoServiceProto.GetGameInfoParam;
import fm.lizhi.ocean.seal.protocol.GameInfoServiceProto.ResponseGetGameList;
import fm.lizhi.ocean.seal.protocol.GameInfoServiceProto.ResponseGetGameDetails;
import fm.lizhi.ocean.seal.protocol.GameInfoServiceProto.GetGameDetailsParam;
import fm.lizhi.ocean.seal.protocol.GameInfoServiceProto.ResponseGetGameDetail;
import fm.lizhi.ocean.seal.protocol.GameInfoServiceProto.ResponseAddGameDetail;
import fm.lizhi.ocean.seal.protocol.GameInfoServiceProto.GameDetail;
import fm.lizhi.ocean.seal.protocol.GameInfoServiceProto.ResponseGetGameInfo;
import fm.lizhi.ocean.seal.util.ObjectUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@ServiceProvider
public class GameInfoServiceImpl implements GameInfoService {

    public static final String SPLIT_ON = ",";

    @Inject
    private GameInfoManager gameInfoManager;
    @Inject
    private GameLanguageRelationManager gameLanguageRelationManager;
    @Inject
    private GameTypeRelationManager gameTypeRelationManager;
    @Inject
    private GuidGenerator guidGenerator;
    @Inject
    private GameAppManager gameAppManager;


    @Override
    public Result<ResponseGetGameList> getGameList(GetGameListParam param) {
        LogContext.addReqLog("appId={}", param.getAppId());
        LogContext.addResLog("appId={}", param.getAppId());

        if(StringUtils.isBlank(param.getAppId())){
            return new Result<>(GET_GAME_LIST_PARAM_FAIL, null);
        }

        //根据appId查询appBean的信息
        GameAppBean appBean = gameAppManager.getGameAppBean(param.getAppId());
        if(null == appBean){
            return new Result<>(GET_GAME_LIST_FAIL, null);
        }

        List<BizGameInfoResult> results = gameInfoManager.getGameInfosByCondition(appBean.getId());

        ResponseGetGameList.Builder resp = ResponseGetGameList.newBuilder();
        for (BizGameInfoResult bizGameInfoResult : results) {
            resp.addGameInfos(GameInfoList.newBuilder()
                    .setSealGameId(bizGameInfoResult.getId())
                    .setChannelGameId(bizGameInfoResult.getChannelGameId())
                    .setName(bizGameInfoResult.getName())
                    .setDesc(bizGameInfoResult.getDesc())
                    .setChannel(bizGameInfoResult.getChannel())
                    .setChannelId(Optional.ofNullable(bizGameInfoResult.getChannelId()).orElse(NumberUtils.LONG_ZERO))
                    .setConfigJson(Optional.ofNullable(bizGameInfoResult.getConfig()).orElse(StringUtils.EMPTY))
                    .setChannelGameIdStr(bizGameInfoResult.getChannelGameIdStr())
                    .setBizGameId(bizGameInfoResult.getBizGameId()).build());
        }
        return new Result<>(GeneralRCode.GENERAL_RCODE_SUCCESS
                , resp.setAppName(appBean.getAppName()).setAppTopic(appBean.getAppTopic()).build());
    }

    @Override
    public Result<ResponseGetGameInfo> getGameInfo(GetGameInfoParam param) {
        String channel = param.getChannel();
        long channelGameId = param.getChannelGameId();

        LogContext.addReqLog("gameId={}`channel={}", channelGameId, channel);
        LogContext.addResLog("gameId={}`channel={}", channelGameId, channel);

        ResponseGetGameInfo.Builder resp = ResponseGetGameInfo.newBuilder();
        GameInfoBean gameInfo = gameInfoManager.getGameInfoPO(channel, channelGameId);
        if (gameInfo == null) {
            return new Result<>(SealRCode.SEAL_RCODE_ILLEGAL_PARAMS_ERROR, resp.build());
        }

        resp.setGameInfo(GameInfo.newBuilder()
                .setSealGameId(gameInfo.getId()).setChannelGameId(gameInfo.getChannelGameId())
                .setName(gameInfo.getName()).setDesc(gameInfo.getDesc())
                .setChannel(gameInfo.getChannel()).build());
        return new Result<>(GeneralRCode.GENERAL_RCODE_SUCCESS, resp.build());
    }

    @Override
    public Result<ResponseGetGameDetails> getGameDetails(GetGameDetailsParam param) {
        String logStr = "gameName={}`gameTypes={}`gameAccessType={}";
        LogContext.addReqLog(logStr, param.getGameName(), param.getGameTypesList(), param.getGameAccessType());
        LogContext.addResLog(logStr, param.getGameName(), param.getGameTypesList(), param.getGameAccessType());
        Integer accessType = param.hasGameAccessType() ? param.getGameAccessType() : null;

        //这里需要判断存在类型条件。如果存在类型，需要过滤出属于这个类型的游戏。而且游戏列表需要查询所有的类型显示出来
        if(!CollectionUtils.isEmpty(param.getGameTypesList())){
            List<Map<String, Object>> gameInfoDetails
                = gameInfoManager.getGameDetails(param.getGameName(), param.getGameTypesList(), accessType);

            if(CollectionUtils.isEmpty(gameInfoDetails)){
                return emptyGetGameDetailsResult();
            }

            List<GameDetail> gameDetails = convertListToPBs(gameInfoDetails);
            LogContext.addResLog("size={}", gameDetails.size());
            return new Result<>(GET_GAME_DETAILS_SUCCESS
                    , ResponseGetGameDetails.newBuilder().addAllGameDetails(distinctGameInfo(gameDetails)).build());
        }

        List<GameInfoBean> beans = gameInfoManager.getGameDetails(param.getGameName(), accessType);
        if(CollectionUtils.isEmpty(beans)){
            return emptyGetGameDetailsResult();
        }

        LogContext.addResLog("size={}", beans.size());
        return new Result<>(GET_GAME_DETAILS_SUCCESS
                , ResponseGetGameDetails.newBuilder().addAllGameDetails(
                beans.stream().map(this::convertBeanToPB).collect(Collectors.toList())).build());
    }

    @Override
    public Result<ResponseGetGameDetail> getGameDetail(long id) {
        LogContext.addReqLog("id={}", id);
        LogContext.addResLog("id={}", id);

        GameInfoBean bean = gameInfoManager.getGameInfoBeanById(id);
        if(null == bean){
            return new Result<>(GET_GAME_DETAIL_FAIL, null);
        }

        return new Result<>(GET_GAME_DETAIL_SUCCESS, ResponseGetGameDetail.newBuilder().setGameDetail(convertBeanToPB(bean)).build());
    }

    @Override
    public Result<ResponseAddGameDetail> addGameDetail(GameDetail gameDetails) {
        if(gameInfoManager.isExistGameIdAndChannelId(gameDetails.getChannelGameId()
                , gameDetails.getChannel(), NumberUtils.LONG_ZERO.longValue())){
           return new Result<>(ADD_GAME_DETAIL_FAIL, null);
        }

        GameInfoBean bean = createBean(gameDetails, true);
        long id = gameInfoManager.insertGameInfo(bean);
        LogContext.addResLog("id={}", id);
        if(id <= NumberUtils.LONG_ZERO.longValue()){
            return new Result<>(ADD_GAME_DETAIL_FAIL, null);
        }
        //成功插入游戏，需要添加语言、类型的关联关系
        //插入所有的语言
        gameLanguageRelationManager.insertOrUpdateGameLanguageByGameId(id, gameDetails.getLanguageAbbreviationList(), true);

        //查询所有的类型ID,然后加入到关联类型表
        gameTypeRelationManager.insertOrUpdateTypeByGameId(id, gameDetails.getGameTypeIdList(), true);

        return new Result<>(ADD_GAME_DETAIL_SUCCESS, ResponseAddGameDetail.newBuilder().setId(id).build());
    }

    @Override
    public Result<Void> modifyGameDetail(GameDetail gameDetails) {
        LogContext.addReqLog("id={}", gameDetails.getId());

        if(gameInfoManager.isExistGameIdAndChannelId(gameDetails.getChannelGameId()
                , gameDetails.getChannel(), gameDetails.getId())){
            return new Result<>(GeneralRCode.GENERAL_RCODE_SERVER_BUSY, null);
        }

        GameInfoBean bean = createBean(gameDetails, false);
        if(null == bean){
            return new Result<>(GeneralRCode.GENERAL_RCODE_SERVER_BUSY, null);
        }

        gameInfoManager.modifyGameInfo(bean);
        LogContext.addResLog("id={}", gameDetails.getId());

        //成功插入游戏，需要添加语言、类型的关联关系
        //插入所有的语言
        gameLanguageRelationManager.insertOrUpdateGameLanguageByGameId(bean.getId(), gameDetails.getLanguageAbbreviationList(), false);

        //查询所有的类型ID,然后加入到关联类型表
        gameTypeRelationManager.insertOrUpdateTypeByGameId(bean.getId(), gameDetails.getGameTypeIdList(), false);

        return new Result<>(GeneralRCode.GENERAL_RCODE_SUCCESS, null);
    }

    @Override
    public Result<Void> delGameDetail(long id, String operator) {
        LogContext.addReqLog("id={}`operator={}", id, operator);
        LogContext.addResLog("id={}`operator={}", id, operator);
        if(id > NumberUtils.LONG_ZERO){
            gameInfoManager.delGameInfoBeanById(id, operator);
        }
        return new Result<>(GeneralRCode.GENERAL_RCODE_SUCCESS, null);
    }


    /**
     * 这个方法比较特殊，原型需求，列表查询游戏根据游戏类型查询游戏。列表又需要把游戏类型合并。所以存在这个特殊方法
     * @param gameDetails
     * @return
     */
    private List<GameDetail> distinctGameInfo(List<GameDetail> gameDetails){
        if(null == gameDetails){
            return Collections.EMPTY_LIST;
        }

        Map<Long, GameDetail> mergeMap = new HashMap<>();
        for (GameDetail gameDetail : gameDetails){
            if(null == gameDetail){
                continue;
            }

            if(mergeMap.containsKey(gameDetail.getId())){
                continue;
            }

            mergeMap.put(gameDetail.getId(), gameDetail);
        }

        return mergeMap.values().stream().collect(Collectors.toList());
    }


    private List<GameDetail> convertListToPBs(List<Map<String, Object>> list){
        if(null == list){
            return null;
        }

        return list.stream().filter(x -> null != x).map(x-> convertMapToPB(x)).collect(Collectors.toList());
    }


    /**
     * 这个方法 要注意是使用在列表上，因为这里需要根据类型进行查询，所以left join了一下
     *
     *     optional int64 id = 1;
     *     optional string gameName = 2;
     *     optional int64 channelGameId = 3; //第三方游戏的频道ID
     *     optional string description = 4; //描述
     *     optional int64 channelId = 5;//第三方
     *     repeated string imageUrls = 6;
     *     repeated string videoUrls = 7;
     *     repeated string languageAbbreviation = 8;//语言简称 zh、en
     *     optional int32 gameAccessType = 9;//接入状态 0-已接入 1-待接入
     *     repeated int64 gameTypeId = 10;//游戏类型 团站、纸牌
     *     optional int32 gamePayWayCode = 11;//付费方式 {@link fm.lizhi.ocean.seal.constant.GamePayWayEnum}
     *     optional int32 lowExpense = 12;//最低消费:0没有 1有
     *     optional string expenseRule = 13;//计费规则
     * @param map
     * @return
     */
    private GameDetail convertMapToPB(Map<String, Object> map){
        if(null == map){
            return null;
        }

        long id = ObjectUtil.castAndDefaultValue(map.get(GameFieldNameConstant.GameInfoTable.ID), Long.class);
        if(id <= NumberUtils.LONG_ZERO){
            return null;
        }

        GameDetail.Builder builder = GameDetail.newBuilder()
                .setId(id)
                .setGameName(ObjectUtil.castAndDefaultValue(map.get(GameFieldNameConstant.GameInfoTable.NAME), String.class))
                .setChannelGameId(ObjectUtil.castAndDefaultValue(map.get(GameFieldNameConstant.GameInfoTable.CHANNEL_GAME_ID), Long.class))
                .setDescription(ObjectUtil.castAndDefaultValue(map.get(GameFieldNameConstant.GameInfoTable.DESC), String.class))
                .setChannel(ObjectUtil.castAndDefaultValue(map.get(GameFieldNameConstant.GameInfoTable.CHANNEL), String.class))
                .setGameAccessType(ObjectUtil.castAndDefaultValue(map.get(GameFieldNameConstant.GameInfoTable.ACCESS_TYPE), Integer.class))
                .setGamePayWayCode(ObjectUtil.castAndDefaultValue(map.get(GameFieldNameConstant.GameInfoTable.PAY_WAY_CODE), Integer.class))
                .setLowExpense(ObjectUtil.castAndDefaultValue(map.get(GameFieldNameConstant.GameInfoTable.LOW_EXPENSE), Integer.class))
                .setLowExpenseValue(ObjectUtil.castAndDefaultValue(map.get(GameFieldNameConstant.GameInfoTable.LOW_EXPENSE_VALUE), String.class))
                .setExpenseRule(ObjectUtil.castAndDefaultValue(map.get(GameFieldNameConstant.GameInfoTable.EXPENSE_RULE), String.class));

        String imageUrl = ObjectUtil.castAndDefaultValue(map.get(GameFieldNameConstant.GameInfoTable.GAME_IMAGE), String.class);
        if(StringUtils.isNotBlank(imageUrl)){
            builder.addAllImageUrls(Splitter.on(SPLIT_ON).omitEmptyStrings().trimResults().splitToList(imageUrl));
        }

        String videoUrl = ObjectUtil.castAndDefaultValue(map.get(GameFieldNameConstant.GameInfoTable.GAME_VIDEO), String.class);
        if(StringUtils.isNotBlank(videoUrl)){
            builder.addAllVideoUrls(Splitter.on(SPLIT_ON).omitEmptyStrings().trimResults().splitToList(videoUrl));
        }

        //查询所有的GameType
        builder.addAllGameTypeId(gameTypeRelationManager.getGameTypeIdByGameId(id));

        //查询所有的语言简称
        builder.addAllLanguageAbbreviation(gameLanguageRelationManager.getGameLanguageByGameId(id));
        return builder.build();
    }

    private GameDetail convertBeanToPB(GameInfoBean bean){
        if(null == bean){
            return null;
        }

        long id = bean.getId();
        if(id <= NumberUtils.LONG_ZERO){
            return null;
        }

        GameDetail.Builder builder = GameDetail.newBuilder()
                .setId(id)
                .setGameName(bean.getName())
                .setChannelGameId(bean.getChannelGameId())
                .setDescription(Optional.ofNullable(bean.getDesc()).orElse(StringUtils.EMPTY))
                .setChannel(Optional.ofNullable(bean.getChannel()).orElse(StringUtils.EMPTY))
                .setGameAccessType(bean.getAccessType())
                .setGamePayWayCode(bean.getPayWayCode())
                .setLowExpense(bean.getLowExpense())
                .setLowExpenseValue(Optional.ofNullable(bean.getLowExpenseValue()).orElse(StringUtils.EMPTY))
                .setExpenseRule(Optional.ofNullable(bean.getExpenseRule()).orElse(StringUtils.EMPTY));

        String imageUrl = bean.getGameImage();
        if(StringUtils.isNotBlank(imageUrl)){
            builder.addAllImageUrls(Splitter.on(SPLIT_ON).omitEmptyStrings().trimResults().splitToList(imageUrl));
        }

        String videoUrl = bean.getGameVideo();
        if(StringUtils.isNotBlank(videoUrl)){
            builder.addAllVideoUrls(Splitter.on(SPLIT_ON).omitEmptyStrings().trimResults().splitToList(videoUrl));
        }

        //查询所有的GameType
        builder.addAllGameTypeId(gameTypeRelationManager.getGameTypeIdByGameId(id));

        //查询所有的语言简称
        builder.addAllLanguageAbbreviation(gameLanguageRelationManager.getGameLanguageByGameId(id));
        return builder.build();
    }

    private GameInfoBean createBean(GameDetail gameDetail, boolean insert){
        GameInfoBean bean = null;
        if(insert){
            bean = new GameInfoBean();
            bean.setId(guidGenerator.genId());
            bean.setStatus(NumberUtils.INTEGER_ONE);
        }else {
            bean = gameInfoManager.getGameInfoBeanById(gameDetail.getId());
            if(bean == null){
                return null;
            }
        }

        Date time = new Date();
        bean.setName(gameDetail.getGameName());
        bean.setChannel(Optional.ofNullable(gameDetail.getChannel()).orElse(StringUtils.EMPTY));
        bean.setChannelGameId(gameDetail.getChannelGameId());
        bean.setDesc(Optional.ofNullable(gameDetail.getDescription()).orElse(StringUtils.EMPTY));
        bean.setAccessType(gameDetail.getGameAccessType());
        bean.setPayWayCode(gameDetail.getGamePayWayCode());
        bean.setLowExpense(gameDetail.getLowExpense());
        bean.setLowExpenseValue(Optional.ofNullable(gameDetail.getLowExpenseValue()).orElse(StringUtils.EMPTY));
        bean.setExpenseRule(Optional.ofNullable(gameDetail.getExpenseRule()).orElse(StringUtils.EMPTY));
        bean.setGameImage(Joiner.on(SPLIT_ON).join(Optional.ofNullable(gameDetail.getImageUrlsList()).orElse(Collections.EMPTY_LIST)));
        bean.setGameVideo(Joiner.on(SPLIT_ON).join(Optional.ofNullable(gameDetail.getVideoUrlsList()).orElse(Collections.EMPTY_LIST)));
        bean.setOperator(Optional.ofNullable(gameDetail.getOperator()).orElse(StringUtils.EMPTY));
        bean.setModifyTime(time);
        if(insert){
            bean.setCreateTime(time);
        }

        return bean;
    }

    private Result<ResponseGetGameDetails> emptyGetGameDetailsResult(){
        return new Result<>(GET_GAME_DETAILS_SUCCESS
                , ResponseGetGameDetails.newBuilder().addAllGameDetails(Collections.emptyList()).build());
    }



}
