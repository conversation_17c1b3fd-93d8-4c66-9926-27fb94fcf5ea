package fm.lizhi.ocean.seal.pojo.bo;


import fm.lizhi.ocean.seal.protocol.GameServiceProto;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;


/**
 *
 * systemType app 系统版本 0 未知 1 ios 2 android
 * sdkVersionCode sdk版本
 * params         参数
 *
 */
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Data
public class GameLastVersionParam {
    private String appId;
    private int systemType;
    private long sdkVersionCode;
    private List<GameServiceProto.GameVersionParams> params;
    private long userId;
}
