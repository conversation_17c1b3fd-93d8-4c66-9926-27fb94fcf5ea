package fm.lizhi.ocean.seal.manager;

import com.google.common.base.Strings;
import com.google.inject.Inject;
import com.netflix.governator.annotations.AutoBindSingleton;
import fm.lizhi.common.datastore.core.annotation.DataStore;
import fm.lizhi.common.datastore.mysql.annotation.Transactional;
import fm.lizhi.commons.util.GuidGenerator;
import fm.lizhi.ocean.seal.dao.bean.GameUserPO;
import fm.lizhi.ocean.seal.dao.mapper.GameUserMapper;
import io.shardingsphere.core.routing.router.masterslave.MasterSlaveRouteOnceVisitedHolder;

import java.util.Date;
import java.util.List;

/**
 * Created in 2022-01-26 16:32.
 *
 * <AUTHOR>
 */
@AutoBindSingleton
public class GameUserManager {

    @Inject
    private GuidGenerator guidGenerator;
    @Inject
    private GameUserMapper gameUserMapper;

    /**
     * 获取游戏对应的用户ID
     *
     * FIXME 这方法入库有啥用？
     *
     * @param userId
     * @param appId
     * @return
     */
    // @Transactional(rollbackFor = Exception.class)
    // @DataStore(namespace = "mysql_ocean_oceanseal")
    public String getGameUserId(long userId, String appId) {
        return userId + "";
//        MasterSlaveRouteOnceVisitedHolder.routeMaster();
//        // 获取用户
//        GameUserPO gameUserPO = getGameUser(userId, appId);
//        if (gameUserPO != null) {
//            return gameUserPO.getUserId() + "";
//        }
//        // 不存在，生成
//        // TODO 存在并发问题
//        GameUserPO insertGameUserPO = new GameUserPO();
//        insertGameUserPO.setId(guidGenerator.genAccountId());
//        insertGameUserPO.setUserId(userId);
//        insertGameUserPO.setAppId(appId);
//        Date date = new Date();
//        insertGameUserPO.setCreateTime(date);
//        insertGameUserPO.setModifyTime(date);
//        int insert = gameUserMapper.insert(insertGameUserPO);
//        if (insert == 1) {
//            return userId + "";
//        }
//        return null;
    }

    /**
     * 获取用户信息
     *
     * @param userId
     * @param appId
     * @return
     */
    private GameUserPO getGameUser(long userId, String appId) {
        GameUserPO selectGameUserPO = new GameUserPO();
        selectGameUserPO.setUserId(userId);
        selectGameUserPO.setAppId(appId);
        return gameUserMapper.selectOne(selectGameUserPO);
    }

    /**
     * 获取用户信息
     *
     * @param userId
     * @return
     */
    public GameUserPO getGameUser(long userId) {
        GameUserPO selectGameUserPO = new GameUserPO();
        selectGameUserPO.setUserId(userId);
        List<GameUserPO> gameUserPOList = gameUserMapper.selectMany(selectGameUserPO);
        if (gameUserPOList == null || gameUserPOList.isEmpty()) {
            return null;
        }
        return gameUserPOList.get(0);
    }

    /**
     * 获取用户ID
     *
     * @param uid
     * @return
     */
    public long converUid2SealUserId(String uid) {
        if (Strings.isNullOrEmpty(uid)) {
            return 0L;
        }
        return Long.parseLong(uid);
    }
}
