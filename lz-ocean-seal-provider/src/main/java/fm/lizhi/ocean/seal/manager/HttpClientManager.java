package fm.lizhi.ocean.seal.manager;

import lombok.extern.slf4j.Slf4j;
import org.apache.http.HttpEntity;
import org.apache.http.client.config.RequestConfig;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClientBuilder;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.util.EntityUtils;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.util.Map;

/**
 * Created in 2022-03-02 15:16.
 *
 * <AUTHOR>
 */
@Slf4j
@Service
public class HttpClientManager {

    /**
     * Timeout in millis.
     */
    private static final int CONNECTION_TIMEOUT_MS = 5 * 1000;

    /**
     * http post request
     *
     * @param url
     * @param headerMap
     * @return
     */
    public String postRequest(String url, Map<String, String> headerMap, String body) {
        CloseableHttpClient httpClient = null;
        CloseableHttpResponse response = null;
        try {
            //设置超时时间
            RequestConfig config = RequestConfig.custom()
                    .setConnectTimeout(CONNECTION_TIMEOUT_MS)
                    .setConnectionRequestTimeout(CONNECTION_TIMEOUT_MS)
                    .setSocketTimeout(CONNECTION_TIMEOUT_MS).build();
            httpClient = HttpClientBuilder.create().setDefaultRequestConfig(config).build();
            HttpPost httpPost = new HttpPost(url);
            StringEntity entity = new StringEntity(body);
            httpPost.setEntity(entity);
            if (headerMap != null && !headerMap.isEmpty()) {
                headerMap.entrySet().stream().forEach(header -> httpPost.setHeader(header.getKey(), header.getValue()));
            }
            response = httpClient.execute(httpPost);
            int statusCode = response.getStatusLine().getStatusCode();
            if (statusCode == 200) {
                HttpEntity responseEntity = response.getEntity();
                // 使用工具类EntityUtils，从响应中取出实体表示的内容并转换成字符串
                String result = EntityUtils.toString(responseEntity, "utf-8");
                return result;
            }
            log.error("postRequest http url={}`body={}`code={}", url, body, statusCode);
        } catch (UnsupportedEncodingException e) {
            log.error("postRequest InterruptedException url={}`body={}", url, body, e);
        } catch (IOException e) {
            log.error("postRequest IOException url={}`body={}", url, body, e);
        } finally {
            // 5、关闭资源
            if (response != null) {
                try {
                    response.close();
                } catch (IOException e) {
                    log.error("postRequest response close IOException url={}`body={}", url, body, e);
                }
            }
            if (httpClient != null) {
                try {
                    httpClient.close();
                } catch (IOException e) {
                    log.error("postRequest httpClient close IOException url={}`body={}", url, body, e);
                }
            }
        }
        return null;
    }
}
