package fm.lizhi.ocean.seal.api.impl;


import fm.lizhi.commons.service.client.annotation.ServiceProvider;
import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.ocean.seal.api.GamePayWayService;
import fm.lizhi.ocean.seal.constant.GamePayWayEnum;
import fm.lizhi.ocean.seal.protocol.GamePayWayServiceProto;
import fm.lizhi.ocean.seal.protocol.GamePayWayServiceProto.ResponseGetAllGamePayWays;
import lombok.extern.slf4j.Slf4j;

import java.util.stream.Collectors;

@Slf4j
@ServiceProvider
public class GamePayWayServiceImpl implements GamePayWayService {
    @Override
    public Result<ResponseGetAllGamePayWays> getAllGamePayWays() {
        return new Result<>(GET_ALL_GAME_PAY_WAYS_SUCCESS
                , ResponseGetAllGamePayWays.newBuilder().addAllGamePayWays(
                        GamePayWayEnum.getAllGamePayWays().stream().map(x->
                             GamePayWayServiceProto.GamePayWay.newBuilder().setPayWayCode(x.getPayWayCode())
                                    .setPayWayName(x.getPayWayName()).build()
                        ).collect(Collectors.toList())).build());
    }
}
