package fm.lizhi.ocean.seal.constant;


import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 付费方式
 */
public enum GamePayWayEnum {
    MONTH_MAU_PAY(1, "月MAU付费"),
    MONTH_PAY(2, "包月付费"),
    SEASON_MAU_PAY(3,"季MAU付费"),
    SEASON_PAY(4,"包季付费"),
    HALF_YEAR_MAU_PAY(5, "半年MAU付费"),
    HALF_YEAR_PAY(7, "包半年付费"),
    YEAR_MAU_PAY(8, "年MAU付费"),
    YEAR_PAY(9, "包年付费"),
    REQ_PAY(10, "总请求量付费"),
    ;

    private static Map<Integer, GamePayWayEnum> payWayMap = new HashMap<>();
    static {
        for (GamePayWayEnum payWayEnum : GamePayWayEnum.values()){
            payWayMap.put(payWayEnum.payWayCode, payWayEnum);
        }
    }

    private int payWayCode;

    private String payWayName;

    GamePayWayEnum(int payWayCode, String payWayName) {
        this.payWayCode = payWayCode;
        this.payWayName = payWayName;
    }

    public static GamePayWayEnum fromCode(int code) {
        return payWayMap.containsKey(code) ? payWayMap.get(code) : null;
    }

    public static List<GamePayWayEnum> getAllGamePayWays(){
        return new ArrayList<>(payWayMap.values());
    }

    public int getPayWayCode() {
        return payWayCode;
    }

    public String getPayWayName() {
        return payWayName;
    }
}
