package fm.lizhi.ocean.seal.manager;

import com.google.inject.Inject;
import com.netflix.governator.annotations.AutoBindSingleton;
import fm.lizhi.ocean.seal.agora.token.RtmTokenBuilder;
import fm.lizhi.ocean.seal.constant.GameChannel;
import fm.lizhi.ocean.seal.dao.bean.GameChannelBean;
import fm.lizhi.ocean.seal.dto.AgoraToken;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * Created in 2022-05-18 11:14.
 *
 * <AUTHOR>
 */
@AutoBindSingleton
public class AgoraManager {
    private static final Logger logger = LoggerFactory.getLogger(AgoraManager.class);
    @Inject
    private GameAppManager gameAppManager;

    /**
     * 生成声网token
     *
     * @param userId 用户ID
     * @param appId  业务的AppId
     * @return
     */
    public AgoraToken generateToken(long userId, String appId) throws Exception {
        AgoraToken agoraToken = new AgoraToken();
        GameChannelBean bean = this.gameAppManager.getChannelInfoByAppIdAndChannel(appId, GameChannel.AGORA);
        if (bean == null) {
            logger.error("Failed to generate agora token, channel info is null, userId:{}, appId:{}", userId, appId);
            throw new RuntimeException();
        }
        RtmTokenBuilder tokenBuilder = new RtmTokenBuilder();
        agoraToken.setToken(tokenBuilder.buildToken(bean.getAppId(), bean.getAppSecret(), String.valueOf(userId), 1));
        // 24小时
        agoraToken.setExpire(24 * 3600);
        return agoraToken;
    }
}