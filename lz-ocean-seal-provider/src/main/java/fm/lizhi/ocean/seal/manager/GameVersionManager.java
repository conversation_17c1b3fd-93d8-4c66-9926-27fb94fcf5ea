package fm.lizhi.ocean.seal.manager;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.google.common.hash.Hashing;
import com.google.gson.JsonObject;
import com.google.inject.Inject;
import com.netflix.governator.annotations.AutoBindSingleton;
import fm.lizhi.common.datastore.mysql.mybatis.PageList;
import fm.lizhi.commons.config.util.JsonUtil;
import fm.lizhi.commons.util.GuidGenerator;
import fm.lizhi.ocean.seal.dao.bean.GameBizGameBean;
import fm.lizhi.ocean.seal.dao.bean.GameCallback;
import fm.lizhi.ocean.seal.dao.bean.GameVersionBean;
import fm.lizhi.ocean.seal.dao.mapper.GameVersionBeanMapper;
import fm.lizhi.ocean.seal.dao.mapper.ext.GameVersionBeanExtMapper;
import fm.lizhi.ocean.seal.pojo.bo.GameLastVersionParam;
import fm.lizhi.ocean.seal.protocol.GameServiceProto;
import org.apache.commons.codec.digest.DigestUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.swing.text.html.Option;
import java.math.BigDecimal;
import java.util.*;

/**
 * Created in 2022-06-21 17:09.
 *
 * <AUTHOR>
 */
@AutoBindSingleton
public class GameVersionManager {
    private static final Logger logger = LoggerFactory.getLogger(GameVersionManager.class);
    @Inject
    private GameVersionBeanExtMapper gameVersionBeanExtMapper;
    @Inject
    private GameVersionBeanMapper gameVersionBeanMapper;
    @Inject
    private BizGameManager bizGameManager;
    @Inject
    private GuidGenerator guidGenerator;

    /**
     * 查询 某个业务 关联的游戏 支持的seal sdk最低版本的最大游戏版本号。
     * 例如： funbox 关联 ludo游戏  嵌入的 sealSdk 版本为 1000 游戏版本为100
     * 现在存在游戏版本记录： funbox ludo游戏 sealSdk版本最低为999 另外游戏版本已经更新到102
     * 那么根据对比判断。将会查询到 funbox ludo minsealsdk 999 gameversion 102 的记录
     * 这里为什么需要引入appId。理论上是不需要考虑业务的，只需要考虑游戏。但是目前存在的情况是。不同业务定制了不同的seal sdk。而这里的版本又存在重叠。所以需要增加appId
     *
     * 8月24号，增加A/B测试，白名单功能
     * 命中白名单：ext_config TO JSON ，获取whiteList属性，形成List，判断userId是否存在List中
     * 命中A/B流量：
     * 配置命中A，需要下发新版游戏包的流量概率ratio为：10%
     * Boolean hitFlow（userId, ratio）{
     *   - if(ratio == 0) return false;
     *   - else if (ratio == 100) return true;
     *   - hashId = hash(userId) ， 保证userId符合均匀分布;
     *   - 计算出命中比率的因子ratioFactor = 1/ratio
     *   - 判断是否命中 return hashId % ratioFactor == 0;
     * }
     *
     *
     * @param param          param
     *
     * @return
     */
    public List<GameVersionBean> getGameLatestVersion(GameLastVersionParam param) {
        String appId = param.getAppId();
        int systemType = param.getSystemType();
        long sdkVersionCode = param.getSdkVersionCode();
        List<GameVersionBean> beans = new ArrayList<>();

        for (GameServiceProto.GameVersionParams versionParam : param.getParams()) {
            // 查询业务游戏ID
            GameBizGameBean gameBean = this.bizGameManager.getGame(appId, versionParam.getGameId());
            if(null == gameBean){
                continue;
            }
            logger.info("Get game latest version`appId={}`systemType={}`sdkVersion={}`gameId={}`gameVersion={}`userId={}"
                    , appId, systemType, sdkVersionCode, gameBean.getId(), versionParam.getGameVersionCode(), param.getUserId());

            GameVersionBean bean = this.gameVersionBeanExtMapper.selectLatestVersion(
                    appId, gameBean.getId(), systemType, sdkVersionCode, versionParam.getGameVersionCode());
            if(null == bean){
                continue;
            }

            if(param.getUserId() <= 0L || bean.getGameFlow() == 100){
                beans.add(bean);
                continue;
            }

            if(isHitGameLastVersionWhiteList(param.getUserId(), bean.getExtConfig())
                    || isHitFlow(param.getUserId(), bean.getGameFlow())){
                beans.add(bean);
            }
        }
        return beans;
    }

    /**
     *
     * @param id
     * @param appId
     * @param gameId
     * @param systemType //系统版本：0 未知 1 ios 2 android
     * @param minSdkVersion //支持最小seal sdk 版本
     * @param gameVersion //游戏版本
     * @param forceUpdate //true：强制更新，false：非强制更新
     * @param downloadLink //游戏下载链接
     */
    public void insertOrUpdate(long id, String appId, Long gameId
            , int systemType, Long minSdkVersion, Long gameVersion
            , int forceUpdate, String downloadLink, int gameFlow, String extConfig){
        Date date = new Date();
        boolean insert = id <= 0L;
        GameVersionBean bean = new GameVersionBean();
        if(insert){
            bean.setId(guidGenerator.genId());
            bean.setCreateTime(date);
        }else {
            bean = getGameVersionBeanById(id);
            if (null == bean){
                return;
            }
        }

        bean.setAppId(appId);
        bean.setGameId(gameId);
        bean.setSystemType(systemType);
        bean.setMinSdkVersion(minSdkVersion);
        bean.setGameVersion(gameVersion);
        bean.setForceUpdate(forceUpdate);
        bean.setGameFlow(gameFlow);
        bean.setExtConfig(extConfig);
        bean.setDownloadLink(downloadLink);
        bean.setModifyTime(date);
        if(insert){
            gameVersionBeanMapper.insert(bean);
        }else {
            gameVersionBeanMapper.updateByPrimaryKey(bean);
        }

    }

    public void delGameVersionBean(long id){
        GameVersionBean bean = new GameVersionBean();
        bean.setId(id);
        gameVersionBeanMapper.deleteByPrimaryKey(bean);
    }

    public GameVersionBean getGameVersionBeanById(long id){
        GameVersionBean bean = new GameVersionBean();
        bean.setId(id);
        return gameVersionBeanMapper.selectByPrimaryKey(bean);
    }

    public PageList<GameVersionBean> pageSearch(String appId, Long gameId, Integer systemType, Long gameVersion, int pageSize, int pageNumber){
        GameVersionBean bean = new GameVersionBean();
        if(StringUtils.isNotBlank(appId)){
            bean.setAppId(appId);
        }

        if(gameId != null && gameId > 0){
            bean.setGameId(gameId);
        }

        if(null != systemType){
            bean.setSystemType(systemType);
        }

        if(null != gameVersion){
            bean.setGameVersion(gameVersion);
        }

        return gameVersionBeanMapper.selectPage(bean, pageNumber, pageSize);
    }

    private boolean isHitGameLastVersionWhiteList(long userId, String extJson){
        if(StringUtils.isBlank(extJson)){
            return false;
        }

        try {
            JSONObject object = JSONObject.parseObject(extJson);
            if(null == object){
                return false;
            }

            JSONArray jsonArray = object.getJSONArray("whiteList");
            if(null == jsonArray){
                return false;
            }

            return jsonArray.contains(userId);
        }catch (Exception e){

        }
        return false;
    }

    private boolean isHitFlow(long userId, int gameFlow){
        if(gameFlow == 100){
            return true;
        }
        if(gameFlow == 0){
            return false;
        }
        // 因为ID生成器生成的ID值具有偏向性, 直接取模可能分布不均匀, 故先对其进行SHA1哈希
        String sha1Hex = DigestUtils.sha1Hex(String.valueOf(userId));
        // 再截取SHA1哈希的前7位用于转换int, 因为SHA1的结果必定是十六进制字符, 所以可以截取用于转换int.
        // 取7位是因为int的十六进制最大值是8位且第一位为7, 为避免溢出故取7位.
        // SHA1是工业级的哈希算法, 其结果字符串是分布均匀的群, 而截取前N位的结果是一个具有相同性质的群, 同样具有分布均匀的特性.
        String subHex = sha1Hex.substring(0, 7);
        // 根据上述subHex分布均匀的特性, 从16进制转10进制最终取模也能分布均匀
        int subHexInt = Integer.parseInt(subHex, 16);
        return (subHexInt % 100) < gameFlow;
    }
}