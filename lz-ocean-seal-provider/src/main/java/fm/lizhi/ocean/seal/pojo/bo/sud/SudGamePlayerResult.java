package fm.lizhi.ocean.seal.pojo.bo.sud;

import com.google.common.base.Strings;
import fm.lizhi.ocean.seal.protocol.GameReportServiceProto;
import lombok.*;

/**
 * Created in 2022-01-25 10:54.
 *
 * <AUTHOR>
 */
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Data
@ToString
public class SudGamePlayerResult {

    /**
     * 接入方uid，机器人为空字符
     */
    private String uid;
    /**
     * 排名从1开始，平局排名相同
     */
    private int rank;
    /**
     * 0:正常，1:逃跑
     */
    private int is_escaped;
    /**
     * 0:普通用户，1:机器人
     */
    private int is_ai;
    /**
     * 玩家在游戏中的角色
     */
    private int role;
    /**
     * 玩家当前局得到的分数
     */
    private int score;

    /**
     * 胜负结果 0:输，1:赢，2:平局
     */
    private int is_win;

    /**
     * 数据转换
     *
     * @return
     */
    public GameReportServiceProto.GamePlayerSettleResult buildGamePlayerSettleResult() {
        return GameReportServiceProto.GamePlayerSettleResult.newBuilder()
                .setUid(Strings.isNullOrEmpty(uid) ? "0" : uid).setRealUser(is_ai == 0).setEscaped(is_escaped == 1)
                .setRank(rank).setRole(role).setScore(score).setIsWin(is_win).build();
    }
}
