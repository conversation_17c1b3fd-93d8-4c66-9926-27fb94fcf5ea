package fm.lizhi.ocean.seal.dao.bean;

import java.util.Date;
import javax.persistence.*;

/**
 * This class was generated by DataStore MyBatis Generator.
 *
 * 业务回调配置表
 *
 * @date 2025-04-09 06:51:25
 */
@Table(name = "`game_callback`")
public class GameCallback {
    /**
     * 主键ID
     */
    @Id
    @Column(name= "`id`")
    private Long id;

    /**
     * 业务方AppId
     */
    @Column(name= "`app_id`")
    private String appId;

    /**
     * 接口回调key
     */
    @Column(name= "`callback_key`")
    private String callbackKey;

    /**
     * 回调类型，1：用户信息回调 2 回调业务地址 3 游戏接口地址 12 线上业务地址 13线上接口地址
     */
    @Column(name= "`type`")
    private Integer type;

    /**
     * 回调地址
     */
    @Column(name= "`url`")
    private String url;

    @Column(name= "`create_time`")
    private Date createTime;

    @Column(name= "`modify_time`")
    private Date modifyTime;

    /**
     * 事件名
     */
    @Column(name= "`event_name`")
    private String eventName;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getAppId() {
        return appId;
    }

    public void setAppId(String appId) {
        this.appId = appId == null ? null : appId.trim();
    }

    public String getCallbackKey() {
        return callbackKey;
    }

    public void setCallbackKey(String callbackKey) {
        this.callbackKey = callbackKey == null ? null : callbackKey.trim();
    }

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    public String getUrl() {
        return url;
    }

    public void setUrl(String url) {
        this.url = url == null ? null : url.trim();
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getModifyTime() {
        return modifyTime;
    }

    public void setModifyTime(Date modifyTime) {
        this.modifyTime = modifyTime;
    }

    public String getEventName() {
        return eventName;
    }

    public void setEventName(String eventName) {
        this.eventName = eventName == null ? null : eventName.trim();
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", appId=").append(appId);
        sb.append(", callbackKey=").append(callbackKey);
        sb.append(", type=").append(type);
        sb.append(", url=").append(url);
        sb.append(", createTime=").append(createTime);
        sb.append(", modifyTime=").append(modifyTime);
        sb.append(", eventName=").append(eventName);
        sb.append("]");
        return sb.toString();
    }
}