package fm.lizhi.ocean.seal.manager;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.inject.Inject;
import com.netflix.governator.annotations.AutoBindSingleton;
import fm.lizhi.common.datastore.mysql.mybatis.PageList;
import fm.lizhi.common.datastore.redis.client.RedisClient;
import fm.lizhi.commons.util.GuidGenerator;
import fm.lizhi.ocean.seal.conf.DataStoreConfig;
import fm.lizhi.ocean.seal.conf.LzConfig;
import fm.lizhi.ocean.seal.dao.bean.GameBizGameBean;
import fm.lizhi.ocean.seal.dao.bean.GameBizGameRelationBean;
import fm.lizhi.ocean.seal.dao.bean.GameInfoBean;
import fm.lizhi.ocean.seal.dao.mapper.GameBizGameBeanMapper;
import fm.lizhi.ocean.seal.dao.mapper.GameBizGameRelationBeanMapper;
import fm.lizhi.ocean.seal.dao.mapper.ext.BizGameRelationOrcaExtMapper;
import fm.lizhi.ocean.seal.dao.mapper.ext.BizGameRelationOrcaResult;
import fm.lizhi.ocean.seal.redis.RedisKeys;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.util.CollectionUtils;

import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Optional;

/**
 * 业务游戏
 * Created in 2022-04-27 17:46.
 *
 * <AUTHOR>
 */
@AutoBindSingleton
public class BizGameManager {
    private static final Logger logger = LoggerFactory.getLogger(BizGameManager.class);
    @Inject
    private GameBizGameBeanMapper gameBizGameBeanMapper;
    @Inject
    private GameBizGameRelationBeanMapper gameBizGameRelationBeanMapper;
    @Inject
    private GuidGenerator guidGenerator;
    @Inject
    @com.google.inject.name.Named(DataStoreConfig.REDIS_NAME_SPACE)
    private RedisClient redisClient;
    @Inject
    private LzConfig lzConfig;
    @Inject
    private GameInfoManager gameInfoManager;
    @Inject
    private BizGameRelationOrcaExtMapper bizGameRelationOrcaExtMapper;

    /**
     * 根据game app id查询关联集合
     * @param gameAppId
     * @return
     */
    public List<GameBizGameBean> getGameBizGamesByAppId(long gameAppId){
        if(gameAppId <= NumberUtils.LONG_ZERO.longValue()){
            return Collections.EMPTY_LIST;
        }

        GameBizGameBean bizGameBean = new GameBizGameBean();
        bizGameBean.setGameAppId(gameAppId);
        return gameBizGameBeanMapper.selectMany(bizGameBean);
    }

    /**
     * 根据游戏Id获取游戏信息
     *
     * @param gameId 游戏ID，因为业务旧版本原因，可能是渠道游戏ID，可能是平台游戏ID
     * @return
     */
    public GameBizGameBean getGame(String appId, String gameId) {
        // 先从映射表找平台的gameId，如果找不到则从业务游戏表找
        GameBizGameRelationBean relationBean = this.getGameRelation(appId, gameId);
        GameBizGameBean bizGameBean = new GameBizGameBean();
        if (relationBean != null) {
            bizGameBean.setId(relationBean.getId());
        } else {
            bizGameBean.setId(Long.valueOf(gameId));
        }
        return this.gameBizGameBeanMapper.selectByPrimaryKey(bizGameBean);
    }

    /**
     * 查询映射关系
     *
     * @param appId         业务方APPID
     * @param channelGameId 渠道游戏ID
     * @return
     */
    public GameBizGameRelationBean getGameRelation(String appId, String channelGameId) {
        GameBizGameRelationBean relationBean = new GameBizGameRelationBean();
        relationBean.setAppId(appId);
        relationBean.setChannelGameId(channelGameId);
        return this.gameBizGameRelationBeanMapper.selectOne(relationBean);
    }

    /**
     * 合并配置
     *
     * @param bizGameId 游戏ID
     * @param config    原始配置
     * @return
     */
    public String mergeConfig(long bizGameId, String config) {
        String key = RedisKeys.GAME_BIZ_CONFIG.buildKey(String.valueOf(bizGameId));
        String bizConfig = this.redisClient.get(key);
        if (StringUtils.isNotBlank(bizConfig)) {
            JSONObject configObject = JSON.parseObject(config);
            JSONObject bizObject = JSON.parseObject(bizConfig);
            // 优先取业务配置
            Integer minPlayer = bizObject.getInteger("minPlayer");
            Integer maxPlayer = bizObject.getInteger("maxPlayer");
            if (minPlayer != null) {
                configObject.put("minPlayer", minPlayer);
            }
            if (maxPlayer != null) {
                configObject.put("maxPlayer", maxPlayer);
            }
            config = JSON.toJSONString(configObject);
        }
        return config;
    }

    /**
     * 将业务自定义配置信息暂存到缓存中
     *
     * @param id 业务游戏ID，game_biz_game表的id
     * @param config
     */
    public void updateGameConfig(long id, String config) {
        if (StringUtils.isEmpty(config)) {
            config = "";
        }
        // 记录业务方的自定义配置信息
        String key = RedisKeys.GAME_BIZ_CONFIG.buildKey(String.valueOf(id));
        this.redisClient.set(key, config);
        this.redisClient.expire(key, this.lzConfig.getBizGameConfigExpireTime());
        logger.info("Update the game config, key:{}, config:{}", key, config);
    }

    /**
     * 获取游戏
     *
     * @param id
     * @return
     */
    public GameBizGameBean getBizGame(Long id) {
        GameBizGameBean bean = new GameBizGameBean();
        bean.setId(id);
        return this.gameBizGameBeanMapper.selectByPrimaryKey(bean);
    }

    public GameBizGameRelationBean getBizRelationGame(long id){
        GameBizGameRelationBean bizGameRelationBean = new GameBizGameRelationBean();
        bizGameRelationBean.setId(id);
        return this.gameBizGameRelationBeanMapper.selectByPrimaryKey(bizGameRelationBean);
    }

    /**
     * 根据业务游戏Id获取渠道游戏ID
     *
     * @param gameId 业务游戏ID
     * @return 如果返回空则表示没有找到匹配关系
     */
    public String getChannelGameIdByGameId(Long gameId) {
        GameBizGameBean game = this.getBizGame(gameId);
        if (game != null) {
            Long gameInfoId = game.getGameInfoId();
            GameInfoBean gameInfoBean = this.gameInfoManager.getGameInfoBeanById(gameInfoId);
            if (gameInfoBean != null) {
                return gameInfoBean.getChannelGameIdStr();
            }
        }
        return "";
    }

    public BizGameRelationOrcaResult getBizGameRelationOrcaResultById(long id){
        return bizGameRelationOrcaExtMapper.getBizGameRelationById(id);
    }

    public PageList<BizGameRelationOrcaResult> pageSearchBizOrca(long id, long gameAppId, long gameInfoId, long channelId, int pageSize, int pageNumber){
        PageList<BizGameRelationOrcaResult> pageList = new PageList<>();
        pageList.setPageSize(pageSize);
        pageList.setPageNumber(pageNumber);
        int count = bizGameRelationOrcaExtMapper.countBizGameRelationByIds(id, gameAppId, gameInfoId, channelId);
        pageList.setTotal(count);

        List<BizGameRelationOrcaResult> results = bizGameRelationOrcaExtMapper.getBizGameRelationByIds(
                id, gameAppId, gameInfoId, channelId, (pageNumber-1) * pageSize, pageSize);
        pageList.addAll(results);
        return pageList;
    }

    public void delBizGameRelationOrcaResultById(long id){
        GameBizGameBean bizGameBean = new GameBizGameBean();
        bizGameBean.setId(id);
        gameBizGameBeanMapper.deleteByPrimaryKey(bizGameBean);
        GameBizGameRelationBean bizGameRelationBean = new GameBizGameRelationBean();
        bizGameRelationBean.setId(id);
        gameBizGameRelationBeanMapper.deleteByPrimaryKey(bizGameRelationBean);
    }

    public void insertOrUpdate(long id, long gameAppId, long gameInfoId, long channelId, String config, String appId, String channelGameId){
        Date date = new Date();
        boolean insert = id <= 0L;
        GameBizGameBean bizGameBean = new GameBizGameBean();
        GameBizGameRelationBean gameBizGameRelationBean = new GameBizGameRelationBean();
        if(insert){
            bizGameBean.setId(guidGenerator.genId());
            bizGameBean.setCreateTime(date);
            gameBizGameRelationBean.setId(bizGameBean.getId());
            gameBizGameRelationBean.setCreateTime(date);
        }else {
            bizGameBean = getBizGame(id);
            gameBizGameRelationBean = getBizRelationGame(id);
        }

        bizGameBean.setConfig(Optional.ofNullable(config).orElse(StringUtils.EMPTY));
        bizGameBean.setGameAppId(gameAppId);
        bizGameBean.setGameInfoId(gameInfoId);
        bizGameBean.setDel(NumberUtils.INTEGER_ZERO);
        bizGameBean.setChannelId(channelId);
        bizGameBean.setOperator("orcaAdmin");
        bizGameBean.setModifyTime(date);

        gameBizGameRelationBean.setAppId(appId);
        gameBizGameRelationBean.setChannelGameId(channelGameId);
        gameBizGameRelationBean.setModifyTime(date);

        if(insert){
            if(gameBizGameBeanMapper.insert(bizGameBean) > 0){
                gameBizGameRelationBeanMapper.insert(gameBizGameRelationBean);
            }
        }else {
            if(gameBizGameBeanMapper.updateByPrimaryKey(bizGameBean) > 0){
                gameBizGameRelationBeanMapper.updateByPrimaryKey(gameBizGameRelationBean);
            }
        }
    }

    /**
     * 查询映射关系
     *
     * @param channelId 渠道ID
     * @return
     */
    public List<GameBizGameBean> getGames(Long channelId) {
        GameBizGameBean gameBean = new GameBizGameBean();
        gameBean.setChannelId(channelId);
        return this.gameBizGameBeanMapper.selectMany(gameBean);
    }
}