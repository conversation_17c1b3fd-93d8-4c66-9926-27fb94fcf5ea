package fm.lizhi.ocean.seal.constant;

import lombok.Getter;

/**
 * LUK 控制事件类型枚举
 * <AUTHOR>
 */
@Getter
public enum LukControlEventType {

    // ========== 房间事件类型 ==========
    
    /**
     * 控制玩家加入游戏
     */
    USER_JOIN(1, "控制玩家加入游戏", true),

    /**
     * 控制玩家离开游戏
     */
    USER_LEAVE(2, "控制玩家离开游戏", true),

    /**
     * 改变玩家准备状态
     */
    USER_READY(3, "改变玩家准备状态", true),

    /**
     * 踢出特定玩家
     */
    USER_KICK(4, "踢出特定玩家", true),

    /**
     * 开始游戏
     */
    GAME_START(5, "开始游戏", true),

    /**
     * 强制结束游戏
     */
    GAME_END(6, "强制结束游戏", true),

    /**
     * 修改房间设置
     */
    ROOM_SETTING(7, "修改房间设置", true),

    /**
     * 修改玩家身份
     */
    USER_IDENTITY(8, "修改玩家身份", true),

    /**
     * 主动拉取同步房间座位事件
     */
    SYNC_ROOM(9, "主动拉取同步房间座位事件", true),

    /**
     * 刷新用户信息
     */
    REFRESH_USER(10, "刷新用户信息", true),

    /**
     * 快捷开始游戏
     */
    QUICK_START(11, "快捷开始游戏", true),

    // ========== 全局事件类型 ==========

    /**
     * 用户道具发放
     */
    GRANT_PROP(1000, "用户道具发放", false),

    /**
     * 获取用户背包状态
     */
    GET_USER_PROPS(1001, "获取用户背包状态", false),

    /**
     * 查询道具发放状态
     */
    QUERY_PROP_STATUS(1002, "查询道具发放状态", false);

    /**
     * 事件类型值
     */
    private final int type;

    /**
     * 事件描述
     */
    private final String description;

    /**
     * 是否需要房间ID
     */
    private final boolean needRoomId;

    LukControlEventType(int type, String description, boolean needRoomId) {
        this.type = type;
        this.description = description;
        this.needRoomId = needRoomId;
    }

    /**
     * 根据类型值查找枚举
     */
    public static LukControlEventType fromType(Integer type) {
        for (LukControlEventType eventType : values()) {
            if (eventType.type == type) {
                return eventType;
            }
        }
        return null;
    }

    /**
     * 是否为房间事件
     */
    public boolean isRoomEvent() {
        return needRoomId;
    }

    /**
     * 是否为全局事件
     */
    public boolean isGlobalEvent() {
        return !needRoomId;
    }
}
