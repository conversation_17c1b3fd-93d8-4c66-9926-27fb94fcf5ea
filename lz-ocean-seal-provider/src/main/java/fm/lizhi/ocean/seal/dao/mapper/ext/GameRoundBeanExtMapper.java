package fm.lizhi.ocean.seal.dao.mapper.ext;

import fm.lizhi.common.datastore.core.annotation.DataStore;
import fm.lizhi.ocean.seal.dao.bean.GameRoundBean;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

/**
 * Created in 2022-05-16 17:43.
 *
 * <AUTHOR>
 */
@DataStore(namespace = "mysql_ocean_oceanseal")
public interface GameRoundBeanExtMapper {
    /**
     * 根据渠道局ID更新游戏局信息
     *
     * @param channelRoundId 渠道局ID
     * @param gameEndAtTime  游戏结束时间
     * @param gameDuration   游戏时长
     * @param state          状态
     */
    @Update("update game_round set `end_time` = #{gameEndAtTime}, `duration` = #{gameDuration}, `state` = #{state}, `channel_game_mode` = #{gameMode}, `channel_room_id` = #{roomId}, `start_time` = #{startTime}, `end_extra` = #{endExtra} where `channel_round_id` = #{channelRoundId};")
    void updateByChannelRoundId(@Param("channelRoundId") String channelRoundId,
                                @Param("gameEndAtTime") long gameEndAtTime,
                                @Param("gameDuration") int gameDuration,
                                @Param("state") int state,
                                @Param("gameMode") int gameMode,
                                @Param("roomId") String roomId,
                                @Param("startTime") long startTime,
                                @Param("endExtra") String endExtra);

    @Select("select * from game_round where `app_id` = #{gameAppId} and `game_id` = #{gameId} and `biz_room_id` = #{roomId} and `channel_game_id` = #{channelGameId} and `state` != 3")
    GameRoundBean selectNotFinishedGameRound(@Param("gameAppId") long gameAppId, @Param("gameId") long gameId, @Param("channelGameId") String channelGameId, @Param("roomId") String roomId);

    /**
     * 根据渠道游戏局，设置透传参数
     *
     * @param channelRoundId 渠道局ID
     * @param extra          透传参数
     * @return
     */
    @Update("update game_round set `extra` = #{extra} where `channel_round_id` = #{channelRoundId}")
    int updateExtraByChannelRoundId(@Param("channelRoundId") String channelRoundId, @Param("extra") String extra);
}
