package fm.lizhi.ocean.seal.strategy;

import fm.lizhi.ocean.seal.dao.bean.GameBizGameBean;
import fm.lizhi.ocean.seal.dao.bean.GameChannelBean;
import fm.lizhi.ocean.seal.dao.bean.GameInfoBean;
import fm.lizhi.ocean.seal.protocol.GameProxyServiceProto;

/**
 * 游戏代理调用策略接口
 * 根据不同的游戏渠道类型，提供不同的目标调用实现
 * 
 * Created in 2025-07-01
 * 
 * <AUTHOR> Agent
 */
public interface GameProxyInvokeStrategy {
    
    /**
     * 执行目标调用
     *
     * @param param 调用参数
     * @param bizGameBean 业务游戏信息
     * @param gameInfoBean 游戏信息
     * @param gameChannelBean 游戏渠道信息
     * @return 调用结果
     */
    GameProxyServiceProto.ResponseInvokeTarget invokeTarget(
            GameProxyServiceProto.InvokeTargetParams param,
            GameBizGameBean bizGameBean,
            GameInfoBean gameInfoBean, 
            GameChannelBean gameChannelBean);
    
    /**
     * 判断当前策略是否支持指定的渠道
     * 
     * @param channel 渠道名称
     * @return 是否支持
     */
    boolean supports(String channel);
}
