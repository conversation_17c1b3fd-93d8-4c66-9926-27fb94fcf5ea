package fm.lizhi.ocean.seal.dao.bean;

import java.util.Date;
import javax.persistence.*;

/**
 * This class was generated by DataStore MyBatis Generator.
 *
 * 游戏结果表
 *
 * @date 2025-05-14 04:39:02
 */
@Table(name = "`game_round_result`")
public class GameRoundResultBean {
    /**
     * 主键ID
     */
    @Id
    @GeneratedValue
    @Column(name= "`id`")
    private Long id;

    /**
     * 平台的游戏局ID
     */
    @Column(name= "`round_id`")
    private Long roundId;

    /**
     * 渠道局ID
     */
    @Column(name= "`channel_round_id`")
    private String channelRoundId;

    /**
     * 用户ID
     */
    @Column(name= "`user_id`")
    private String userId;

    /**
     * 用户名
     */
    @Column(name= "`user_name`")
    private String userName;

    /**
     * 是否真实用户，1：是，0：否
     */
    @Column(name= "`real_user`")
    private Integer realUser;

    /**
     * 玩家角色
     */
    @Column(name= "`role`")
    private Integer role;

    /**
     * 是否逃跑，1：是，0：否
     */
    @Column(name= "`escaped`")
    private Integer escaped;

    /**
     * 排名，从1开始
     */
    @Column(name= "`rank`")
    private Integer rank;

    /**
     * 得分
     */
    @Column(name= "`score`")
    private Integer score;

    /**
     * 0表示没有信息，1：输，2：赢，3：平局
     */
    @Column(name= "`is_win`")
    private Integer isWin;

    /**
     * 创建时间
     */
    @Column(name= "`create_time`")
    private Date createTime;

    /**
     * 修改时间
     */
    @Column(name= "`modify_time`")
    private Date modifyTime;

    /**
     * 奖励
     */
    @Column(name= "`award`")
    private Integer award;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getRoundId() {
        return roundId;
    }

    public void setRoundId(Long roundId) {
        this.roundId = roundId;
    }

    public String getChannelRoundId() {
        return channelRoundId;
    }

    public void setChannelRoundId(String channelRoundId) {
        this.channelRoundId = channelRoundId == null ? null : channelRoundId.trim();
    }

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId == null ? null : userId.trim();
    }

    public String getUserName() {
        return userName;
    }

    public void setUserName(String userName) {
        this.userName = userName == null ? null : userName.trim();
    }

    public Integer getRealUser() {
        return realUser;
    }

    public void setRealUser(Integer realUser) {
        this.realUser = realUser;
    }

    public Integer getRole() {
        return role;
    }

    public void setRole(Integer role) {
        this.role = role;
    }

    public Integer getEscaped() {
        return escaped;
    }

    public void setEscaped(Integer escaped) {
        this.escaped = escaped;
    }

    public Integer getRank() {
        return rank;
    }

    public void setRank(Integer rank) {
        this.rank = rank;
    }

    public Integer getScore() {
        return score;
    }

    public void setScore(Integer score) {
        this.score = score;
    }

    public Integer getIsWin() {
        return isWin;
    }

    public void setIsWin(Integer isWin) {
        this.isWin = isWin;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getModifyTime() {
        return modifyTime;
    }

    public void setModifyTime(Date modifyTime) {
        this.modifyTime = modifyTime;
    }

    public Integer getAward() {
        return award;
    }

    public void setAward(Integer award) {
        this.award = award;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", roundId=").append(roundId);
        sb.append(", channelRoundId=").append(channelRoundId);
        sb.append(", userId=").append(userId);
        sb.append(", userName=").append(userName);
        sb.append(", realUser=").append(realUser);
        sb.append(", role=").append(role);
        sb.append(", escaped=").append(escaped);
        sb.append(", rank=").append(rank);
        sb.append(", score=").append(score);
        sb.append(", isWin=").append(isWin);
        sb.append(", createTime=").append(createTime);
        sb.append(", modifyTime=").append(modifyTime);
        sb.append(", award=").append(award);
        sb.append("]");
        return sb.toString();
    }
}