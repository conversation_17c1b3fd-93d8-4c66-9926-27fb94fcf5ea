# LUK 控制事件通用方法使用说明

## 概述

本次重构将LUK渠道的PublishControlEvent调用逻辑统一封装到`LukManger`中，提供了通用的控制事件发布方法。

## 主要变更

### 1. 新增支持类

- `LukControlEventRequest` - 控制事件请求参数类
- `LukControlEventType` - LUK控制事件类型枚举

### 2. LukManger新增方法

- `publishControlEvent(LukControlEventRequest request)` - 通用控制事件发布方法
- `publishRoomEvent(...)` - 房间事件发布便捷方法
- `publishGlobalEvent(...)` - 全局事件发布便捷方法

### 3. 重构的策略类

- `LukGamePropGrantStrategy` - 使用新的通用方法发布道具发放事件
- `LukGameProxyInvokeStrategy` - 使用新的通用方法发布各种控制事件

## 使用方式

### 方式1：使用便捷方法

```java
// 发布房间事件
Response<?> response = lukManager.publishRoomEvent(
    appId, gameId, roomId, 
    LukControlEventType.USER_JOIN, 
    eventData
);

// 发布全局事件
Response<?> response = lukManager.publishGlobalEvent(
    appId, gameId, 
    LukControlEventType.GRANT_PROP, 
    eventData
);
```

### 方式2：使用通用方法

```java
// 创建请求参数
LukControlEventRequest request = LukControlEventRequest.createRoomEvent(
    appId, gameId, roomId, eventType, eventData
);

// 发布事件
Response<?> response = lukManager.publishControlEvent(request);
```

## 支持的事件类型

### 房间事件 (需要roomId)
- USER_JOIN(1) - 控制玩家加入游戏
- USER_LEAVE(2) - 控制玩家离开游戏
- USER_READY(3) - 改变玩家准备状态
- USER_KICK(4) - 踢出特定玩家
- GAME_START(5) - 开始游戏
- GAME_END(6) - 强制结束游戏
- ROOM_SETTING(7) - 修改房间设置
- USER_IDENTITY(8) - 修改玩家身份
- SYNC_ROOM(9) - 同步房间座位事件
- REFRESH_USER(10) - 刷新用户信息

### 全局事件 (不需要roomId)
- GRANT_PROP(1000) - 用户道具发放
- GET_USER_PROPS(1001) - 获取用户背包状态
- QUERY_PROP_STATUS(1002) - 查询道具发放状态

## 优势

1. **统一管理** - 所有LUK控制事件调用集中在LukManger中
2. **类型安全** - 通过枚举避免事件类型错误
3. **代码复用** - 消除重复的SDK调用代码
4. **易于维护** - 统一的错误处理和日志记录
5. **向后兼容** - 不破坏现有的公共接口

## 注意事项

1. 房间事件必须提供roomId参数
2. 全局事件不需要roomId参数
3. 事件数据格式需要符合LUK厂商接口文档要求
4. 方法会自动处理SDK实例创建和参数校验
