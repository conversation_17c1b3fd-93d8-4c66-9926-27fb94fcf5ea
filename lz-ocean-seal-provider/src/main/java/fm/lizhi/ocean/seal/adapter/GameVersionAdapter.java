package fm.lizhi.ocean.seal.adapter;

import com.google.inject.Inject;
import com.netflix.governator.annotations.AutoBindSingleton;
import fm.lizhi.ocean.seal.dao.bean.GameBizGameBean;
import fm.lizhi.ocean.seal.dao.bean.GameVersionBean;
import fm.lizhi.ocean.seal.manager.BizGameManager;
import fm.lizhi.ocean.seal.protocol.GameServiceProto;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.ArrayList;
import java.util.List;

/**
 * Created in 2022-06-21 17:50.
 *
 * <AUTHOR>
 */
@AutoBindSingleton
public class GameVersionAdapter {
    private static final Logger logger = LoggerFactory.getLogger(GameVersionAdapter.class);
    @Inject
    private BizGameManager bizGameManager;

    public List<GameServiceProto.GameVersionInfo> convert(List<GameVersionBean> beans) {
        List<GameServiceProto.GameVersionInfo> gameVersionInfos = new ArrayList<>();
        for (GameVersionBean bean : beans) {
            GameServiceProto.GameVersionInfo.Builder builder = GameServiceProto.GameVersionInfo.newBuilder();
            builder.setGameVersionCode(bean.getGameVersion());
            builder.setGameId(bean.getGameId());
            builder.setChannelGameId(this.bizGameManager.getChannelGameIdByGameId(bean.getGameId()));
            builder.setForceUpdate(bean.getForceUpdate() == 1);
            builder.setDownloadLink(bean.getDownloadLink());
            gameVersionInfos.add(builder.build());
        }
        return gameVersionInfos;
    }
}