package fm.lizhi.ocean.seal.http;

import com.alibaba.fastjson.JSONObject;
import com.netflix.governator.annotations.AutoBindSingleton;
import fm.lizhi.ocean.seal.http.helper.HttpClientHelper;
import org.apache.http.impl.client.CloseableHttpClient;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.util.HashMap;
import java.util.Map;

/**
 * http客户端
 * <p>
 * Created in 2022-05-30 15:44.
 *
 * <AUTHOR>
 */
@AutoBindSingleton
public class HttpClient extends AbstractHttpClient {
    private static Logger logger = LoggerFactory.getLogger(HttpClient.class);

    public HttpClient() {
        super(HttpClientHelper.getHttpClientBuilder().build());
    }

    public HttpClient(CloseableHttpClient httpClient) {
        super(httpClient);
    }

    /**
     * 发送POST请求，返回JSON对象
     *
     * @param uri     请求地址
     * @param params  请求参数
     * @param headers 请求头
     * @param entity  实体内容
     * @return
     */
    public JSONObject postJsonObject(String uri, Map<String, Object> params, Map<String, String> headers, String entity) throws IOException {
        byte[] result = this.post(uri, params, headers, entity.getBytes());
        String retString = new String(result, StandardCharsets.UTF_8);
        return JSONObject.parseObject(retString);
    }

    /**
     * 发送GET请求并获取JSON对象
     *
     * @param url    请求地址
     * @param params 参数
     * @return
     */
    public JSONObject getJsonObject(String url, Map<String, Object> params) throws IOException {
        byte[] bytes = this.get(url, params, null);
        return JSONObject.parseObject(new String(bytes, StandardCharsets.UTF_8));
    }

    /**
     * 发送GET请求并获取JSON对象
     *
     * @param url    请求地址
     * @param params 参数
     * @return
     */
    public JSONObject getJsonObject(String url, Map<String, Object> params, Map<String, String> headers) throws IOException {
        byte[] bytes = this.get(url, params, headers);
        return JSONObject.parseObject(new String(bytes, StandardCharsets.UTF_8));
    }

    /**
     * 发送GET请求并获取JSON对象
     *
     * @param url 请求地址
     * @return
     */
    public JSONObject getJsonObject(String url) throws IOException {
        byte[] bytes = this.get(url, null, null);
        return JSONObject.parseObject(new String(bytes, StandardCharsets.UTF_8));
    }

    /**
     * 发送GET请求
     *
     * @param url     请求地址
     * @param params  参数
     * @param headers 请求头
     * @return
     * @throws IOException
     */
    public String getString(String url, Map<String, Object> params, Map<String, String> headers) throws IOException {
        byte[] result = this.get(url, params, headers);
        return new String(result, StandardCharsets.UTF_8);
    }
}