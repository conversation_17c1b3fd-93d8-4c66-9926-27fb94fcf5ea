package fm.lizhi.ocean.seal.http.helper;

import org.apache.http.client.config.RequestConfig;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * Created in 2022-05-30 15:44.
 *
 * <AUTHOR>
 */
public class RequestConfigHelper {
    private static Logger logger = LoggerFactory.getLogger(RequestConfigHelper.class);

    /**
     * 获取默认的请求配置
     *
     * @return
     */
    public static RequestConfig.Builder getRequestConfigBuilder() {
        // 请求配置
        return RequestConfig.custom()
                .setConnectionRequestTimeout(5000) // 从连接池获取连接的超时时间
                .setConnectTimeout(5000) // 连接超时时间
                .setSocketTimeout(5000) // 等待回应超时时间
                .setMaxRedirects(20)// 设置最大重定向次数
                //.setLocalAddress() // 本机存在多个网卡的情况下，用哪个本地网卡连接目标地址
                //.setProxy() // 设置代理服务器
                ;
    }
}