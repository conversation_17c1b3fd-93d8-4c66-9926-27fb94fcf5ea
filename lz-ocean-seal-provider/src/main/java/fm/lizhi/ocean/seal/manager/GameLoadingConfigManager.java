package fm.lizhi.ocean.seal.manager;

import com.google.inject.Inject;
import com.netflix.governator.annotations.AutoBindSingleton;
import fm.lizhi.ocean.seal.dao.bean.GameBizGameBean;
import fm.lizhi.ocean.seal.dao.bean.GameInfoBean;
import fm.lizhi.ocean.seal.protocol.GameServiceProto;
import fm.lizhi.ocean.seal.strategy.GameLoadingConfigStrategy;
import fm.lizhi.ocean.seal.strategy.factory.GameLoadingConfigStrategyFactory;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

/**
 * 游戏加载配置管理器
 * 负责协调策略的使用，提供统一的游戏加载配置获取接口
 * 
 * Created in 2025-06-27
 * 
 * <AUTHOR> Agent
 */
@Slf4j
@AutoBindSingleton
public class GameLoadingConfigManager {
    
    @Inject
    private GameLoadingConfigStrategyFactory strategyFactory;
    
    /**
     * 根据渠道类型获取游戏加载配置
     *
     * @param channel      渠道名称
     * @param bizGameBean  业务游戏信息
     * @param gameInfoBean 游戏信息
     * @param appId
     * @return 游戏加载配置，如果无法生成则返回null
     */
    public GameServiceProto.GameLoadingConfig getGameLoadingConfig(String channel,
                                                                   GameBizGameBean bizGameBean,
                                                                   GameInfoBean gameInfoBean, String appId) {
        if (StringUtils.isBlank(channel)) {
            log.error("Channel is blank, cannot get game loading config");
            return null;
        }
        
        if (bizGameBean == null) {
            log.error("BizGameBean is null, cannot get game loading config");
            return null;
        }
        
        if (gameInfoBean == null) {
            log.error("GameInfoBean is null, cannot get game loading config");
            return null;
        }
        
        try {
            // 获取对应渠道的策略
            GameLoadingConfigStrategy strategy = strategyFactory.getStrategy(channel);
            if (strategy == null) {
                log.error("No strategy found for channel: {}, gameId: {}", channel, bizGameBean.getId());
                return null;
            }
            
            // 使用策略生成配置
            GameServiceProto.GameLoadingConfig config = strategy.getGameLoadingConfig(bizGameBean, gameInfoBean, appId);
            
            if (config == null) {
                log.error("Strategy {} failed to generate config for channel: {}, gameId: {}", 
                        strategy.getClass().getSimpleName(), channel, bizGameBean.getId());
                return null;
            }
            
            log.info("Successfully generated game loading config for channel: {}, gameId: {}, url: {}", 
                    channel, bizGameBean.getId(), config.getUrl());
            
            return config;
            
        } catch (Exception e) {
            log.error("Error getting game loading config for channel: {}, gameId: {}", 
                    channel, bizGameBean.getId(), e);
            return null;
        }
    }
    
    /**
     * 检查指定渠道是否支持
     * 
     * @param channel 渠道名称
     * @return 是否支持
     */
    public boolean isChannelSupported(String channel) {
        if (StringUtils.isBlank(channel)) {
            return false;
        }
        
        GameLoadingConfigStrategy strategy = strategyFactory.getStrategy(channel);
        return strategy != null;
    }
}
