package fm.lizhi.ocean.seal.manager;

import com.google.inject.Inject;
import com.netflix.governator.annotations.AutoBindSingleton;
import com.yibasan.lizhi.tracker.BigDataLogger;
import com.yibasan.lizhi.tracker.model.Event;
import fm.lizhi.ocean.seal.protocol.GameReportServiceProto;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.HashMap;

/**
 * 游戏数据上报
 * <p>
 * Created in 2022-04-08 14:22.
 *
 * <AUTHOR>
 */
@AutoBindSingleton
public class GameDataReportManager {
    private static final Logger logger = LoggerFactory.getLogger(GameDataReportManager.class);
    @Inject
    private BigDataLogger bigDataLogger;

    /**
     * 上报游戏结束信息给大数据
     *
     * @param result 游戏结束后，上游厂商回调的结果
     */
    public void reportGameEnd(GameReportServiceProto.GameSettleResult result) {
//        try {
//            // 需要上报的属性，游戏ID、游戏局ID、厂商渠道、收到数据时间、游戏在厂商里的实际开始时间、实际结束时间、数据所属appId
//            Event event = new Event();
//            // 因为是属于游戏厂商的回调，没有用户的概念，默认填个456
//            event.setUserid("1234517478397401644");
//            event.set_login_id("0");
//            HashMap<String, Object> map = new HashMap<>();
//            // 游戏ID
//            map.put("gameId", result.getGameId());
//            // 游戏局ID
//            map.put("gameRoundId", result.getGameRoundId());
//            // 游戏厂商/渠道
//            map.put("channel", result.getChannel());
//            // 实际收到回调的时间，毫秒
//            map.put("receivedTimeMs", System.currentTimeMillis());
//            // 游戏实际开始时间，毫秒
//            map.put("startTime", result.getGameStartAtTime());
//            // 游戏实际结束时间，毫秒
//            map.put("endTime", result.getGameEndAtTime());
//            // 应用ID
//            map.put("appId", result.getAppId());
//            event.setProperties(map);
//            // 因为是属于游戏厂商的回调，没有用户的概念，默认填个123
//            this.bigDataLogger.track("horacecbd6e609822b46c0fb9ef04ed1ff33684d", true,"EVENT_SUPPORT_SEAL_GAME_SETTLE_DATA_REPORT_TO_SERVER", event);
//            logger.info("report game end to bigdata, param:{}",  map);
//        } catch (Exception e) {
//            logger.error("report game end information to big data exception, msg:{}, gameResult:{}", e.getMessage(), result, e);
//        }
    }
}