package fm.lizhi.ocean.seal.manager;

import com.google.inject.Inject;
import com.netflix.governator.annotations.AutoBindSingleton;
import fm.lizhi.commons.util.GuidGenerator;
import fm.lizhi.ocean.seal.dao.bean.GameLanguageBean;
import fm.lizhi.ocean.seal.dao.mapper.GameLanguageBeanMapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.util.Date;
import java.util.List;
import java.util.Optional;

@Slf4j
@AutoBindSingleton
public class GameLanguageManager {

    @Inject
    private GuidGenerator guidGenerator;
    @Inject
    private GameLanguageBeanMapper gameLanguageBeanMapper;

    /**
     * 插入语言
     * @param name
     * @param abbreviation
     */
    public void insertGameLanguage(String name, String abbreviation){
        Date date = new Date();
        GameLanguageBean bean = new GameLanguageBean();
        bean.setId(guidGenerator.genId());
        bean.setName(Optional.ofNullable(name).orElse(StringUtils.EMPTY));
        bean.setAbbreviation(Optional.ofNullable(abbreviation).orElse(StringUtils.EMPTY));
        bean.setModifyTime(date);
        bean.setCreateTime(date);
        gameLanguageBeanMapper.insert(bean);
    }

    public List<GameLanguageBean> getGameLanguages(){
        return gameLanguageBeanMapper.selectMany(new GameLanguageBean());
    }
}
