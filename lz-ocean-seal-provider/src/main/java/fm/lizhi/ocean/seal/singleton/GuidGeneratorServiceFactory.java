package fm.lizhi.ocean.seal.singleton;

import com.google.inject.Provider;
import com.netflix.governator.annotations.AutoBindSingleton;
import fm.lizhi.commons.util.GuidGenerator;
import org.springframework.context.annotation.Bean;

/**
 * 
 * 服务消费者service初始化类
 *
 * 如果需要通过rpc调用其他服务，打开注释，把SampleService改成要调用的service
 */
@AutoBindSingleton
public class GuidGeneratorServiceFactory implements Provider<GuidGenerator> {

	@Override
    public GuidGenerator get() {
        return new GuidGenerator();
	}

}
