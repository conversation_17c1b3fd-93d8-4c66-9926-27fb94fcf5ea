package fm.lizhi.ocean.seal.pojo.bo.lizhi;

import fm.lizhi.ocean.seal.protocol.GameReportServiceProto;
import lombok.Data;

/**
 * <AUTHOR>
 * @since 2019-12-24 15:48
 */
@Data
public class LizhiGameUser {
    private long userId;
    /**
     * 状态：0-正常，1-已离开
     */
    private int escaped;
    /**
     * 玩家顺序
     */
    private int userIndex;
    /**
     * 结果 0:表示没有信息，1:输，2:赢，3:平局
     */
    private int winStatus;
    /**
     * 排名
     */
    private int rank;
    /**
     * 得分
     */
    private int score;

    /**
     * 是否是真实用户
     */
    private boolean isRealUser;

    /**
     * 数据转换
     *
     * @return
     */
    public GameReportServiceProto.GamePlayerResult buildGamePlayerResult() {
        return GameReportServiceProto.GamePlayerResult.newBuilder()
                .setUid(userId + "").setRealUser(isRealUser).build();
    }

    /**
     * 数据转换
     *
     * @return
     */
    public GameReportServiceProto.GamePlayerSettleResult buildGamePlayerSettleResult() {
        return GameReportServiceProto.GamePlayerSettleResult.newBuilder()
                .setUid(userId + "").setRealUser(isRealUser).setEscaped(escaped == 1)
                .setRank(rank).setScore(score).setIsWin(winStatus).build();
    }
}
