package fm.lizhi.ocean.seal.gamechannel;

import com.google.inject.Inject;
import com.netflix.governator.annotations.AutoBindSingleton;
import fm.lizhi.game.auth.SealAuth;
import fm.lizhi.game.auth.enums.SealAuthResultCode;
import fm.lizhi.game.auth.pojo.JwtUserInfo;
import fm.lizhi.game.auth.pojo.ParseResult;
import fm.lizhi.game.auth.pojo.SealAuthToken;
import fm.lizhi.ocean.seal.constant.ChannelType;
import fm.lizhi.ocean.seal.constant.PlatformMessageCodeEnum;
import fm.lizhi.ocean.seal.constant.TokenTypeEnum;
import fm.lizhi.ocean.seal.manager.GameAppManager;
import fm.lizhi.ocean.seal.manager.LukManger;
import fm.lizhi.ocean.seal.pojo.bo.ChannelCodeInfo;
import fm.lizhi.ocean.seal.pojo.bo.GameChannelAuthInfo;
import fm.lizhi.ocean.seal.pojo.bo.GameUidInfo;
import lombok.extern.slf4j.Slf4j;

import static fm.lizhi.game.auth.helper.JwtHelper.verifyToken;

/**
 * @description: LUK渠道认证信息原子服务类
 * @author: guoyibin
 * @create: 2025/07/01 16:00
 */
@Slf4j
@AutoBindSingleton
public class ChannelLukAuthorService implements IGameChannelAuthorService{

    private static final Long DEFAULT_MIN_TOKEN_EXPIRE_DURATION_SECOND = 7200L;

    @Inject
    private GameAppManager gameAppManager;

    @Inject
    private LukManger lukManger;

    @Override
    public PlatformMessageCodeEnum verifySSToke(String appId, String token) {
        throw new UnsupportedOperationException("LUK渠道不支持SSToken");
    }

    @Override
    public PlatformMessageCodeEnum verifyCode(String appId, String code) {
        throw new UnsupportedOperationException("LUK渠道不支持Code");
    }

    @Override
    public ChannelCodeInfo getCode(String appId, String uid) {
        throw new UnsupportedOperationException("LUK渠道不支持获取Code");
    }

    @Override
    public GameUidInfo getUid(String token, TokenTypeEnum tokenType) {
        throw new UnsupportedOperationException("LUK渠道不支持获取Uid");
    }

    @Override
    public GameChannelAuthInfo getAuthInfo(String appId, String token, TokenTypeEnum tokenType) {
        SealAuth sealAuth = gameAppManager.createLiZhiSealAuth(appId, ChannelType.LUK.getName());
        if(null == sealAuth){
            return new GameChannelAuthInfo().setSuccess(false).setErrorCode(PlatformMessageCodeEnum.NOT_FOUND.getCode());
        }

        SealAuthResultCode sealAuthResultCode = verifyLukToken(sealAuth, token, tokenType);
        if (sealAuthResultCode.getCode() != SealAuthResultCode.SUCCESS.getCode()) {
            return new GameChannelAuthInfo().setSuccess(false).setErrorCode(sealAuthResultCode.getCode());
        }

        ParseResult parseResult = getUserInfoByToken(sealAuth, token, tokenType);
        JwtUserInfo userInfo = parseResult.getUserInfo();
        if(!parseResult.isSuccess() || userInfo == null) {
            log.warn("parse luckCode fail;parseResult={};loginToken={}", parseResult, token);
            return new GameChannelAuthInfo().setSuccess(false).setErrorCode(PlatformMessageCodeEnum.UNDEFINE.getCode());
        }
        SealAuthToken sealAuthToken = sealAuth.getSealAuthToken(userInfo, DEFAULT_MIN_TOKEN_EXPIRE_DURATION_SECOND);
        return new GameChannelAuthInfo().setChannel(ChannelType.LUK.getName()).setSsToken(sealAuthToken.getToken()).setExpireTime(sealAuthToken.getExpireDate());
    }

    private SealAuthResultCode verifyLukToken(SealAuth sealAuth, String token, TokenTypeEnum tokenType) {
        if (TokenTypeEnum.CHANNEL_CODE.equals(tokenType)) {
            return sealAuth.verifySealAuthCode(token);
        } else if (TokenTypeEnum.CHANNEL_SS_TOKEN.equals(tokenType)) {
            return sealAuth.verifySealAuthToken(token);
        }
        return SealAuthResultCode.TOKEN_INVALID;
    }

    private ParseResult getUserInfoByToken(SealAuth sealAuth, String token, TokenTypeEnum tokenType) {
        if (TokenTypeEnum.CHANNEL_CODE.equals(tokenType)) {
            return sealAuth.getUserInfoInSealAuthCode(token);
        } else if (TokenTypeEnum.CHANNEL_SS_TOKEN.equals(tokenType)) {
            return sealAuth.getUserInfoInSealAuthToken(token);
        }

        ParseResult emptyResult = new ParseResult();
        emptyResult.setErrorCode(SealAuthResultCode.TOKEN_INVALID);
        return emptyResult;
    }
}
