package fm.lizhi.ocean.seal.dao.bean;

import java.util.Date;
import javax.persistence.*;

/**
 * This class was generated by DataStore MyBatis Generator.
 *
 * 游戏局记录表
 *
 * @date 2022-05-16 04:54:34
 */
@Table(name = "`game_round`")
public class GameRoundBean {
    /**
     * 主键，游戏局ID
     */
    @Id
    @GeneratedValue
    @Column(name= "`id`")
    private Long id;

    /**
     * game_app表Id
     */
    @Column(name= "`app_id`")
    private Long appId;

    /**
     * 游戏ID，game_biz_game表Id
     */
    @Column(name= "`game_id`")
    private Long gameId;

    /**
     * 业务房间ID
     */
    @Column(name= "`biz_room_id`")
    private String bizRoomId;

    /**
     * 渠道游戏ID
     */
    @Column(name= "`channel_game_id`")
    private String channelGameId;

    /**
     * 渠道局ID
     */
    @Column(name= "`channel_round_id`")
    private String channelRoundId;

    /**
     * 渠道房间ID
     */
    @Column(name= "`channel_room_id`")
    private String channelRoomId;

    /**
     * 游戏模式
     */
    @Column(name= "`channel_game_mode`")
    private Integer channelGameMode;

    /**
     * 游戏局状态，参照GameRoundState
     */
    @Column(name= "`state`")
    private Integer state;

    /**
     * 透传参数
     */
    @Column(name= "`extra`")
    private String extra;

    /**
     * 结束透传参数
     */
    @Column(name= "`end_extra`")
    private String endExtra;

    /**
     * 游戏开始时间，毫秒
     */
    @Column(name= "`start_time`")
    private Long startTime;

    /**
     * 游戏结束时间，毫秒
     */
    @Column(name= "`end_time`")
    private Long endTime;

    /**
     * 游戏时长，毫秒数
     */
    @Column(name= "`duration`")
    private Integer duration;

    /**
     * 创建时间
     */
    @Column(name= "`create_time`")
    private Date createTime;

    /**
     * 修改时间
     */
    @Column(name= "`modify_time`")
    private Date modifyTime;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getAppId() {
        return appId;
    }

    public void setAppId(Long appId) {
        this.appId = appId;
    }

    public Long getGameId() {
        return gameId;
    }

    public void setGameId(Long gameId) {
        this.gameId = gameId;
    }

    public String getBizRoomId() {
        return bizRoomId;
    }

    public void setBizRoomId(String bizRoomId) {
        this.bizRoomId = bizRoomId == null ? null : bizRoomId.trim();
    }

    public String getChannelGameId() {
        return channelGameId;
    }

    public void setChannelGameId(String channelGameId) {
        this.channelGameId = channelGameId == null ? null : channelGameId.trim();
    }

    public String getChannelRoundId() {
        return channelRoundId;
    }

    public void setChannelRoundId(String channelRoundId) {
        this.channelRoundId = channelRoundId == null ? null : channelRoundId.trim();
    }

    public String getChannelRoomId() {
        return channelRoomId;
    }

    public void setChannelRoomId(String channelRoomId) {
        this.channelRoomId = channelRoomId == null ? null : channelRoomId.trim();
    }

    public Integer getChannelGameMode() {
        return channelGameMode;
    }

    public void setChannelGameMode(Integer channelGameMode) {
        this.channelGameMode = channelGameMode;
    }

    public Integer getState() {
        return state;
    }

    public void setState(Integer state) {
        this.state = state;
    }

    public String getExtra() {
        return extra;
    }

    public void setExtra(String extra) {
        this.extra = extra == null ? null : extra.trim();
    }

    public String getEndExtra() {
        return endExtra;
    }

    public void setEndExtra(String endExtra) {
        this.endExtra = endExtra == null ? null : endExtra.trim();
    }

    public Long getStartTime() {
        return startTime;
    }

    public void setStartTime(Long startTime) {
        this.startTime = startTime;
    }

    public Long getEndTime() {
        return endTime;
    }

    public void setEndTime(Long endTime) {
        this.endTime = endTime;
    }

    public Integer getDuration() {
        return duration;
    }

    public void setDuration(Integer duration) {
        this.duration = duration;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getModifyTime() {
        return modifyTime;
    }

    public void setModifyTime(Date modifyTime) {
        this.modifyTime = modifyTime;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", appId=").append(appId);
        sb.append(", gameId=").append(gameId);
        sb.append(", bizRoomId=").append(bizRoomId);
        sb.append(", channelGameId=").append(channelGameId);
        sb.append(", channelRoundId=").append(channelRoundId);
        sb.append(", channelRoomId=").append(channelRoomId);
        sb.append(", channelGameMode=").append(channelGameMode);
        sb.append(", state=").append(state);
        sb.append(", extra=").append(extra);
        sb.append(", startTime=").append(startTime);
        sb.append(", endTime=").append(endTime);
        sb.append(", duration=").append(duration);
        sb.append(", createTime=").append(createTime);
        sb.append(", modifyTime=").append(modifyTime);
        sb.append("]");
        return sb.toString();
    }
}