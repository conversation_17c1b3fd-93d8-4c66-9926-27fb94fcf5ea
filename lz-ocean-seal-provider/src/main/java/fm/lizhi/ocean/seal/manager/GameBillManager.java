package fm.lizhi.ocean.seal.manager;

import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSON;
import com.google.inject.Inject;
import com.netflix.governator.annotations.AutoBindSingleton;
import fm.lizhi.commons.util.GuidGenerator;
import fm.lizhi.ocean.seal.dao.bean.GameBillBean;
import fm.lizhi.ocean.seal.dao.bean.GameBizGameBean;
import fm.lizhi.ocean.seal.dao.mapper.GameBillBeanMapper;
import fm.lizhi.ocean.seal.pojo.bo.sud.GameUserHeartBeat;
import fm.lizhi.ocean.seal.redis.CommonRedisManager;
import fm.lizhi.ocean.seal.redis.SealTokenKey;
import lombok.extern.slf4j.Slf4j;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2025/4/21 下午4:12
 * @description
 */
@Slf4j
@AutoBindSingleton
public class GameBillManager {

    @Inject
    private GameBillBeanMapper gameBillBeanMapper;
    @Inject
    private GuidGenerator guidGenerator;
    @Inject
    private BizGameManager bizGameManager;
    @Inject
    private CommonRedisManager commonRedisManager;

    public boolean insertOrUpdate(String msg) {

        GameUserHeartBeat heartBeat = JSON.parseObject(msg, GameUserHeartBeat.class);
        Date date = new Date();

        // 转换时间戳
        DateTime changeDateTime = DateUtil.date(heartBeat.getChangedTime());
        String changeDateStr = DateUtil.format(changeDateTime, "yyyy-MM-dd");
        DateTime recordDay = DateUtil.parseDate(changeDateStr);
        int recordHour = changeDateTime.getHours();

        // 查询业务游戏id
        GameBizGameBean bizGameBean = bizGameManager.getGame(heartBeat.getAppId(), String.valueOf(heartBeat.getGameId()));
        if (bizGameBean == null) {
            log.error("GameBillManager getGame not exist.`appId={" + heartBeat.getAppId() + "}`gameId={" + heartBeat.getGameId() + "}");
            return true;
        }

        // 业务游戏id
        Long bizGameBeanId = bizGameBean.getId();
        // 加锁
        String lockKey = SealTokenKey.HEART_LOCK.getKey(heartBeat.getUserId());
        if (!commonRedisManager.lockWithTimeout(lockKey, 2000L, 4100L)) {
            log.error("GameBillManager lock error.`lockKey={}", lockKey);
            return false;
        }

        try {
            // 查询当前统计日期是否有记录
            GameBillBean billBean = getGameBill(heartBeat, recordDay, recordHour, bizGameBeanId);
            if (null == billBean) {
                // 每小时写入一条新的记录
                billBean = new GameBillBean();
                billBean.setId(guidGenerator.genId());
                billBean.setAppId(heartBeat.getAppId());
                billBean.setGameId(bizGameBeanId);
                billBean.setNjId(heartBeat.getNjId());
                billBean.setUserId(heartBeat.getUserId());
                billBean.setUsage(1L);
                billBean.setRecordDay(recordDay);
                billBean.setRecordHour(recordHour);
                billBean.setCreateTime(date);
                billBean.setModifyTime(date);
                gameBillBeanMapper.insert(billBean);
            } else {
                // 更新
                gameBillBeanMapper.updateBillUsage(billBean.getId());
            }
        } catch (Exception e) {
            log.error("GameBillManager.insertOrUpdate error msg={}", msg, e);
        } finally {
            commonRedisManager.releaseLock(lockKey);
        }
        return true;
    }

    public GameBillBean getGameBill(GameUserHeartBeat heartBeat, DateTime recordDay, int recordHour, Long bizGameBeanId) {
        GameBillBean billBean = new GameBillBean();
        billBean.setUserId(heartBeat.getUserId());
        billBean.setNjId(heartBeat.getNjId());
        billBean.setAppId(heartBeat.getAppId());
        billBean.setGameId(bizGameBeanId);
        billBean.setRecordDay(recordDay);
        billBean.setRecordHour(recordHour);
        return gameBillBeanMapper.selectOne(billBean);
    }
}
