package fm.lizhi.ocean.seal.strategy.impl;

import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONObject;
import com.google.inject.Inject;
import com.netflix.governator.annotations.AutoBindSingleton;
import fm.lizhi.commons.config.util.JsonUtil;
import fm.lizhi.ocean.seal.constant.GameChannel;
import fm.lizhi.ocean.seal.constant.GameOperateEventMapping;
import fm.lizhi.ocean.seal.constant.GamePropEventMapping;
import fm.lizhi.ocean.seal.dao.bean.GameBizGameBean;
import fm.lizhi.ocean.seal.dao.bean.GameChannelBean;
import fm.lizhi.ocean.seal.dao.bean.GameInfoBean;
import fm.lizhi.ocean.seal.manager.LukManger;
import fm.lizhi.ocean.seal.constant.LukControlEventType;
import fm.lizhi.ocean.seal.protocol.GameProxyServiceProto;
import fm.lizhi.ocean.seal.strategy.GameProxyInvokeStrategy;
import io.github.cfgametech.Response;
import lombok.extern.slf4j.Slf4j;

import java.util.Map;

import static fm.lizhi.ocean.seal.api.GameProxyService.INVOKE_TARGET_ERROR;

/**
 * LUK 渠道游戏代理调用策略实现
 * 使用 LUK SDK 方式调用 LUK 渠道接口
 * 
 * Created in 2025-07-01
 *
 */
@Slf4j
@AutoBindSingleton
public class LukGameProxyInvokeStrategy implements GameProxyInvokeStrategy {

    @Inject
    private LukManger lukManager;
    
    @Override
    public GameProxyServiceProto.ResponseInvokeTarget invokeTarget(
            GameProxyServiceProto.InvokeTargetParams param,
            GameBizGameBean bizGameBean,
            GameInfoBean gameInfoBean, 
            GameChannelBean gameChannelBean) {
        
        log.info("LUK strategy invoke target, gameId: {}, channelGameId: {}, event: {}", 
                bizGameBean.getId(), gameInfoBean.getChannelGameIdStr(), param.getEventName());
        
        try {
            // 解析事件数据
            Map<String, Object> dataMap = JSONObject.parseObject(param.getDataJson(), Map.class);

            // 根据事件映射获取 LUK 控制事件类型
            LukControlEventType eventType = findLukControlEventType(param.getEventName());
            if (eventType == null) {
                log.error("Failed to find LUK event mapping for event: {}", param.getEventName());
                throw new IllegalArgumentException("Failed to find LUK event mapping for event: " + param.getEventName());
            }

            String roomId = StrUtil.isNotBlank(param.getRoomId()) ? param.getRoomId() : MapUtil.getStr(dataMap, "room_id");
            Response<?> sdkResult = lukManager.publishRoomEvent(
                        param.getAppId(),
                        String.valueOf(gameInfoBean.getChannelGameId()),
                        roomId,
                        eventType,
                        dataMap
                );

            // 将 SDK 结果转换为统一的响应格式
            return convertLukResultToResponse(sdkResult);
            
        } catch (Exception e) {
            log.error("LUK strategy invoke target failed, gameId: {}, event: {}", 
                     bizGameBean.getId(), param.getEventName(), e);
            throw new RuntimeException("LUK SDK invoke failed", e);
        }
    }

    
    /**
     * 根据事件名称查找对应的 LUK 控制事件类型
     */
    private LukControlEventType findLukControlEventType(String eventName) {
        String channelEvent = GameOperateEventMapping.getChannelEvent(GameChannel.LUK, eventName);
        if (StrUtil.isNotEmpty(channelEvent)) {
            return LukControlEventType.fromType(Integer.valueOf(eventName));
        }

        return null;
    }
    

    /**
     * 将 LUK SDK 的返回结果转换为标准的 ResponseInvokeTarget 格式
     */
    private GameProxyServiceProto.ResponseInvokeTarget convertLukResultToResponse(Response<?> sdkResult) {
        GameProxyServiceProto.ResponseInvokeTarget.Builder builder = 
                GameProxyServiceProto.ResponseInvokeTarget.newBuilder();
        
        try {
            if (sdkResult != null) {
                // 处理标准的 LUK SDK Response 格式
                if (sdkResult.suc()) {
                    builder.setBizCode(0);
                    builder.setMsg("success");
                    builder.setData(JsonUtil.dumps(sdkResult.getData()));
                } else {
                    builder.setBizCode(sdkResult.getCode());
                    builder.setMsg(sdkResult.getMessage());
                    builder.setData("");
                }
            }
            
        } catch (Exception e) {
            log.error("Error converting LUK result to response", e);
            builder.setBizCode(INVOKE_TARGET_ERROR);
            builder.setMsg("Failed to convert LUK response: " + e.getMessage());
            builder.setData("");
        }
        
        return builder.build();
    }
    
    @Override
    public boolean supports(String channel) {
        return GameChannel.LUK.equals(channel);
    }
}
