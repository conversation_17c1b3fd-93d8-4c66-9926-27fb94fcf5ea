package fm.lizhi.ocean.seal.util;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Random;

/**
 * 时间工具类
 * <p>
 * Created in 2022-05-16 16:27.
 *
 * <AUTHOR>
 */
public class TimeUtils {
    private static final Logger logger = LoggerFactory.getLogger(TimeUtils.class);
    private static Random random = new Random();

    /**
     * 获取基于基准秒的随机加数
     *
     * @param base 基础秒
     * @return
     */
    public static int getSeconds(int base) {
        return base + random.nextInt(300);
    }

    public static String formatDateTime(java.util.Date mydate, String strFormat) {
        String strReturn = "";
        if (mydate == null) return strReturn;
        if (strFormat == null) strFormat = "yyyy-MM-dd";
        try {
            java.text.SimpleDateFormat sdf = new java.text.SimpleDateFormat(strFormat);
            strReturn = sdf.format(mydate);
        } catch (Exception ex) {
        }
        return strReturn;
    }
}