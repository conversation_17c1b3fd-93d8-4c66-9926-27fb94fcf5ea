package fm.lizhi.ocean.seal.kafka;

import com.google.common.base.Strings;
import fm.lizhi.ocean.seal.constant.ReportTypeEnum;

/**
 * Created in 2022-01-28 10:44.
 *
 * <AUTHOR>
 */
@Deprecated
public class KafkaTopicUtil {
    /**
     * （业务名_topic_直播后端服务_直播）
     */
    private static String TOPIC_PRE = "lz_ocean_topic_seal";

    /**
     * 生成topic
     *
     * @param reportTypeEnum
     * @param appName
     * @return
     */
    public static String genTopic(ReportTypeEnum reportTypeEnum, String appName) {
        String topicName = TOPIC_PRE + "_" + reportTypeEnum.getType();
        if (Strings.isNullOrEmpty(appName)) {
            return topicName;
        }
        return topicName + "_" + appName;
    }
}
