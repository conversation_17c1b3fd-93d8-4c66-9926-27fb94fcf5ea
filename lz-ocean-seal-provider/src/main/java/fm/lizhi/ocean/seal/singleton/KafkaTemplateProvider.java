package fm.lizhi.ocean.seal.singleton;

import com.google.inject.Inject;
import com.google.inject.Provider;
import fm.lizhi.common.kafka.ioc.api.KafkaTemplate;
import fm.lizhi.common.kafka.ioc.guice.KafkaProvider;
import fm.lizhi.ocean.seal.conf.LzConfig;

/**
 * Created in 2022-01-28 09:27.
 *
 * <AUTHOR>
 */
@KafkaProvider
public class KafkaTemplateProvider implements Provider<KafkaTemplate> {

    @Inject
    private LzConfig config;

    @Override
    public KafkaTemplate get() {
        return new KafkaTemplate(config.getKafkaClusterNamespace());
    }
}