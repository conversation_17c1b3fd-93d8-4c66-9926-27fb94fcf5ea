package fm.lizhi.ocean.seal.pojo.bo.sud;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2025/4/22 下午4:28
 * @description
 */
@AllArgsConstructor
@NoArgsConstructor
@ToString
@Data
public class SudAppGameFee {

    /**
     * 渠道appId
     */
    private String app_id;

    /**
     * 渠道app名称
     */
    private String app_name;

    /**
     * 渠道游戏id
     */
    private String game_id;

    /**
     * 游戏名称
     */
    private String game_name;

    /**
     * 使用量(分钟)
     */
    private Long usage;

    /**
     * 总费用（不包含折扣）
     */
    private BigDecimal fee;
}
