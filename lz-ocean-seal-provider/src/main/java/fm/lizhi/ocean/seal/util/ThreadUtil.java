package fm.lizhi.ocean.seal.util;

import com.google.common.util.concurrent.ThreadFactoryBuilder;

import java.util.concurrent.*;

public final class ThreadUtil {

    private ThreadUtil() {}

    /**
     * TODO 后续返回CPU密集型和IO密集型的线程池
     * @param name
     * @param corePoolSize
     * @return
     */
    public static ExecutorService generateExecutorService(String name, int corePoolSize) {
        return generateExecutorService(name,corePoolSize,2*corePoolSize);
    }

    /**
     * 返回延迟任务线程池
     * @param name
     * @param corePoolSize
     * @return
     */
    public static ScheduledThreadPoolExecutor generateDelayExecutorService(String name, int corePoolSize) {
        ThreadFactory threadFactory = new ThreadFactoryBuilder()
                .setNameFormat("thread-" + name + "-%d")
                .build();
        return new ScheduledThreadPoolExecutor(corePoolSize, threadFactory);
    }

    /**
     * TODO 后续返回CPU密集型和IO密集型的线程池
     * @param name
     * @param corePoolSize
     * @return
     */
    public static ExecutorService generateExecutorService(String name, int corePoolSize, int maxPoolSize) {
        return generateExecutorService(name, corePoolSize, maxPoolSize, new LinkedBlockingQueue<>());
    }
    /**
     * TODO 后续返回CPU密集型和IO密集型的线程池
     * @param name
     * @param corePoolSize
     * @return
     */
    public static ExecutorService generateExecutorService(String name, int corePoolSize, int maxPoolSize, BlockingQueue<Runnable> workQueue) {
        return new ThreadPoolExecutor(
                corePoolSize, maxPoolSize, TimeUnit.SECONDS.toMillis(60),
                TimeUnit.MILLISECONDS,
                workQueue,
                new ThreadFactoryBuilder()
                .setNameFormat("thread-" + name + "-%d")
                .build(),
                new ThreadPoolExecutor.AbortPolicy());
    }
}
