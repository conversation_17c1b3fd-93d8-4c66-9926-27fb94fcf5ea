package fm.lizhi.ocean.seal.http.helper;

import fm.lizhi.ocean.seal.http.interceptors.DefaultRequestInterceptor;
import fm.lizhi.ocean.seal.http.interceptors.DefaultResponseInterceptor;
import org.apache.http.impl.client.DefaultHttpRequestRetryHandler;
import org.apache.http.impl.client.HttpClientBuilder;
import org.apache.http.impl.client.HttpClients;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.concurrent.TimeUnit;

/**
 * Created in 2022-05-30 15:44.
 *
 * <AUTHOR>
 */
public class HttpClientHelper {
    private static Logger logger = LoggerFactory.getLogger(HttpClientHelper.class);

    /**
     * 获取客户端构建器
     *
     * @return
     */
    public static HttpClientBuilder getHttpClientBuilder() {
        HttpClientBuilder httpClientBuilder = HttpClients.custom()
                // 设置连接池
                .setConnectionManager(ConnectionManagerHelper.getConnectionManager())
                // 设置连接配置
                .setDefaultRequestConfig(RequestConfigHelper.getRequestConfigBuilder().build())
                // 设置连接的最长存活时间
                .setConnectionTimeToLive(5, TimeUnit.MINUTES)
                .setRetryHandler(new DefaultHttpRequestRetryHandler(0, true));

        // 设置请求拦截器
        httpClientBuilder.addInterceptorLast(new DefaultRequestInterceptor());
        httpClientBuilder.addInterceptorLast(new DefaultResponseInterceptor());
        return httpClientBuilder;
    }
}