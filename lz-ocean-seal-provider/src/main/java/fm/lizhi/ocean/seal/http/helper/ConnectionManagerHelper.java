package fm.lizhi.ocean.seal.http.helper;

import org.apache.http.impl.conn.PoolingHttpClientConnectionManager;
import org.apache.http.pool.PoolStats;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Timer;
import java.util.TimerTask;
import java.util.concurrent.TimeUnit;

/**
 * Created in 2022-05-30 15:44.
 *
 * <AUTHOR>
 */
public class ConnectionManagerHelper {
    private static Logger logger = LoggerFactory.getLogger(ConnectionManagerHelper.class);

    /**
     * 获取默认的连接池
     *
     * @return
     */
    public static PoolingHttpClientConnectionManager getConnectionManager() {
        PoolingHttpClientConnectionManager connectionManager = new PoolingHttpClientConnectionManager();
        // 最大总连接数
        connectionManager.setMaxTotal(500);
        // 设置路由的最大连接数
        connectionManager.setDefaultMaxPerRoute(500);
        // 设置某个路由的最大连接数
        //connectionManager.setMaxPerRoute(new HttpRoute(new HttpHost("", 80)), 10);
        // 定义多长时间不活跃的连接，需要验证有效性
        connectionManager.setValidateAfterInactivity((int) TimeUnit.SECONDS.toMillis(3));

        new Timer().schedule(new TimerTask() {
            @Override
            public void run() {
                connectionManager.closeExpiredConnections();
                connectionManager.closeIdleConnections(30, TimeUnit.SECONDS);
                PoolStats stats = connectionManager.getTotalStats();
                //logger.info("http connection stats:{}", stats);
            }
        }, 0, 1000);
        return connectionManager;
    }
}