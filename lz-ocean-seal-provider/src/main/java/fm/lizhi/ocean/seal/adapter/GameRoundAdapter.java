package fm.lizhi.ocean.seal.adapter;

import fm.lizhi.ocean.seal.dao.bean.GameRoundBean;
import fm.lizhi.ocean.seal.dao.bean.GameRoundResultBean;
import fm.lizhi.ocean.seal.dto.GameStartDTO;
import fm.lizhi.ocean.seal.protocol.GameReportServiceProto;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;

/**
 * Created in 2022-05-16 16:25.
 *
 * <AUTHOR>
 */
@Mapper(componentModel = "default")
public interface GameRoundAdapter {
    /**
     * 转换对象
     *
     * @param result
     * @return
     */
    GameStartDTO convert(GameReportServiceProto.GameStartResult result);

    ///**
    // * 转换对象
    // *
    // * @param result
    // * @return
    // */
    //@Mapping(source = "roomId", target = "channelRoomId")
    //@Mapping(source = "gameMode", target = "channelGameMode")
    //@Mapping(source = "gameRoundId", target = "channelRoundId")
    //@Mapping(source = "gameStartAtTime", target = "startTime")
    //@Mapping(source = "reportGameInfoExtras", target = "extra")
    //@Mapping(source = "gameId", target = "channelGameId")
    //@Mapping(source = "roomId", target = "bizRoomId")
    //GameRoundBean convertBean(GameReportServiceProto.GameStartResult result);

    /**
     * 转换对象
     *
     * @param playerResult
     * @return
     */
    @Mapping(source = "uid", target = "userId")
    @Mapping(source = "realUser", target = "realUser", ignore = true)
    @Mapping(source = "escaped", target = "escaped", ignore = true)
    GameRoundResultBean convertRoundResultBean(GameReportServiceProto.GamePlayerSettleResult playerResult);
}