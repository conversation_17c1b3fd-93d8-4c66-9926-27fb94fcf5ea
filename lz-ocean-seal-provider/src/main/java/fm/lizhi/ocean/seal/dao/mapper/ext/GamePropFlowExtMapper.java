package fm.lizhi.ocean.seal.dao.mapper.ext;

import fm.lizhi.common.datastore.core.annotation.DataStore;
import fm.lizhi.ocean.seal.dao.bean.GamePropFlowBean;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import java.util.Date;
import java.util.List;

/**
 * 游戏道具流水扩展数据访问接口
 * <AUTHOR>
 */
@DataStore(namespace = "mysql_ocean_oceanseal")
public interface GamePropFlowExtMapper {

    /**
     * 根据唯一ID查询流水（用于幂等性检查）
     *
     * @param uniqueId 唯一ID
     * @param status
     * @return 流水信息
     */
    @Select("SELECT * FROM game_prop_flow WHERE unique_id = #{uniqueId} and grant_status = #{status}")
    GamePropFlowBean selectByUniqueId(@Param("uniqueId") String uniqueId, @Param("status") int status);

    /**
     * 根据条件查询流水列表
     * @param userId 用户ID
     * @param propId 道具ID
     * @param grantStatus 发放状态
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @param offset 偏移量
     * @param limit 限制数量
     * @return 流水列表
     */
    @Select("<script>" +
            "SELECT * FROM game_prop_flow WHERE 1=1 " +
            "<if test='userId != null'> AND user_id = #{userId} </if>" +
            "<if test='propId != null'> AND prop_id = #{propId} </if>" +
            "<if test='grantStatus != null'> AND grant_status = #{grantStatus} </if>" +
            "<if test='startTime != null'> AND create_time >= #{startTime} </if>" +
            "<if test='endTime != null'> AND create_time &lt;= #{endTime} </if>" +
            "ORDER BY create_time DESC " +
            "<if test='offset != null and limit != null'> LIMIT #{offset}, #{limit} </if>" +
            "</script>")
    List<GamePropFlowBean> selectByConditions(@Param("userId") Long userId,
                                             @Param("propId") Long propId,
                                             @Param("grantStatus") Integer grantStatus,
                                             @Param("startTime") Date startTime,
                                             @Param("endTime") Date endTime,
                                             @Param("offset") Integer offset,
                                             @Param("limit") Integer limit);

    /**
     * 根据条件统计流水数量
     * @param userId 用户ID
     * @param propId 道具ID
     * @param grantStatus 发放状态
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 流水数量
     */
    @Select("<script>" +
            "SELECT COUNT(*) FROM game_prop_flow WHERE 1=1 " +
            "<if test='userId != null'> AND user_id = #{userId} </if>" +
            "<if test='propId != null'> AND prop_id = #{propId} </if>" +
            "<if test='grantStatus != null'> AND grant_status = #{grantStatus} </if>" +
            "<if test='startTime != null'> AND create_time >= #{startTime} </if>" +
            "<if test='endTime != null'> AND create_time &lt;= #{endTime} </if>" +
            "</script>")
    int countByConditions(@Param("userId") Long userId,
                         @Param("propId") Long propId,
                         @Param("grantStatus") Integer grantStatus,
                         @Param("startTime") Date startTime,
                         @Param("endTime") Date endTime);

    /**
     * 更新流水状态
     * @param id 流水ID
     * @param grantStatus 发放状态
     * @return 影响行数
     */
    @Update("UPDATE game_prop_flow SET grant_status = #{grantStatus}, modify_time = NOW() WHERE id = #{id}")
    int updateGrantStatus(@Param("id") Long id, @Param("grantStatus") Integer grantStatus);

    /**
     * 根据用户ID和道具ID查询用户道具流水
     * @param userId 用户ID
     * @param propId 道具ID
     * @param grantStatus 发放状态（可选）
     * @return 流水列表
     */
    @Select("<script>" +
            "SELECT * FROM game_prop_flow " +
            "WHERE user_id = #{userId} AND prop_id = #{propId} " +
            "<if test='grantStatus != null'> AND grant_status = #{grantStatus} </if>" +
            "ORDER BY create_time DESC" +
            "</script>")
    List<GamePropFlowBean> selectByUserIdAndPropId(@Param("userId") Long userId,
                                                   @Param("propId") Long propId,
                                                   @Param("grantStatus") Integer grantStatus);
}
