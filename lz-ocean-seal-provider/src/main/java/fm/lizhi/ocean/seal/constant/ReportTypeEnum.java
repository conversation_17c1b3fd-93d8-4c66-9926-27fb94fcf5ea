package fm.lizhi.ocean.seal.constant;

public enum ReportTypeEnum {

    /**
     * 游戏开始
     */
    GAME_START("game_start"),
    /**
     * 游戏结算
     */
    GAME_SETTLE("game_settle"),
    /**
     * 房间用户人数变更通知
     */
    ROOM_USERS_CHANGED("room_users_changed"),
    /**
     * 消费业务服务端用户心跳
     */
    HEARTBEAT("heartbeat"),
    ;

    private final String type;

    ReportTypeEnum(String type) {
        this.type = type;
    }

    public String getType() {
        return type;
    }

    public static ReportTypeEnum from(String type) {
        for (ReportTypeEnum value : values()) {
            if (value.getType().equals(type)) {
                return value;
            }
        }
        return null;
    }
}
