package fm.lizhi.ocean.seal.kafka;

import com.google.inject.Inject;
import com.netflix.governator.annotations.AutoBindSingleton;
import fm.lizhi.common.kafka.common.SendResult;
import fm.lizhi.common.kafka.ioc.api.KafkaTemplate;
import fm.lizhi.ocean.seal.conf.LzConfig;
import fm.lizhi.ocean.seal.constant.KafkaTopicConstant;
import fm.lizhi.ocean.seal.constant.ReportTypeEnum;
import fm.lizhi.ocean.seal.dao.bean.GameAppBean;
import fm.lizhi.ocean.seal.manager.GameAppManager;
import fm.lizhi.ocean.seal.pojo.bo.sud.GameUserHeartBeat;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

/**
 * Created in 2022-01-28 10:22.
 *
 * <AUTHOR>
 */
@Slf4j
@AutoBindSingleton
public class ReportGameResultProducer {
    @Inject
    private KafkaTemplate kafkaTemplate;
    @Inject
    private GameAppManager gameAppManager;

    /**
     * 发送游戏开始消息
     */
    public boolean sendGameStartMsg(String appId, GameStartResult gameStartResult) {
        String topic = getTopic(appId, ReportTypeEnum.GAME_START);
        if (StringUtils.isEmpty(topic)) {
            // 默认成功
            return true;
        }
        log.info("sendGameStartMsg-topic: {}", topic);
        SendResult result = kafkaTemplate.send(topic, gameStartResult.getGameRoundId(), gameStartResult);
        if (result.isSuccess()) {
            return true;
        }
        log.error("sendGameStartMsg fail. error={},param={}", result.getCause(), gameStartResult.toString());
        return false;
    }

    /**
     * 发送游戏结束消息
     *
     * @param appId
     * @param gameSettleResult
     * @return
     */
    public boolean sendGameSettleMsg(String appId, GameSettleResult gameSettleResult) {
        String topic = getTopic(appId, ReportTypeEnum.GAME_SETTLE);

        if (StringUtils.isEmpty(topic)) {
            // 默认成功
            return true;
        }
        log.info("sendGameSettleMsg-topic={}`msg={} ", topic, gameSettleResult.toString());
        SendResult result = kafkaTemplate.send(topic, gameSettleResult.getGameRoundId(), gameSettleResult);
        if (result.isSuccess()) {
            return true;
        }
        log.error("sendGameSettleMsg fail. error={},param={}", result.getCause(), gameSettleResult.toString());
        return false;
    }

    /**
     * 房间用户人数变更通知
     */
    public boolean sendRoomUsersChangedMsg(String appId, RoomUsersChangedResult roomUsersChangedResult) {
        String topic = getTopic(appId, ReportTypeEnum.ROOM_USERS_CHANGED);
        if (StringUtils.isEmpty(topic)) {
            // 默认成功
            return true;
        }
        log.info("sendRoomUsersChangedMsg-topic: {}", topic);
        SendResult result = kafkaTemplate.send(topic, roomUsersChangedResult.getRoomId(), roomUsersChangedResult);
        if (result.isSuccess()) {
            return true;
        }
        log.error("sendRoomUsersChangedMsg fail. error={},param={}", result.getCause(), roomUsersChangedResult.toString());
        return false;
    }


    /**
     * 发送用户心跳（demo）
     * 不要调用
     */
    public boolean sendUserHeartMsg(GameUserHeartBeat heartBeat) {
        String topic = KafkaTopicConstant.TOPIC_HEARTBEAT_PP;
        if (StringUtils.isEmpty(topic)) {
            // 默认成功
            return true;
        }
        log.info("sendUserHeartMsg-topic: {}", topic);
        SendResult result = kafkaTemplate.send(topic, heartBeat.getUserId().toString(), heartBeat);
        if (result.isSuccess()) {
            return true;
        }
        log.error("sendUserHeartMsg fail. error={},param={}", result.getCause(), heartBeat.toString());
        return false;
    }

    private String getTopic(String appId, ReportTypeEnum reportTypeEnum) {
        GameAppBean gameAppBeanCache = gameAppManager.getGameAppBeanCache(appId);
        if (gameAppBeanCache == null || StringUtils.isEmpty(gameAppBeanCache.getAppTopic())) {
            log.warn("appId: {} not set kafka topic", appId);
            return null;
        }
        return gameAppBeanCache.getAppTopic() + "_" + reportTypeEnum.getType();
    }

}
