package fm.lizhi.ocean.seal.api.impl;


import com.google.inject.Inject;
import fm.lizhi.commons.config.util.JsonUtil;
import fm.lizhi.commons.service.client.annotation.ServiceProvider;
import fm.lizhi.commons.service.client.log.LogContext;
import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.ocean.seal.api.GameLanguageService;
import fm.lizhi.ocean.seal.dao.bean.GameLanguageBean;
import fm.lizhi.ocean.seal.manager.GameLanguageManager;
import fm.lizhi.ocean.seal.protocol.GameLanguageServiceProto.GameLanguage;
import fm.lizhi.ocean.seal.protocol.GameLanguageServiceProto.ResponseGetAllLanguages;
import lombok.extern.slf4j.Slf4j;

import java.util.List;
import java.util.stream.Collectors;

@Slf4j
@ServiceProvider
public class GameLanguageServiceImpl implements GameLanguageService {
    @Inject
    private GameLanguageManager gameLanguageManager;


    @Override
    public Result<ResponseGetAllLanguages> getAllLanguages() {
        List<GameLanguageBean> gameLanguageBeans = gameLanguageManager.getGameLanguages();
        if(gameLanguageBeans == null){
            return new Result<>(GET_ALL_LANGUAGES_FAIL, null);
        }

        LogContext.addResLog("bean={}", JsonUtil.dumps(gameLanguageBeans));
        return new Result<>(GET_ALL_LANGUAGES_SUCCESS
                , ResponseGetAllLanguages.newBuilder().addAllGameLanguages(
                gameLanguageBeans.stream().map(x-> GameLanguage.newBuilder().setId(x.getId())
                        .setLanguageName(x.getName()).setAbbreviation(x.getAbbreviation()).build()).collect(Collectors.toList())
        ).build());
    }
}
