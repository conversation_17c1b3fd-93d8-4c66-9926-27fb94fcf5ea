package fm.lizhi.ocean.seal.gamechannel;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.google.common.base.Strings;
import com.google.inject.Inject;
import com.netflix.governator.annotations.AutoBindSingleton;
import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.game.auth.SealAuth;
import fm.lizhi.ocean.seal.conf.LzConfig;
import fm.lizhi.ocean.seal.constant.ChannelType;
import fm.lizhi.ocean.seal.dao.bean.GameAppBean;
import fm.lizhi.ocean.seal.dao.bean.GameBizGameBean;
import fm.lizhi.ocean.seal.dao.bean.GameChannelBean;
import fm.lizhi.ocean.seal.manager.BizGameManager;
import fm.lizhi.ocean.seal.manager.GameAppManager;
import fm.lizhi.ocean.seal.manager.GameChannelManager;
import fm.lizhi.ocean.seal.pojo.AuthInfo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.util.CollectionUtils;
import tech.sud.mgp.auth.api.SudMGPAuth;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * Created in 2022-03-22 10:48.
 *
 * <AUTHOR>
 */
@Slf4j
@AutoBindSingleton
public class SudAuthorService {
    @Inject
    private GameAppManager gameAppManager;

    /**
     * 获取游戏信息
     *
     * @param appId
     * @return
     */
    public SealAuth getSudAuthInfo(String appId) {
        return gameAppManager.createLiZhiSealAuth(appId, ChannelType.SUD.getName());
    }

    /**
     * 根据AppId返回sud鉴权sdk
     *
     * @param appId
     * @return
     */
    public SudMGPAuth getSudMGPAuth(String appId) {
        // 获取对应app的配置
        SealAuth authInfo = getSudAuthInfo(appId);
        if(authInfo == null){
            return null;
        }
        // 返回鉴权类
        return new SudMGPAuth(authInfo.getAppId(), authInfo.getAppSecret());
    }
}
