package fm.lizhi.ocean.seal.api.impl;

import com.google.inject.Inject;
import com.googlecode.protobuf.format.JsonFormat;
import fm.lizhi.commons.service.client.annotation.ServiceProvider;
import fm.lizhi.commons.service.client.log.LogContext;
import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.commons.service.client.rcode.GeneralRCode;
import fm.lizhi.ocean.seal.api.GameProxyService;
import fm.lizhi.ocean.seal.dao.bean.GameAppBean;
import fm.lizhi.ocean.seal.dao.bean.GameBizGameBean;
import fm.lizhi.ocean.seal.dao.bean.GameBizGameRelationBean;
import fm.lizhi.ocean.seal.dao.bean.GameChannelBean;
import fm.lizhi.ocean.seal.manager.BizGameManager;
import fm.lizhi.ocean.seal.manager.GameAppManager;
import fm.lizhi.ocean.seal.manager.GameChannelManager;
import fm.lizhi.ocean.seal.manager.GameProxyManager;
import fm.lizhi.ocean.seal.protocol.GameProxyServiceProto.GetAppIdAppSecretParam;
import fm.lizhi.ocean.seal.protocol.GameProxyServiceProto.InvokeTargetParams;
import fm.lizhi.ocean.seal.protocol.GameProxyServiceProto.ResponseInvokeTarget;
import fm.lizhi.ocean.seal.protocol.GameProxyServiceProto.ResponseGetAppIdAppSecretByParam;
import org.apache.commons.lang3.math.NumberUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * 游戏代理服务
 * <p>
 * Created in 2022-06-23 11:12.
 *
 * <AUTHOR>
 */
@ServiceProvider
public class GameProxyServiceImpl implements GameProxyService {
    private static final Logger logger = LoggerFactory.getLogger(GameProxyServiceImpl.class);
    @Inject
    private GameProxyManager gameProxyManager;
    @Inject
    private GameAppManager gameAppManager;
    @Inject
    private GameChannelManager gameChannelManager;
    @Inject
    private BizGameManager bizGameManager;

    /**
     * 获取App回调配置信息
     *
     * @param param 参数
     * @return //if rcode == 1 参数非法<br>
     * //if rcode == 2 映射不存在<br>
     * //if rcode == 3 目标服务错误<br>
     * //if rcode == 4 内部错误<br>
     */
    @Override
    public Result<ResponseInvokeTarget> invokeTarget(InvokeTargetParams param) {
        String jsonParams = JsonFormat.printToString(param);
        logger.info("Call target interface, params:{}", jsonParams);
        try {
            ResponseInvokeTarget result = gameProxyManager.invokeTarget(param);
            return new Result<>(GeneralRCode.GENERAL_RCODE_SUCCESS, result);
        } catch (Exception e) {
            logger.error("Call target interface error, params:{}", param, e);
            return new Result<>(INVOKE_TARGET_ERROR, null);
        }
    }

    @Override
    public Result<ResponseGetAppIdAppSecretByParam> getAppIdAppSecretByParam(GetAppIdAppSecretParam param) {
        LogContext.addReqLog("appId={}`gameId={}`type={}", param.getAppId(), param.getGameId(), param.getType());
        LogContext.addResLog("appId={}`gameId={}`type={}", param.getAppId(), param.getGameId(), param.getType());

        boolean isGetBiz = isGetBizAppIdAppSecret(param.getType());
        ResponseGetAppIdAppSecretByParam.Builder resp = ResponseGetAppIdAppSecretByParam.newBuilder();
        // 获取业务的 key 6518293  secret 6518293 传入的appId为业务appId。
        if (isGetBiz) {
            GameAppBean gameAppBean = gameAppManager.getGameAppBean(param.getAppId());
            if(null == gameAppBean){
                throw new IllegalArgumentException("get gameApp not exist.`appId={" + param.getAppId() + "}`gameId={" + param.getGameId() + "}");
            }
            resp.setAppId(gameAppBean.getAppId()).setAppSecret(gameAppBean.getAppSecret());
        }else {
            // 获取游戏渠道的 key 30129761 secret 30129761 按照之前的场景传入的appId还是为业务的appId，但是gameId为渠道gameId。
            // 所以可以根据渠道游戏id与业务appId获取对应的渠道
            GameBizGameBean gameBizGameBean = bizGameManager.getGame(param.getAppId(), param.getGameId());
            if(null == gameBizGameBean){
                throw new IllegalArgumentException("biz game not exist.`appId={" + param.getAppId() + "}`gameId={" + param.getGameId() + "}");
            }

            GameChannelBean gameChannelBean = gameChannelManager.getGameChannel(gameBizGameBean.getChannelId());
            if(null == gameChannelBean){
                throw new IllegalArgumentException("channel not exist.`appId={" + param.getAppId() + "}`gameId={" + param.getGameId() + "}");
            }
            resp.setAppId(gameChannelBean.getAppId()).setAppSecret(gameChannelBean.getAppSecret());
        }
        LogContext.addResLog("appId={}`appSecret={}", resp.getAppId(), resp.getAppSecret());
        return new Result<>(GeneralRCode.GENERAL_RCODE_SUCCESS, resp.build());
    }

    private boolean isGetBizAppIdAppSecret(int type){
        return NumberUtils.INTEGER_ONE.intValue() == type;
    }


}