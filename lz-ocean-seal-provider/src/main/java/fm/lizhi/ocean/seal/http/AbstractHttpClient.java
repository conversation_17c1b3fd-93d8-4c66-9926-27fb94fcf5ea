package fm.lizhi.ocean.seal.http;

import fm.lizhi.ocean.seal.http.utils.ContextUtils;
import fm.lizhi.ocean.seal.http.utils.QueryStringUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.HttpStatus;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.client.methods.HttpUriRequest;
import org.apache.http.entity.ByteArrayEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.util.EntityUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.util.HashMap;
import java.util.Map;

/**
 * Created in 2022-05-30 15:44.
 *
 * <AUTHOR>
 */
public abstract class AbstractHttpClient {
    private static Logger logger = LoggerFactory.getLogger(AbstractHttpClient.class);
    private CloseableHttpClient httpClient;

    public AbstractHttpClient(CloseableHttpClient httpClient) {
        this.httpClient = httpClient;
    }

    /**
     * 发送Http请求并返回一个结果
     *
     * @param request http请求
     * @return
     * @throws IOException
     */
    public CloseableHttpResponse execute(HttpUriRequest request) throws IOException {
        return this.httpClient.execute(request);
    }

    /**
     * 发送GET请求并获取响应数据
     *
     * @param url     请求地址
     * @param params  参数，可以传null
     * @param headers 请求头，可以传null
     * @return
     */
    public byte[] get(String url, Map<String, Object> params, Map<String, String> headers) throws IOException {
        if (params != null && !params.isEmpty()) {
            String queryString = QueryStringUtils.toQueryString(params);
            url += queryString;
        }
        HttpGet get = new HttpGet(url);
        if (headers != null && !headers.isEmpty()) {
            for (Map.Entry<String, String> header : headers.entrySet()) {
                get.addHeader(header.getKey(), header.getValue());
            }
        }
        CloseableHttpResponse response = this.execute(get);
        if (response.getStatusLine().getStatusCode() != HttpStatus.SC_OK) {
            logger.error("http get error, status:{}, url:{}, params:{}, headers:{}", response.getStatusLine(), url, params, headers);
            throw new RuntimeException("http get error, status:" + response.getStatusLine() + ", url:" + url + ", params:" + params + ", headers:" + headers);
        }
        return EntityUtils.toByteArray(response.getEntity());
    }

    /**
     * 发送GET请求并获取响应数据
     *
     * @param uri 请求地址
     * @return
     */
    public byte[] get(String uri) throws IOException {
        return this.get(uri, null, null);
    }

    /**
     * 发送GET请求并获取响应数据
     *
     * @param uri    请求地址
     * @param params 参数
     * @return
     */
    public String getString(String uri, Map<String, Object> params) throws IOException {
        byte[] bytes = this.get(uri, params, null);
        return new String(bytes, StandardCharsets.UTF_8);
    }

    /**
     * 发送GET请求并获取响应数据
     *
     * @param uri 请求地址
     * @return
     */
    public String getString(String uri) throws IOException {
        return this.getString(uri, null);
    }

    /**
     * 发送POST请求
     *
     * @param uri     请求地址
     * @param params  请求参数
     * @param headers 请求头
     * @param entity  实体内容
     * @return
     */
    public byte[] post(String uri, Map<String, Object> params, Map<String, String> headers, byte[] entity) throws IOException {
        String queryString = QueryStringUtils.toQueryString(params);
        if(StringUtils.isNotBlank(queryString)){
            uri += "?" + queryString;
        }

        HttpPost post = new HttpPost(uri);

        // 设置头信息
        if (headers != null) {
            for (Map.Entry<String, String> entry : headers.entrySet()) {
                post.addHeader(entry.getKey(), entry.getValue());
            }
        } else {
            headers = new HashMap<>();
        }
        // 灯塔路由
        headers.put("X-Real-IP", ContextUtils.getUserIpFromContext());

        // 设置实体数据
        if (entity != null) {
            ByteArrayEntity arrayEntity = new ByteArrayEntity(entity);
            arrayEntity.setContentType("UTF-8");
            post.setEntity(arrayEntity);
        }

        // 发送请求
        CloseableHttpResponse response = this.execute(post);
        if (response.getStatusLine().getStatusCode() != HttpStatus.SC_OK) {
            throw new RuntimeException("http post error, status:" + response.getStatusLine() + ", uri:" + uri + ", params:" + params);
        }
        return EntityUtils.toByteArray(response.getEntity());
    }

    /**
     * 发送POST请求
     *
     * @param uri     请求地址
     * @param params  请求参数
     * @param headers 请求头
     * @param entity  实体内容
     * @return
     */
    public String postString(String uri, Map<String, Object> params, Map<String, String> headers, String entity) throws IOException {
        byte[] result = this.post(uri, params, headers, entity.getBytes());
        return new String(result, StandardCharsets.UTF_8);
    }


    /**
     * 发送POST请求
     *
     * @param uri     请求地址
     * @param params  请求参数
     * @param headers 请求头
     * @return
     */
    public String postString(String uri, Map<String, Object> params, Map<String, String> headers) throws IOException {
        byte[] result = this.post(uri, params, headers, null);
        return new String(result, StandardCharsets.UTF_8);
    }
}