package fm.lizhi.ocean.seal.http.utils;

import com.google.common.base.Splitter;
import fm.lizhi.biz.data.collector.DataCtxContainer;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * Created in 2022-06-23 16:22.
 *
 * <AUTHOR>
 */
public class ContextUtils {
    private static final Logger logger = LoggerFactory.getLogger(ContextUtils.class);

    /**
     * 获取用户IP，用于灯塔路由
     *
     * @return
     */
    public static String getUserIpFromContext() {
        String userIp = null;
        if (DataCtxContainer.get() != null && DataCtxContainer.get().getAppHeadCtx() != null) {
            userIp = DataCtxContainer.get().getAppHeadCtx().getIp();
            logger.info("get userIp from AppHeadCtx!userIp:[{}]", userIp);
        } else if (DataCtxContainer.get() != null && DataCtxContainer.get().getHttpCtx() != null) {
            userIp = DataCtxContainer.get().getHttpCtx().getIp();
            logger.info("get userIp from HttpCtx!userIp:[{}]", userIp);
        }
        if (StringUtils.isNotEmpty(userIp) && userIp.contains(":")) {
            userIp = Splitter.on(":").trimResults().omitEmptyStrings().splitToList(userIp).get(0);
        }
        return userIp;
    }
}