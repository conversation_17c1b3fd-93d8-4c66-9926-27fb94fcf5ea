package fm.lizhi.ocean.seal.kafka;

import com.google.inject.Inject;
import com.netflix.governator.annotations.AutoBindSingleton;
import fm.lizhi.commons.queue.callback.MsgFuture;
import fm.lizhi.commons.queue.service.Consumer;
import fm.lizhi.ocean.seal.conf.LzConfig;
import fm.lizhi.ocean.seal.constant.CommonConstant;
import fm.lizhi.ocean.seal.constant.KafkaTopicConstant;
import fm.lizhi.ocean.seal.manager.GameBillManager;
import lombok.extern.slf4j.Slf4j;

import javax.annotation.PostConstruct;

/**
 * 消费业务服务端发送的用户心跳
 *
 * <AUTHOR>
 * @date 2025/4/21 下午2:38
 * @description
 */
@Slf4j
@AutoBindSingleton
public class PPUserHeartConsumer {
    @Inject
    private LzConfig lzConfig;
    @Inject
    private GameBillManager gameBillManager;

    @PostConstruct
    public void init() {

        try {
            new Consumer(lzConfig.isProduct(),
                    lzConfig.getKafkaConfigKey(),
                    lzConfig.getKafkaClusterNamespace(),
                    CommonConstant.GROUP_LZ_OCEAN_SEAL,
                    KafkaTopicConstant.TOPIC_HEARTBEAT_PP, 1, 3,
                    new MsgFuture() {
                        @Override
                        public boolean msgReceived(String key, String msg, long timestamp, int partition, long offset) {
                            log.info("PPUserHeartConsumer msg={}", msg);
                            try {
                                return gameBillManager.insertOrUpdate(msg);
                            } catch (Exception e) {
                                log.error("PPUserHeartConsumer error msg={}", msg, e);
                            }
                            return true;
                        }
                    }).start();
        } catch (Exception e) {
            log.error(String.format("[PPUserHeartConsumer-exception] throws exception=%s", e.getMessage()), e);
        }
    }
}
