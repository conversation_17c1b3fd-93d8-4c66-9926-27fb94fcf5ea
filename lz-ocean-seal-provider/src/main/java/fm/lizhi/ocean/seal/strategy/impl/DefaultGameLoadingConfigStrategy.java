package fm.lizhi.ocean.seal.strategy.impl;

import com.netflix.governator.annotations.AutoBindSingleton;
import fm.lizhi.ocean.seal.dao.bean.GameBizGameBean;
import fm.lizhi.ocean.seal.dao.bean.GameInfoBean;
import fm.lizhi.ocean.seal.protocol.GameServiceProto;
import fm.lizhi.ocean.seal.strategy.GameLoadingConfigStrategy;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

/**
 * 默认游戏加载配置策略实现
 * 用于处理未知或不支持的渠道类型
 * 
 * Created in 2025-06-27
 * 
 * <AUTHOR> Agent
 */
@Slf4j
@AutoBindSingleton
public class DefaultGameLoadingConfigStrategy implements GameLoadingConfigStrategy {

    @Override
    public GameServiceProto.GameLoadingConfig getGameLoadingConfig(GameBizGameBean bizGameBean, GameInfoBean gameInfoBean, String appId) {
        try {
            log.info("Generating default game loading config for gameId: {}, channelGameId: {}, channel: {}", 
                    bizGameBean.getId(), gameInfoBean.getChannelGameIdStr(), gameInfoBean.getChannel());
            
            // 默认的游戏加载配置逻辑
            String url = generateDefaultGameUrl(gameInfoBean);
            String gZipUrl = generateDefaultGZipUrl(gameInfoBean);
            
            return GameServiceProto.GameLoadingConfig.newBuilder()
                    .setGameId(gameInfoBean.getChannelGameId())
                    .setUrl(url)
                    .setGZipUrl(gZipUrl)
                    .build();
                    
        } catch (Exception e) {
            log.error("Error generating default game loading config for gameId: {}", bizGameBean.getId(), e);
            return null;
        }
    }

    @Override
    public boolean supports(String channel) {
        // 默认策略支持所有渠道，但优先级最低
        return true;
    }
    
    /**
     * 生成默认的游戏 URL
     * 
     * @param gameInfoBean 游戏信息
     * @return 游戏 URL
     */
    private String generateDefaultGameUrl(GameInfoBean gameInfoBean) {
        return "";
    }
    
    /**
     * 生成默认的游戏压缩包 URL
     * 
     * @param gameInfoBean 游戏信息
     * @return 压缩包 URL
     */
    private String generateDefaultGZipUrl(GameInfoBean gameInfoBean) {
        return "";
    }
}
