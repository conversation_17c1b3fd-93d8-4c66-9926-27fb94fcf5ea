package fm.lizhi.ocean.seal.http.interceptors;

import org.apache.http.HttpException;
import org.apache.http.HttpRequest;
import org.apache.http.HttpRequestInterceptor;
import org.apache.http.client.protocol.HttpClientContext;
import org.apache.http.protocol.HttpContext;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;

/**
 * Created in 2022-05-30 15:44.
 *
 * <AUTHOR>
 */
public class DefaultRequestInterceptor implements HttpRequestInterceptor {
    private static Logger logger = LoggerFactory.getLogger(DefaultRequestInterceptor.class);

    /**
     * Processes a request.
     * On the client side, this step is performed before the request is
     * sent to the server. On the server side, this step is performed
     * on incoming messages before the message body is evaluated.
     *
     * @param request the request to preprocess
     * @param context the context for the request
     * @throws HttpException in case of an HTTP protocol violation
     * @throws IOException   in case of an I/O error
     */
    @Override
    public void process(HttpRequest request, HttpContext context) throws HttpException, IOException {
        HttpClientContext clientContext = (HttpClientContext) context;
        clientContext.setAttribute("interceptor.current_time_millis", System.currentTimeMillis());
    }
}