 // FIXME 2025-02-27 精简暂时用不到的代码

package fm.lizhi.ocean.seal.cronjob;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.base.Strings;
import com.google.inject.Inject;
import com.netflix.governator.annotations.AutoBindSingleton;
import fm.lizhi.common.datastore.redis.client.RedisClient;
import fm.lizhi.ocean.seal.conf.DataStoreConfig;
import fm.lizhi.ocean.seal.conf.LzConfig;
import fm.lizhi.ocean.seal.constant.ReportTypeEnum;
import fm.lizhi.ocean.seal.kafka.GameSettleResult;
import fm.lizhi.ocean.seal.kafka.GameStartResult;
import fm.lizhi.ocean.seal.kafka.ReportGameResultProducer;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.concurrent.BasicThreadFactory;
import redis.clients.jedis.JedisPubSub;

import javax.annotation.PostConstruct;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.ScheduledThreadPoolExecutor;
import java.util.concurrent.ThreadLocalRandom;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.locks.LockSupport;

/**
 * Created in 2022-02-24 09:47.
 *
 * <AUTHOR>
 */
@Slf4j
@AutoBindSingleton
public class DispatchGameReportJob {
    @Inject
    @com.google.inject.name.Named(DataStoreConfig.REDIS_NAME_SPACE)
    private RedisClient redisClient;
    @Inject
    private LzConfig lzConfig;
    @Inject
    private ReportGameResultProducer reportGameResultProducer;

    @PostConstruct
    private void init() {
        // 是否开启任务执行游戏数据分发（从redis获取数据，再推送到消息队列。由于线上回调域名只有一个，需要做线上和预发的分发处理）
        if (lzConfig.getDispatchGameReportSwitch() != 1) {
            return;
        }
        // 分发线程池
        ScheduledExecutorService scheduler = new ScheduledThreadPoolExecutor(1,
                new BasicThreadFactory.Builder().namingPattern("getAndSendGameReport-schedule-pool-%d").daemon(true).build());
        scheduler.schedule(new Runnable() {
            @Override
            public void run() {
                // 获取redis里的列表数据，进行消息队列分发
                log.info("DispatchGameReportJob start");
//                if (EnvUtils.isOffice()) {
//                    subscribeGameReportAndSend();
//                } else {
                    getAndSendGameReport();
//                }
                log.info("DispatchGameReportJob end");
            }
        }, 5, TimeUnit.SECONDS);
    }

    /**
     * 过期时间
     */
    private static final int EXPIRE_TIME_SECOND = 3 * 24 * 60 * 60;
    /**
     * 随机时间
     */
    private static final int RANDOM_EXPIRE_TIME = 10000;
    /**
     * 游戏消息上报渠道
     */
    private static final String GAME_REPORT_CHANNEL = "game_report_channel";

    /**
     * 是否需要做数据分发
     *
     * @param env
     * @return
     */
    public boolean checkNeedDispatchMsg(int env) {
        return env == lzConfig.getDispatchGameReportSwitchEnv();
    }

    /**
     * 设置游戏当局数据上报的环境
     *
     * @param gameRoundId
     * @param env
     * @return
     */
    public boolean setGameReportDataEnv(String gameRoundId, int env) {
        String key = genReportDataEnvKey(gameRoundId);
        int expireSecond = EXPIRE_TIME_SECOND + ThreadLocalRandom.current().nextInt(RANDOM_EXPIRE_TIME);
        String result = redisClient.setex(key, expireSecond, env + "");
        return Objects.equals("OK", result);
    }

    /**
     * 获取游戏当局数据上报的环境
     *
     * @param gameRoundId
     * @return
     */
    public String getGameReportDataEnv(String gameRoundId) {
        String key = genReportDataEnvKey(gameRoundId);
        return redisClient.get(key);
    }

    /**
     * 写入游戏开始数据到列表
     *
     * @param message
     * @return
     */
    private boolean addGameStartResultToList(String message) {
        String key = GameReportKey.GAME_REPORT_DATA.getKey();
        Long newListLen = redisClient.rpush(key, message);
        return null != newListLen;
    }

    /**
     * 转发游戏开始数据
     *
     * @param appName
     * @param data
     * @return
     */
    public boolean dispatchGameStartResult(String appName, GameStartResult data) {
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("type", ReportTypeEnum.GAME_START.getType());
        jsonObject.put("appName", appName);
        jsonObject.put("data", data);
        String message = jsonObject.toJSONString();
        // 灯塔环境使用redis的pub/subscribe模式
        /*
        if (EnvUtils.isOffice()) {
            publishGameReport(message);
            return true;
        }
         */
        // 其他使用redis的阻塞列表模式
        return addGameStartResultToList(message);
    }

    /**
     * 发送游戏消息到redis的订阅者
     *
     * @param message
     * @return
     */
    private boolean publishGameReport(String message) {
        Long publish = redisClient.publish(GAME_REPORT_CHANNEL, message);
        log.info("publish message publishCount={}`message={}", publish, message);
        return publish != null;
    }

    /**
     * 转发游戏结束数据
     *
     * @param appName
     * @param data
     * @return
     */
    public boolean dispatchGameSettleResult(String appName, GameSettleResult data) {
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("type", ReportTypeEnum.GAME_SETTLE.getType());
        jsonObject.put("appName", appName);
        jsonObject.put("data", data);
        String message = jsonObject.toJSONString();
        // 灯塔环境使用redis的pub/subscribe模式
        /*
        if (EnvUtils.isOffice()) {
            publishGameReport(message);
            return true;
        }
         */
        // 其他使用redis的阻塞列表模式
        return addGameSettleResultToList(message);
    }

    /**
     * 写入游戏结束数据到列表
     *
     * @param message
     * @return
     */
    private boolean addGameSettleResultToList(String message) {
        String key = GameReportKey.GAME_REPORT_DATA.getKey();
        Long newListLen = redisClient.rpush(key, message);
        return null != newListLen;
    }

    /**
     * 订阅并发送游戏数据
     */
    private void subscribeGameReportAndSend() {
        log.info("DispatchGameReportJob start subscribeGameReportAndSend");
        redisClient.subscribe(new JedisPubSub() {
            @Override
            public void onMessage(String channel, String message) {
                super.onMessage(channel, message);
                try {
                    sendGameReportMessage(message);
                } catch (Exception e) {
                    log.error("game report channel subscribe error", e);
                }
            }
        }, GAME_REPORT_CHANNEL);
    }

    /**
     * 获取并发送游戏数据
     */
    private void getAndSendGameReport() {
        log.info("DispatchGameReportJob start getAndSendGameReport");
        String key = GameReportKey.GAME_REPORT_DATA.getKey();
        while (true) {
            try {
                List<String> gameDataList = redisClient.blpop(60, key);
                if (gameDataList == null || gameDataList.isEmpty()) {
                    continue;
                }
                String data = gameDataList.get(1);
                sendGameReportMessage(data);
            } catch (Exception e) {
                log.error("getAndSendGameReport task error", e);
                LockSupport.parkNanos(1000000000L);
            }
        }
    }

    /**
     * 发送Redis里的数据到消息队列
     *
     * @param data
     */
    private void sendGameReportMessage(String data) {
        if (Strings.isNullOrEmpty(data)) {
            log.warn("sendGameReportMessage data is null");
            return;
        }
        JSONObject jsonObject = JSON.parseObject(data);
        String type = jsonObject.getString("type");
        ReportTypeEnum reportTypeEnum = ReportTypeEnum.from(type);
        if (reportTypeEnum == null) {
            return;
        }
        String appName = jsonObject.getString("appName");
        String gameReportData = jsonObject.getString("data");
        if (reportTypeEnum == ReportTypeEnum.GAME_START) {
            boolean sendGameStartMsg = reportGameResultProducer.sendGameStartMsg(appName, JSON.parseObject(gameReportData, GameStartResult.class));
            log.info("sendGameReportMessage.sendGameStartMsg result={}`data={}", sendGameStartMsg, data);
        } else if (reportTypeEnum == ReportTypeEnum.GAME_SETTLE) {
            boolean sendGameSettleMsg = reportGameResultProducer.sendGameSettleMsg(appName, JSON.parseObject(gameReportData, GameSettleResult.class));
            log.info("sendGameReportMessage.sendGameSettleMsg result={}`data={}", sendGameSettleMsg, data);
        }
    }

    /**
     * 生成游戏当局数据上报的key
     *
     * @param gameRoundId
     * @return
     */
    private String genReportDataEnvKey(String gameRoundId) {
        return GameReportKey.GAME_REPORT_ENV.getKey(gameRoundId);
    }
}
