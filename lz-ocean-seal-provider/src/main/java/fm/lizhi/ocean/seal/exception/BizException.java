package fm.lizhi.ocean.seal.exception;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * 业务异常
 * <p>
 * Created in 2022-04-27 18:23.
 *
 * <AUTHOR>
 */
public class BizException extends RuntimeException {
    private static final Logger logger = LoggerFactory.getLogger(BizException.class);

    /**
     * Constructs a new runtime exception with the specified detail message.
     * The cause is not initialized, and may subsequently be initialized by a
     * call to {@link #initCause}.
     *
     * @param message the detail message. The detail message is saved for
     *                later retrieval by the {@link #getMessage()} method.
     */
    public BizException(String message) {
        super(message);
    }
}