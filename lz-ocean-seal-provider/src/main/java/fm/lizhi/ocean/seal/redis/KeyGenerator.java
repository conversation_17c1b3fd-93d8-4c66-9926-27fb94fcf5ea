package fm.lizhi.ocean.seal.redis;


public class KeyGenerator {

    public interface CacheKeyType {
        /**
         * 获取前缀
         *
         * @return
         */
        String getPrefix();

        /**
         * 获取key
         *
         * @param args 拼在key后面，_隔开
         * @return
         */
        default String getKey(Object... args) {
            StringBuilder sb = new StringBuilder(getPrefix());
            sb.append("_").append(this);
            for (Object o : args) {
                if (o != null) {
                    sb.append("_").append(o);
                }
            }
            return sb.toString();
        }
    }
}
