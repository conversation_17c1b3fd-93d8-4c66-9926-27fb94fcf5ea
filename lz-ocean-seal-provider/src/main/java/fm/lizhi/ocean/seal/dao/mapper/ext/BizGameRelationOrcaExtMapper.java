package fm.lizhi.ocean.seal.dao.mapper.ext;

import fm.lizhi.common.datastore.core.annotation.DataStore;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

@DataStore(namespace = "mysql_ocean_oceanseal")
public interface BizGameRelationOrcaExtMapper {

    @Select("select re.*,ga.app_name,gi.`name`,gc.channel from (select gb.id, gb.game_app_id,gb.game_info_id,gb.channel_id,gb.config,gbr.app_id,gbr.channel_game_id from game_biz_game gb,game_biz_game_relation gbr where gb.id = #{id} and gb.id = gbr.id) re, game_app ga, game_info gi, game_channel gc where re.game_app_id = ga.id and re.game_info_id = gi.id and re.channel_id = gc.id")
    BizGameRelationOrcaResult getBizGameRelationById(@Param("id") Long id);

    @Select("<script>\n" +
                "select re.*,ga.app_name,gi.`name`,gc.channel from (select gb.id, gb.game_app_id,gb.game_info_id,gb.channel_id,gb.config,gbr.app_id,gbr.channel_game_id from game_biz_game gb,game_biz_game_relation gbr where gb.id = gbr.id " +
                "    <if test=\"id != null and id != 0 \">\n" +
                "      and gb.id = #{id}\n" +
                "    </if>\n" +
                "    <if test=\"gameAppId != null and gameAppId != 0 \">\n" +
                "      and gb.game_app_id = #{gameAppId}\n" +
                "    </if>\n" +
                "    <if test=\"gameInfoId != null and gameInfoId != 0 \">\n" +
                "      and gb.game_info_id = #{gameInfoId}\n" +
                "    </if>\n" +
                "    <if test=\"channelId != null and channelId != 0 \">\n" +
                "      and gb.channel_id = #{channelId}\n" +
                "    </if>\n" +
                " order by gb.modify_time limit #{offset},#{pageSize}) re, game_app ga, game_info gi, game_channel gc where re.game_app_id = ga.id and re.game_info_id = gi.id and re.channel_id = gc.id" +
            "</script>")
    List<BizGameRelationOrcaResult> getBizGameRelationByIds(
            @Param("id") Long id, @Param("gameAppId") Long gameAppId, @Param("gameInfoId") Long gameInfoId
            , @Param("channelId") Long channelId, @Param("offset") int offset, @Param("pageSize") int pageSize);

    @Select("<script>\n" +
            "select count(1) from game_biz_game where 1 = 1 " +
            "    <if test=\"id != null and id != 0 \">\n" +
            "      and id = #{id}\n" +
            "    </if>\n" +
            "    <if test=\"gameAppId != null and gameAppId != 0 \">\n" +
            "      and game_app_id = #{gameAppId}\n" +
            "    </if>\n" +
            "    <if test=\"gameInfoId != null and gameInfoId != 0 \">\n" +
            "      and game_info_id = #{gameInfoId}\n" +
            "    </if>\n" +
            "    <if test=\"channelId != null and channelId != 0 \">\n" +
            "      and channel_id = #{channelId}\n" +
            "    </if>\n" +
            "</script>")
    int countBizGameRelationByIds(@Param("id") Long id, @Param("gameAppId") Long gameAppId, @Param("gameInfoId") Long gameInfoId, @Param("channelId") Long channelId);
}
