package fm.lizhi.ocean.seal.strategy.impl;

import cn.hutool.core.collection.CollUtil;
import com.google.inject.Inject;
import com.netflix.governator.annotations.AutoBindSingleton;
import fm.lizhi.commons.config.util.JsonUtil;
import fm.lizhi.ocean.seal.constant.GameChannel;
import fm.lizhi.ocean.seal.dao.bean.GameBizGameBean;
import fm.lizhi.ocean.seal.dao.bean.GameChannelBean;
import fm.lizhi.ocean.seal.dao.bean.GameInfoBean;
import fm.lizhi.ocean.seal.dao.bean.GamePropBean;
import fm.lizhi.ocean.seal.manager.BizGameManager;
import fm.lizhi.ocean.seal.manager.GameChannelManager;
import fm.lizhi.ocean.seal.manager.GameInfoManager;
import fm.lizhi.ocean.seal.manager.LukManger;
import fm.lizhi.ocean.seal.pojo.bo.luk.GrantPropDetail;
import fm.lizhi.ocean.seal.constant.LukControlEventType;
import fm.lizhi.ocean.seal.protocol.GamePropServiceProto.GrantGamePropParam;
import fm.lizhi.ocean.seal.pojo.GamePropGrantResult;
import fm.lizhi.ocean.seal.strategy.GamePropGrantStrategy;
import io.github.cfgametech.Response;
import lombok.extern.slf4j.Slf4j;

import java.util.HashMap;
import java.util.Map;

/**
 * LUK 渠道道具发放策略实现
 * <AUTHOR>
 */
@Slf4j
@AutoBindSingleton
public class LukGamePropGrantStrategy implements GamePropGrantStrategy {

    @Inject
    private LukManger lukManager;

    @Inject
    private BizGameManager bizGameManager;
    @Inject
    private GameChannelManager gameChannelManager;
    @Inject
    private GameInfoManager gameInfoManager;

    @Override
    public boolean supports(String channel) {
        return GameChannel.LUK.equals(channel);
    }


    @Override
    public GamePropGrantResult grantProp(GrantGamePropParam param,
                                         GamePropBean gameProp,
                                         GameBizGameBean bizGameBean,
                                         GameInfoBean gameInfoBean,
                                         GameChannelBean gameChannelBean) {
        
        log.info("LUK grant prop, gameId: {}, propId: {}, userId: {}", bizGameBean.getId(), gameProp.getId(), param.getUserId());

        try {
            // 构建道具发放数据
            Map<String, Object> grantPropData = buildGrantPropData(param, gameProp);

            // 调用通用的控制事件发布方法
            Response<?> sdkResult = lukManager.publishGlobalEvent(
                    param.getAppId(),
                    String.valueOf(gameInfoBean.getChannelGameId()),
                    LukControlEventType.GRANT_PROP,
                    grantPropData
            );

            // 转换结果
            return convertLukResult(sdkResult);
            
        } catch (Exception e) {
            log.error("LUK grant prop failed, gameId: {}, propId: {}, userId: {}", 
                     bizGameBean.getId(), gameProp.getId(), param.getUserId(), e);
            return GamePropGrantResult.failure(GRANT_FAIL, "LUK grant prop failed: " + e.getMessage());
        }
    }

    @Override
    public GamePropGrantResult grantProp(GrantGamePropParam param, GamePropBean gameProp) {
        // 查询gameId是属于游戏渠道的gameId，还是属于seal平台biz_gameId
        GameBizGameBean bizGameBean = bizGameManager.getGame(param.getAppId(), param.getGameId());
        if (bizGameBean == null) {
            throw new IllegalArgumentException("gameId not exist. appId=" + param.getAppId() + ", gameId=" + param.getGameId());
        }

        // 获取游戏信息
        GameInfoBean gameInfoBean = gameInfoManager.getGameInfoBeanById(bizGameBean.getGameInfoId());
        if (gameInfoBean == null) {
            throw new IllegalArgumentException("gameInfo not exist. appId=" + param.getAppId() + ", gameId=" + param.getGameId());
        }

        // 获取渠道信息
        GameChannelBean gameChannelBean = gameChannelManager.getGameChannel(bizGameBean.getChannelId());
        if (gameChannelBean == null) {
            throw new IllegalArgumentException("channel not exist. appId=" + param.getAppId() + ", gameId=" + param.getGameId());
        }

        // 调用原有的发放方法
        return grantProp(param, gameProp, bizGameBean, gameInfoBean, gameChannelBean);
    }



    /**
     * 构建道具发放数据
     */
    private Map<String, Object> buildGrantPropData(GrantGamePropParam param, GamePropBean gameProp) {
        Map<String, Object> dataMap = new HashMap<>();
        dataMap.put("user_id", String.valueOf(param.getUserId()));
        dataMap.put("details", CollUtil.newArrayList(new GrantPropDetail()
                        .setProp_id(String.valueOf(gameProp.getChannelPropId()))
                        .setNum(param.getNum())
                        .setDuration(param.getDuration())
                        .setDuration_reset(false)
                ));
        dataMap.put("unique_id", param.getUniqueId());
        return dataMap;
    }

    /**
     * 转换 LUK 结果
     */
    private GamePropGrantResult convertLukResult(Response<?> sdkResult) {
        if (sdkResult != null) {
            if (sdkResult.suc()) {
                return GamePropGrantResult.success(JsonUtil.dumps(sdkResult.getData()));
            } else {
                return GamePropGrantResult.failure(sdkResult.getCode(), sdkResult.getMessage());
            }
        } else {
            return GamePropGrantResult.failure(GRANT_FAIL, "LUK SDK returned null result");
        }
    }
}
