package fm.lizhi.ocean.seal.adapter;

import fm.lizhi.ocean.seal.dao.bean.*;
import fm.lizhi.ocean.seal.dao.mapper.ext.BizGameRelationOrcaResult;
import fm.lizhi.ocean.seal.protocol.GameOrcaApiProto;
import fm.lizhi.ocean.seal.protocol.GameOrcaApiProto.GameChannelPB;
import org.apache.commons.lang3.StringUtils;
import org.springframework.util.CollectionUtils;

import java.util.Collections;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

public class GameOrcaApiAdapter {
    public static GameChannelPB convertChannelToPB(GameChannelBean bean){
        if(null == bean){
            return null;
        }

        return GameChannelPB.newBuilder()
                .setId(bean.getId())
                .setAppId(bean.getAppId())
                .setAppKey(bean.getAppKey())
                .setAppSecret(bean.getAppSecret())
                .setChannel(bean.getChannel())
                .setName(bean.getName())
                .setExtConfig(Optional.ofNullable(bean.getExtConfig()).orElse(StringUtils.EMPTY))
                .setCreateTime(bean.getCreateTime().getTime())
                .setModifyTime(bean.getModifyTime().getTime())
                .build();
    }

    public static List<GameChannelPB> convertGameChannelPBs(List<GameChannelBean> beans){
        if(CollectionUtils.isEmpty(beans)){
            return Collections.emptyList();
        }

        return beans.stream().map(GameOrcaApiAdapter::convertChannelToPB).collect(Collectors.toList());
    }

    public static GameOrcaApiProto.GameAppInfoPB convertGameAppToPB(GameAppBean bean){
        if(null == bean){
            return null;
        }
        return GameOrcaApiProto.GameAppInfoPB.newBuilder()
                .setId(bean.getId())
                .setAppId(bean.getAppId())
                .setAppName(bean.getAppName())
                .setAppAlias(Optional.ofNullable(bean.getAppAlias()).orElse(StringUtils.EMPTY))
                .setAppSecret(bean.getAppSecret())
                .setAppTopic(Optional.ofNullable(bean.getAppTopic()).orElse(StringUtils.EMPTY))
                .build();
    }

    public static List<GameOrcaApiProto.GameAppInfoPB> convertGameAppToPBs(List<GameAppBean> beans){
        if(CollectionUtils.isEmpty(beans)){
            return Collections.emptyList();
        }

        return beans.stream().map(GameOrcaApiAdapter::convertGameAppToPB).collect(Collectors.toList());
    }

    public static GameOrcaApiProto.GameInfoPB convertGameInfoToPB(GameInfoBean bean){
        if(null == bean){
            return null;
        }
        return GameOrcaApiProto.GameInfoPB.newBuilder()
                .setId(bean.getId())
                .setName(bean.getName())
                .setChannelGameId(bean.getChannelGameIdStr())
                .setCaptain(bean.getCaptain())
                .setDesc(Optional.ofNullable(bean.getDesc()).orElse(StringUtils.EMPTY))
                .setExtraJson(Optional.ofNullable(bean.getExtraJson()).orElse(StringUtils.EMPTY))
                .setRenderType(bean.getRenderType())
                .setChannel(bean.getChannel())
                .build();
    }

    public static List<GameOrcaApiProto.GameInfoPB> convertGameInfoToPBs(List<GameInfoBean> beans){
        if(CollectionUtils.isEmpty(beans)){
            return Collections.emptyList();
        }

        return beans.stream().map(GameOrcaApiAdapter::convertGameInfoToPB).collect(Collectors.toList());
    }

    public static GameOrcaApiProto.GameBizGamePB convertGameBizGameToPB(BizGameRelationOrcaResult bean){
        if(null == bean){
            return null;
        }
        return GameOrcaApiProto.GameBizGamePB.newBuilder()
                .setId(bean.getId())
                .setGameAppId(bean.getGameAppId())
                .setGameInfoId(bean.getGameInfoId())
                .setChannelId(bean.getChannelId())
                .setConfig(Optional.ofNullable(bean.getConfig()).orElse(StringUtils.EMPTY))
                .setAppId(bean.getAppId()).setChannelGameId(bean.getChannelGameId())
                .setAppName(bean.getAppName())
                .setChannel(bean.getChannel())
                .setName(bean.getName())
                .build();
    }

    public static List<GameOrcaApiProto.GameBizGamePB> convertGameBizGameToPBs(List<BizGameRelationOrcaResult> beans){
        if(CollectionUtils.isEmpty(beans)){
            return Collections.emptyList();
        }

        return beans.stream().map(GameOrcaApiAdapter::convertGameBizGameToPB).collect(Collectors.toList());
    }

    public static GameOrcaApiProto.GameCallbackPB convertGameCallbackToPB(GameCallback bean){
        if(null == bean){
            return null;
        }
        return GameOrcaApiProto.GameCallbackPB.newBuilder()
                .setId(bean.getId())
                .setAppId(bean.getAppId())
                .setCallbackKey(Optional.ofNullable(bean.getCallbackKey()).orElse(StringUtils.EMPTY))
                .setType(bean.getType())
                .setUrl(bean.getUrl())
                .build();
    }

    public static List<GameOrcaApiProto.GameCallbackPB> convertGameCallbackToPBs(List<GameCallback> beans){
        if(CollectionUtils.isEmpty(beans)){
            return Collections.emptyList();
        }

        return beans.stream().map(GameOrcaApiAdapter::convertGameCallbackToPB).collect(Collectors.toList());
    }

    public static GameOrcaApiProto.GameVersionPB convertGameVersionToPB(GameVersionBean bean){
        if(null == bean){
            return null;
        }
        return GameOrcaApiProto.GameVersionPB.newBuilder()
                .setId(bean.getId())
                .setAppId(bean.getAppId())
                .setGameId(bean.getGameId())
                .setSystemType(bean.getSystemType())
                .setMinSdkVersion(bean.getMinSdkVersion())
                .setGameVersion(bean.getGameVersion())
                .setForceUpdate(bean.getForceUpdate())
                .setDownloadLink(bean.getDownloadLink())
                .setGameFlow(bean.getGameFlow())
                .setExtConfig(Optional.ofNullable(bean.getExtConfig()).orElse(StringUtils.EMPTY))
                .setCreateTime(bean.getCreateTime().getTime())
                .setModifyTime(bean.getModifyTime().getTime())
                .build();
    }

    public static List<GameOrcaApiProto.GameVersionPB> convertGameVersionToPBs(List<GameVersionBean> beans){
        if(CollectionUtils.isEmpty(beans)){
            return Collections.emptyList();
        }

        return beans.stream().map(GameOrcaApiAdapter::convertGameVersionToPB).collect(Collectors.toList());
    }
}
