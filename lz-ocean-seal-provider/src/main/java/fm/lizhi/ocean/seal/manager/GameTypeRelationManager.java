package fm.lizhi.ocean.seal.manager;

import com.google.inject.Inject;
import com.netflix.governator.annotations.AutoBindSingleton;
import fm.lizhi.commons.util.GuidGenerator;
import fm.lizhi.ocean.seal.dao.bean.GameTypeRelationBean;
import fm.lizhi.ocean.seal.dao.mapper.GameTypeRelationBeanMapper;
import fm.lizhi.ocean.seal.dao.mapper.ext.GameTypeRelationExtMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.CollectionUtils;

import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

@Slf4j
@AutoBindSingleton
public class GameTypeRelationManager {

    @Inject
    private GuidGenerator guidGenerator;
    @Inject
    private GameTypeRelationBeanMapper mapper;
    @Inject
    private GameTypeRelationExtMapper gameTypeRelationExtMapper;

    public void insertOrUpdateTypeByGameId(long gameInfoId, List<Long> typeIds, boolean insert){
        if(!insert){
            //不是插入，先删除关联表
            gameTypeRelationExtMapper.delRelationByGameId(gameInfoId);
        }

        if(CollectionUtils.isEmpty(typeIds)){
            return;
        }

        Date date = new Date();
        mapper.batchInsert(typeIds.stream().map(x->{
            GameTypeRelationBean bean = new GameTypeRelationBean();
            bean.setId(guidGenerator.genId());
            bean.setGameId(gameInfoId);
            bean.setGameTypeId(x);
            bean.setCreateTime(date);
            bean.setModifyTime(date);
            return bean;
        }).collect(Collectors.toList()));
    }

    public List<Long> getGameTypeIdByGameId(long gameId){
        List<GameTypeRelationBean> beans = getGameTypeRelationsByGameId(gameId);
        if(null == beans){
            return Collections.EMPTY_LIST;
        }

        return beans.stream().map(x->x.getGameTypeId()).collect(Collectors.toList());
    }

    public List<GameTypeRelationBean> getGameTypeRelationsByGameId(long gameId){
        GameTypeRelationBean bean = new GameTypeRelationBean();
        bean.setGameId(gameId);
        return mapper.selectMany(bean);
    }
}
