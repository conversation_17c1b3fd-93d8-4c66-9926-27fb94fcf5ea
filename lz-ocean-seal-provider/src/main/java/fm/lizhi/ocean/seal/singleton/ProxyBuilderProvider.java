package fm.lizhi.ocean.seal.singleton;

import com.google.inject.Inject;
import com.google.inject.Provider;
import com.netflix.governator.annotations.AutoBindSingleton;

import fm.lizhi.commons.config.config.ConfigBean;
import fm.lizhi.commons.service.client.codec.DataCenterServiceConsumerCodec;
import fm.lizhi.commons.service.client.proxy.ProxyBuilder;
import fm.lizhi.ocean.seal.conf.LzConfig;
import fm.lizhi.ocean.seal.log.LizhiLogger;

/*
 * 服务消费者代理构建器
 */
@AutoBindSingleton
public class ProxyBuilderProvider implements Provider<ProxyBuilder> {

	@Inject
    LzConfig lzConf;

	@Override
	public ProxyBuilder get() {
		try {
			ProxyBuilder proxyBuilder 	=	new ProxyBuilder();
			//新版配置中心已经不需要显式传ConfigBean(配置中心sdk会自动获取)，但服务框架目前还需要该参数，所以无论是正式环境或测试环境，传ConfigBean.TEST_OFFICE即可
			//lz-commons-config 2.4.0或以上可以这样直接传ConfigBean.TEST_OFFICE
			proxyBuilder.init(new DataCenterServiceConsumerCodec(), null, lzConf.getDcProxyConfigKey(), lzConf.getDcProxyConnectionCount(), ConfigBean.TEST_OFFICE, ProxyBuilder.DEFAULT_CLOSE_CONN_AFTER_NOT_RESP_HB_SECONDS, ProxyBuilder.DEFAULT_READ_IDLE_INTERVAL_SECONDS);
			return proxyBuilder;
		} catch (Exception e) {
			LizhiLogger.error("get proxyBuilder singletone throws exception, errMsg: " + e.getMessage(), e);
			throw new RuntimeException(e);
		}
	}

}
