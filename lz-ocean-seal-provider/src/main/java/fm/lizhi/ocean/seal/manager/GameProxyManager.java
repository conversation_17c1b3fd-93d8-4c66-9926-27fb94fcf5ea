package fm.lizhi.ocean.seal.manager;

import com.google.inject.Inject;
import com.googlecode.protobuf.format.JsonFormat;
import com.netflix.governator.annotations.AutoBindSingleton;
import fm.lizhi.ocean.seal.dao.bean.GameBizGameBean;
import fm.lizhi.ocean.seal.dao.bean.GameChannelBean;
import fm.lizhi.ocean.seal.dao.bean.GameInfoBean;
import fm.lizhi.ocean.seal.protocol.GameProxyServiceProto;
import fm.lizhi.ocean.seal.strategy.GameProxyInvokeStrategy;
import fm.lizhi.ocean.seal.strategy.factory.GameProxyInvokeStrategyFactory;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * Created in 2022-06-23 11:16.
 *
 * <AUTHOR>
 */
@AutoBindSingleton
public class GameProxyManager {
    private static final Logger logger = LoggerFactory.getLogger(GameProxyManager.class);

    @Inject
    private BizGameManager bizGameManager;
    @Inject
    private GameChannelManager gameChannelManager;
    @Inject
    private GameInfoManager gameInfoManager;
    @Inject
    private GameProxyInvokeStrategyFactory strategyFactory;

    /**
     * 调用目标方法
     *
     * @param param 参数
     * @return
     */
    public GameProxyServiceProto.ResponseInvokeTarget invokeTarget(GameProxyServiceProto.InvokeTargetParams param) {
        String jsonParams = JsonFormat.printToString(param);
        logger.info("Call invoke target,params:{}", jsonParams);

        // 查询gameId是属于游戏渠道的gameId，还是属于seal平台biz_gameId
        GameBizGameBean bizGameBean = bizGameManager.getGame(param.getAppId(), param.getGameId());
        if (bizGameBean == null) {
            throw new IllegalArgumentException("gameId not exist. appId=" + param.getAppId() + ", gameId=" + param.getGameId());
        }

        // 获取游戏信息
        GameInfoBean gameInfoBean = gameInfoManager.getGameInfoBeanById(bizGameBean.getGameInfoId());
        if (gameInfoBean == null) {
            throw new IllegalArgumentException("gameInfo not exist. appId=" + param.getAppId() + ", gameId=" + param.getGameId());
        }

        // 获取渠道信息
        GameChannelBean gameChannelBean = gameChannelManager.getGameChannel(bizGameBean.getChannelId());
        if (gameChannelBean == null) {
            throw new IllegalArgumentException("channel not exist. appId=" + param.getAppId() + ", gameId=" + param.getGameId());
        }

        // 使用策略模式进行调用
        GameProxyInvokeStrategy strategy = strategyFactory.getStrategy(gameChannelBean.getChannel());
        return strategy.invokeTarget(param, bizGameBean, gameInfoBean, gameChannelBean);
    }


}