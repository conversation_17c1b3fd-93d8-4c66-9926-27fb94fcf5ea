package fm.lizhi.ocean.seal.dao.mapper.ext;

/**
 * This class is for record all table name.
 */
public class GameFieldNameConstant {

    public static class GameInfoTable{
        public static String ID = "id";
        public static String NAME = "name";
        public static String CHANNEL_GAME_ID = "channel_game_id";
        public static String DESC = "desc";
        public static String CHANNEL = "channel";
        public static String GAME_IMAGE = "game_image";
        public static String GAME_VIDEO = "game_video";
        public static String ACCESS_TYPE = "access_type";//接入状态 0-已接入 1-待接入
        public static String PAY_WAY_CODE = "pay_way_code";//付费方式 {@link fm.lizhi.ocean.seal.constant.GamePayWayEnum}
        public static String EXPENSE_RULE = "expense_rule";//计费规则
        public static String LOW_EXPENSE = "low_expense";//最低消费:0没有 1有
        public static String LOW_EXPENSE_VALUE = "low_expense_value";//最低消费:0没有 1有
    }

    public static class GameBizGameTable{
        public static String CHANNEL_ID = "channel_id";
    }

    public static class GameTypeRelationTable{
        public static String GAME_TYPE_ID = "game_type_id";
    }
}
