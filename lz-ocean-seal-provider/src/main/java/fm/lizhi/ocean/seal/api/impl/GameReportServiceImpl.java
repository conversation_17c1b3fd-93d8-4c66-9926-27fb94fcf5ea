package fm.lizhi.ocean.seal.api.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.base.Strings;
import com.google.inject.Inject;
import com.googlecode.protobuf.format.JsonFormat;
import fm.lizhi.commons.service.client.annotation.ServiceProvider;
import fm.lizhi.commons.service.client.log.LogContext;
import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.commons.service.client.rcode.GeneralRCode;
import fm.lizhi.game.auth.SealAuth;
import fm.lizhi.ocean.seal.agora.service.AgoraService;
import fm.lizhi.ocean.seal.api.GameReportService;
import fm.lizhi.ocean.seal.conf.LzConfig;
import fm.lizhi.ocean.seal.constant.ChannelType;
import fm.lizhi.ocean.seal.constant.GameChannel;
import fm.lizhi.ocean.seal.constant.SealGameUrlConstant;
import fm.lizhi.ocean.seal.constant.SealRCode;
import fm.lizhi.ocean.seal.cronjob.DispatchGameReportJob;
import fm.lizhi.ocean.seal.dao.bean.GameBizGameBean;
import fm.lizhi.ocean.seal.dao.bean.GameChannelBean;
import fm.lizhi.ocean.seal.kafka.*;
import fm.lizhi.ocean.seal.manager.*;
import fm.lizhi.ocean.seal.pojo.bo.lizhi.LizhiReportGameData;
import fm.lizhi.ocean.seal.pojo.bo.lizhi.LizhiRoomGameRound;
import fm.lizhi.ocean.seal.pojo.bo.sud.GameUserHeartBeat;
import fm.lizhi.ocean.seal.pojo.bo.sud.SudGameReportInfo;
import fm.lizhi.ocean.seal.pojo.bo.sud.SudGameResult;
import fm.lizhi.ocean.seal.pojo.bo.sud.SudReportInfo;
import fm.lizhi.ocean.seal.protocol.GameReportServiceProto;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Slf4j
@ServiceProvider
public class GameReportServiceImpl implements GameReportService {
    private static final Logger logger = LoggerFactory.getLogger(GameReportServiceImpl.class);
    @Inject
    private GameUserManager gameUserManager;
    @Inject
    private ReportGameResultProducer reportGameResultProducer;
    @Inject
    private DispatchGameReportJob dispatchGameReportJob;
    @Inject
    private HttpClientManager httpClientManager;
    @Inject
    private LzConfig lzConfig;
    @Inject
    private GameDataReportManager gameDataReportManager;
    @Inject
    private GameAppManager gameAppManager;
    @Inject
    private GameRoundManager gameRoundManager;
    @Inject
    private AgoraService agoraService;
    @Inject
    private BizGameManager bizGameManager;

    @Override
    public Result<Void> gameStartReport(GameReportServiceProto.GameStartResult result) {
        String params = JsonFormat.printToString(result);
        logger.info("game start report, params:{}", params);
        GameStartResult gameStartResult = gameStartResult2KafkaMessage(result);
        String gameRoundId = gameStartResult.getGameRoundId();
        LogContext.addReqLog("gameSettleReport param={}", params);
        LogContext.addResLog("gameSettleReport gameId={}`gameRoundId={}", gameStartResult.getGameId(), gameRoundId);
        // 环境是客户端透传过来的
        int resultEnv = result.getEnv();
        this.gameRoundManager.channelStartRound(result);
        if (dispatchGameReportJob.checkNeedDispatchMsg(resultEnv)) {
            // 需要先存储本局所属的游戏环境
            boolean setGameReportDataEnv = dispatchGameReportJob.setGameReportDataEnv(gameRoundId, resultEnv);
            if (!setGameReportDataEnv) {
                return new Result<>(SealRCode.SEAL_RCODE_REPORT_GAME_DATA_SAVE_ENV, null);
            }
            // 进行转发
            boolean resultToList = dispatchGameReportJob.dispatchGameStartResult(result.getAppId(), gameStartResult);
            return new Result<>(resultToList ? GeneralRCode.GENERAL_RCODE_SUCCESS : SealRCode.SEAL_RCODE_REPORT_GAME_DATA_SAVE_DATA, null);
        }
        boolean sendGameStartMsg = reportGameResultProducer.sendGameStartMsg(result.getAppId(), gameStartResult);
        return new Result<>(sendGameStartMsg ? GeneralRCode.GENERAL_RCODE_SUCCESS : SealRCode.SEAL_RCODE_UNKNOWN_ERROR, null);
    }

    /**
     * 对象转换
     *
     * @param result
     * @return
     */
    private GameStartResult gameStartResult2KafkaMessage(GameReportServiceProto.GameStartResult result) {
        GameStartResult gameStartResult = new GameStartResult();
        gameStartResult.setGameId(result.getGameId());
        gameStartResult.setGameMode(result.getGameMode());
        gameStartResult.setGameRoundId(result.getGameRoundId());
        gameStartResult.setGameStartAtTime(result.getGameStartAtTime());
        gameStartResult.setReportGameInfoExtras(result.getReportGameInfoExtras());
        gameStartResult.setRoomId(result.getRoomId());
        gameStartResult.setRawResult(result.getRawResult());
        int gamePlayersCount = result.getGamePlayersCount();
        GamePlayer[] gamePlayers = new GamePlayer[gamePlayersCount];
        if (gamePlayersCount > 0) {
            List<GameReportServiceProto.GamePlayerResult> gamePlayersList = result.getGamePlayersList();
            int index = 0;
            for (GameReportServiceProto.GamePlayerResult gamePlayerResult : gamePlayersList) {
                GamePlayer gamePlayer = new GamePlayer();
                gamePlayer.setRealUser(gamePlayerResult.getRealUser());
                long userId = gameUserManager.converUid2SealUserId(gamePlayerResult.getUid());
                gamePlayer.setUserId(userId);
                gamePlayers[index] = gamePlayer;
                index++;
            }
        }
        gameStartResult.setGamePlayers(gamePlayers);

        // 如果透传参数为空，则再取一次
//        if (StringUtils.isBlank(gameStartResult.getReportGameInfoExtras())) {
//            String extra = this.gameRoundManager.getExtraByChannelRoundId(result.getGameRoundId());
//            gameStartResult.setReportGameInfoExtras(extra);
//        }
        return gameStartResult;
    }

    /**
     * 对象转换
     *
     * @param result
     * @return
     */
    private GameSettleResult gameSettleResult2KafkaMessage(GameReportServiceProto.GameSettleResult result, Long gameAppId) {
        String extra = result.getReportGameInfoExtras();
        if (GameChannel.AGORA.equals(result.getChannel()) || StringUtils.isBlank(extra)) {
            extra = this.gameRoundManager.getExtraByChannelRoundId(result.getGameRoundId(), gameAppId);
        }
        GameSettleResult gameSettleResult = new GameSettleResult();
        gameSettleResult.setGameId(result.getGameId());
        gameSettleResult.setGameMode(result.getGameMode());
        gameSettleResult.setGameRoundId(result.getGameRoundId());
        gameSettleResult.setGameStartAtTime(result.getGameStartAtTime());
        gameSettleResult.setGameDuration(result.getGameDuration());
        gameSettleResult.setGameEndAtTime(result.getGameEndAtTime());
        gameSettleResult.setRoomId(result.getRoomId());
        gameSettleResult.setReportGameInfoExtras(extra);
        gameSettleResult.setRawResult(result.getRawResult());
        int gamePlayersCount = result.getGamePlayersCount();
        GamePlayerSettleResult[] gamePlayers = new GamePlayerSettleResult[gamePlayersCount];
        if (gamePlayersCount > 0) {
            List<GameReportServiceProto.GamePlayerSettleResult> gamePlayersList = result.getGamePlayersList();
            int index = 0;
            for (GameReportServiceProto.GamePlayerSettleResult gamePlayerResult : gamePlayersList) {
                GamePlayerSettleResult gamePlayer = new GamePlayerSettleResult();
                gamePlayer.setRealUser(gamePlayerResult.getRealUser());
                long userId = gameUserManager.converUid2SealUserId(gamePlayerResult.getUid());
                gamePlayer.setUserId(userId);
                gamePlayer.setEscaped(gamePlayerResult.getEscaped());
                gamePlayer.setRank(gamePlayerResult.getRank());
                gamePlayer.setRole(gamePlayerResult.getRole());
                gamePlayer.setScore(gamePlayerResult.getScore());
                gamePlayer.setIsWin(gamePlayerResult.getIsWin());
                gamePlayer.setAward(gamePlayerResult.getAward());
                gamePlayers[index] = gamePlayer;
                index++;
            }
        }
        gameSettleResult.setGamePlayerSettleResults(gamePlayers);
        return gameSettleResult;
    }

    @Override
    public Result<Void> gameSettleReport(GameReportServiceProto.GameSettleResult result) {
        String params = JsonFormat.printToString(result);
        logger.info("game settle report, params:{}", params);
        GameBizGameBean bizGameBean = this.bizGameManager.getGame(result.getAppId(), result.getChannelGameId());
        Long gameAppId = bizGameBean.getGameAppId();
        GameSettleResult gameSettleResult = gameSettleResult2KafkaMessage(result, gameAppId);
        LogContext.addReqLog("gameSettleReport param={}", params);
        LogContext.addResLog("gameSettleReport gameId={}`gameRoundId={}", gameSettleResult.getGameId(), gameSettleResult.getGameRoundId());
        this.gameRoundManager.channelEndRound(result, gameAppId);
//        this.gameDataReportManager.reportGameEnd(result);
        // 判断是否有透传的客户端环境变量，如果没有，需要去redis获取
        String reportDataEnv = result.getEnv() >= 0 ? result.getEnv() + "" : dispatchGameReportJob.getGameReportDataEnv(gameSettleResult.getGameRoundId());
        if (!Strings.isNullOrEmpty(reportDataEnv) && dispatchGameReportJob.checkNeedDispatchMsg(Integer.parseInt(reportDataEnv))) {
            // 需要先做转发
            boolean resultToList = dispatchGameReportJob.dispatchGameSettleResult(result.getAppId(), gameSettleResult);
            return new Result<>(resultToList ? GeneralRCode.GENERAL_RCODE_SUCCESS : SealRCode.SEAL_RCODE_REPORT_GAME_DATA_SAVE_DATA, null);
        }
        boolean sendGameSettleMsg = reportGameResultProducer.sendGameSettleMsg(result.getAppId(), gameSettleResult);
        return new Result<>(sendGameSettleMsg ? GeneralRCode.GENERAL_RCODE_SUCCESS : SealRCode.SEAL_RCODE_UNKNOWN_ERROR, null);
    }

    @Override
    public Result<GameReportServiceProto.ResponseGetRoomGameReportList> getRoomGameReportList(String roomId, String gameChannel, int pageNum, int pageCount, String appId) {
        String logStr = "roomId={}`gameChannel={}`pageNum={}`pageCount={}`appId={}";
        LogContext.addReqLog(logStr, roomId, gameChannel, pageNum, pageCount, appId);
        LogContext.addResLog(logStr, roomId, gameChannel, pageNum, pageCount, appId);
        if (Strings.isNullOrEmpty(roomId) || Strings.isNullOrEmpty(gameChannel) || pageNum <= 0 || pageCount > 10) {
            return new Result<>(GET_ROOM_GAME_REPORT_LIST_ILLEGAL_PARAMS, null);
        }
        if(ChannelType.LIZHI.getName().equals(gameChannel)) {
            return getRoomGameReportListFromLiZhi(roomId, pageNum, pageCount, appId);
        }
        return getRoomGameReportListFromSud(roomId, pageNum, pageCount, appId);
    }

    /**
     * 从即构获取房间游戏结算数据
     *
     * @param roomId
     * @param pageNum
     * @param pageCount
     * @param appId
     * @return
     */
    @NotNull
    private Result<GameReportServiceProto.ResponseGetRoomGameReportList> getRoomGameReportListFromSud(String roomId, int pageNum, int pageCount, String appId) {
        GameChannelBean gameChannelBean = gameAppManager.getChannelInfoByAppIdAndChannel(appId, ChannelType.SUD.getName());
        if(null == gameChannelBean){
            return new Result<>(GET_ROOM_GAME_REPORT_LIST_FAIL, null);
        }
        JSONObject body = buildPostReportBody(gameChannelBean, null
                , roomId, pageNum, pageCount, true, false);

        String url = getGameChannelUrlByName(SealGameUrlConstant.PAGE_SUD_GAME_INFO_URL, gameChannelBean);
        if(StringUtils.isBlank(url)){
            return new Result<>(GET_ROOM_GAME_REPORT_LIST_FAIL, null);
        }
        String response = httpClientManager.postRequest(url, buildPostHeaderMap(), body.toJSONString());
        if (response == null) {
            return new Result<>(GET_ROOM_GAME_REPORT_LIST_FAIL, null);
        }
        GameReportServiceProto.ResponseGetRoomGameReportList.Builder builder = GameReportServiceProto.ResponseGetRoomGameReportList.newBuilder();
        try {
            JSONObject respJsonObject = JSON.parseObject(response);
            String jsonData = respJsonObject.getString("data");
            if (jsonData == null) {
                LogContext.addResLog("data=null");
                return new Result<>(GET_ROOM_GAME_REPORT_LIST_NO_DATA, null);
            }
            List<SudGameReportInfo> jsonArray = JSON.parseArray(jsonData, SudGameReportInfo.class);
            jsonArray.stream().forEach(reportInfo -> {
                GameReportServiceProto.GameResult.Builder gameResultBuilder = GameReportServiceProto.GameResult.newBuilder();
                gameResultBuilder.setGameRoundId(reportInfo.getGame_round_id());
                SudReportInfo sudReportInfo = reportInfo.getReport_info();
                if (sudReportInfo.getGame_start() != null) {
                    gameResultBuilder.setStartResult(sudReportInfo.getGame_start().buildGameStartResult());
                }
                if (sudReportInfo.getGame_settle() != null) {
                    gameResultBuilder.setSettleResult(sudReportInfo.getGame_settle().buildGameSettleResult());
                }
                builder.addResult(gameResultBuilder);
            });
            return new Result<>(GeneralRCode.GENERAL_RCODE_SUCCESS, builder.build());
        } catch (Exception e) {
            log.error("json to sudGameReportInfo fail", e);
        }
        return new Result<>(GET_ROOM_GAME_REPORT_LIST_NO_DATA, null);
    }

    /**
     * 从即构获取房间游戏结算数据
     *
     * @param roomId
     * @param pageNum
     * @param pageCount
     * @return
     */
    @NotNull
    private Result<GameReportServiceProto.ResponseGetRoomGameReportList> getRoomGameReportListFromLiZhi(String roomId, int pageNum, int pageCount, String appId) {
        GameChannelBean gameChannelBean = gameAppManager.getChannelInfoByAppIdAndChannel(appId, ChannelType.LIZHI.getName());
        if(null == gameChannelBean){
            return new Result<>(GET_ROOM_GAME_REPORT_LIST_FAIL, null);
        }
        JSONObject body = buildPostReportBody(gameChannelBean, null, roomId, pageNum, pageCount, false, false);

        String url = getGameChannelUrlByName(SealGameUrlConstant.PAGE_LIZHI_GAME_INFO_URL, gameChannelBean);
        if(StringUtils.isBlank(url)){
            return new Result<>(GET_ROOM_GAME_REPORT_LIST_FAIL, null);
        }

        String response = httpClientManager.postRequest(url, buildPostHeaderMap(), body.toJSONString());
        if (response == null) {
            return new Result<>(GET_ROOM_GAME_REPORT_LIST_FAIL, null);
        }
        GameReportServiceProto.ResponseGetRoomGameReportList.Builder builder = GameReportServiceProto.ResponseGetRoomGameReportList.newBuilder();
        try {
            JSONObject respJsonObject = JSON.parseObject(response);
            String jsonData = respJsonObject.getString("data");
            if (jsonData == null) {
                LogContext.addResLog("data=null");
                return new Result<>(GET_ROOM_GAME_REPORT_LIST_NO_DATA, null);
            }
            List<LizhiRoomGameRound> jsonArray = JSON.parseArray(jsonData, LizhiRoomGameRound.class);
            jsonArray.stream().forEach(reportInfo -> {
                GameReportServiceProto.GameResult.Builder gameResultBuilder = GameReportServiceProto.GameResult.newBuilder();
                gameResultBuilder.setGameRoundId(reportInfo.getGameRoundId()+"");
                if (reportInfo.getGameStartData() != null) {
                    gameResultBuilder.setStartResult(reportInfo.getGameStartData().buildGameStartResult());
                }
                if (reportInfo.getGameEndData() != null) {
                    gameResultBuilder.setSettleResult(reportInfo.getGameEndData().buildGameSettleResult());
                }
                builder.addResult(gameResultBuilder);
            });
            return new Result<>(GeneralRCode.GENERAL_RCODE_SUCCESS, builder.build());
        } catch (Exception e) {
            log.error("json to sudGameReportInfo fail", e);
        }
        return new Result<>(GET_ROOM_GAME_REPORT_LIST_NO_DATA, null);

    }

    @Override
    public Result<GameReportServiceProto.ResponseGetGameSettleReport> getGameSettleReport(String gameRoundId, String gameChannel, String appId) {
        String logStr = "gameRoundId={}`gameChannel={}`appId={}";
        LogContext.addReqLog(logStr, gameRoundId, gameChannel, appId);
        LogContext.addResLog(logStr, gameRoundId, gameChannel, appId);
        if (Strings.isNullOrEmpty(gameChannel) || Strings.isNullOrEmpty(gameRoundId)) {
            return new Result<>(GET_GAME_SETTLE_REPORT_ILLEGAL_PARAMS, null);
        }
        if(ChannelType.LIZHI.getName().equals(gameChannel)) {
            return geGameSettleReportFromLiZhi(gameRoundId, appId);
        }
        if (GameChannel.AGORA.equals(gameChannel)) {
            return this.agoraService.getGameSettleInfo(appId, gameRoundId);
        }
        return geGameSettleReportFromSud(gameRoundId, appId);
    }

    @Override
    public Result<Void> roomUsersChanged(GameReportServiceProto.RoomUsersChangedResult result) {
        String params = JsonFormat.printToString(result);
        logger.info("room users Changed, params:{}", params);
        RoomUsersChangedResult gameStartResult = roomUsersChangedResult2KafkaMessage(result);
        LogContext.addReqLog("room users Changed param={}", params);
        boolean sendRoomUsersChangedMsg = reportGameResultProducer.sendRoomUsersChangedMsg(result.getAppId(), gameStartResult);
        return new Result<>(sendRoomUsersChangedMsg ? GeneralRCode.GENERAL_RCODE_SUCCESS : SealRCode.SEAL_RCODE_UNKNOWN_ERROR, null);
    }

    @Override
    public Result<Void> sendUserHeart(GameReportServiceProto.RoomGameUserHeartBeat result) {
        String params = JsonFormat.printToString(result);
        logger.info("send user heart, params:{}", params);
        GameUserHeartBeat gameStartResult = roomGameUserHeartBeat2KafkaMessage(result);
        LogContext.addReqLog("send user heart param={}", params);
        boolean sendUserHeartMsg = reportGameResultProducer.sendUserHeartMsg(gameStartResult);
        return new Result<>(sendUserHeartMsg ? GeneralRCode.GENERAL_RCODE_SUCCESS : SealRCode.SEAL_RCODE_UNKNOWN_ERROR, null);
    }

    /**
     * 对象转换
     *
     * @param result
     * @return
     */
    private GameUserHeartBeat roomGameUserHeartBeat2KafkaMessage(GameReportServiceProto.RoomGameUserHeartBeat result) {
        GameUserHeartBeat gameUserHeartBeat = new GameUserHeartBeat();
        gameUserHeartBeat.setAppId(result.getAppId());
        gameUserHeartBeat.setGameId(result.getGameId());
        gameUserHeartBeat.setNjId(result.getNjId());
        gameUserHeartBeat.setUserId(result.getUserId());
        gameUserHeartBeat.setChangedTime(result.getChangedTime());
        return gameUserHeartBeat;
    }

    /**
     * 对象转换
     *
     * @param result
     * @return
     */
    private RoomUsersChangedResult roomUsersChangedResult2KafkaMessage(GameReportServiceProto.RoomUsersChangedResult result) {
        RoomUsersChangedResult roomUsersChangedResult = new RoomUsersChangedResult();
        roomUsersChangedResult.setRoomId(result.getRoomId());
        roomUsersChangedResult.setMgId(result.getMgId());
        roomUsersChangedResult.setPlayerTotal(result.getPlayerTotal());
        roomUsersChangedResult.setObTotal(result.getObTotal());
        roomUsersChangedResult.setChangedTime(result.getChangedTime());
        roomUsersChangedResult.setRawResult(result.getRawResult());
        return roomUsersChangedResult;
    }

    /**
     * 从荔枝获取游戏结算数据
     *
     * @param gameRoundId
     * @return
     */
    @NotNull
    private Result<GameReportServiceProto.ResponseGetGameSettleReport> geGameSettleReportFromLiZhi(String gameRoundId, String appId) {
        GameChannelBean gameChannelBean = gameAppManager.getChannelInfoByAppIdAndChannel(appId, ChannelType.LIZHI.getName());
        if(null == gameChannelBean){
            return new Result<>(GET_GAME_SETTLE_REPORT_FAIL, null);
        }
        JSONObject body = buildPostReportBody(gameChannelBean, gameRoundId, null, null, null, false, true);

        String url = getGameChannelUrlByName(SealGameUrlConstant.LIZHI_GAME_INFO_URL, gameChannelBean);
        if(StringUtils.isBlank(url)){
            return new Result<>(GET_ROOM_GAME_REPORT_LIST_FAIL, null);
        }

        String response = httpClientManager.postRequest(url, buildPostHeaderMap(), body.toJSONString());
        if (response == null) {
            return new Result<>(GET_GAME_SETTLE_REPORT_FAIL, null);
        }
        try {
            JSONObject jsonObject = JSON.parseObject(response);
            String reportMsg = jsonObject.getString("data");
            if (reportMsg == null) {
                LogContext.addResLog("data=null");
                return new Result<>(GET_GAME_SETTLE_REPORT_NO_DATA, null);
            }
            LizhiReportGameData gameResult = JSON.parseObject(reportMsg, LizhiReportGameData.class);
            GameReportServiceProto.ResponseGetGameSettleReport.Builder builder = GameReportServiceProto.ResponseGetGameSettleReport.newBuilder();
            builder.setResult(gameResult.buildGameSettleResult());
            return new Result<>(GeneralRCode.GENERAL_RCODE_SUCCESS, builder.build());
        } catch (Exception e) {
            log.error("json to report_msg fail", e);
        }
        return new Result<>(GET_GAME_SETTLE_REPORT_NO_DATA, null);
    }

    /**
     * 从即构获取游戏结算数据
     *
     * @param gameRoundId
     * @param appId
     * @return
     */
    @NotNull
    private Result<GameReportServiceProto.ResponseGetGameSettleReport> geGameSettleReportFromSud(String gameRoundId, String appId) {
        GameChannelBean gameChannelBean = gameAppManager.getChannelInfoByAppIdAndChannel(appId, ChannelType.SUD.getName());
        if(null == gameChannelBean){
            return new Result<>(GET_GAME_SETTLE_REPORT_FAIL, null);
        }
        JSONObject body = buildPostReportBody(gameChannelBean, gameRoundId, null, null, null, true, true);

        String url = getGameChannelUrlByName(SealGameUrlConstant.SUD_GAME_INFO_URL, gameChannelBean);
        if(StringUtils.isBlank(url)){
            return new Result<>(GET_ROOM_GAME_REPORT_LIST_FAIL, null);
        }

        String response = httpClientManager.postRequest(url, buildPostHeaderMap(), body.toJSONString());
        if (response == null) {
            return new Result<>(GET_GAME_SETTLE_REPORT_FAIL, null);
        }
        try {
            JSONObject jsonObject = JSON.parseObject(response);
            JSONObject jsonData = (JSONObject) jsonObject.get("data");
            if (jsonData == null) {
                LogContext.addResLog("data=null");
                return new Result<>(GET_GAME_SETTLE_REPORT_NO_DATA, null);
            }
            String reportMsg = jsonData.getString("report_msg");
            if (Strings.isNullOrEmpty(reportMsg)) {
                LogContext.addResLog("report_msg=null");
                return new Result<>(GET_GAME_SETTLE_REPORT_NO_DATA, null);
            }
            SudGameResult sudGameResult = JSON.parseObject(reportMsg, SudGameResult.class);
            GameReportServiceProto.ResponseGetGameSettleReport.Builder builder = GameReportServiceProto.ResponseGetGameSettleReport.newBuilder();
            builder.setResult(sudGameResult.buildGameSettleResult());
            return new Result<>(GeneralRCode.GENERAL_RCODE_SUCCESS, builder.build());
        } catch (Exception e) {
            log.error("json to report_msg fail", e);
        }
        return new Result<>(GET_GAME_SETTLE_REPORT_NO_DATA, null);
    }

    private JSONObject buildPostReportBody(GameChannelBean gameChannelBean, String gameRoundId
            , String roomId, Integer pageNum, Integer pageCount, boolean sud, boolean settle){
        SealAuth sealAuth = new SealAuth(gameChannelBean.getAppId(), gameChannelBean.getAppSecret());
        if(null == sealAuth){
            return null;
        }

        JSONObject body = new JSONObject();
        if(sud){
            body.put("app_id", sealAuth.getAppId());
            body.put("app_secret", sealAuth.getAppSecret());
            if(settle){
                body.put("game_round_id", gameRoundId);
                body.put("report_type", "game_settle");
            }else {
                body.put("room_id", roomId);
                body.put("page_no", pageNum - 1);
                body.put("page_size", pageCount);
            }
        }else {
            body.put("appId", sealAuth.getAppId());
            body.put("appSecret", sealAuth.getAppSecret());
            if(settle){
                body.put("gameRoundId", gameRoundId);
                body.put("reportType", 2);
            }else {
                body.put("roomId", roomId);
                body.put("pageNum", pageNum);
                body.put("pageCount", pageCount);
            }
        }

        return body;
    }

    private Map<String, String> buildPostHeaderMap(){
        Map<String, String> headerMap = new HashMap<>();
        headerMap.put("Content-Type", "application/json");
        return headerMap;
    }

    private String getGameChannelUrlByName(String urlName, GameChannelBean bean){
        if(StringUtils.isBlank(bean.getExtConfig()) || StringUtils.isBlank(urlName)){
            return bean.getExtConfig();
        }
        JSONObject jsonObject = null;
        try {
            jsonObject = JSON.parseObject(bean.getExtConfig());
        }catch (Exception e){
            logger.warn("get game channel url by Name error.`urlName={}`channelId={}", urlName, bean.getId(), e);
            return StringUtils.EMPTY;
        }
        if(null == jsonObject){
            return StringUtils.EMPTY;
        }
        return jsonObject.getString(urlName);
    }

}
