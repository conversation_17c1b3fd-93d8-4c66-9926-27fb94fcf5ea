package fm.lizhi.ocean.seal.redis;

import org.apache.commons.lang3.StringUtils;

/**
 * Created in 2022-05-16 14:18.
 *
 * <AUTHOR>
 */
public enum RedisKeys {
    /**
     * 开始游戏的信息
     */
    START_GAME_INFO,
    /**
     * 业务自定义配置信息
     */
    GAME_BIZ_CONFIG,
    /**
     * 游戏局透传参数，临时存储
      */
    GAME_ROUND_EXTRA,
    /**
     * 用户心跳
     */
    GAME_USER_HEART_BEAT
    ;

    /**
     * 构建Key
     *
     * @param param 参数
     * @return
     */
    public String buildKey(String param) {
        if (StringUtils.isNotBlank(param)) {
            param = "_" + param;
        } else {
            param = "";
        }
        return "LZ_OCEAN_SEAL_" + this.name() + param;
    }
}
