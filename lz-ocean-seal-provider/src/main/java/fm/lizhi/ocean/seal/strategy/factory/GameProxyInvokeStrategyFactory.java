package fm.lizhi.ocean.seal.strategy.factory;

import com.google.inject.Inject;
import com.netflix.governator.annotations.AutoBindSingleton;
import fm.lizhi.ocean.seal.constant.GameChannel;
import fm.lizhi.ocean.seal.strategy.GameProxyInvokeStrategy;
import fm.lizhi.ocean.seal.strategy.impl.LukGameProxyInvokeStrategy;
import fm.lizhi.ocean.seal.strategy.impl.SudGameProxyInvokeStrategy;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;

/**
 * 游戏代理调用策略工厂
 * 根据渠道类型返回对应的策略实现
 * 
 * Created in 2025-07-01
 * 
 * <AUTHOR> Agent
 */
@Slf4j
@AutoBindSingleton
public class GameProxyInvokeStrategyFactory {
    
    @Inject
    private SudGameProxyInvokeStrategy sudStrategy;
    
    @Inject
    private LukGameProxyInvokeStrategy lukStrategy;
    
    /**
     * 根据渠道类型获取对应的策略实现
     * 
     * @param channel 渠道名称
     * @return 对应的策略实现
     * @throws IllegalArgumentException 如果没有找到对应的策略
     */
    public GameProxyInvokeStrategy getStrategy(String channel) {
        if (StringUtils.isBlank(channel)) {
            log.error("Channel is blank, cannot determine strategy");
            throw new IllegalArgumentException("Channel cannot be blank");
        }


        List<GameProxyInvokeStrategy> strategies = getSupportedChannels();
        
        GameProxyInvokeStrategy strategy = strategies.stream()
                .filter(s -> s.supports(channel))
                .findFirst()
                .orElse(null);
        
        if (strategy == null) {
            log.error("No strategy found for channel: {}", channel);
            throw new IllegalArgumentException("Unsupported channel: " + channel);
        }
        
        log.debug("Found strategy for channel: {}, strategy: {}", channel, strategy.getClass().getSimpleName());
        return strategy;
    }
    
    /**
     * 获取所有支持的渠道列表
     *
     * @return 支持的渠道列表
     */
    public List<GameProxyInvokeStrategy> getSupportedChannels() {
        return Arrays.asList(sudStrategy, lukStrategy);
    }
    
    /**
     * 检查是否支持指定的渠道
     *
     * @param channel 渠道名称
     * @return 是否支持
     */
    public boolean isChannelSupported(String channel) {
        if (StringUtils.isBlank(channel)) {
            return false;
        }
        
        try {
            getStrategy(channel);
            return true;
        } catch (IllegalArgumentException e) {
            return false;
        }
    }
}
