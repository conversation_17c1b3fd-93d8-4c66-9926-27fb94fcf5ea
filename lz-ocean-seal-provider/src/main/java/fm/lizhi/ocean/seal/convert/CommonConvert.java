package fm.lizhi.ocean.seal.convert;

import java.util.Date;

/**
 * 通用转换器, 提供一些通用的mapstruct转换方法. 使用示例:
 * <pre>{@code
 *     @Data
 *     public class SourceBean {
 *         private Date date;
 *     }
 *
 *     @Data
 *     public class TargetBean {
 *         private Long date;
 *     }
 *
 *     @Mapper(uses = CommonConverter.class)
 *     public interface MyMapper {
 *         TargetBean convert(SourceBean source);
 *     }
 * }</pre>
 */
public class CommonConvert {

    public static Long dateToLong(Date date) {
        return date != null ? date.getTime() : null;
    }

    public static Date longToDate(Long time) {
        return time != null ? new Date(time) : null;
    }
}
