package fm.lizhi.ocean.seal.dao.mapper.ext;

import fm.lizhi.common.datastore.core.annotation.DataStore;
import fm.lizhi.common.datastore.mysql.constant.ParamContants;
import fm.lizhi.common.datastore.mysql.mybatis.PageList;
import fm.lizhi.ocean.seal.dao.bean.GamePropBean;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * 游戏道具扩展数据访问接口
 * <AUTHOR>
 */
@DataStore(namespace = "mysql_ocean_oceanseal")
public interface GamePropExtMapper {

    /**
     * 根据渠道游戏ID和渠道道具ID查询道具
     * @param channelGameId 渠道游戏ID
     * @param channelPropId 渠道道具ID
     * @return 道具信息
     */
    @Select("SELECT * FROM game_prop WHERE channel_game_id = #{channelGameId} AND channel_prop_id = #{channelPropId} AND deleted = 0")
    GamePropBean selectByChannelGameIdAndChannelPropId(@Param("channelGameId") Long channelGameId, 
                                                       @Param("channelPropId") Long channelPropId);

    /**
     * 根据条件查询道具列表
     * @param channelGameId 渠道游戏ID
     * @param channelId 渠道ID
     * @param type 道具类型
     * @return 道具列表
     */
    @Select("<script>" +
            "SELECT * FROM game_prop WHERE deleted = 0 " +
            "<if test='appId != null' and appId != ''> AND app_id = #{appId} </if>" +
            "<if test='channelGameId != null' and channelGameId != ''> AND channel_game_id = #{channelGameId} </if>" +
            "<if test='channelId != null' and channelId != 0> AND channel_id = #{channelId} </if>" +
            "<if test='type != null'> AND type = #{type} </if>" +
            "ORDER BY create_time DESC " +
            "</script>")
    PageList<GamePropBean> selectByConditions(@Param("appId") String appId,
                                              @Param("channelGameId") String channelGameId,
                                              @Param("channelId") Long channelId,
                                              @Param("type") Integer type,
                                              @Param(ParamContants.PAGE_NUMBER) Integer pageNo,
                                              @Param(ParamContants.PAGE_SIZE) Integer pageSize);
}
