package fm.lizhi.ocean.seal.strategy.factory;

import com.google.inject.Inject;
import com.netflix.governator.annotations.AutoBindSingleton;
import fm.lizhi.ocean.seal.constant.GameChannel;
import fm.lizhi.ocean.seal.strategy.GameLoadingConfigStrategy;
import fm.lizhi.ocean.seal.strategy.impl.DefaultGameLoadingConfigStrategy;
import fm.lizhi.ocean.seal.strategy.impl.LukGameLoadingConfigStrategy;
import fm.lizhi.ocean.seal.strategy.impl.SudGameLoadingConfigStrategy;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;

/**
 * 游戏加载配置策略工厂
 * 根据渠道类型返回对应的策略实现
 * 
 * Created in 2025-06-27
 * 
 * <AUTHOR> Agent
 */
@Slf4j
@AutoBindSingleton
public class GameLoadingConfigStrategyFactory {
    
    @Inject
    private SudGameLoadingConfigStrategy sudStrategy;
    
    @Inject
    private LukGameLoadingConfigStrategy lukStrategy;

    @Inject
    private DefaultGameLoadingConfigStrategy defaultStrategy;
    
    /**
     * 根据渠道类型获取对应的策略实现
     * 
     * @param channel 渠道名称
     * @return 对应的策略实现，如果没有找到则返回null
     */
    public GameLoadingConfigStrategy getStrategy(String channel) {
        if (StringUtils.isBlank(channel)) {
            log.warn("Channel is blank, cannot determine strategy");
            return null;
        }
        
        // 优先匹配具体的渠道策略
        List<GameLoadingConfigStrategy> specificStrategies = Arrays.asList(
                sudStrategy, lukStrategy
        );

        for (GameLoadingConfigStrategy strategy : specificStrategies) {
            if (strategy.supports(channel)) {
                log.debug("Found specific strategy for channel: {}, strategy: {}", channel, strategy.getClass().getSimpleName());
                return strategy;
            }
        }

        // 如果没有找到具体的策略，使用默认策略
        log.warn("No specific strategy found for channel: {}, using default strategy", channel);
        return defaultStrategy;
    }
    
    /**
     * 获取所有支持的渠道列表
     *
     * @return 支持的渠道列表
     */
    public List<String> getSupportedChannels() {
        return Collections.unmodifiableList(Arrays.asList(GameChannel.SUD, GameChannel.LUK));
    }
}
