package fm.lizhi.ocean.seal.dao.bean;

import java.util.Date;
import javax.persistence.*;

/**
 * This class was generated by DataStore MyBatis Generator.
 *
 * 业务侧游戏消耗账单
 *
 * @date 2025-04-22 05:51:14
 */
@Table(name = "`game_bill`")
public class GameBillBean {
    /**
     * 主键ID
     */
    @Id
    @GeneratedValue
    @Column(name= "`id`")
    private Long id;

    /**
     * 平台分配给业务方的appId
     */
    @Column(name= "`app_id`")
    private String appId;

    /**
     * 房主ID
     */
    @Column(name= "`nj_id`")
    private Long njId;

    /**
     * 平台分配给业务方的gameId
     */
    @Column(name= "`game_id`")
    private Long gameId;

    /**
     * 用户id
     */
    @Column(name= "`user_id`")
    private Long userId;

    /**
     * 使用量(分钟)
     */
    @Column(name= "`usage`")
    private Long usage;

    /**
     * 统计日期（天）
     */
    @Column(name= "`record_day`")
    private Date recordDay;

    /**
     * 统计日期（小时）
     */
    @Column(name= "`record_hour`")
    private Integer recordHour;

    /**
     * 创建时间
     */
    @Column(name= "`create_time`")
    private Date createTime;

    /**
     * 修改时间
     */
    @Column(name= "`modify_time`")
    private Date modifyTime;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getAppId() {
        return appId;
    }

    public void setAppId(String appId) {
        this.appId = appId == null ? null : appId.trim();
    }

    public Long getNjId() {
        return njId;
    }

    public void setNjId(Long njId) {
        this.njId = njId;
    }

    public Long getGameId() {
        return gameId;
    }

    public void setGameId(Long gameId) {
        this.gameId = gameId;
    }

    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    public Long getUsage() {
        return usage;
    }

    public void setUsage(Long usage) {
        this.usage = usage;
    }

    public Date getRecordDay() {
        return recordDay;
    }

    public void setRecordDay(Date recordDay) {
        this.recordDay = recordDay;
    }

    public Integer getRecordHour() {
        return recordHour;
    }

    public void setRecordHour(Integer recordHour) {
        this.recordHour = recordHour;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getModifyTime() {
        return modifyTime;
    }

    public void setModifyTime(Date modifyTime) {
        this.modifyTime = modifyTime;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", appId=").append(appId);
        sb.append(", njId=").append(njId);
        sb.append(", gameId=").append(gameId);
        sb.append(", userId=").append(userId);
        sb.append(", usage=").append(usage);
        sb.append(", recordDay=").append(recordDay);
        sb.append(", recordHour=").append(recordHour);
        sb.append(", createTime=").append(createTime);
        sb.append(", modifyTime=").append(modifyTime);
        sb.append("]");
        return sb.toString();
    }
}