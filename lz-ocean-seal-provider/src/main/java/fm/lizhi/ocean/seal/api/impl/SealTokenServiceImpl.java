package fm.lizhi.ocean.seal.api.impl;

import com.google.inject.Inject;
import fm.lizhi.commons.service.client.annotation.ServiceProvider;
import fm.lizhi.commons.service.client.log.LogContext;
import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.game.auth.SealAuth;
import fm.lizhi.game.auth.enums.SealAuthResultCode;
import fm.lizhi.game.auth.helper.HashingTokenHelper;
import fm.lizhi.game.auth.pojo.JwtUserInfo;
import fm.lizhi.game.auth.pojo.ParseResult;
import fm.lizhi.game.auth.pojo.SealAuthToken;
import fm.lizhi.ocean.seal.api.SealTokenService;
import fm.lizhi.ocean.seal.conf.LzConfig;
import fm.lizhi.ocean.seal.dao.bean.GameAppBean;
import fm.lizhi.ocean.seal.dao.bean.GameInfoBean;
import fm.lizhi.ocean.seal.manager.GameAppManager;
import fm.lizhi.ocean.seal.manager.RedisManager;
import fm.lizhi.ocean.seal.protocol.SealTokenServiceProto;
import fm.lizhi.ocean.seal.protocol.SealTokenServiceProto.SealHashingTokenParam;
import fm.lizhi.ocean.seal.protocol.SealTokenServiceProto.ResponseGetSealHashingToken;
import fm.lizhi.ocean.seal.protocol.SealTokenServiceProto.ResponseVerifySealToken;
import fm.lizhi.ocean.seal.protocol.SealTokenServiceProto.ResponseGetUserInfoBySealToken;
import fm.lizhi.ocean.seal.redis.CommonRedisManager;
import fm.lizhi.ocean.seal.redis.SealTokenKey;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;

@Slf4j
@ServiceProvider
public class SealTokenServiceImpl implements SealTokenService {

    @Inject
    private LzConfig config;
    @Inject
    private RedisManager redisManager;
    @Inject
    private GameAppManager gameAppManager;
    @Inject
    private CommonRedisManager commonRedisManager;

    /**生成新版的jwt，主要是用于区分之前已经发版的验证方式*/
    private final String JWT_VERSION = "1";

    private final int MIN_TTL = 30;

    @Override
    public Result<ResponseGetSealHashingToken> getSealHashingToken(SealHashingTokenParam sealHashingTokenParam) {
        printGetSealHashingTokenLog(sealHashingTokenParam);
        //验证userId是否有效
        if(unNormalUserId(sealHashingTokenParam.getUserId())){
            return new Result<>(GET_SEAL_HASHING_TOKEN_FAIL, null);
        }
        //验证AppId是否存在
        GameAppBean bean = gameAppManager.getGameAppBean(sealHashingTokenParam.getAppId());
        if(null == bean){
            return new Result<>(GET_SEAL_HASHING_TOKEN_ILLEGAL_PARAMS, null);
        }

        //验证hashToken是否有效
        if(!sealHashingTokenParam.getNotVerifySign()){
            fm.lizhi.game.auth.pojo.SealHashingTokenParam verifyParam = buildSHA256TokenParam(sealHashingTokenParam, bean.getAppSecret());
            if(unableHashingToken(verifyParam)){
                return new Result<>(GET_SEAL_HASHING_TOKEN_FAIL, null);
            }
        }

        String lockKey = SealTokenKey.TOKEN_LOCK.getKey(sealHashingTokenParam.getUserId());
        if (!commonRedisManager.lockWithTimeout(lockKey, 1000L, 2100L)) {
            log.error("sealToken lock error.`lockKey={}", lockKey);
            return new Result<>(GET_SEAL_HASHING_TOKEN_ERROR, null);
        }

        SealAuthToken sealToken = null;
        try {
            sealToken = getExistJwtByUserId(bean, sealHashingTokenParam.getUserId());
            if(null == sealToken){
                //根据传入的参数生成jwt字符串
                sealToken = buildJwtToken(bean, sealHashingTokenParam);
                if(null == sealToken){
                    return new Result<>(GET_SEAL_HASHING_TOKEN_ERROR, null);
                }
                //把jwt字符串填入redis中，设置过期时间若干小时
                setJwtToRedis(sealToken.getToken(), sealHashingTokenParam.getUserId());
            }
        }catch (Exception e){
            log.error("get seal token error.`userId={}`appId={}`key={}"
                    , sealHashingTokenParam.getUserId(), sealHashingTokenParam.getAppId(), lockKey, e);
        }finally {
            commonRedisManager.releaseLock(lockKey);
        }

        LogContext.addResLog("token={}`expire={}", sealToken.getToken(), sealToken.getExpireDate());
        return new Result<>(GET_SEAL_HASHING_TOKEN_SUCCESS
                , ResponseGetSealHashingToken.newBuilder().setSealToken(sealToken.getToken())
                .setExpireDate(sealToken.getExpireDate()).build());
    }

    @Override
    public Result<ResponseVerifySealToken> verifySealToken(String sealToken, String appId) {
        String logStr = "sealToken={}`appId={}";
        LogContext.addReqLog(logStr, sealToken, appId);
        LogContext.addResLog(logStr, sealToken, appId);
        return new Result<>(verifySealToken(sealToken, appId, null), null);
    }

    @Override
    public Result<ResponseGetUserInfoBySealToken> getUserInfoBySealToken(String sealToken, String appId) {
        String logStr = "sealToken={}`appId={}";
        LogContext.addReqLog(logStr, sealToken, appId);
        LogContext.addResLog(logStr, sealToken, appId);
        //验证签名，验证通过，直接返回用户参数
        JwtUserInfo info = new JwtUserInfo();
        int resultCode = verifySealToken(sealToken, appId, info);
        LogContext.addResLog("resultCode={}", resultCode);
        if(resultCode != VERIFY_SEAL_TOKEN_SUCCESS){
            return new Result<>(VERIFY_SEAL_TOKEN_INVALID, null);
        }

        return new Result<>(VERIFY_SEAL_TOKEN_SUCCESS, ResponseGetUserInfoBySealToken.newBuilder()
                .setSealUser(SealTokenServiceProto.JwtUserInfo.newBuilder().setUserId(info.getId())
                        .setRole(info.getRole()).setUserName(info.getUsername()).build()).build());
    }

    /**
     * 验证SealToken
     * @param sealToken
     * @param appId
     * @param jwtUserInfo
     * @return
     */
    private int verifySealToken(String sealToken, String appId, JwtUserInfo jwtUserInfo){
        //sealToken判断是否为空
        if(StringUtils.isBlank(sealToken)){
            return VERIFY_SEAL_TOKEN_INVALID;
        }

        //验证AppId是否存在
        GameAppBean bean = gameAppManager.getGameAppBean(appId);
        if(null == bean){
            return VERIFY_SEAL_TOKEN_ERROR;
        }

        //解析SealToken
        JwtUserInfo resultUser = getUserIdByParseJwt(bean, sealToken);
        if(null == resultUser){
            return VERIFY_SEAL_TOKEN_INVALID;
        }

        if(null != jwtUserInfo){
            jwtUserInfo.setId(resultUser.getId());
            jwtUserInfo.setUsername(resultUser.getUsername());
            jwtUserInfo.setRole(resultUser.getRole());
        }

        //判断版本。这里是为了兼容旧版
        if(StringUtils.isBlank(resultUser.getVersion())){
            return VERIFY_SEAL_TOKEN_SUCCESS;
        }

        //如果存在，续命。如果不存在，返回无效token
        boolean existJwt = isExistJwtInRedis(sealToken, resultUser.getId());
        if(!existJwt){
            return VERIFY_SEAL_TOKEN_INVALID;
        }

        return VERIFY_SEAL_TOKEN_SUCCESS;
    }

    /**
     * 判断redis是否存在jwt(即sealToken)
     * @param jwt jwt 即 sealToken
     * @param userId
     * @return
     */
    private boolean isExistJwtInRedis(String jwt, long userId){
        String key = SealTokenKey.TOKEN.getKey(userId);
        String existJwt = redisManager.getRedisClient().get(key);
        if(StringUtils.isBlank(existJwt) || !existJwt.equals(jwt)){
            log.info("not exist jwt in redis.`userId={}`jwt={}`existJwt={}", userId, jwt, existJwt);
            return false;
        }

        int expire = (int)config.getHashingSealTokenExpire();
        Long result = redisManager.getRedisClient().expire(key, expire);
        log.info("exist jwt to redis.`key={}`expire={}`jwt={}`result={}", key, expire, jwt, result);
        return true;
    }

    /**
     * 设置jwt(即sealToken) 到redis
     * @param jwt
     * @param userId
     */
    private void setJwtToRedis(String jwt, long userId){
        String key = SealTokenKey.TOKEN.getKey(userId);
        int expire = (int)config.getHashingSealTokenExpire();
        String result = redisManager.getRedisClient().setex(key, expire, jwt);
        log.info("set jwt to redis.`key={}`expire={}`jwt={}`result={}", key, expire, jwt, result);
    }

    /**
     * 根据用户ID获取缓存中存在超过30s的sealToken.此处还会校验redis中的jwt是否已过期
     * @param bean
     * @param userId
     * @return
     */
    private SealAuthToken getExistJwtByUserId(GameAppBean bean, long userId){
        if(userId <= NumberUtils.LONG_ZERO.longValue()){
            return null;
        }

        String key = SealTokenKey.TOKEN.getKey(userId);
        if(redisManager.getRedisClient().ttl(key) > MIN_TTL){
            int expire = (int)config.getHashingSealTokenExpire();
           // long expireDate = System.currentTimeMillis() + expire;

            //jwt 为正常串.
            String sealToken = redisManager.getRedisClient().get(key);
            if(getUserIdByParseJwt(bean, sealToken) == null){
                return null;
            }

            //续命
            redisManager.getRedisClient().expire(key, expire);
            //这里返回的过期时间会存在问题。当jwt的时间为接近当前时间时，返回的过期时间就存在问题。但由于jwt的过期时间设置为一个很大的值，所以问题不大
            return new SealAuthToken(redisManager.getRedisClient().get(key), System.currentTimeMillis() + expire * 1000);
        }

        return null;
    }

    private SealAuth buildSealAuth(GameAppBean bean){
        return new SealAuth(bean.getAppId(), bean.getAppSecret());
    }

    private SealAuthToken buildJwtToken(GameAppBean bean, SealHashingTokenParam param){
        SealAuth sealAuth = buildSealAuth(bean);
        JwtUserInfo jwtUserInfo = new JwtUserInfo();
        jwtUserInfo.setId(param.getUserId());
        jwtUserInfo.setVersion(JWT_VERSION);
        return sealAuth.getSealAuthToken(jwtUserInfo, config.getJwtSealTokenExpire());
    }

    private JwtUserInfo getUserIdByParseJwt(GameAppBean bean, String jwt){
        SealAuth sealAuth = buildSealAuth(bean);
        ParseResult parseResult = sealAuth.getUserInfoInSealAuthToken(jwt);
        if(!parseResult.isSuccess()){
            log.warn("getUserIdByParseJwt fail`parseResult={}`jwt={}`appId={}", parseResult, jwt, bean.getAppId());
            return null;
        }

        JwtUserInfo parseUserInfo = parseResult.getUserInfo();
        if (parseUserInfo == null || parseUserInfo.getId() <= NumberUtils.LONG_ZERO) {
            log.warn("parse user info invalid`parseResult={}`parseUserInfo={}`jwt={}`appId={}", parseResult, parseUserInfo, jwt, bean.getAppId());
            return null;
        }
        return parseUserInfo;
    }

    private boolean unableHashingToken(fm.lizhi.game.auth.pojo.SealHashingTokenParam verifyParam){
        return !HashingTokenHelper.verifySHA256Token(verifyParam);
    }

    private fm.lizhi.game.auth.pojo.SealHashingTokenParam buildSHA256TokenParam(SealHashingTokenParam param, String appSecret){
        if(null == param){
            return null;
        }

        fm.lizhi.game.auth.pojo.SealHashingTokenParam verifyParam = new fm.lizhi.game.auth.pojo.SealHashingTokenParam();
        verifyParam.setAppId(param.getAppId());
        verifyParam.setAppSecret(appSecret);
        verifyParam.setCurrentTimeMillis(param.getCurrentTimeMillis());
        verifyParam.setUserId(param.getUserId());
        verifyParam.setHashingToken(param.getHashingToken());
        return verifyParam;
    }

    private boolean unNormalUserId(long userId){
        return userId <= NumberUtils.INTEGER_ZERO;
    }

    private void printGetSealHashingTokenLog(SealHashingTokenParam param){
        String logStr = "userId={}`appId={}`currentTimeMillis={}`hashingToken={}";
        LogContext.addReqLog(logStr, param.getUserId(), param.getAppId(), param.getCurrentTimeMillis(), param.getHashingToken());
        LogContext.addResLog(logStr, param.getUserId(), param.getAppId(), param.getCurrentTimeMillis(), param.getHashingToken());
    }
}
