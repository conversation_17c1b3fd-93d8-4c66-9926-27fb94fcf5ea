package fm.lizhi.ocean.seal.dao.bean;

import java.util.Date;
import javax.persistence.*;

/**
 * This class was generated by DataStore MyBatis Generator.
 *
 * 游戏版本表，用于管理内置下发版本
 *
 * @date 2022-08-24 11:03:37
 */
@Table(name = "`game_version`")
public class GameVersionBean {
    /**
     * 主键ID
     */
    @Id
    @GeneratedValue
    @Column(name= "`id`")
    private Long id;

    /**
     * seal 颁发给业务的appId
     */
    @Column(name= "`app_id`")
    private String appId;

    /**
     * game_biz_game表ID
     */
    @Column(name= "`game_id`")
    private Long gameId;

    /**
     * 系统版本：0 未知 1 ios 2 android
     */
    @Column(name= "`system_type`")
    private Integer systemType;

    /**
     * 最小App版本，builder号
     */
    @Column(name= "`min_sdk_version`")
    private Long minSdkVersion;

    /**
     * 游戏版本号，builder号
     */
    @Column(name= "`game_version`")
    private Long gameVersion;

    /**
     * true：强制更新，false：非强制更新
     */
    @Column(name= "`force_update`")
    private Integer forceUpdate;

    /**
     * 游戏下载链接
     */
    @Column(name= "`download_link`")
    private String downloadLink;

    /**
     * 游戏流量：0%～100%，0为关闭该游戏下载，100为开放所有用户下载。用于A/B流量
     */
    @Column(name= "`game_flow`")
    private Integer gameFlow;

    /**
     * 扩展参数，可用whiteList属性明表示白名单。标志为白名单的用户ID可直接下载
     */
    @Column(name= "`ext_config`")
    private String extConfig;

    @Column(name= "`create_time`")
    private Date createTime;

    @Column(name= "`modify_time`")
    private Date modifyTime;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getAppId() {
        return appId;
    }

    public void setAppId(String appId) {
        this.appId = appId == null ? null : appId.trim();
    }

    public Long getGameId() {
        return gameId;
    }

    public void setGameId(Long gameId) {
        this.gameId = gameId;
    }

    public Integer getSystemType() {
        return systemType;
    }

    public void setSystemType(Integer systemType) {
        this.systemType = systemType;
    }

    public Long getMinSdkVersion() {
        return minSdkVersion;
    }

    public void setMinSdkVersion(Long minSdkVersion) {
        this.minSdkVersion = minSdkVersion;
    }

    public Long getGameVersion() {
        return gameVersion;
    }

    public void setGameVersion(Long gameVersion) {
        this.gameVersion = gameVersion;
    }

    public Integer getForceUpdate() {
        return forceUpdate;
    }

    public void setForceUpdate(Integer forceUpdate) {
        this.forceUpdate = forceUpdate;
    }

    public String getDownloadLink() {
        return downloadLink;
    }

    public void setDownloadLink(String downloadLink) {
        this.downloadLink = downloadLink == null ? null : downloadLink.trim();
    }

    public Integer getGameFlow() {
        return gameFlow;
    }

    public void setGameFlow(Integer gameFlow) {
        this.gameFlow = gameFlow;
    }

    public String getExtConfig() {
        return extConfig;
    }

    public void setExtConfig(String extConfig) {
        this.extConfig = extConfig == null ? null : extConfig.trim();
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getModifyTime() {
        return modifyTime;
    }

    public void setModifyTime(Date modifyTime) {
        this.modifyTime = modifyTime;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", appId=").append(appId);
        sb.append(", gameId=").append(gameId);
        sb.append(", systemType=").append(systemType);
        sb.append(", minSdkVersion=").append(minSdkVersion);
        sb.append(", gameVersion=").append(gameVersion);
        sb.append(", forceUpdate=").append(forceUpdate);
        sb.append(", downloadLink=").append(downloadLink);
        sb.append(", gameFlow=").append(gameFlow);
        sb.append(", extConfig=").append(extConfig);
        sb.append(", createTime=").append(createTime);
        sb.append(", modifyTime=").append(modifyTime);
        sb.append("]");
        return sb.toString();
    }
}