package fm.lizhi.ocean.seal.manager;

import com.google.inject.Inject;
import com.netflix.governator.annotations.AutoBindSingleton;
import fm.lizhi.commons.util.GuidGenerator;
import fm.lizhi.ocean.seal.dao.bean.GameLanguageRelationBean;
import fm.lizhi.ocean.seal.dao.mapper.GameLanguageRelationBeanMapper;
import fm.lizhi.ocean.seal.dao.mapper.ext.GameLanguageRelationExtMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.CollectionUtils;

import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

@Slf4j
@AutoBindSingleton
public class GameLanguageRelationManager {

    @Inject
    private GuidGenerator guidGenerator;

    @Inject
    private GameLanguageRelationExtMapper gameLanguageRelationExtMapper;

    @Inject
    private GameLanguageRelationBeanMapper gameLanguageRelationBeanMapper;

    /**
     * 批量插入或者修改游戏信息与语言关联表
     * @param gameInfoId
     * @param abbreviations
     * @param insert
     */
    public void insertOrUpdateGameLanguageByGameId(long gameInfoId, List<String> abbreviations, boolean insert){
        if(!insert){
            //不是插入，先删除关联表
            gameLanguageRelationExtMapper.delRelationByGameId(gameInfoId);
        }

        if(CollectionUtils.isEmpty(abbreviations)){
            return;
        }

        Date date = new Date();
        gameLanguageRelationBeanMapper.batchInsert(abbreviations.stream().map(x->{
                GameLanguageRelationBean bean = new GameLanguageRelationBean();
                bean.setId(guidGenerator.genId());
                bean.setGameId(gameInfoId);
                bean.setLanguageAbbreviation(x);
                bean.setCreateTime(date);
                bean.setModifyTime(date);
                return bean;
            }).collect(Collectors.toList()));
    }

    public List<String> getGameLanguageByGameId(long gameId){
        List<GameLanguageRelationBean> beans = getGameLanguageRelationsByGameId(gameId);
        if(null == beans){
            return Collections.EMPTY_LIST;
        }

        return beans.stream().map(x->x.getLanguageAbbreviation()).collect(Collectors.toList());
    }

    public List<GameLanguageRelationBean> getGameLanguageRelationsByGameId(long gameId){
        GameLanguageRelationBean bean = new GameLanguageRelationBean();
        bean.setGameId(gameId);
        return gameLanguageRelationBeanMapper.selectMany(bean);
    }
}
