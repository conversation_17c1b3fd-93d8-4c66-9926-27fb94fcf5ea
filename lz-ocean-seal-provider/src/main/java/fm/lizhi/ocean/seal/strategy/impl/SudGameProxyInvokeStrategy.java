package fm.lizhi.ocean.seal.strategy.impl;

import cn.hutool.crypto.digest.HMac;
import cn.hutool.crypto.digest.HmacAlgorithm;
import com.alibaba.fastjson.JSONObject;
import com.google.inject.Inject;
import com.googlecode.protobuf.format.JsonFormat;
import com.netflix.governator.annotations.AutoBindSingleton;
import fm.lizhi.commons.config.util.JsonUtil;
import fm.lizhi.ocean.seal.constant.GameChannel;
import fm.lizhi.ocean.seal.constant.GameOperateEventMapping;
import fm.lizhi.ocean.seal.constant.GamePropEventMapping;
import fm.lizhi.ocean.seal.dao.bean.GameBizGameBean;
import fm.lizhi.ocean.seal.dao.bean.GameCallback;
import fm.lizhi.ocean.seal.dao.bean.GameChannelBean;
import fm.lizhi.ocean.seal.dao.bean.GameInfoBean;
import fm.lizhi.ocean.seal.http.HttpClient;
import fm.lizhi.ocean.seal.manager.GameCallbackManager;
import fm.lizhi.ocean.seal.protocol.GameProxyServiceProto;
import fm.lizhi.ocean.seal.strategy.GameProxyInvokeStrategy;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.util.HashMap;
import java.util.Map;
import java.util.Optional;
import java.util.UUID;

/**
 * SUD 渠道游戏代理调用策略实现
 * 使用 HTTP 请求方式调用 SUD 渠道接口
 * 
 * Created in 2025-07-01
 * 
 * <AUTHOR> Agent
 */
@Slf4j
@AutoBindSingleton
public class SudGameProxyInvokeStrategy implements GameProxyInvokeStrategy {
    
    @Inject
    private HttpClient httpClient;
    @Inject
    private GameCallbackManager gameCallbackManager;
    
    @Override
    public GameProxyServiceProto.ResponseInvokeTarget invokeTarget(
            GameProxyServiceProto.InvokeTargetParams param,
            GameBizGameBean bizGameBean,
            GameInfoBean gameInfoBean, 
            GameChannelBean gameChannelBean) {
        String jsonParams = JsonFormat.printToString(param);

        // 对于 HTTP 调用的渠道，需要获取目标URL
        String targetUrl = getTargetInterfaceUrl(param, bizGameBean);
        if (targetUrl == null) {
            log.error("Call invoke target interface error, url is null, params:{}", jsonParams);
            throw new RuntimeException("target url is null");
        }

        try {
            log.info("SUD strategy invoke target, gameId: {}, channelGameId: {}", bizGameBean.getId(), gameInfoBean.getChannelGameIdStr());

            // 构建 HTTP 请求
            Map<String, String> headers = new HashMap<>();
            headers.put("Content-Type", "application/json;charset=utf-8");
            
            String body = buildSudHttpRequest(headers, param, gameInfoBean, gameChannelBean);
            
            // 发送 HTTP 请求
            JSONObject result = httpClient.postJsonObject(targetUrl, null, headers, body);
            log.info("SUD strategy invoke result: {}, body: {}", result, body);
            
            // 构建响应
            return buildResponse(result);
            
        } catch (Exception e) {
            log.error("SUD strategy invoke target failed, gameId: {}, url: {}", 
                     bizGameBean.getId(), targetUrl, e);
            throw new RuntimeException("SUD invoke target failed", e);
        }
    }
    
    /**
     * 构建 SUD HTTP 请求
     */
    private String buildSudHttpRequest(Map<String, String> headers, 
                                      GameProxyServiceProto.InvokeTargetParams param,
                                      GameInfoBean gameInfoBean, 
                                      GameChannelBean gameChannelBean) {
        
        String gameId = gameInfoBean.getChannelGameIdStr();
        String timestamp = System.currentTimeMillis() + "";
        
        // 构建请求体
        Map<String, Object> entry = new HashMap<>();

        // 优先从道具事件映射中查找，如果没有则从游戏操作事件映射中查找
        Object channelEvent = GamePropEventMapping.getChannelEvent(GameChannel.SUD, param.getEventName());
        if (channelEvent == null) {
            channelEvent = GameOperateEventMapping.getChannelEvent(GameChannel.SUD, param.getEventName());
        }

        entry.put("event", channelEvent);
        entry.put("mg_id", gameId);
        entry.put("timestamp", timestamp);
        Map dataMap = JSONObject.parseObject(param.getDataJson(), Map.class);
        entry.put("data", dataMap);
        
        String nonce = UUID.randomUUID().toString();
        String body = JsonUtil.dumps(entry);
        
        // SUD 签名逻辑
        String signContent = String.format("%s\n%s\n%s\n%s\n", 
                                          gameChannelBean.getAppId(), timestamp, nonce, body);
        HMac hMac = new HMac(HmacAlgorithm.HmacSHA1, gameChannelBean.getAppSecret().getBytes());
        String signature = hMac.digestHex(signContent);
        
        // 设置 SUD 认证头
        headers.put("Authorization", 
                   "Sud-Auth app_id=\""+gameChannelBean.getAppId()+
                   "\",nonce=\""+nonce+"\",timestamp=\""+timestamp+
                   "\",signature=\""+signature+"\"");
        
        return body;
    }
    
    /**
     * 构建响应对象
     */
    private GameProxyServiceProto.ResponseInvokeTarget buildResponse(JSONObject result) {
        GameProxyServiceProto.ResponseInvokeTarget.Builder builder = 
                GameProxyServiceProto.ResponseInvokeTarget.newBuilder();
        
        if (result.containsKey("ret_code") && result.containsKey("ret_msg")) {
            builder.setBizCode(result.getIntValue("ret_code"));
            builder.setMsg(Optional.ofNullable(result.getString("ret_msg")).orElse(StringUtils.EMPTY));
            builder.setData(Optional.ofNullable(result.getString("data")).orElse(StringUtils.EMPTY));
            return builder.build();
        } else {
            log.warn("SUD strategy result illegal: {}", result);
            throw new RuntimeException("SUD result illegal: " + result);
        }
    }


    /**
     * 获取目标接口的URL地址
     * 游戏调用业务, type = 1，需要传入该游戏调用哪个业务。 所以appId 为 业务的appId.  gameId 为 渠道的游戏ID
     * <p>
     * 业务调用游戏 type = 2, 需要传入调用的
     *
     * @param param 参数
     * @return
     */
    private String getTargetInterfaceUrl(GameProxyServiceProto.InvokeTargetParams param, GameBizGameBean bizGameBean) {
        String url = null;
        GameCallback gameCallback = gameCallbackManager.getGameCallback(param.getAppId(), String.valueOf(bizGameBean.getId()), gameCallbackManager.getCallBackTypeByGameInterface(), "push_event");
        if (gameCallback == null || StringUtils.isBlank(gameCallback.getUrl())) {
            return null;
        }

        if (StringUtils.isNotBlank(gameCallback.getUrl())) {
            url = gameCallback.getUrl();
        }
        return url;
    }
    
    @Override
    public boolean supports(String channel) {
        return GameChannel.SUD.equals(channel);
    }
}
