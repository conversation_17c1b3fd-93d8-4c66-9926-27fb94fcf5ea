 // FIXME 2025-02-27 精简暂时用不到的代码

package fm.lizhi.ocean.seal.cronjob;


import fm.lizhi.ocean.seal.redis.KeyGenerator;

/**
 * <AUTHOR>
 * @date // :
 */
public enum GameReportKey implements KeyGenerator.CacheKeyType {

    /**
     * 游戏上报环境
     */
    GAME_REPORT_ENV,

    /**
     * 游戏上报数据
     */
    GAME_REPORT_DATA,

    ;

    @Override
    public String getPrefix() {
        return "SEAL";
    }


}
