package fm.lizhi.ocean.seal.pojo.bo.luk;

import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
public class GrantPropDetail {

    /**
     * 游戏内定义的道具 ID
     */
    private String prop_id;

    /**
     * 大于 0 的发放数量
     */
    private int num;

    /**
     * 有效时长（单位：秒），小于 0 为永久
     */
    private long duration;

    /**
     * 仅时效性道具有效，移除后再发放（用于重置过期时间）
     */
    private boolean duration_reset;

}
