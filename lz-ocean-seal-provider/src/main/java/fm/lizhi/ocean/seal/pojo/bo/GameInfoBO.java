package fm.lizhi.ocean.seal.pojo.bo;

import fm.lizhi.ocean.seal.dao.bean.GameInfoBean;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * Created in 2022-01-28 14:58.
 *
 * <AUTHOR>
 */
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Data
public class GameInfoBO {
    private long id;

    /**
     * 用户id
     */
    private long channelGameId;

    /**
     * 游戏名称
     */
    private String name;

    /**
     * 游戏描述
     */
    private String desc;

    /**
     * 扩展参数
     */
    private String extraJson;

    /**
     * 游戏来源渠道
     */
    private String channel;

    /**
     * 构建游戏信息
     *
     * @param gameInfoPO
     * @return
     */
    public static GameInfoBO buildGameInfo(GameInfoBean gameInfoPO) {
        return GameInfoBO.builder().id(gameInfoPO.getId()).channelGameId(gameInfoPO.getChannelGameId())
                .channel(gameInfoPO.getChannel()).name(gameInfoPO.getName()).desc(gameInfoPO.getDesc())
                .build();
    }
}
