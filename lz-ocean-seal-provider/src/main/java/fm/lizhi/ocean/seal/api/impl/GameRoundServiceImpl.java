package fm.lizhi.ocean.seal.api.impl;

import com.google.inject.Inject;
import com.googlecode.protobuf.format.JsonFormat;
import fm.lizhi.commons.service.client.annotation.ServiceProvider;
import fm.lizhi.commons.service.client.log.LogContext;
import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.commons.service.client.rcode.GeneralRCode;
import fm.lizhi.ocean.seal.api.GameRoundService;
import fm.lizhi.ocean.seal.dao.bean.GameBizGameBean;
import fm.lizhi.ocean.seal.dao.bean.GameRoundBean;
import fm.lizhi.ocean.seal.manager.BizGameManager;
import fm.lizhi.ocean.seal.manager.GameInfoManager;
import fm.lizhi.ocean.seal.manager.GameRoundManager;
import fm.lizhi.ocean.seal.protocol.GameRoundServiceProto;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * 自研游戏用的？
 *
 * Created in 2022-05-11 17:30.
 *
 * <AUTHOR>
 */
@Deprecated
@ServiceProvider
public class GameRoundServiceImpl implements GameRoundService {
    private static final Logger logger = LoggerFactory.getLogger(GameRoundServiceImpl.class);
    @Inject
    private GameRoundManager gameRoundManager;
    @Inject
    private BizGameManager bizGameManager;
    @Inject
    private GameInfoManager gameInfoManager;
    /**
     * 关联游戏局信息
     *
     * @param param 参数
     * @return //if rcode == 1 参数非法<br>
     * //if rcode == 2 平台局ID不存在<br>
     * //if rcode == 3 内部错误<br>
     */
    @Override
    public Result<GameRoundServiceProto.ResponseRelationGameRound> relationGameRound(GameRoundServiceProto.RelationGameRoundParam param) {
        GameRoundServiceProto.ResponseRelationGameRound.Builder builder = GameRoundServiceProto.ResponseRelationGameRound.newBuilder();
        String channelRoundId = param.getChannelRoundId();
        String extra = param.getExtra();
        LogContext.addReqLog("channelRoundId={}`extra={}", channelRoundId, extra);
        LogContext.addResLog("channelRoundId={}`extra={}", channelRoundId, extra);
        logger.info("Relation game round info, channelRoundId:{}, extra:{}", channelRoundId, extra);
        this.gameRoundManager.relationGameRound(channelRoundId, extra);
        return new Result<>(GeneralRCode.GENERAL_RCODE_SUCCESS, builder.build());
    }

    /**
     * 开始平台游戏局，客户端在开始游戏的时候调用
     *
     * @param param 参数
     * @return //if rcode == 1 参数非法<br>
     * //if rcode == 2 游戏ID不存在<br>
     * //if rcode == 3 内部错误<br>
     */
    @Override
    public Result<GameRoundServiceProto.ResponseStartGameRound> startGameRound(GameRoundServiceProto.StartGameRoundParam param) {
        GameRoundServiceProto.ResponseStartGameRound.Builder builder = GameRoundServiceProto.ResponseStartGameRound.newBuilder();
        String gameId = param.getGameId();
        String appId = param.getAppId();
        String groupId = param.getGroupId();
        String extra = param.getExtra();
        try {
            if (StringUtils.isEmpty(gameId) || StringUtils.isEmpty(appId) | StringUtils.isEmpty(groupId)) {
                logger.warn("Failed to start game round, param error, gameId:{}, appId:{}, groupId:{}, extra:{}", gameId, appId, groupId, extra);
                return new Result<>(START_GAME_ROUND_ILLEGAL_PARAMS, builder.build());
            }

            // 查询游戏信息
            GameBizGameBean bizGameBean = this.bizGameManager.getGame(appId, gameId);
            if (bizGameBean == null) {
                logger.error("Failed to start game round, game not exists, gameId:{}, appId:{}, groupId:{}, extra:{}", gameId, appId, groupId, extra);
                return new Result<>(START_GAME_ROUND_NOT_EXISTS, builder.build());
            }

            // 开始一局游戏
            long roundId = this.gameRoundManager.startGameRound(bizGameBean, gameId, appId, groupId, extra);
            logger.info("Start the game round, gameId:{}, appId:{}, groupId:{}, extra:{}, roundId:{}", gameId, appId, groupId, extra, roundId);
            builder.setGameRound(GameRoundServiceProto.GameRound.newBuilder().setRoundId(roundId).build());
            return new Result<>(GeneralRCode.GENERAL_RCODE_SUCCESS, builder.build());
        } catch (Exception e) {
            logger.error("Exception to start game round, gameId:{}, appId:{}, groupId:{}, extra:{}, msg:{}", gameId, appId, groupId, extra, e.getMessage(), e);
        }
        return new Result<>(START_GAME_ROUND_ERROR, builder.build());
    }

    /**
     * 开始平台游戏局，客户端在开始游戏的时候调用
     *
     * @param param 参数
     * @return //if rcode == 1 参数非法<br>
     * //if rcode == 2 游戏ID不存在<br>
     * //if rcode == 3 内部错误<br>
     */
    @Override
    public Result<GameRoundServiceProto.ResponseEndGameRound> endGameRound(GameRoundServiceProto.EndGameRoundParam param) {
        String params = JsonFormat.printToString(param);
        logger.info("End game round, params:{}", params);
        GameRoundServiceProto.ResponseEndGameRound.Builder builder = GameRoundServiceProto.ResponseEndGameRound.newBuilder();
        this.gameRoundManager.endGameRound(param);
        return new Result<>(GeneralRCode.GENERAL_RCODE_SUCCESS, builder.build());
    }
}