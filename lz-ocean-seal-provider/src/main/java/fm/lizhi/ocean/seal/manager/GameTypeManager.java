package fm.lizhi.ocean.seal.manager;


import com.google.inject.Inject;
import com.netflix.governator.annotations.AutoBindSingleton;
import fm.lizhi.commons.util.GuidGenerator;
import fm.lizhi.ocean.seal.dao.bean.GameTypeBean;
import fm.lizhi.ocean.seal.dao.mapper.GameTypeBeanMapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.util.Date;
import java.util.List;
import java.util.Optional;

@Slf4j
@AutoBindSingleton
public class GameTypeManager {
    @Inject
    private GuidGenerator guidGenerator;
    @Inject
    private GameTypeBeanMapper gameTypeBeanMapper;

    public void insertGameType(String typeName){
        Date date = new Date();
        GameTypeBean bean = new GameTypeBean();
        bean.setId(guidGenerator.genId());
        bean.setName(Optional.ofNullable(typeName).orElse(StringUtils.EMPTY));
        bean.setModifyTime(date);
        bean.setCreateTime(date);
        gameTypeBeanMapper.insert(bean);
    }

    public List<GameTypeBean> getAllGameTypes(){
        return gameTypeBeanMapper.selectMany(new GameTypeBean());
    }
}
