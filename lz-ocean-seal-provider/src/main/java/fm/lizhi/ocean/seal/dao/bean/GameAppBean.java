package fm.lizhi.ocean.seal.dao.bean;

import java.util.Date;
import javax.persistence.*;

/**
 * This class was generated by DataStore MyBatis Generator.
 *
 * 业务信息表，存储业务的配置信息
 *
 * @date 2022-04-25 11:27:46
 */
@Table(name = "`game_app`")
public class GameAppBean {
    /**
     * 主键ID
     */
    @Id
    @Column(name= "`id`")
    private Long id;

    /**
     * 平台分配给业务方的appId
     */
    @Column(name= "`app_id`")
    private String appId;

    /**
     * app秘钥
     */
    @Column(name= "`app_secret`")
    private String appSecret;

    /**
     * app中文名称
     */
    @Column(name= "`app_name`")
    private String appName;

    /**
     * app别名，代号
     */
    @Column(name= "`app_alias`")
    private String appAlias;

    /**
     * 消息topic，用于事件信息广播
     */
    @Column(name= "`app_topic`")
    private String appTopic;

    /**
     * 0：正常 1：删除
     */
    @Column(name= "`del`")
    private Integer del;

    /**
     * 操作者
     */
    @Column(name= "`operator`")
    private String operator;

    /**
     * 创建时间
     */
    @Column(name= "`create_time`")
    private Date createTime;

    /**
     * 修改时间
     */
    @Column(name= "`modify_time`")
    private Date modifyTime;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getAppId() {
        return appId;
    }

    public void setAppId(String appId) {
        this.appId = appId == null ? null : appId.trim();
    }

    public String getAppSecret() {
        return appSecret;
    }

    public void setAppSecret(String appSecret) {
        this.appSecret = appSecret == null ? null : appSecret.trim();
    }

    public String getAppName() {
        return appName;
    }

    public void setAppName(String appName) {
        this.appName = appName == null ? null : appName.trim();
    }

    public String getAppAlias() {
        return appAlias;
    }

    public void setAppAlias(String appAlias) {
        this.appAlias = appAlias == null ? null : appAlias.trim();
    }

    public String getAppTopic() {
        return appTopic;
    }

    public void setAppTopic(String appTopic) {
        this.appTopic = appTopic == null ? null : appTopic.trim();
    }

    public Integer getDel() {
        return del;
    }

    public void setDel(Integer del) {
        this.del = del;
    }

    public String getOperator() {
        return operator;
    }

    public void setOperator(String operator) {
        this.operator = operator == null ? null : operator.trim();
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getModifyTime() {
        return modifyTime;
    }

    public void setModifyTime(Date modifyTime) {
        this.modifyTime = modifyTime;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", appId=").append(appId);
        sb.append(", appSecret=").append(appSecret);
        sb.append(", appName=").append(appName);
        sb.append(", appAlias=").append(appAlias);
        sb.append(", appTopic=").append(appTopic);
        sb.append(", del=").append(del);
        sb.append(", operator=").append(operator);
        sb.append(", createTime=").append(createTime);
        sb.append(", modifyTime=").append(modifyTime);
        sb.append("]");
        return sb.toString();
    }
}