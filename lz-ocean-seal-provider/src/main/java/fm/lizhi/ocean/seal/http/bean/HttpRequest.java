package fm.lizhi.ocean.seal.http.bean;

import fm.lizhi.ocean.seal.http.constants.HttpMethod;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.HashMap;
import java.util.Map;

/**
 * Created in 2022-05-30 15:44.
 *
 * <AUTHOR>
 */
public class HttpRequest {
    private static Logger logger = LoggerFactory.getLogger(HttpRequest.class);
    /**
     * 请求地址
     */
    private String uri;
    /**
     * 请求方法
     */
    private HttpMethod method = HttpMethod.GET;
    /**
     * 请求参数
     */
    private Map<String, Object> params = new HashMap<>();
    /**
     * header头
     */
    private Map<String, Object> headers = new HashMap<>();
    /**
     * cookies
     */
    private Map<String, Object> cookies = new HashMap<>();

    public String getUri() {
        return uri;
    }

    public HttpMethod getMethod() {
        return method;
    }

    public Map<String, Object> getParams() {
        return params;
    }

    public Map<String, Object> getHeaders() {
        return headers;
    }

    public Map<String, Object> getCookies() {
        return cookies;
    }

    /**
     * 私有化构造方法，不能构建实例
     */
    private HttpRequest(String uri) {
        this.uri = uri;
    }

    @Override
    public String toString() {
        return "HttpRequest{" +
                "uri='" + uri + '\'' +
                ", method=" + method +
                ", params=" + params +
                ", headers=" + headers +
                ", cookies=" + cookies +
                '}';
    }

    /**
     * 创建一个构建器
     *
     * @param uri 资源地址
     * @return
     */
    public static Builder newBuilder(String uri) {
        return new Builder(uri);
    }

    static class Builder {
        private HttpRequest httpRequest;

        private Builder(String uri) {
            this.httpRequest = new HttpRequest(uri);
        }

        /**
         * 设置请求参数
         *
         * @param name  参数名
         * @param value 参数值
         * @return
         */
        public Builder addParameter(String name, Object value) {
            this.httpRequest.params.put(name, value);
            return this;
        }

        /**
         * 设置请求头
         *
         * @param name  头名
         * @param value 请求头值
         * @return
         */
        public Builder addHeader(String name, Object value) {
            this.httpRequest.headers.put(name, value);
            return this;
        }

        /**
         * 设置Cookie
         *
         * @param name  Cookie名
         * @param value Cookie值
         * @return
         */
        public Builder addCookie(String name, Object value) {
            this.httpRequest.cookies.put(name, value);
            return this;
        }

        /**
         * 设置请求方法
         *
         * @param method 请求方法
         * @return
         */
        public Builder setMethod(HttpMethod method) {
            this.httpRequest.method = method;
            return this;
        }

        /**
         * 构建请求
         *
         * @return
         */
        public HttpRequest build() {
            return this.httpRequest;
        }
    }
}