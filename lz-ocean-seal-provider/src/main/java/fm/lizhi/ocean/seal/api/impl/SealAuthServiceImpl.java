package fm.lizhi.ocean.seal.api.impl;

import fm.lizhi.commons.service.client.annotation.ServiceProvider;
import fm.lizhi.commons.service.client.log.LogContext;
import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.commons.service.client.rcode.GeneralRCode;
import fm.lizhi.game.auth.SealAuth;
import fm.lizhi.game.auth.enums.SealAuthResultCode;
import fm.lizhi.game.auth.pojo.JwtUserInfo;
import fm.lizhi.game.auth.pojo.ParseResult;
import fm.lizhi.game.auth.pojo.SealAuthToken;
import fm.lizhi.ocean.seal.api.SealAuthService;
import fm.lizhi.ocean.seal.conf.LzConfig;
import fm.lizhi.ocean.seal.constant.ChannelType;
import fm.lizhi.ocean.seal.constant.SealRCode;
import fm.lizhi.ocean.seal.dao.bean.GameUserPO;
import fm.lizhi.ocean.seal.protocol.GameAuthServiceProto;
import fm.lizhi.ocean.seal.protocol.SealAuthServiceProto;
import fm.lizhi.pongpong.middle.user.api.token.PongTokenService;
import fm.lizhi.pongpong.middle.user.protocol.token.PongTokenProto;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import tech.sud.mgp.auth.ErrorCodeEnum;
import tech.sud.mgp.auth.api.SudUid;

import javax.inject.Inject;


/**
 * 2022 年5月22号废弃该类。由于旧版本占用了这个op，所以并未马上删除该类
 */
@Slf4j
@Deprecated
@ServiceProvider
public class SealAuthServiceImpl implements SealAuthService {
    @Inject
    private LzConfig lzConfig;

    @Deprecated
    @Override
    public Result<SealAuthServiceProto.ResponseGetSealToken> getSealToken(long userId, int appId, String channel, String checkCode) {
        log.error("调用了废弃的接口");
        return new Result<>(SealAuthService.GET_SEAL_TOKEN_FAIL, null);

        //        String logStr = "userId={}`checkCode={}`channel={}";
//        LogContext.addReqLog(logStr, userId, checkCode, channel);
//        LogContext.addResLog(logStr, userId, checkCode, channel);
//        SealAuthServiceProto.ResponseGetSealToken.Builder resp = SealAuthServiceProto.ResponseGetSealToken
//                .newBuilder();
//
//        try {
//            SealAuth sealAuth = new SealAuth("30129761", "AFJIOENGFUNWQ141298IE");
//            JwtUserInfo jwtUserInfo = new JwtUserInfo();
//            jwtUserInfo.setId(userId);
//            SealAuthToken sdkToken = sealAuth.getSealAuthToken(jwtUserInfo);
//            SealAuthServiceProto.SealToken.Builder token = SealAuthServiceProto.SealToken.newBuilder()
//                    .setToken(sdkToken.getToken())
//                    .setExpireDate(sdkToken.getExpireDate());
//            resp.setToken(token);
//            return new Result<>(GeneralRCode.GENERAL_RCODE_SUCCESS, resp.build());
//        } catch (Exception e) {
//            log.error("get seal sdk err;userId={};appId={};channel={}", userId, appId, channel, e);
//            return new Result<>(SealAuthService.GET_SEAL_TOKEN_FAIL, resp.build());
//        }

    }

    @Override
    public Result<SealAuthServiceProto.ResponseGetRefreshSealToken> getRefreshSealToken(String token, String channel) {
        log.error("调用了废弃的接口");
        return new Result<>(SealAuthService.GET_SEAL_TOKEN_FAIL, null);
        //        String logStr = "token={}`channel={}";
//        LogContext.addReqLog(logStr, token, channel);
//        LogContext.addResLog(logStr, token, channel);
//        SealAuthServiceProto.ResponseGetRefreshSealToken.Builder resp = SealAuthServiceProto.ResponseGetRefreshSealToken.newBuilder();
//        try {
//            SealAuth sealAuth = new SealAuth("30129761", "AFJIOENGFUNWQ141298IE");
//            ParseResult parseResult = sealAuth.getUserInfoInSealAuthToken(token);
//            JwtUserInfo parseUserInfo = parseResult.getUserInfo();
//            if(!(parseResult.isSuccess() || parseResult.getErrorCode() == SealAuthResultCode.TOKEN_EXPIRED)) {
//                log.warn("parse SealSDK Token fail;parseResult={};channel={};sealToken={}", parseResult, channel, token);
//                return new Result<>(GET_USER_INFO_BY_SEAL_TOKEN_FAIL, resp.build());
//            }
//            if (parseUserInfo == null || parseUserInfo.getId() <= 0L) {
//                log.warn("parse SealSDK Token userInfo invalid;parseResult={};channel={};sealToken={};userInfo={}", parseResult, channel, token, parseUserInfo);
//                return new Result<>(GET_USER_INFO_BY_SEAL_TOKEN_FAIL, resp.build());
//            }
//            JwtUserInfo jwtUserInfo = new JwtUserInfo();
//            jwtUserInfo.setId(parseUserInfo.getId());
//            jwtUserInfo.setUsername(parseUserInfo.getUsername());
//            jwtUserInfo.setRole(parseUserInfo.getRole());
//            SealAuthToken sdkToken = sealAuth.getSealAuthToken(jwtUserInfo);
//            SealAuthServiceProto.SealToken.Builder refreshToken = SealAuthServiceProto.SealToken.newBuilder()
//                    .setToken(sdkToken.getToken())
//                    .setExpireDate(sdkToken.getExpireDate());
//            resp.setToken(refreshToken);
//            return new Result<>(GeneralRCode.GENERAL_RCODE_SUCCESS, resp.build());
//        } catch (Exception e) {
//            log.error("refresh seal sdk err;token={};channel={}", token, channel, e);
//            return new Result<>(SealAuthService.GET_SEAL_TOKEN_FAIL, resp.build());
//        }

    }

    @Override
    public Result<SealAuthServiceProto.ResponseGetUserInfoBySealToken> getUserInfoBySealToken(String token, String appId, String channel) {
        String logStr = "token={}`channel={}";
        LogContext.addReqLog(logStr, token, channel);
        LogContext.addResLog(logStr, token, channel);
        SealAuthServiceProto.ResponseGetUserInfoBySealToken.Builder resp = SealAuthServiceProto.ResponseGetUserInfoBySealToken.newBuilder();
        try {
            SealAuth sealAuth = new SealAuth("30129761", "AFJIOENGFUNWQ141298IE");
            ParseResult parseResult = sealAuth.getUserInfoInSealAuthToken(token);
            JwtUserInfo userInfo = parseResult.getUserInfo();
            if(!(parseResult.isSuccess() || parseResult.getErrorCode() == SealAuthResultCode.TOKEN_EXPIRED) || userInfo == null) {
                log.warn("parse SealSDK Token fail;parseResult={};appId={};channel={};sealToken={}", parseResult, appId, channel, token);
                return new Result<>(GET_USER_INFO_BY_SEAL_TOKEN_FAIL, resp.build());
            }
            if (userInfo.getId() <= 0L) {
                log.warn("parse SealSDK Token userInfo invalid;parseResult={};appId={};channel={};sealToken={};userInfo={}", parseResult, appId, channel, token, userInfo);
                return new Result<>(GET_USER_INFO_BY_SEAL_TOKEN_FAIL, resp.build());
            }
            SealAuthServiceProto.JwtUserInfo.Builder builder = SealAuthServiceProto.JwtUserInfo.newBuilder();
            resp.setSealUser(builder.setUserId(userInfo.getId()).setUserName(userInfo.getUsername()).setRole(userInfo.getRole())).build();
            if(parseResult.getErrorCode() == SealAuthResultCode.TOKEN_EXPIRED) {
                return new Result<>(GET_USER_INFO_BY_SEAL_TOKEN_TOKEN_EXPIRED, resp.build());
            } else {
                return new Result<>(GeneralRCode.GENERAL_RCODE_SUCCESS, resp.build());
            }
        } catch (Exception e) {
            log.error("seal sdk getUidBySSToken fail", e);
        }
        return new Result<>(SealRCode.SEAL_RCODE_GAME_SDK_UNKNOWN_ERROR, resp.build());
    }
}
