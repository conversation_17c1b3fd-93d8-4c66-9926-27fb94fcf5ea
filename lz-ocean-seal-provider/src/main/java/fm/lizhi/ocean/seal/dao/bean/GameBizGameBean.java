package fm.lizhi.ocean.seal.dao.bean;

import java.util.Date;
import javax.persistence.*;

/**
 * This class was generated by DataStore MyBatis Generator.
 *
 * 业务游戏信息表
 *
 * @date 2022-05-06 11:53:38
 */
@Table(name = "`game_biz_game`")
public class GameBizGameBean {
    /**
     * 游戏ID
     */
    @Id
    @GeneratedValue
    @Column(name= "`id`")
    private Long id;

    /**
     * game_app表Id
     */
    @Column(name= "`game_app_id`")
    private Long gameAppId;

    /**
     * 游戏信息表ID
     */
    @Column(name= "`game_info_id`")
    private Long gameInfoId;

    /**
     * 个性化配置参数
     */
    @Column(name= "`config`")
    private String config;

    /**
     * 渠道ID
     */
    @Column(name= "`channel_id`")
    private Long channelId;

    /**
     * 0：正常 1：删除
     */
    @Column(name= "`del`")
    private Integer del;

    /**
     * 操作者
     */
    @Column(name= "`operator`")
    private String operator;

    /**
     * 创建时间
     */
    @Column(name= "`create_time`")
    private Date createTime;

    /**
     * 修改时间
     */
    @Column(name= "`modify_time`")
    private Date modifyTime;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getGameAppId() {
        return gameAppId;
    }

    public void setGameAppId(Long gameAppId) {
        this.gameAppId = gameAppId;
    }

    public Long getGameInfoId() {
        return gameInfoId;
    }

    public void setGameInfoId(Long gameInfoId) {
        this.gameInfoId = gameInfoId;
    }

    public String getConfig() {
        return config;
    }

    public void setConfig(String config) {
        this.config = config == null ? null : config.trim();
    }

    public Long getChannelId() {
        return channelId;
    }

    public void setChannelId(Long channelId) {
        this.channelId = channelId;
    }

    public Integer getDel() {
        return del;
    }

    public void setDel(Integer del) {
        this.del = del;
    }

    public String getOperator() {
        return operator;
    }

    public void setOperator(String operator) {
        this.operator = operator == null ? null : operator.trim();
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getModifyTime() {
        return modifyTime;
    }

    public void setModifyTime(Date modifyTime) {
        this.modifyTime = modifyTime;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", gameAppId=").append(gameAppId);
        sb.append(", gameInfoId=").append(gameInfoId);
        sb.append(", config=").append(config);
        sb.append(", channelId=").append(channelId);
        sb.append(", del=").append(del);
        sb.append(", operator=").append(operator);
        sb.append(", createTime=").append(createTime);
        sb.append(", modifyTime=").append(modifyTime);
        sb.append("]");
        return sb.toString();
    }
}