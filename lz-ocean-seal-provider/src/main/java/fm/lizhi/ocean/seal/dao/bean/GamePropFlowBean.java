package fm.lizhi.ocean.seal.dao.bean;

import java.util.Date;
import javax.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

/**
 * This class was generated by DataStore MyBatis Generator.
 *
 * 游戏道具流水
 *
 * @date 2025-07-02 07:48:52
 */
@Table(name = "`game_prop_flow`")
@Getter
@Setter
@ToString
@EqualsAndHashCode
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class GamePropFlowBean {
    /**
     * ID
     */
    @Id
    @GeneratedValue
    @Column(name= "`id`")
    private Long id;

    /**
     * 道具ID
     */
    @Column(name= "`prop_id`")
    private Long propId;

    /**
     * 用户ID
     */
    @Column(name= "`user_id`")
    private Long userId;

    /**
     * 幂等的唯一 ID
     */
    @Column(name= "`unique_id`")
    private String uniqueId;

    /**
     * 有效时长（单位：秒），小于 0 代表永久
     */
    @Column(name= "`duration_sec`")
    private Integer durationSec;

    /**
     * 渠道游戏 ID
     */
    @Column(name= "`channel_game_id`")
    private String channelGameId;

    /**
     * 渠道道具 ID
     */
    @Column(name= "`channel_prop_id`")
    private String channelPropId;

    /**
     * 发放数量
     */
    @Column(name= "`num`")
    private Integer num;

    /**
     * Seal 分发给业务的 ID
     */
    @Column(name= "`app_id`")
    private Long appId;

    /**
     * 道具类型, 1皮肤 2道具
     */
    @Column(name= "`type`")
    private Integer type;

    /**
     * 发放状态，0未发放，1发放中，2成功, 3失败
     */
    @Column(name= "`grant_status`")
    private Integer grantStatus;

    /**
     * 创建时间
     */
    @Column(name= "`create_time`")
    private Date createTime;

    /**
     * 更新时间
     */
    @Column(name= "`modify_time`")
    private Date modifyTime;

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", propId=").append(propId);
        sb.append(", userId=").append(userId);
        sb.append(", uniqueId=").append(uniqueId);
        sb.append(", durationSec=").append(durationSec);
        sb.append(", channelGameId=").append(channelGameId);
        sb.append(", channelPropId=").append(channelPropId);
        sb.append(", num=").append(num);
        sb.append(", appId=").append(appId);
        sb.append(", type=").append(type);
        sb.append(", grantStatus=").append(grantStatus);
        sb.append(", createTime=").append(createTime);
        sb.append(", modifyTime=").append(modifyTime);
        sb.append("]");
        return sb.toString();
    }
}