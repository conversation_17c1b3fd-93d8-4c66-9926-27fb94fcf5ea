package fm.lizhi.ocean.seal.util;

import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;

public class ObjectUtil {
    private final static String LONG = "Long";
    private final static String INTEGER = "Integer";
    private final static String STRING = "String";

    public static <T> T castAndDefaultValue(Object obj, Class<T> t){
        String className = t.getSimpleName();
        if(null == obj){
            switch (className){
                case LONG : return (T) NumberUtils.LONG_ZERO;
                case INTEGER : return (T) NumberUtils.INTEGER_ZERO;
                default:return (T) StringUtils.EMPTY;
            }
        }

        return t.cast(obj);
    }

    public static void main(String[] args) {
        System.out.println(castAndDefaultValue(234, Integer.class));
        System.out.println(castAndDefaultValue(234L, Long.class));
        System.out.println(castAndDefaultValue("234", String.class));
        System.out.println(castAndDefaultValue(null, Integer.class));
        System.out.println(castAndDefaultValue(null, Long.class));
        System.out.println(castAndDefaultValue(null, String.class));
    }
}
