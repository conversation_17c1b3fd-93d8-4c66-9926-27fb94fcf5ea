package fm.lizhi.ocean.seal.dao.mapper.ext;


import fm.lizhi.common.datastore.core.annotation.DataStore;
import org.apache.ibatis.annotations.Delete;
import org.apache.ibatis.annotations.Param;

@DataStore(namespace = "mysql_ocean_oceanseal")
public interface GameLanguageRelationExtMapper {

    @Delete("delete from game_language_relation where game_id = #{gameId} ")
    void delRelationByGameId(@Param("gameId") long gameId);
}
