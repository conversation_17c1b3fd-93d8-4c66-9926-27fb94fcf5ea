package fm.lizhi.ocean.seal.strategy.impl;

import com.netflix.governator.annotations.AutoBindSingleton;
import fm.lizhi.ocean.seal.constant.GameChannel;
import fm.lizhi.ocean.seal.dao.bean.GameBizGameBean;
import fm.lizhi.ocean.seal.dao.bean.GameInfoBean;
import fm.lizhi.ocean.seal.protocol.GameServiceProto;
import fm.lizhi.ocean.seal.strategy.GameLoadingConfigStrategy;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

/**
 * SUD 渠道游戏加载配置策略实现
 * SUD 只需要返回 GameId 就可以实现 SDK 的初始化
 * 
 * Created in 2025-06-27
 * 
 * <AUTHOR> Agent
 */
@Slf4j
@AutoBindSingleton
public class SudGameLoadingConfigStrategy implements GameLoadingConfigStrategy {

    @Override
    public GameServiceProto.GameLoadingConfig getGameLoadingConfig(GameBizGameBean bizGameBean, GameInfoBean gameInfoBean, String appId) {
        try {
            log.info("Generating SUD game loading config for gameId: {}, channelGameId: {}", 
                    bizGameBean.getId(), gameInfoBean.getChannelGameIdStr());
            
            // SUD 渠道的游戏加载配置逻辑
            // 这里可以根据 SUD 渠道的特定需求来构建配置
            String url = generateSudGameUrl(gameInfoBean);
            String gZipUrl = generateSudGZipUrl(gameInfoBean);
            
            return GameServiceProto.GameLoadingConfig.newBuilder()
                    .setGameId(gameInfoBean.getChannelGameId())
                    .setUrl(url)
                    .setGZipUrl(gZipUrl)
                    .build();
                    
        } catch (Exception e) {
            log.error("Error generating SUD game loading config for gameId: {}", bizGameBean.getId(), e);
            return null;
        }
    }

    @Override
    public boolean supports(String channel) {
        return GameChannel.SUD.equals(channel);
    }
    
    /**
     * 生成 SUD 渠道的游戏 URL
     * 
     * @param gameInfoBean 游戏信息
     * @return 游戏 URL
     */
    private String generateSudGameUrl(GameInfoBean gameInfoBean) {
        return "";
    }
    
    /**
     * 生成 SUD 渠道的游戏压缩包 URL
     * 
     * @param gameInfoBean 游戏信息
     * @return 压缩包 URL
     */
    private String generateSudGZipUrl(GameInfoBean gameInfoBean) {
        return "";
    }
}
