package fm.lizhi.ocean.seal.constant;

/**
 * SUD错误码映射
 */
public enum SUDMessageCodeMappingEnum {

    SUCCESS(0, "成功", PlatformMessageCodeEnum.SUCCESS),
    TOKEN_CREATE_FAILED(1001, "Token创建失败", PlatformMessageCodeEnum.TOKEN_CREATE_FAILED),
    TOKEN_VERIFY_FAILED(1002, "Token校验失败", PlatformMessageCodeEnum.TOKEN_VERIFY_FAILED),
    TOKEN_DECODE_FAILED(1003, "Token解析失败", PlatformMessageCodeEnum.TOKEN_DECODE_FAILED),
    TOKEN_INVALID(1004, "Token非法", PlatformMessageCodeEnum.TOKEN_INVALID),
    TOKEN_EXPIRED(1005, "Token过期", PlatformMessageCodeEnum.TOKEN_EXPIRED),
    UNDEFINE(9999, "未知错误", PlatformMessageCodeEnum.UNDEFINE);

    private int code;

    private String msg;

    private PlatformMessageCodeEnum platformMessageCodeEnum;

    SUDMessageCodeMappingEnum(int code, String msg, PlatformMessageCodeEnum platformMessageCodeEnum) {
        this.code = code;
        this.msg = msg;
        this.platformMessageCodeEnum = platformMessageCodeEnum;
    }

    public static PlatformMessageCodeEnum findBySudCode(int code) {
        for (SUDMessageCodeMappingEnum value : values()) {
            if (value.getCode() == code) {
                return value.getPlatformMessageCodeEnum();
            }
        }
        return UNDEFINE.getPlatformMessageCodeEnum();
    }

    public int getCode() {
        return code;
    }

    public String getMsg() {
        return msg;
    }

    public PlatformMessageCodeEnum getPlatformMessageCodeEnum() {
        return platformMessageCodeEnum;
    }
}
