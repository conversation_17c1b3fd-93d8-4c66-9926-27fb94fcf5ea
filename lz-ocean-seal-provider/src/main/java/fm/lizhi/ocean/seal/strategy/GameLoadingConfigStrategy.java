package fm.lizhi.ocean.seal.strategy;

import fm.lizhi.ocean.seal.dao.bean.GameBizGameBean;
import fm.lizhi.ocean.seal.dao.bean.GameInfoBean;
import fm.lizhi.ocean.seal.protocol.GameServiceProto;

/**
 * 游戏加载配置策略接口
 * 根据不同的游戏渠道类型，提供不同的游戏加载配置生成策略
 * 
 * Created in 2025-06-27
 * 
 * <AUTHOR> Agent
 */
public interface GameLoadingConfigStrategy {
    
    /**
     * 获取游戏加载配置
     *
     * @param bizGameBean  业务游戏信息
     * @param gameInfoBean 游戏信息
     * @param appId
     * @return 游戏加载配置，如果无法生成则返回null
     */
    GameServiceProto.GameLoadingConfig getGameLoadingConfig(GameBizGameBean bizGameBean, GameInfoBean gameInfoBean, String appId);
    
    /**
     * 判断当前策略是否支持指定的渠道
     * 
     * @param channel 渠道名称
     * @return 是否支持
     */
    boolean supports(String channel);
}
