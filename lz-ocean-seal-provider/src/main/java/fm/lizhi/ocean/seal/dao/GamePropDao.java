package fm.lizhi.ocean.seal.dao;

import com.google.inject.Inject;
import com.netflix.governator.annotations.AutoBindSingleton;
import fm.lizhi.common.datastore.mysql.annotation.Transactional;
import fm.lizhi.common.datastore.mysql.mybatis.PageList;
import fm.lizhi.ocean.seal.dao.bean.GamePropBean;
import fm.lizhi.ocean.seal.dao.bean.GamePropFlowBean;
import fm.lizhi.ocean.seal.dao.mapper.GamePropBeanMapper;
import fm.lizhi.ocean.seal.dao.mapper.GamePropFlowBeanMapper;
import fm.lizhi.ocean.seal.dao.mapper.ext.GamePropExtMapper;
import fm.lizhi.ocean.seal.dao.mapper.ext.GamePropFlowExtMapper;
import lombok.extern.slf4j.Slf4j;

import java.util.Date;

/**
 * 游戏道具数据访问对象
 * <AUTHOR>
 */
@Slf4j
@AutoBindSingleton
public class GamePropDao {

    @Inject
    private GamePropBeanMapper gamePropBeanMapper;

    @Inject
    private GamePropExtMapper gamePropExtMapper;

    @Inject
    private GamePropFlowBeanMapper gamePropFlowBeanMapper;

    @Inject
    private GamePropFlowExtMapper gamePropFlowExtMapper;

    /**
     * 根据渠道道具ID查询道具
     *
     * @param channelPropId 道具ID
     * @param appId
     * @param gameId
     * @return 道具信息
     */
    public GamePropBean selectOne(String channelPropId, String appId, String gameId) {
        GamePropBean param = new GamePropBean();
        param.setChannelPropId(channelPropId);
        param.setDeleted(false);
        param.setAppId(Long.valueOf(appId));
        param.setChannelGameId(gameId);

        return gamePropBeanMapper.selectOne(param);
    }

    /**
     * 根据渠道游戏ID和渠道道具ID查询道具
     * @param channelGameId 渠道游戏ID
     * @param channelPropId 渠道道具ID
     * @return 道具信息
     */
    public GamePropBean selectByChannelGameIdAndChannelPropId(Long channelGameId, Long channelPropId) {
        log.debug("Select game prop by channelGameId: {}, channelPropId: {}", channelGameId, channelPropId);
        return gamePropExtMapper.selectByChannelGameIdAndChannelPropId(channelGameId, channelPropId);
    }

    /**
     * 根据条件查询道具列表
     * @param channelGameId 渠道游戏ID
     * @param channelId 渠道ID
     * @param type 道具类型
     * @param pageNo 页码
     * @param pageSize 页长
     * @return 道具列表
     */
    public PageList<GamePropBean> selectPropListByConditions(String channelGameId, Long channelId, Integer type,
                                                             Integer pageNo, Integer pageSize) {
        return gamePropExtMapper.selectByConditions(channelGameId, channelId, type, pageNo, pageSize);
    }

    /**
     * 插入道具
     * @param record 道具信息
     * @return 影响行数
     */
    @Transactional(rollbackFor = Exception.class)
    public int insert(GamePropBean record) {
        log.info("Insert game prop: {}", record.getName());
        record.setCreateTime(new Date());
        record.setModifyTime(new Date());
        return gamePropBeanMapper.insert(record);
    }


    /**
     * 根据ID更新道具
     * @param record 道具信息
     * @return 影响行数
     */
    @Transactional(rollbackFor = Exception.class)
    public int updateByPrimaryKey(GamePropBean record) {
        log.info("Update game prop by id: {}", record.getId());
        record.setModifyTime(new Date());
        return gamePropBeanMapper.updateByPrimaryKey(record);
    }


    // ==================== 道具流水相关方法 ====================


    /**
     * 根据唯一ID查询流水（用于幂等性检查）
     *
     * @param uniqueId 唯一ID
     * @param status
     * @return 流水信息
     */
    public GamePropFlowBean selectFlowByUniqueId(String uniqueId, int status) {
        return gamePropFlowExtMapper.selectByUniqueId(uniqueId, status);
    }



    /**
     * 写入流水
     * @param record 流水信息
     * @return 影响行数
     */
    @Transactional(rollbackFor = Exception.class)
    public int insertFlow(GamePropFlowBean record) {
        if (record.getCreateTime() == null) {
            record.setCreateTime(new Date());
        }
        if (record.getModifyTime() == null) {
            record.setModifyTime(new Date());
        }
        return gamePropFlowBeanMapper.insert(record);
    }


    /**
     * 更新流水状态
     * @param id 流水ID
     * @param grantStatus 发放状态
     * @return 影响行数
     */
    @Transactional(rollbackFor = Exception.class)
    public int updateFlowGrantStatus(Long id, Integer grantStatus) {
        return gamePropFlowExtMapper.updateGrantStatus(id, grantStatus);
    }
}
