package fm.lizhi.ocean.seal.dao.bean;

import java.util.Date;
import javax.persistence.*;

/**
 * This class was generated by DataStore MyBatis Generator.
 *
 * 新版游戏与旧版游戏映射表，用于兼容业务方
 *
 * @date 2022-04-28 05:24:01
 */
@Table(name = "`game_biz_game_relation`")
public class GameBizGameRelationBean {
    /**
     * 主键，game_biz_game表ID
     */
    @Id
    @Column(name= "`id`")
    private Long id;

    /**
     * 业务appId
     */
    @Column(name= "`app_id`")
    private String appId;

    /**
     * 渠道游戏ID
     */
    @Column(name= "`channel_game_id`")
    private String channelGameId;

    @Column(name= "`create_time`")
    private Date createTime;

    @Column(name= "`modify_time`")
    private Date modifyTime;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getAppId() {
        return appId;
    }

    public void setAppId(String appId) {
        this.appId = appId == null ? null : appId.trim();
    }

    public String getChannelGameId() {
        return channelGameId;
    }

    public void setChannelGameId(String channelGameId) {
        this.channelGameId = channelGameId == null ? null : channelGameId.trim();
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getModifyTime() {
        return modifyTime;
    }

    public void setModifyTime(Date modifyTime) {
        this.modifyTime = modifyTime;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", appId=").append(appId);
        sb.append(", channelGameId=").append(channelGameId);
        sb.append(", createTime=").append(createTime);
        sb.append(", modifyTime=").append(modifyTime);
        sb.append("]");
        return sb.toString();
    }
}