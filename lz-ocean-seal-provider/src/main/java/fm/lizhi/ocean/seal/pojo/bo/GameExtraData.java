package fm.lizhi.ocean.seal.pojo.bo;

import com.alibaba.fastjson.JSONObject;
import com.google.common.base.Strings;
import lombok.*;
import lombok.extern.slf4j.Slf4j;

/**
 * Created in 2022-01-25 10:54.
 *
 * <AUTHOR>
 */
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Data
@ToString
@Slf4j
public class GameExtraData {
    private String appId;
    private int env;
    /**
     * 游戏上报信息扩展参数（透传），取值范围：长度不超过1024字节，超过则截断
     */
    private String extras;

    /**
     * 转换参数
     *
     * @return
     */
    public static GameExtraData buildGameExtraData(String reportGameInfoExtras) {
        if (Strings.isNullOrEmpty(reportGameInfoExtras)) {
            return GameExtraData.builder().appId("").extras("").env(-1).build();
        }
        try {
            JSONObject parse = JSONObject.parseObject(reportGameInfoExtras);
            String appId = parse.getString("appId") == null ? "" : parse.getString("appId");
            int env = parse.getInteger("env") == null ? -1 : parse.getInteger("env");
            String extras = parse.getString("ext") == null ? reportGameInfoExtras : parse.getString("ext");
            return GameExtraData.builder().appId(appId).extras(extras).env(env).build();
        } catch (Exception e) {
            log.error("buildGameStartResult conExtra2Json error.object={}", reportGameInfoExtras, e);
        }
        return GameExtraData.builder().appId("").extras(reportGameInfoExtras).env(0).build();

    }

}
