package fm.lizhi.ocean.seal.manager;

import com.google.inject.Inject;
import com.googlecode.protobuf.format.JsonFormat;
import com.netflix.governator.annotations.AutoBindSingleton;
import fm.lizhi.common.datastore.redis.client.RedisClient;
import fm.lizhi.commons.util.GuidGenerator;
import fm.lizhi.ocean.seal.adapter.GameRoundAdapter;
import fm.lizhi.ocean.seal.conf.DataStoreConfig;
import fm.lizhi.ocean.seal.constant.GameRoundState;
import fm.lizhi.ocean.seal.dao.bean.GameBizGameBean;
import fm.lizhi.ocean.seal.dao.bean.GameInfoBean;
import fm.lizhi.ocean.seal.dao.bean.GameRoundBean;
import fm.lizhi.ocean.seal.dao.bean.GameRoundResultBean;
import fm.lizhi.ocean.seal.dao.mapper.GameRoundBeanMapper;
import fm.lizhi.ocean.seal.dao.mapper.GameRoundResultBeanMapper;
import fm.lizhi.ocean.seal.dao.mapper.ext.GameRoundBeanExtMapper;
import fm.lizhi.ocean.seal.exception.BizException;
import fm.lizhi.ocean.seal.protocol.GameReportServiceProto;
import fm.lizhi.ocean.seal.protocol.GameRoundServiceProto;
import fm.lizhi.ocean.seal.redis.RedisKeys;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import redis.clients.jedis.params.SetParams;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * 游戏局
 * <p>
 * Created in 2022-04-27 17:46.
 *
 * <AUTHOR>
 */
@AutoBindSingleton
public class GameRoundManager {
    private static final Logger logger = LoggerFactory.getLogger(GameRoundManager.class);
    @Inject
    private GameRoundBeanMapper gameRoundBeanMapper;
    @Inject
    private GuidGenerator guidGenerator;
    @Inject
    @com.google.inject.name.Named(DataStoreConfig.REDIS_NAME_SPACE)
    private RedisClient redisClient;
    @Inject
    private GameRoundAdapter gameRoundAdapter;
    @Inject
    private GameRoundBeanExtMapper gameRoundBeanExtMapper;
    @Inject
    private GameRoundResultBeanMapper gameRoundResultBeanMapper;
    @Inject
    private GameInfoManager gameInfoManager;
    @Inject
    private BizGameManager bizGameManager;

    /**
     * 获取游戏局信息
     *
     * @param roundId 局ID
     * @return
     */
    public GameRoundBean getRound(long roundId) {
        GameRoundBean bean = new GameRoundBean();
        bean.setId(roundId);
        return this.gameRoundBeanMapper.selectOne(bean);
    }

    /**
     * 更新游戏局信息
     *
     * @param channelRoundId 渠道局ID
     * @param extra          透传参数
     */
    public void relationGameRound(String channelRoundId, String extra) {
        if (StringUtils.isBlank(extra)) {
            return;
        }
        int num = this.gameRoundBeanExtMapper.updateExtraByChannelRoundId(channelRoundId, extra);
        // 如果更新时，没有找到对应的渠道局ID，则临时存储3小时的缓冲时间
        if (num <= 0) {
            String key = RedisKeys.GAME_ROUND_EXTRA.buildKey(channelRoundId);
            SetParams setParams = SetParams.setParams();
            setParams.ex((int) TimeUnit.HOURS.toSeconds(3));
            this.redisClient.set(key, extra, setParams);
            logger.info("Relation game round, game round not found, channelRoundId:{}, extra:{}", channelRoundId, extra);
        }
    }

    /**
     * 根据主键更新实体信息
     *
     * @param bean 实体
     */
    private void updateGameRound(GameRoundBean bean) {
        bean.setModifyTime(new Date());
        int count = this.gameRoundBeanMapper.updateByPrimaryKey(bean);
        if (count != 1) {
            logger.error("Failed to update game round, failed to insert database, count:{}, bean:{}", count, bean);
            throw new BizException("Failed to update game round, failed to insert database");
        }
    }

    /**
     * 渠道游戏开始，记录开始相关数据
     *
     * @param result
     */
    public void channelStartRound(GameReportServiceProto.GameStartResult result) {
        String params = JsonFormat.printToString(result);
        // 如果查不到未结束的局，则可能为老版本SDK，没有loadGame，因此没有生成游戏局，则新增一局游戏
        GameBizGameBean bizGameBean = this.bizGameManager.getGame(result.getAppId(), result.getChannelGameId());

        String gameRoundId = result.getGameRoundId();
        Long gameAppId = bizGameBean.getGameAppId();

        GameRoundBean gameRoundBean = new GameRoundBean();
        gameRoundBean.setAppId(gameAppId);
        gameRoundBean.setGameId(bizGameBean.getId());
        gameRoundBean.setChannelGameId(result.getChannelGameId());
        gameRoundBean.setChannelRoundId(gameRoundId);
        gameRoundBean.setStartTime(result.getGameStartAtTime());
        gameRoundBean.setState(GameRoundState.GAME_IN.getValue());
        gameRoundBean.setChannelRoomId(result.getRoomId());
        gameRoundBean.setBizRoomId(result.getRoomId());
        gameRoundBean.setChannelGameMode(result.getGameMode());
        String extra = result.getReportGameInfoExtras();
        if (StringUtils.isBlank(extra)) {
            extra = this.getExtraByChannelRoundId(gameRoundId, gameAppId);
        }
        gameRoundBean.setExtra(extra);
        Date date = new Date();

        // 20250328新增：先查询游戏局是否存在，存在就更新，而不是再插入
        GameRoundBean exist = new GameRoundBean();
        exist.setAppId(gameAppId);
        exist.setChannelRoundId(gameRoundId);
        Long existId = gameRoundBeanMapper.isExist(gameRoundId, gameAppId);
        if (existId != null && existId > 0) {
            logger.info("gameRoundId: {}, gameAppId: {} 已存在，可能是并发插入", gameRoundId, gameAppId);
            gameRoundBean.setId(existId);
            gameRoundBean.setModifyTime(date);
            this.gameRoundBeanMapper.updateByPrimaryKey(gameRoundBean);
        } else {
            gameRoundBean.setId(this.guidGenerator.genId());
            gameRoundBean.setCreateTime(date);
            int insert = this.gameRoundBeanMapper.insert(gameRoundBean);
            logger.info("Receive the start game callback, insert the game round bean, insert:{}, result:{}, gameRoundBean:{}", insert, params, gameRoundBean);
        }
    }

    /**
     * 根据渠道游戏局ID获取透传参数
     *
     * @param channelRoundId 渠道游戏局ID
     * @return
     */
    public String getExtraByChannelRoundId(String channelRoundId, Long gameAppId) {
        // 先从库里找，如果找得到则返回，找不到则从缓存里找
        GameRoundBean roundBean = this.getRoundByChannelRoundId(channelRoundId, gameAppId);
        if (roundBean != null && StringUtils.isNotBlank(roundBean.getExtra())) {
            return roundBean.getExtra();
        }
        String key = RedisKeys.GAME_ROUND_EXTRA.buildKey(channelRoundId);
        String extra = this.redisClient.get(key);
        // 再将透传参数设置到库里
        this.gameRoundBeanExtMapper.updateExtraByChannelRoundId(channelRoundId, extra);
        return extra;
    }

    /**
     * 查询未结束的游戏局
     *
     * @param appId         业务方appId
     * @param channelGameId 渠道游戏ID
     * @param roomId        房间号
     * @return
     */
    public GameRoundBean getUnFinishedGameRound(String appId, String channelGameId, String roomId) {
        GameBizGameBean bizGameBean = this.bizGameManager.getGame(appId, channelGameId);
        long gameAppId = bizGameBean.getGameAppId();
        long gameId = bizGameBean.getId();
        logger.info("get unfinished game round, appId:{}, channelGameId:{}, roomId:{}, gameAppId:{}, gameId:{}", appId, channelGameId, roomId, gameAppId, gameId);
        return this.gameRoundBeanExtMapper.selectNotFinishedGameRound(gameAppId, gameId, channelGameId, roomId);
    }

    /**
     * 渠道游戏结束，记录结算数据
     */
    public void channelEndRound(GameReportServiceProto.GameSettleResult result, Long gameAppId) {
        String params = JsonFormat.printToString(result);
//        this.cleanWaitPageInfo(result.getRoomId());

        logger.info("Receive the end game callback, update game information, result:{}", params);
        // 记录结算数据
        long roundId = 0;
        String endExtra = "";
        GameRoundBean bean = this.getRoundByChannelRoundId(result.getGameRoundId(), gameAppId);
        if (bean != null) {
            roundId = bean.getId();
        }

        if(StringUtils.isNotEmpty(result.getReportGameInfoExtras())){
            endExtra = result.getReportGameInfoExtras();
        }

        // 更新游戏局信息
        this.gameRoundBeanExtMapper.updateByChannelRoundId(
                result.getGameRoundId(),
                result.getGameEndAtTime(),
                result.getGameDuration(),
                GameRoundState.GAME_END.getValue(),
                result.getGameMode(),
                result.getRoomId(),
                result.getGameStartAtTime(),
                endExtra
        );

        // 生成一堆需要插入的对象
        // TODO 这里貌似会重复插入？
        List<GameRoundResultBean> beans = new ArrayList<>();
        Date date = new Date();
        for (GameReportServiceProto.GamePlayerSettleResult playerResult : result.getGamePlayersList()) {
            GameRoundResultBean resultBean = this.gameRoundAdapter.convertRoundResultBean(playerResult);
            resultBean.setId(this.guidGenerator.genId());
            resultBean.setRoundId(roundId);
            resultBean.setRole(playerResult.getRole());
            resultBean.setRealUser(playerResult.getRealUser() ? 1 : 0);
            resultBean.setEscaped(playerResult.getEscaped() ? 1 : 0);
            resultBean.setChannelRoundId(result.getGameRoundId());
            resultBean.setCreateTime(date);
            resultBean.setModifyTime(date);
            beans.add(resultBean);
        }
        this.gameRoundResultBeanMapper.batchInsert(beans);
    }

    /**
     * 清理等待页信息
     *
     * @param roomId 房间号
     */
    public void cleanWaitPageInfo(String roomId) {
        this.redisClient.del("WEB_OCEAN_SEAL_WEBSOCKET_GROUP_INFO_" + roomId);
        this.redisClient.del("WEB_OCEAN_SEAL_WEBSOCKET_GROUP_" + roomId);
        logger.info("Clean wait page info");
    }

    /**
     * 根据厂商局ID获取游戏局信息
     *
     * @param channelRoundId 厂商局ID
     * @return
     */
    public GameRoundBean getRoundByChannelRoundId(String channelRoundId, Long gameAppId) {
        GameRoundBean gameRoundBean = new GameRoundBean();
        gameRoundBean.setChannelRoundId(channelRoundId);
        gameRoundBean.setAppId(gameAppId);
        return this.gameRoundBeanMapper.selectOne(gameRoundBean);
    }

    /**
     * 加载游戏，含义是开始一局游戏
     *
     * @param bizGameBean 业务游戏信息
     * @param gameId      gameId
     * @param appId       应用ID
     * @param groupId     组ID
     * @param extra       透传参数
     * @return
     */
    public long startGameRound(GameBizGameBean bizGameBean, String gameId, String appId, String groupId, String extra) {
        long roundId;
        GameInfoBean gameInfoBean = this.gameInfoManager.getGameInfoBeanById(bizGameBean.getGameInfoId());
        GameRoundBean unFinishedGameRound = this.getUnFinishedGameRound(appId, gameInfoBean.getChannelGameIdStr(), groupId);
        if (unFinishedGameRound != null) {
            logger.info("Load an existing game, gameId:{}, appId:{}, groupId:{}, extra:{}, bizGameBean:{}, oldRound:{}", gameId, appId, groupId, extra, bizGameBean, unFinishedGameRound);
            roundId = unFinishedGameRound.getId();
        } else {
            Date date = new Date();
            GameRoundBean gameRoundBean = new GameRoundBean();
            roundId = this.guidGenerator.genId();
            gameRoundBean.setId(roundId);
            gameRoundBean.setAppId(bizGameBean.getGameAppId());
            gameRoundBean.setChannelGameId(gameInfoBean.getChannelGameIdStr());
            gameRoundBean.setGameId(bizGameBean.getId());
            gameRoundBean.setBizRoomId(groupId);
            gameRoundBean.setState(GameRoundState.LOAD_GAME_WAIT_PAGE.getValue());
            if (StringUtils.isNotEmpty(extra)) {
                gameRoundBean.setExtra(extra);
            }
            gameRoundBean.setCreateTime(date);
            gameRoundBean.setModifyTime(date);
            int count = this.gameRoundBeanMapper.insert(gameRoundBean);
            if (count != 1) {
                logger.error("Failed to start game round, failed to insert database, count:{}, groupId:{}, extra:{}, bizGameBean:{}, gameInfoBean:{}", count, groupId, extra, bizGameBean, gameInfoBean);
                throw new BizException("Failed to start game round, failed to insert database");
            }
        }
        return roundId;
    }

    /**
     * 客户端回调结束游戏局
     *
     * @param param
     */
    public void endGameRound(GameRoundServiceProto.EndGameRoundParam param) {
        // 目前只处理等待页相关状态清零
        this.cleanWaitPageInfo(param.getGroupId());
    }
}