package fm.lizhi.ocean.seal.manager;

import com.google.inject.Inject;
import com.netflix.governator.annotations.AutoBindSingleton;
import fm.lizhi.common.datastore.mysql.mybatis.PageList;
import fm.lizhi.commons.util.GuidGenerator;
import fm.lizhi.ocean.seal.dao.bean.GameChannelBean;
import fm.lizhi.ocean.seal.dao.bean.GameInfoBean;
import fm.lizhi.ocean.seal.dao.mapper.GameInfoBeanMapper;
import fm.lizhi.ocean.seal.dao.mapper.ext.BizGameInfoResult;
import fm.lizhi.ocean.seal.dao.mapper.ext.GameInfoBeanExtMapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;

import java.util.*;

/**
 * Created in 2022-01-26 16:32.
 *
 * <AUTHOR>
 */
@Slf4j
@AutoBindSingleton
public class GameInfoManager {
    @Inject
    private GameInfoBeanMapper gameInfoMapper;
    @Inject
    private GameInfoBeanExtMapper gameInfoBeanExtMapper;
    @Inject
    private GuidGenerator guidGenerator;

    public PageList<GameInfoBean> pageSearch(long id, String channel, String name, int pageSize, int number){
        GameInfoBean bean = new GameInfoBean();
        if(id > 0L){
            bean.setId(id);
        }
        if(StringUtils.isNotBlank(channel)){
            bean.setChannel(channel);
        }
        if(StringUtils.isNotBlank(name)){
            bean.setName(name);
        }

        return gameInfoMapper.selectPage(bean, number, pageSize);
    }

    public List<GameInfoBean> getAllGameInfoBeans(){
        GameInfoBean bean = new GameInfoBean();
        bean.setStatus(NumberUtils.INTEGER_ONE);
        return gameInfoMapper.selectMany(bean);
    }

    /**
     * 根据业务的gameAppId 查询游戏列表
     * @param gameAppId 存储appId的记录ID
     * @return
     */
    public List<BizGameInfoResult> getGameInfosByCondition(long gameAppId){
        return gameInfoBeanExtMapper.getBizGameInfosByGameAppId(gameAppId);
    }

    /**
     * 根据渠道名称与游戏ID获取游戏信息。这里的接口是兼容早期表冗余的字段
     * @param channel
     * @param channelGameId
     * @return
     */
    public GameInfoBean getGameInfoPO(String channel, long channelGameId) {
        GameInfoBean bean = new GameInfoBean();
        bean.setChannel(channel);
        bean.setChannelGameId(channelGameId);
        return gameInfoMapper.selectOne(bean);
    }

    /**
     * 存在 相同gameId 与 channelId 的数据
     * @param gameId
     * @param gameInfoId
     * @return
     */
    public boolean isExistGameIdAndChannelId(long gameId, String channel, long gameInfoId){
        GameInfoBean bean = getGameInfoPO(channel, gameId);
        if(null == bean){
            return false;
        }

        if(gameInfoId <= NumberUtils.LONG_ZERO.longValue()){
            return true;
        }

        return gameInfoId != bean.getId().longValue();
    }

    public List<Map<String, Object>> getGameDetails(String gameName, List<Long> gameTypes, Integer accessType){
        return gameInfoBeanExtMapper.searchGameDetails(gameName, gameTypes, accessType);
    }

    public List<GameInfoBean> getGameDetails(String gameName, Integer accessType){
        return gameInfoBeanExtMapper.getGameDetails(gameName, accessType);
    }

    public GameInfoBean getGameInfoBeanById(long id){
        GameInfoBean bean = new GameInfoBean();
        bean.setId(id);
        return gameInfoMapper.selectByPrimaryKey(bean);
    }

    public void delGameInfoBeanById(long id, String operator){
        GameInfoBean bean = getGameInfoBeanById(id);
        bean.setStatus(NumberUtils.INTEGER_ZERO);
        bean.setModifyTime(new Date());
        bean.setOperator(operator);
        gameInfoMapper.updateByPrimaryKey(bean);
    }

    public void delGameInfoById(long id){
        GameInfoBean bean = new GameInfoBean();
        bean.setId(id);
        gameInfoMapper.deleteByPrimaryKey(bean);
    }

    public void insertOrUpdateGameInfo(long id, String name, String channelGameId, int captain, int renderType
            , String desc, String extraJson, String channel){
        Date date = new Date();
        boolean insert = id <= 0L;
        GameInfoBean bean = new GameInfoBean();
        if(insert){
            bean.setId(guidGenerator.genId());
            bean.setCreateTime(date);
        }else {
            bean = getGameInfoBeanById(id);
            if(null == bean){
                return;
            }
        }

        bean.setName(name);
        bean.setChannelGameId(Optional.of(bean.getChannelGameId()).orElse(NumberUtils.LONG_ZERO));
        bean.setChannelGameIdStr(channelGameId);
        bean.setCaptain(captain);
        bean.setRenderType(renderType);
        bean.setDesc(Optional.ofNullable(desc).orElse(StringUtils.EMPTY));
        bean.setExtraJson(Optional.ofNullable(extraJson).orElse(StringUtils.EMPTY));
        bean.setChannel(channel);
        bean.setOperator("orcaAdmin");
        bean.setModifyTime(date);
        if(insert){
            gameInfoMapper.insert(bean);
        }else {
            gameInfoMapper.updateByPrimaryKey(bean);
        }
    }

    public long insertGameInfo(GameInfoBean bean){
        return gameInfoMapper.insert(bean) > NumberUtils.INTEGER_ZERO.intValue() ? bean.getId() : NumberUtils.LONG_ZERO.longValue();
    }

    public void modifyGameInfo(GameInfoBean bean){
        gameInfoMapper.updateByPrimaryKey(bean);
    }



}
