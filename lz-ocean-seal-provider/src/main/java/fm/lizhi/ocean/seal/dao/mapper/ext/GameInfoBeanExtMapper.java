package fm.lizhi.ocean.seal.dao.mapper.ext;


import fm.lizhi.common.datastore.core.annotation.DataStore;
import fm.lizhi.ocean.seal.dao.bean.GameInfoBean;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;
import java.util.Map;

@DataStore(namespace = "mysql_ocean_oceanseal")
public interface GameInfoBeanExtMapper {

    @Select("<script>\n" +
            " select g.*,t.game_type_id from (select * from `game_info` \n" +
            "  <where>\n" + " status = 1 " +
            "    <if test=\"accessType != null\">\n" +
            "      and `access_type` = #{accessType}\n" +
            "    </if>\n" +
            "    <if test=\"gameName != null and gameName != '' \">\n" +
            "      and `name` like concat('%', #{gameName}, '%') \n" +
            "    </if>\n" +
            ") g left join `game_type_relation` t on g.id = t.game_id where 1 = 1 " +
            "    <if test=\"gameTypes != null\">\n" +
            "      and t.game_type_id in " +
            "    <foreach item=\"gameType\" collection=\"gameTypes\" open='(' separator=',' close=')'>\n" +
            "      #{gameType}\n" +
            "    </foreach>\n" +
            "    </if>\n" +
            "  </where>\n" +
            "  order by `modify_time` desc\n" +
            "</script>")
    List<Map<String, Object>> searchGameDetails(@Param("gameName") String gameName
            ,@Param("gameTypes") List<Long> gameTypes,@Param("accessType") Integer accessType);

    @Select("<script>\n" +
            " select * from `game_info` \n" +
            "  <where>\n" + " status = 1 " +
            "    <if test=\"accessType != null\">\n" +
            "      and `access_type` = #{accessType}\n" +
            "    </if>\n" +
            "    <if test=\"gameName != null and gameName != '' \">\n" +
            "      and `name` like concat('%', #{gameName}, '%') \n" +
            "    </if>\n" +
            "  </where>\n" +
            "  order by `modify_time` desc\n" +
            "</script>")
    List<GameInfoBean> getGameDetails(@Param("gameName") String gameName, @Param("accessType") Integer accessType);

    @Select("select bg.id AS bizGameId,bg.channel_id AS channelId,bg.config,gi.id,gi.`name`,gi.`desc`,gi.channel,gi.channel_game_id AS channelGameId,gi.channel_game_id_str AS channelGameIdStr from game_biz_game bg left join game_info gi on bg.game_info_id = gi.id where  gi.`status` = 1 and bg.game_app_id = #{gameAppId} ")
    List<BizGameInfoResult> getBizGameInfosByGameAppId(@Param("gameAppId") Long gameAppId);
}
