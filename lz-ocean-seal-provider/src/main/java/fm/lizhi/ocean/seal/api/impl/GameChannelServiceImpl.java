package fm.lizhi.ocean.seal.api.impl;


import com.alibaba.fastjson.JSON;
import com.google.inject.Inject;
import fm.lizhi.commons.config.util.JsonUtil;
import fm.lizhi.commons.service.client.annotation.ServiceProvider;
import fm.lizhi.commons.service.client.log.LogContext;
import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.commons.service.client.rcode.GeneralRCode;
import fm.lizhi.ocean.seal.api.GameChannelService;
import fm.lizhi.ocean.seal.dao.bean.GameChannelBean;
import fm.lizhi.ocean.seal.dto.AgoraToken;
import fm.lizhi.ocean.seal.manager.AgoraManager;
import fm.lizhi.ocean.seal.manager.GameChannelManager;
import fm.lizhi.ocean.seal.protocol.GameChannelServiceProto;
import fm.lizhi.ocean.seal.protocol.GameChannelServiceProto.EnumGameChannel;
import fm.lizhi.ocean.seal.protocol.GameChannelServiceProto.GameChannel;
import fm.lizhi.ocean.seal.protocol.GameChannelServiceProto.ResponseGetAllGameChannels;
import fm.lizhi.ocean.seal.protocol.GameChannelServiceProto.ResponseGetEnumGameChannels;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.List;
import java.util.stream.Collectors;

@Slf4j
@ServiceProvider
public class GameChannelServiceImpl implements GameChannelService {
    private static final Logger logger = LoggerFactory.getLogger(GameChannelServiceImpl.class);
    @Inject
    private GameChannelManager gameChannelManager;
    @Inject
    private AgoraManager agoraManager;
    @Override
    public Result<ResponseGetAllGameChannels> getAllGameChannels() {

        List<GameChannelBean> gameChannelBeans = gameChannelManager.getAllGameChannels();
        if(gameChannelBeans == null){
            return new Result<>(GET_ALL_GAME_CHANNELS_FAIL, null);
        }

        LogContext.addResLog("bean={}", JsonUtil.dumps(gameChannelBeans));
        return new Result<>(GET_ALL_GAME_CHANNELS_SUCCESS
                , ResponseGetAllGameChannels.newBuilder().addAllGameChannels(
                gameChannelBeans.stream().map(x->GameChannel.newBuilder().setId(x.getId())
                        .setChannelName(x.getName()).setAbbreviation(x.getChannel()).build()).collect(Collectors.toList())
        ).build());
    }

    @Override
    public Result<ResponseGetEnumGameChannels> getEnumGameChannels() {
        List<GameChannelBean> gameChannelBeans = gameChannelManager.getAllGameChannels();
        if(gameChannelBeans == null){
            return new Result<>(GET_ENUM_GAME_CHANNELS_FAIL, null);
        }

        return new Result<>(GET_ALL_GAME_CHANNELS_SUCCESS
                , ResponseGetEnumGameChannels.newBuilder()
                .addAllEnumGameChannel(
                gameChannelBeans.stream()
                        .map(x->x.getChannel()).distinct().filter(StringUtils::isNotEmpty)
                        .map(x-> EnumGameChannel.newBuilder().setLabelChannel(x).setValueChannel(x).build())
                        .collect(Collectors.toList())
        ).build());
    }

    /**
     * 获取平台SDK与渠道交互的Token
     *
     * @param param
     * @return //if rcode == 1 参数非法<br>
     * //if rcode == 2 内部错误<br>
     */
    @Override
    public Result<GameChannelServiceProto.ResponseGetChannelToken> getChannelToken(GameChannelServiceProto.GetChannelTokenParam param) {
        GameChannelServiceProto.ResponseGetChannelToken.Builder builder = GameChannelServiceProto.ResponseGetChannelToken.newBuilder();
        String appId = param.getAppId();
        String channel = param.getChannel();
        long userId = param.getUserId();
        logger.info("Get channel token, appId:{}, channel:{}, userId:{}", appId, channel, userId);
        try {
            if (fm.lizhi.ocean.seal.constant.GameChannel.AGORA.equals(channel)) {
                // 声网渠道token
                AgoraToken agoraToken = this.agoraManager.generateToken(userId, appId);
                builder.setToken(GameChannelServiceProto.ChannelToken.newBuilder()
                        .setToken(agoraToken.getToken())
                        .setExpireTime(agoraToken.getExpire())
                        .build());
                return new Result<>(GeneralRCode.GENERAL_RCODE_SUCCESS, builder.build());
            }
            return new Result<>(GET_CHANNEL_TOKEN_ILLEGAL_PARAMS, builder.build());
        } catch (Exception e) {
            logger.error("Get channel token exception, appId:{}, channel:{}, userId:{}, msg:{}", appId, channel, userId, e.getMessage(), e);
        }
        return new Result<>(GET_CHANNEL_TOKEN_ERROR, builder.build());
    }
}
