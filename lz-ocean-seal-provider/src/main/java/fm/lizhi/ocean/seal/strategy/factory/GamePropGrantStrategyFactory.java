package fm.lizhi.ocean.seal.strategy.factory;

import com.google.inject.Inject;
import com.netflix.governator.annotations.AutoBindSingleton;
import fm.lizhi.ocean.seal.strategy.GamePropGrantStrategy;
import fm.lizhi.ocean.seal.strategy.GameProxyInvokeStrategy;
import fm.lizhi.ocean.seal.strategy.impl.LukGamePropGrantStrategy;
import lombok.extern.slf4j.Slf4j;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 游戏道具发放策略工厂
 * <AUTHOR>
 */
@Slf4j
@AutoBindSingleton
public class GamePropGrantStrategyFactory {

    @Inject
    private LukGamePropGrantStrategy lukGamePropGrantStrategy;

    /**
     * 根据渠道获取策略
     * @param channel 渠道名称
     * @return 策略实例
     */
    public GamePropGrantStrategy getStrategy(String channel) {
        log.debug("Get game prop grant strategy for channel: {}", channel);
        
        for (GamePropGrantStrategy strategy : getSupportedChannels()) {
            if (strategy.supports(channel)) {
                log.debug("Found strategy: {} for channel: {}", strategy.getClass().getSimpleName(), channel);
                return strategy;
            }
        }
        
        log.warn("No strategy found for channel: {}", channel);
        throw new IllegalArgumentException("No strategy found for channel: " + channel);
    }


    /**
     * 获取所有支持的渠道列表
     *
     * @return 支持的渠道列表
     */
    public List<GamePropGrantStrategy> getSupportedChannels() {
        return Arrays.asList(lukGamePropGrantStrategy);
    }
}
