package fm.lizhi.ocean.seal;

import fm.lizhi.commons.config.service.ConfigService;
import fm.lizhi.commons.initiator.Initiator;
import fm.lizhi.commons.service.client.codec.DataCenterServiceProviderCodec;
import fm.lizhi.commons.service.client.proxy.Provider;
import fm.lizhi.ocean.seal.conf.LzConfig;
import fm.lizhi.ocean.seal.log.LizhiLogger;

/**
 * 启动类
 */
public class Bootstrap {

	private static final int MAX_WAITING_TASK_COUNT = 10000;
    private static String scanPackageName = "fm.lizhi.ocean.seal";

    public static void main(String[] args) throws Exception{
        try {
        	Initiator.init();
        	LzConfig lzConf	=	ConfigService.loadConfig(LzConfig.class);
            startServer(lzConf);
        } catch (Throwable t) {
            LizhiLogger.error(t.getMessage(), t);
        }
    }
    
    private static void startServer(LzConfig lzConf) throws Exception{
    	final Provider provider = new Provider();
        provider.setCodec(new DataCenterServiceProviderCodec());
        // 设置服务代理配置key
        provider.addDcProxyAddressKey(lzConf.getDcProxyConfigKey(),null);
        // 设置连接数
        provider.setConnectionPerHostCount(lzConf.getDcProxyConnectionCount());
        // 设置默认线程池工作线程数
        provider.setDefaultExecutorThreadCount(lzConf.getWorkerCount(),MAX_WAITING_TASK_COUNT);
        // 设置配置对象，然后在项目中随时inject拿到配置对象
        provider.setProperties(lzConf);
        // 设置扫描路径
        provider.setScanPackageName(scanPackageName);
        provider.start();
    }
}
