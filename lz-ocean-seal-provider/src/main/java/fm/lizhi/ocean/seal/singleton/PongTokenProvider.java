package fm.lizhi.ocean.seal.singleton;

import com.google.inject.Inject;
import com.google.inject.Provider;
import com.netflix.governator.annotations.AutoBindSingleton;
import fm.lizhi.commons.service.client.proxy.ProxyBuilder;
import fm.lizhi.ocean.seal.api.GameAuthService;
import fm.lizhi.pongpong.middle.user.api.token.PongTokenService;


/**
 * <AUTHOR>
 */
@AutoBindSingleton
public class PongTokenProvider implements Provider<PongTokenService> {


    @Inject
    protected ProxyBuilder proxyBuilder;

    @Override
    public PongTokenService get() {
        return proxyBuilder.buildProxy(PongTokenService.class);
    }
}
