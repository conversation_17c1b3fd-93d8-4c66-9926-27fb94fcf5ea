package fm.lizhi.ocean.seal.pojo.bo.sud;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

/**
 * 推送事件data实体
 * <AUTHOR>
 * @date 2025/4/14 上午11:37
 * @description
 */
@ToString
@NoArgsConstructor
@AllArgsConstructor
public class SudPushEvent {

    /**
     * 房间ID
     */
    private String room_id;
    /**
     * 透传参数
     */
    private String report_game_info_extras;
    /**
     * 透传参数key
     */
    private String report_game_info_key;

    /**
     * 用户ID
     */
    private String uid;

    /**
     * 是否取消准备（用户处于准备态时有效） 默认:false
     * false:返回错误
     * true:取消准备
     */
    private boolean is_cancel_ready;

    public String getRoom_id() {
        return room_id;
    }

    public void setRoom_id(String room_id) {
        this.room_id = room_id;
    }

    public String getReport_game_info_extras() {
        return report_game_info_extras;
    }

    public void setReport_game_info_extras(String report_game_info_extras) {
        this.report_game_info_extras = report_game_info_extras;
    }

    public String getReport_game_info_key() {
        return report_game_info_key;
    }

    public void setReport_game_info_key(String report_game_info_key) {
        this.report_game_info_key = report_game_info_key;
    }

    public String getUid() {
        return uid;
    }

    public void setUid(String uid) {
        this.uid = uid;
    }

    public boolean isIs_cancel_ready() {
        return is_cancel_ready;
    }

    public void setIs_cancel_ready(boolean is_cancel_ready) {
        this.is_cancel_ready = is_cancel_ready;
    }
}
