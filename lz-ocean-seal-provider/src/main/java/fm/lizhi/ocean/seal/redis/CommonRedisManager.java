package fm.lizhi.ocean.seal.redis;

import com.google.inject.Inject;
import com.netflix.governator.annotations.AutoBindSingleton;
import fm.lizhi.common.datastore.redis.client.RedisClient;
import fm.lizhi.ocean.seal.manager.RedisManager;
import lombok.extern.slf4j.Slf4j;
import redis.clients.jedis.params.SetParams;

/**
 * redis 客户端
 */
@Slf4j
@AutoBindSingleton
public class CommonRedisManager {
    @Inject
    private RedisManager redisManager;

    private final static String LOCK_VALUE = "1";

    private final static String OK = "OK";

    public RedisClient getRedisClient() {
        return redisManager.getRedisClient();
    }

    /**
     * 分布式锁
     * @param lockKey key
     * @param expiredTime 过期时间
     * @param timeoutTime 超时时间
     * @return
     */
    public boolean lockWithTimeout(String lockKey, long expiredTime, long timeoutTime){
        long timeout = System.currentTimeMillis() + timeoutTime;
        try {
            while (System.currentTimeMillis() < timeout){
                if(setNx(lockKey, expiredTime)){
                    return true;
                }

                try {
                    Thread.sleep(10);
                } catch (InterruptedException e) {
                    Thread.currentThread().interrupt();
                    log.error("RedisLock interrupt error.`lockKey={}", lockKey, e);
                }
            }
        }catch (Exception e){
            log.error("RedisLock error.`lockKey={}", lockKey, e);
        }
        return false;
    }

    public void releaseLock(String lockKey){
        try {
            getRedisClient().del(lockKey);
        }catch (Exception e){
            log.error("release redisLock error.`lockKey={}", lockKey, e);
        }
    }

    private boolean setNx(String lockKey, long expiredTime){
        SetParams params = new SetParams();
        params.nx();
        params.px(expiredTime);
        return OK.equals(getRedisClient().set(lockKey, LOCK_VALUE, params));
    }
}
