package fm.lizhi.ocean.seal.dto;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

/**
 * Created in 2022-05-16 16:13.
 *
 * <AUTHOR>
 */
@Data
@ToString
@NoArgsConstructor
public class GameStartDTO {
    /**
     * 游戏厂商ID
     */
    private long gameId;
    /**
     * 房间ID
     */
    private String roomId;
    /**
     * 游戏模式
     */
    private int gameMode;
    /**
     * 本局游戏的id （可能会重复上报，使用该字段去重）
     */
    private String gameRoundId;
    /**
     * 游戏真正开始时间(毫秒)
     */
    private long gameStartAtTime;
    /**
     * 游戏上报信息扩展参数（透传），取值范围：长度不超过1024字节，超过则截断
     */
    private String reportGameInfoExtras;
    /**
     * 业务方appId
     */
    private String appId;
    /**
     * 所属环境
     */
    private int env;
}