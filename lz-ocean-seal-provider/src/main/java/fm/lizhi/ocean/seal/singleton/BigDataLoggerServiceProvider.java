package fm.lizhi.ocean.seal.singleton;

import com.google.inject.Inject;
import com.google.inject.Provider;
import com.netflix.governator.annotations.AutoBindSingleton;
import com.yibasan.lizhi.tracker.BigDataLogger;
import com.yibasan.lizhi.tracker.consumer.AsyncBatchConsumer;
import fm.lizhi.ocean.seal.conf.LzConfig;
import fm.lizhi.ocean.seal.util.ThreadUtil;

/**
 * Created in 2022-02-15 09:03.
 *
 * <AUTHOR>
 */
@AutoBindSingleton
public class BigDataLoggerServiceProvider implements Provider<BigDataLogger> {

    @Inject
    private LzConfig lzConfig;

    @Override
    public BigDataLogger get() {
        return new BigDataLogger(new AsyncBatchConsumer(lzConfig.getBigDataServerUrl(),
                ThreadUtil.generateExecutorService("big-data", 5)));
    }
}
