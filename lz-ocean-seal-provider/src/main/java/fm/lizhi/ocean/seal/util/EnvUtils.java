package fm.lizhi.ocean.seal.util;

import com.ctrip.framework.apollo.core.enums.Env;
import com.lizhi.commons.config.core.util.ConfigUtils;

/**
 * 环境相关的
 *
 * <AUTHOR>
 * @date 2021/3/3
 */
public class EnvUtils {
	/**
     * 环境 - 测试
     */
    public static final String ENV_OFFICE = "OFFICE";
    /**
     * 环境 - 预发
     */
    public static final String ENV_PRE = "PRE";
    /**
     * 环境 - 正式
     */
    public static final String ENV_PRODUCT = "PRODUCT";

    /**
     * 是否是线上或者预发的环境
     */
    public static boolean isProduct() {
        return isPre() || isOnline();
    }

    /**
     * 是否是预发环境
     */
    public static boolean isPre() {
        return Env.PRE == ConfigUtils.getEnv();
    }

    /**
     * 是否是线上环境
     */
    public static boolean isOnline() {
        return Env.PRO == ConfigUtils.getEnv();
    }

    /**
     * 是否是docker环境
     */
    public static boolean isOffice() {
        return Env.TEST == ConfigUtils.getEnv();
    }

    /**
     * 返回当前所处环境
     *
     * @return
     */
    public static String getEnv() {
        if (isOffice()) {
            return ENV_OFFICE;
        }
        if (isPre()) {
            return ENV_PRE;
        }
        return ENV_PRODUCT;
    }
}