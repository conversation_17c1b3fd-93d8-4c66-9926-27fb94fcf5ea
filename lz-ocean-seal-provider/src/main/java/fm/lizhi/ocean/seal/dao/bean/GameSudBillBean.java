package fm.lizhi.ocean.seal.dao.bean;

import java.math.BigDecimal;
import java.util.Date;
import javax.persistence.*;

/**
 * This class was generated by DataStore MyBatis Generator.
 *
 * sud侧游戏消耗账单
 *
 * @date 2025-04-23 02:51:19
 */
@Table(name = "`game_sud_bill`")
public class GameSudBillBean {
    /**
     * 主键ID
     */
    @Id
    @GeneratedValue
    @Column(name= "`id`")
    private Long id;

    /**
     * 渠道appId
     */
    @Column(name= "`app_id`")
    private String appId;

    /**
     * 渠道app名称
     */
    @Column(name= "`app_name`")
    private String appName;

    /**
     * 业务游戏id
     */
    @Column(name= "`game_id`")
    private Long gameId;

    /**
     * 渠道游戏id
     */
    @Column(name= "`channel_game_id`")
    private String channelGameId;

    /**
     * 游戏名称
     */
    @Column(name= "`game_name`")
    private String gameName;

    /**
     * 使用量(分钟)
     */
    @Column(name= "`usage`")
    private Long usage;

    /**
     * 总费用（不包含折扣）
     */
    @Column(name= "`fee`")
    private BigDecimal fee;

    /**
     * 账单月份(yyyy-MM)
     */
    @Column(name= "`month`")
    private String month;

    /**
     * 创建时间
     */
    @Column(name= "`create_time`")
    private Date createTime;

    /**
     * 修改时间
     */
    @Column(name= "`modify_time`")
    private Date modifyTime;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getAppId() {
        return appId;
    }

    public void setAppId(String appId) {
        this.appId = appId == null ? null : appId.trim();
    }

    public String getAppName() {
        return appName;
    }

    public void setAppName(String appName) {
        this.appName = appName == null ? null : appName.trim();
    }

    public Long getGameId() {
        return gameId;
    }

    public void setGameId(Long gameId) {
        this.gameId = gameId;
    }

    public String getChannelGameId() {
        return channelGameId;
    }

    public void setChannelGameId(String channelGameId) {
        this.channelGameId = channelGameId == null ? null : channelGameId.trim();
    }

    public String getGameName() {
        return gameName;
    }

    public void setGameName(String gameName) {
        this.gameName = gameName == null ? null : gameName.trim();
    }

    public Long getUsage() {
        return usage;
    }

    public void setUsage(Long usage) {
        this.usage = usage;
    }

    public BigDecimal getFee() {
        return fee;
    }

    public void setFee(BigDecimal fee) {
        this.fee = fee;
    }

    public String getMonth() {
        return month;
    }

    public void setMonth(String month) {
        this.month = month == null ? null : month.trim();
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getModifyTime() {
        return modifyTime;
    }

    public void setModifyTime(Date modifyTime) {
        this.modifyTime = modifyTime;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", appId=").append(appId);
        sb.append(", appName=").append(appName);
        sb.append(", gameId=").append(gameId);
        sb.append(", channelGameId=").append(channelGameId);
        sb.append(", gameName=").append(gameName);
        sb.append(", usage=").append(usage);
        sb.append(", fee=").append(fee);
        sb.append(", month=").append(month);
        sb.append(", createTime=").append(createTime);
        sb.append(", modifyTime=").append(modifyTime);
        sb.append("]");
        return sb.toString();
    }
}