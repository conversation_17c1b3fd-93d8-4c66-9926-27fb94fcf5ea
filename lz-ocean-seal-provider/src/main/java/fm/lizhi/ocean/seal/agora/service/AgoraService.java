package fm.lizhi.ocean.seal.agora.service;

import com.alibaba.fastjson.JSONObject;
import com.google.inject.Inject;
import com.netflix.governator.annotations.AutoBindSingleton;
import com.netflix.governator.annotations.WarmUp;
import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.ocean.seal.agora.bean.Game;
import fm.lizhi.ocean.seal.agora.token.RtmTokenBuilder;
import fm.lizhi.ocean.seal.conf.LzConfig;
import fm.lizhi.ocean.seal.dao.bean.GameBizGameBean;
import fm.lizhi.ocean.seal.dao.bean.GameChannelBean;
import fm.lizhi.ocean.seal.dao.bean.GameRoundBean;
import fm.lizhi.ocean.seal.exception.BizException;
import fm.lizhi.ocean.seal.http.HttpClient;
import fm.lizhi.ocean.seal.manager.BizGameManager;
import fm.lizhi.ocean.seal.manager.GameChannelManager;
import fm.lizhi.ocean.seal.manager.GameRoundManager;
import fm.lizhi.ocean.seal.protocol.GameReportServiceProto;
import org.apache.commons.codec.digest.DigestUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.annotation.PostConstruct;
import javax.crypto.Mac;
import javax.crypto.spec.SecretKeySpec;
import java.util.*;
import java.util.concurrent.TimeUnit;

/**
 * Created in 2022-05-30 16:01.
 *
 * <AUTHOR>
 */
@AutoBindSingleton
public class AgoraService {
    private static final Logger logger = LoggerFactory.getLogger(AgoraService.class);
    private static final String AGORA_API_GAME_LIST = "https://api.agora.io/cn/v1.0/projects/{appId}/game-service/games?";
    private static final String AGORA_API_SETTLE_INFO = "https://api.agora.io/cn/v1.0/projects/{appId}/game-service/games/{gameId}/event?";
    private static final String AGORA_API_GAME_LIST_UNIQUE_ID = "get_game_list_unique_id";
    @Inject
    private GameChannelManager gameChannelManager;
    @Inject
    private GameRoundManager gameRoundManager;
    @Inject
    private BizGameManager bizGameManager;
    @Inject
    private HttpClient httpClient;
    @Inject
    private LzConfig lzConfig;

    //去除声网代码
    //@WarmUp
    //@PostConstruct
    public void init() {
        if (this.lzConfig.isStartTimerGetGameList()) {
            new Timer().schedule(new TimerTask() {
                @Override
                public void run() {
                    try {
                        List<Game> gameList = getGameList("ad44394daaba4bac947a6bf45df0ccb7", 1, 10);
                        logger.info("Get game list from agora, gameList:{}", gameList);

                        //getGameSettleInfo("", "e6efb297-1601-4f4e-b6e9-f9aebe3a3b41");
                    } catch (Exception e) {
                        logger.error("Get game list error, msg:{}", e.getMessage(), e);
                    }
                }
            }, 1000, 3000);
        }
    }

    /**
     * 获取游戏列表
     *
     * @param channelAppId 渠道分配的appId
     * @param page         页码
     * @param limit        最大数
     */
    public List<Game> getGameList(String channelAppId, int page, int limit) throws Exception {
        GameChannelBean bean = this.gameChannelManager.getChannelByAppId(channelAppId);
        if (bean == null) {
            logger.error("Failed to get game list from agora, bean is null, appId:{}, page:{}, limit:{}", channelAppId, page, limit);
            throw new BizException("Failed to get game list from agora, bean is null");
        }
        String params = this.builderParams(channelAppId, page, limit);
        String sign = DigestUtils.sha256Hex(params);
        String token = new RtmTokenBuilder().buildToken(channelAppId, bean.getAppSecret(), AGORA_API_GAME_LIST_UNIQUE_ID, Integer.MAX_VALUE);
        String url = AGORA_API_GAME_LIST.replace("{appId}", channelAppId) + params;
        HashMap<String, String> headers = new HashMap<>();
        headers.put("x-agora-game-signature", sign);
        headers.put("x-agora-token", token);
        headers.put("x-agora-uid", AGORA_API_GAME_LIST_UNIQUE_ID);
        JSONObject jsonObject = httpClient.getJsonObject(url, null, headers);
        logger.info("Get game list from agora, appId:{}, page:{}, limit:{}, result:{}", channelAppId, page, limit, jsonObject);
        if (jsonObject.getIntValue("code") != 0) {
            logger.error("Failed to get game list from agora, agora return error, appId:{}, page:{}, limit:{}, result:{}", channelAppId, page, limit, jsonObject);
            throw new BizException("Failed to get game list from agora, agora return error");
        }
        return jsonObject.getJSONObject("data").getJSONArray("items").toJavaList(Game.class);
    }

    /**
     * 构建参数
     *
     * @param channelAppId 渠道应用ID
     * @param page
     * @param limit
     * @return
     */
    private String builderParams(String channelAppId, int page, int limit) {
        StringBuilder sb = new StringBuilder();
        sb.append("appId=");
        sb.append(channelAppId);
        sb.append("&expireTime=");
        sb.append(System.currentTimeMillis() + TimeUnit.MINUTES.toMillis(5000));
        sb.append("&limit=");
        sb.append(limit);
        sb.append("&page=");
        sb.append(page);
        sb.append("&timestamp=");
        sb.append(System.currentTimeMillis() + TimeUnit.MINUTES.toMillis(500));
        return sb.toString();
    }

    /**
     * 获取游戏结算数据
     *
     * @param appId       有可能是业务APPID
     * @param gameRoundId 渠道游戏局ID
     * @return
     */
    public Result<GameReportServiceProto.ResponseGetGameSettleReport> getGameSettleInfo(String appId, String gameRoundId) {
       /* try {
            GameRoundBean gameRoundBean = this.gameRoundManager.getRoundByChannelRoundId(gameRoundId);
            if (gameRoundBean == null) {
                logger.info("Failed to get game round, appId:{}, gameRoundId:{}", appId, gameRoundId);
                throw new BizException("Failed to get game round");
            }
            GameBizGameBean gameBean = this.bizGameManager.getBizGame(gameRoundBean.getGameId());
            if (gameBean == null) {
                logger.info("Failed to get game, appId:{}, gameRoundId:{}, gameId:{}", appId, gameRoundId, gameRoundBean.getGameId());
                throw new BizException("Failed to get game");
            }
            GameChannelBean gameChannelBean = this.gameChannelManager.getGameChannel(gameBean.getChannelId());
            if (gameChannelBean == null) {
                logger.info("Failed to get game, appId:{}, gameRoundId:{}, gameId:{}", appId, gameRoundId, gameRoundBean.getGameId());
                throw new BizException("Failed to get game");
            }
            String channelGameId = gameRoundBean.getChannelGameId();
            String channelAppId = gameChannelBean.getAppId();
            String appSecret = gameChannelBean.getAppSecret();

            String params = this.builderGetSettleInfoParams(channelAppId, gameRoundId, channelGameId);
            String sign = this.hMACSHA256(params, appSecret);
            String token = new RtmTokenBuilder().buildToken(channelAppId, appSecret, AGORA_API_GAME_LIST_UNIQUE_ID, Integer.MAX_VALUE);
            String url = AGORA_API_SETTLE_INFO.replace("{appId}", channelAppId).replace("{gameId}", channelGameId) + params;
            HashMap<String, String> headers = new HashMap<>();
            headers.put("x-agora-game-signature", sign);
            headers.put("x-agora-token", token);
            headers.put("x-agora-uid", AGORA_API_GAME_LIST_UNIQUE_ID);
            JSONObject jsonObject = this.httpClient.getJsonObject(url, null, headers);
            logger.info("Get game settle info, gameRoundId:{}, channelGameId:{}, channelAppId:{}, result:{}", gameRoundId, channelGameId, channelAppId, jsonObject);
            if (jsonObject.getIntValue("code") == 0) {
                GameReportServiceProto.ResponseGetGameSettleReport.Builder builder = GameReportServiceProto.ResponseGetGameSettleReport.newBuilder();
                GameReportServiceProto.GameSettleResult.Builder gameResultBuilder = GameReportServiceProto.GameSettleResult.newBuilder();
                //optional int32 gameMode = 3; //游戏模式
                //optional string gameRoundId = 4; //本局游戏的id （可能会重复上报，使用该字段去重）
                //optional int64 gameStartAtTime = 5; //游戏真正开始时间(毫秒)
                //optional int64 gameEndAtTime = 6; //游戏真正结束时间(毫秒)
                //optional int32 gameDuration = 7; //游戏真正的进行时间(秒)
                //repeated GamePlayerSettleResult gamePlayers = 8; //GamePlayer对象数组
                //optional string appId = 9; //appID
                //optional int32 env = 10; //所属环境
                //optional string reportGameInfoExtras = 11; //游戏上报信息扩展参数（透传），取值范围：长度不超过1024字节，超过则截断
                //optional string channel = 12; //游戏渠道
                //optional string channelGameId = 13; // 渠道游戏ID
                // TODO 声网调提供线上接口后需要做对应包装

                builder.setResult(gameResultBuilder.build());
                return new Result<>(0, builder.build());
            }
            return new Result<>(1, null);
        } catch (Exception e) {
            logger.error("Failed to get game settle info, appId:{}, gameRoundId:{}", appId, gameRoundId, e);
            throw new RuntimeException(e);
        }*/
        return new Result<>(0, null);

    }

    private String hMACSHA256(String data, String key) throws Exception {
        Mac sha256_HMAC = Mac.getInstance("HmacSHA256");
        SecretKeySpec secret_key = new SecretKeySpec(key.getBytes("UTF-8"), "HmacSHA256");
        sha256_HMAC.init(secret_key);
        byte[] array = sha256_HMAC.doFinal(data.getBytes("UTF-8"));
        StringBuilder sb = new StringBuilder();
        for (byte item : array) {
            sb.append(Integer.toHexString((item & 0xFF) | 0x100).substring(1, 3));
        }
        return sb.toString().toUpperCase();
    }


    private String builderGetSettleInfoParams(String channelAppId, String gameRoundId, String channelGameId) {
        Map<String, Object> map = new TreeMap<>();
        map.put("appId", channelAppId);
        map.put("gameRoundId", gameRoundId);
        map.put("gameId", channelGameId);
        map.put("eventType", 102);
        map.put("expireTime", System.currentTimeMillis() + TimeUnit.MINUTES.toMillis(5000));
        map.put("pageSize", 10);
        map.put("page", 1);
        map.put("timestamp", System.currentTimeMillis() + TimeUnit.MINUTES.toMillis(500));
        StringBuilder sb = new StringBuilder();
        for (Map.Entry<String, Object> entry : map.entrySet()) {
            sb.append(entry.getKey());
            sb.append("=");
            sb.append(entry.getValue());
            sb.append("&");
        }
        sb.deleteCharAt(sb.length() - 1);
        return sb.toString();
    }
}