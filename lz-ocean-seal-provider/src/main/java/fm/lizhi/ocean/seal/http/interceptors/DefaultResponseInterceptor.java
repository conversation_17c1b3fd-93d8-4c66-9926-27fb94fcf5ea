package fm.lizhi.ocean.seal.http.interceptors;

import org.apache.http.HttpException;
import org.apache.http.HttpResponse;
import org.apache.http.HttpResponseInterceptor;
import org.apache.http.client.protocol.HttpClientContext;
import org.apache.http.protocol.HttpContext;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;

/**
 * Created in 2022-05-30 15:44.
 *
 * <AUTHOR>
 */
public class DefaultResponseInterceptor implements HttpResponseInterceptor {
    private static Logger logger = LoggerFactory.getLogger(DefaultResponseInterceptor.class);

    @Override
    public void process(HttpResponse response, HttpContext context) throws HttpException, IOException {
        HttpClientContext clientContext = (HttpClientContext) context;
        long times = (long) clientContext.getAttribute("interceptor.current_time_millis");

        String uri = clientContext.getTargetHost().getSchemeName() + "://" + clientContext.getTargetHost().getHostName() + clientContext.getRequest().getRequestLine().getUri();
        int statusCode = clientContext.getResponse().getStatusLine().getStatusCode();
        logger.info("http get, uri:{}, code:{}, use {}ms", uri, statusCode, (System.currentTimeMillis() - times));
    }
}