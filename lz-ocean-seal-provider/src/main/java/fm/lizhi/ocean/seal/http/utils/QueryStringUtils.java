package fm.lizhi.ocean.seal.http.utils;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Map;

/**
 * 查询字符串工具类
 *
 * Created in 2022-05-30 15:44.
 *
 * <AUTHOR>
 */
public class QueryStringUtils {
    private static Logger logger = LoggerFactory.getLogger(QueryStringUtils.class);

    /**
     * 将参数转换为查询字符串
     *
     * @param params 参数
     * @return 返回查询字符串
     */
    public static String toQueryString(Map<String, Object> params) {
        StringBuilder sb = new StringBuilder();
        if (params != null) {
            for (Map.Entry<String, Object> entry : params.entrySet()) {
                sb.append(entry.getKey());
                sb.append("=");
                sb.append(entry.getValue());
                sb.append("&");
            }
        }
        if (sb.length() > 0) {
            sb.deleteCharAt(sb.length() - 1);
        }
        return sb.toString();
    }
}