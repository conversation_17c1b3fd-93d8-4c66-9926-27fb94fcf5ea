package fm.lizhi.ocean.seal.xxl;

import com.netflix.governator.annotations.AutoBindSingleton;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import fm.lizhi.ocean.seal.manager.GameSudBillManager;
import fm.lizhi.ocean.seal.util.TimeUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import javax.inject.Inject;
import java.util.Date;

/**
 * 每小时查询sud账单
 *
 * <AUTHOR>
 * 2025年4月22日 11:08:23
 */
@Slf4j
@JobHandler("GameSubBillJob")
@AutoBindSingleton(baseClass = IJobHandler.class, multiple = true)
public class GameSudBillJob extends IJobHandler {
    @Inject
    private GameSudBillManager gameSudBillManager;

    @Override
    public ReturnT<String> execute(String s) throws Exception {
        return doExecute(StringUtils.isEmpty(s) ? "" : s.trim());
    }

    private ReturnT<String> doExecute(String param) {
        log.info("GameSubBillJob start time:{},param:{}", TimeUtils.formatDateTime(new Date(), "yyyy-MM-dd HH:mm:ss"), param);

        try {
            gameSudBillManager.handleSudBillJob();
            log.info("GameSubBillJob end time:{},param:{}", TimeUtils.formatDateTime(new Date(), "yyyy-MM-dd HH:mm:ss"), param);
        } catch (Exception e) {
            log.error("GameSubBillJob Call invoke target interface error, exception", e);
            throw new RuntimeException(e);
        }
        return ReturnT.SUCCESS;
    }
}
