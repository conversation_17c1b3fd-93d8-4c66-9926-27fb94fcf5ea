package fm.lizhi.ocean.seal.api.impl;


import com.google.inject.Inject;
import fm.lizhi.commons.config.util.JsonUtil;
import fm.lizhi.commons.service.client.annotation.ServiceProvider;
import fm.lizhi.commons.service.client.log.LogContext;
import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.ocean.seal.api.GameTypeService;
import fm.lizhi.ocean.seal.dao.bean.GameTypeBean;
import fm.lizhi.ocean.seal.manager.GameTypeManager;
import fm.lizhi.ocean.seal.protocol.GameTypeServiceProto.GameType;
import fm.lizhi.ocean.seal.protocol.GameTypeServiceProto.ResponseGetAllTypes;
import lombok.extern.slf4j.Slf4j;

import java.util.List;
import java.util.stream.Collectors;

@Slf4j
@ServiceProvider
public class GameTypeServiceImpl implements GameTypeService {

    @Inject
    private GameTypeManager gameTypeManager;


    @Override
    public Result<ResponseGetAllTypes> getAllTypes() {
        List<GameTypeBean> gameTypeBeans = gameTypeManager.getAllGameTypes();
        if(null == gameTypeBeans){
            return new Result<>(GET_ALL_TYPES_FAIL, null);
        }

        LogContext.addResLog("bean={}", JsonUtil.dumps(gameTypeBeans));
        return new Result<>(GET_ALL_TYPES_SUCCESS, ResponseGetAllTypes.newBuilder().addAllGameTypes(
                gameTypeBeans.stream().map(x-> GameType.newBuilder().setId(x.getId())
                        .setTypeName(x.getName()).build()).collect(Collectors.toList())
        ).build());
    }
}
