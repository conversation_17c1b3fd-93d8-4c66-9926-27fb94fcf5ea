package fm.lizhi.ocean.seal.singleton;

import com.google.inject.Provider;
import com.netflix.governator.annotations.AutoBindSingleton;
import fm.lizhi.ocean.seal.adapter.GameRoundAdapter;
import fm.lizhi.ocean.seal.adapter.GameRoundAdapterImpl;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * Created in 2022-05-16 20:02.
 *
 * <AUTHOR>
 */
@AutoBindSingleton
public class GameRoundAdapterProvider implements Provider<GameRoundAdapter> {
    private static final Logger logger = LoggerFactory.getLogger(GameRoundAdapterProvider.class);

    @Override
    public GameRoundAdapter get() {
        return new GameRoundAdapterImpl();
    }
}