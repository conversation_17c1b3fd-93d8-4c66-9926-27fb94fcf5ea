package fm.lizhi.ocean.seal.manager;

import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.google.inject.Inject;
import com.netflix.governator.annotations.AutoBindSingleton;
import fm.lizhi.commons.config.util.JsonUtil;
import fm.lizhi.commons.util.GuidGenerator;
import fm.lizhi.ocean.seal.conf.LzConfig;
import fm.lizhi.ocean.seal.dao.bean.GameBizGameBean;
import fm.lizhi.ocean.seal.dao.bean.GameSudBillBean;
import fm.lizhi.ocean.seal.dao.mapper.GameSudBillBeanMapper;
import fm.lizhi.ocean.seal.pojo.bo.sud.SudAppGameFee;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.binary.Hex;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;
import org.springframework.web.client.RestTemplate;

import javax.crypto.Cipher;
import javax.crypto.Mac;
import javax.crypto.spec.IvParameterSpec;
import javax.crypto.spec.SecretKeySpec;
import java.nio.charset.StandardCharsets;
import java.security.InvalidKeyException;
import java.security.NoSuchAlgorithmException;
import java.util.*;

/**
 * <AUTHOR>
 * @date 2025/4/22 下午2:30
 * @description
 */
@Slf4j
@AutoBindSingleton
public class GameSudBillManager {

    @Inject
    private LzConfig lzConfig;
    @Inject
    private GameSudBillBeanMapper gameSudBillBeanMapper;
    @Inject
    private GuidGenerator guidGenerator;
    @Inject
    private BizGameManager bizGameManager;

    public void handleSudBillJob() throws Exception {

        // 准备参数
        String code = lzConfig.getSudBillCode();
        String timestamp = System.currentTimeMillis() / 1000 + "";
        String trace = UUID.randomUUID().toString();
        String hmacSecret = lzConfig.getSudBillHmacSecret();

        // 构造请求体
        Map<String, String> entry = new HashMap<>();
        String month = DateUtil.format(new Date(), "yyyy-MM");
        entry.put("month", month);
        String body = JsonUtil.dumps(entry);

        // 构造待签名原始内容
        String plainText = code + "\n" + timestamp + "\n" + trace + "\n" + body + "\n";

        // 签名算法
        String algorithm = "HmacSHA256";
        String signature = generateHmacSignature(algorithm, hmacSecret, plainText);

        // 创建 RestTemplate 实例
        RestTemplate restTemplate = new RestTemplate();
        // 设置请求头
        HttpHeaders headers = new HttpHeaders();
        headers.set("content-type", "application/json");
        headers.set("x-sud-code", code);
        headers.set("x-sud-timestamp", timestamp);
        headers.set("x-sud-trace", trace);
        headers.set("x-sud-signature", signature);
        // 设置请求体
        HttpEntity<String> request = new HttpEntity<>(body, headers);
        // 发送 POST 请求
        ResponseEntity<String> response = restTemplate.exchange(lzConfig.getSudBillUrl(), HttpMethod.POST, request, String.class);


        // AES 密钥（Base64 格式）
        String aesSecretBase64 = lzConfig.getAesSecretBase64();
        // 加密后的响应体
        String responseBody = response.getBody();
        // 响应头中的签名
        String signatureFromHeader = response.getHeaders().get("x-sud-signature").get(0);

        String decryptedPlainText = null;
        try {
            // 验证签名
            boolean isValidSignature = verifySignature(code, timestamp, trace, responseBody, hmacSecret, signatureFromHeader);
            if (!isValidSignature) {
                // 签名验证失败
                throw new RuntimeException("签名验证失败");
            }

            // 解密响应体
            decryptedPlainText = decryptResponse(aesSecretBase64, responseBody);
            log.info("sud明文账单={} ", decryptedPlainText);
        } catch (Exception e) {
            log.error("解析响应失败", e);
            throw e;
        }

        // 写入数据库
        JSONObject result = JSONObject.parseObject(decryptedPlainText);
        if (result.containsKey("code") && result.containsKey("msg")) {
            if (result.getIntValue("code") != 0) {
                log.error("handleSudBillJob result error code={}, msg={}", result.getIntValue("code"), result.getString("msg"));
                return;
            }
            String appFees = result.getJSONObject("data").getString("app_game_fees");
            List<SudAppGameFee> sudAppGameFees = JSONArray.parseArray(appFees, SudAppGameFee.class);
            for (SudAppGameFee sudAppGameFee : sudAppGameFees) {
                // 写入/更新记录
                insertOrUpdate(sudAppGameFee, month);
            }
        } else {
            log.warn("handleSudBillJob result illegal result:{}", result);
            throw new RuntimeException("sud返回响应格式错误, " + result);
        }
    }

    /**
     * 使用 HMAC 算法生成签名
     *
     * @param algorithm 算法名称，例如 "HmacSHA256"
     * @param secret    HMAC 密钥
     * @param plainText 待签名的原始内容
     * @return 十六进制编码的签名结果
     */
    public static String generateHmacSignature(String algorithm, String secret, String plainText) {
        try {
            // 创建 Mac 实例
            Mac mac = Mac.getInstance(algorithm);

            // 初始化密钥
            SecretKeySpec secretKeySpec = new SecretKeySpec(secret.getBytes(StandardCharsets.UTF_8), algorithm);
            mac.init(secretKeySpec);

            // 计算签名
            byte[] digest = mac.doFinal(plainText.getBytes(StandardCharsets.UTF_8));

            // 将字节数组转换为十六进制字符串
            return Hex.encodeHexString(digest);
        } catch (NoSuchAlgorithmException | InvalidKeyException e) {
            throw new RuntimeException("签名生成失败", e);
        }
    }


    /**
     * 解密响应内容
     *
     * @param aesSecretBase64 AES 密钥（Base64 格式）
     * @param responseBody    加密后的响应体
     * @return 解密后的明文
     * @throws Exception 解密过程中可能抛出的异常
     */
    private static String decryptResponse(String aesSecretBase64, String responseBody) throws Exception {
        // 分割响应体
        String[] parts = responseBody.split("\\.");
        if (parts.length != 2) {
            throw new IllegalArgumentException("响应体格式错误");
        }
        String aesIvBase64 = parts[0]; // Base64 编码的 IV
        String cipherTextBase64 = parts[1]; // Base64 编码的密文

        // 解码 AES 密钥和 IV
        byte[] aesSecret = Base64.getDecoder().decode(aesSecretBase64);
        byte[] ivBytes = Base64.getDecoder().decode(aesIvBase64);

        // 初始化 Cipher
        Cipher cipher = Cipher.getInstance("AES/CBC/PKCS5Padding");
        SecretKeySpec secretKeySpec = new SecretKeySpec(aesSecret, "AES");
        IvParameterSpec ivParameterSpec = new IvParameterSpec(ivBytes);
        cipher.init(Cipher.DECRYPT_MODE, secretKeySpec, ivParameterSpec);

        // 解密并返回明文
        byte[] plainTextBytes = cipher.doFinal(Base64.getDecoder().decode(cipherTextBase64));
        return new String(plainTextBytes, StandardCharsets.UTF_8);
    }


    private static boolean verifySignature(String code, String timestamp, String trace, String responseBody,
                                           String hmacSecret, String signatureFromHeader) throws Exception {
        // 构造待签名原始内容
        String plainText = code + "\n" + timestamp + "\n" + trace + "\n" + responseBody + "\n";

        // 计算签名
        String algorithm = "HmacSHA256";
        String computedSignature = generateHmacSignature(algorithm, hmacSecret, plainText);

        // 比较签名值
        return computedSignature.equals(signatureFromHeader);
    }

    public void insertOrUpdate(SudAppGameFee sudAppGameFee, String recordMonth) {
        Date date = new Date();
        // 查询当前统计日期是否有记录
        GameSudBillBean sudBillBean = getGameSudBill(sudAppGameFee, recordMonth);
        if (null == sudBillBean) {
            // 每小时写入一条新的记录
            sudBillBean = new GameSudBillBean();
            sudBillBean.setId(guidGenerator.genId());
            sudBillBean.setAppId(sudAppGameFee.getApp_id());
            sudBillBean.setAppName(sudAppGameFee.getApp_name());
            // 查询业务游戏id
            GameBizGameBean bizGameBean = bizGameManager.getGame(sudAppGameFee.getApp_id(), sudAppGameFee.getGame_id());
            if (bizGameBean == null) {
                log.warn("GameSudBillManager getGame not exist.`appId={" + sudAppGameFee.getApp_id() + "}`gameId={" + sudAppGameFee.getGame_id() + "}");
                return;
                // throw new IllegalArgumentException("GameSudBillManager getGame not exist.`appId={" + sudAppGameFee.getApp_id() + "}`gameId={" + sudAppGameFee.getGame_id() + "}");
            }
            sudBillBean.setGameId(bizGameBean.getId());
            sudBillBean.setChannelGameId(sudAppGameFee.getGame_id());
            sudBillBean.setGameName(sudAppGameFee.getGame_name());
            sudBillBean.setUsage(sudAppGameFee.getUsage());
            sudBillBean.setFee(sudAppGameFee.getFee());
            sudBillBean.setMonth(recordMonth);
            sudBillBean.setCreateTime(date);
            sudBillBean.setModifyTime(date);
            gameSudBillBeanMapper.insert(sudBillBean);
        } else {
            // 更新
            sudBillBean.setUsage(sudAppGameFee.getUsage());
            sudBillBean.setFee(sudAppGameFee.getFee());
            sudBillBean.setModifyTime(date);
            gameSudBillBeanMapper.updateByPrimaryKey(sudBillBean);
        }
    }

    public GameSudBillBean getGameSudBill(SudAppGameFee sudAppGameFee, String recordMonth) {
        GameSudBillBean sudBillBean = new GameSudBillBean();
        sudBillBean.setAppId(sudAppGameFee.getApp_id());
        sudBillBean.setChannelGameId(sudAppGameFee.getGame_id());
        sudBillBean.setMonth(recordMonth);
        return gameSudBillBeanMapper.selectOne(sudBillBean);
    }
}
