package fm.lizhi.ocean.seal.manager;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONObject;
import com.google.inject.Inject;
import com.netflix.governator.annotations.AutoBindSingleton;
import fm.lizhi.ocean.seal.dao.bean.GameBizGameBean;
import fm.lizhi.ocean.seal.dao.bean.GameCallback;
import fm.lizhi.ocean.seal.dao.bean.GameChannelBean;
import fm.lizhi.ocean.seal.dao.bean.GameInfoBean;
import fm.lizhi.ocean.seal.pojo.bo.luk.LukControlEventRequest;
import fm.lizhi.ocean.seal.constant.LukControlEventType;
import io.github.cfgametech.Response;
import io.github.cfgametech.SDK;
import io.github.cfgametech.beans.PublishControlEventRequest;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;

import java.util.Map;

/**
 * <AUTHOR>
 */
@Slf4j
@AutoBindSingleton
public class LukManger {

    @Inject
    private GameChannelManager gameChannelManager;
    @Inject
    private GameCallbackManager gameCallbackManager;
    @Inject
    private BizGameManager bizGameManager;
    @Inject
    private GameInfoManager gameInfoManager;

    @Inject
    private GameAppManager gameAppManager;
    /**
     * 创建 LUK SDK 实例
     */
    public SDK createLukSdk(GameBizGameBean bizGameBean, GameChannelBean gameChannelBean, String appId) {
        try {
            GameCallback gameCallback = gameCallbackManager.getGameCallback(
                    appId,
                    String.valueOf(bizGameBean.getId()),
                    gameCallbackManager.getCallBackTypeByGameInterface(),
                    null);

            if (gameCallback == null) {
                log.error("Failed to get game callback for LUK SDK, gameId: {}, appId: {}",
                        bizGameBean.getId(), appId);
                return null;
            }

            return new SDK(gameChannelBean.getAppSecret(), gameCallback.getUrl());

        } catch (Exception e) {
            log.error("Error creating LUK SDK, gameId: {}, channelId: {}",
                    bizGameBean.getId(), gameChannelBean.getId(), e);
            return null;
        }
    }

    /**
     * 通用控制事件发布方法
     *
     * @param request 控制事件请求参数
     * @return SDK 响应结果
     */
    public Response<?> publishControlEvent(LukControlEventRequest request) {
        if (request == null) {
            log.error("LukControlEventRequest is null");
            return null;
        }

        try {
            // 参数校验
            if (StrUtil.isBlank(request.getAppId()) || StrUtil.isBlank(request.getGameId())) {
                log.error("AppId or GameId is blank in LukControlEventRequest");
                return null;
            }

            if (request.getEventType() == null) {
                log.error("EventType is null in LukControlEventRequest");
                return null;
            }

            // 校验房间事件是否提供了房间ID
            if (request.getEventType().isRoomEvent() && StrUtil.isBlank(request.getRoomId())) {
                log.error("RoomId is required for room event type: {}", request.getEventType());
                return null;
            }

            // 创建SDK实例
            SDK lukSdk = createLukSdk(request.getAppId(), request.getGameId());
            if (lukSdk == null) {
                log.error("Failed to create LUK SDK for publishControlEvent");
                return null;
            }

            // 获取游戏信息（用于构建请求）
            GameBizGameBean bizGameBean = bizGameManager.getGame(request.getAppId(), request.getGameId());
            if (bizGameBean == null) {
                log.error("GameBizGameBean not found for appId: {}, gameId: {}", request.getAppId(), request.getGameId());
                return null;
            }

            GameInfoBean gameInfoBean = gameInfoManager.getGameInfoBeanById(bizGameBean.getGameInfoId());
            if (gameInfoBean == null) {
                log.error("GameInfoBean not found for gameInfoId: {}", bizGameBean.getGameInfoId());
                return null;
            }

            GameChannelBean gameChannelBean = gameChannelManager.getGameChannel(bizGameBean.getChannelId());
            if (gameChannelBean == null) {
                log.error("GameChannelBean not found for channelId: {}", bizGameBean.getChannelId());
                return null;
            }

            // 构建并发送请求
            return doPublishControlEvent(lukSdk, request, gameInfoBean, gameChannelBean);

        } catch (Exception e) {
            log.error("Error publishing LUK control event", e);
            return null;
        }
    }

    /**
     * 发布房间事件的便捷方法
     *
     * @param appId 应用ID
     * @param gameId 游戏ID
     * @param roomId 房间ID
     * @param eventType 事件类型
     * @param eventData 事件数据
     * @return SDK 响应结果
     */
    public Response<?> publishRoomEvent(String appId, String gameId, String roomId,
                                       LukControlEventType eventType, Map<String, Object> eventData) {
        LukControlEventRequest request = LukControlEventRequest.createRoomEvent(
                appId, gameId, roomId, eventType, eventData);
        return publishControlEvent(request);
    }

    /**
     * 发布全局事件的便捷方法
     *
     * @param appId 应用ID
     * @param gameId 游戏ID
     * @param eventType 事件类型
     * @param eventData 事件数据
     * @return SDK 响应结果
     */
    public Response<?> publishGlobalEvent(String appId, String gameId,
                                         LukControlEventType eventType, Map<String, Object> eventData) {
        LukControlEventRequest request = LukControlEventRequest.createGlobalEvent(
                appId, gameId, eventType, eventData);
        return publishControlEvent(request);
    }

    /**
     * 执行控制事件发布
     */
    private Response<?> doPublishControlEvent(SDK lukSdk, LukControlEventRequest request,
                                            GameInfoBean gameInfoBean, GameChannelBean gameChannelBean) {
        try {
            // 构建PublishControlEventRequest
            PublishControlEventRequest.Builder builder = new PublishControlEventRequest.Builder();
            builder.setAppId(Integer.parseInt(gameChannelBean.getAppId()));
            builder.setGameId(Math.toIntExact(gameInfoBean.getChannelGameId()));
            builder.setTimestamp(request.getTimestamp() != null ? request.getTimestamp() : System.currentTimeMillis());
            builder.setType(request.getEventType().getType());

            // 设置房间ID（如果是房间事件）
            if (request.getEventType().isRoomEvent() && StrUtil.isNotBlank(request.getRoomId())) {
                builder.setRoomId(request.getRoomId());
            }

            // 设置事件数据
            String dataJson = JSONObject.toJSONString(request.getEventData());
            builder.setData(dataJson);

            log.info("Publishing LUK control event, type: {}, appId: {}, gameId: {}, roomId: {}, dataJson: {}",
                    request.getEventType(), gameChannelBean.getAppId(), gameInfoBean.getChannelGameId(), request.getRoomId(), dataJson);

    /**
     * 创建 LUK SDK 实例
     *
     * @param appId 应用ID
     * @return SDK 实例，如果无法创建则返回 null
     */
    public SDK createLukSdk(String appId, String gameId) {
        if (StringUtils.isBlank(appId) || StringUtils.isBlank(gameId)) {
            log.error("AppId is blank, cannot create LUK SDK");
            return null;
        }
        try {

            // 查询gameId是属于游戏渠道的gameId，还是属于seal平台biz_gameId
            GameBizGameBean bizGameBean = bizGameManager.getGame(appId, gameId);
            if (bizGameBean == null) {
                throw new IllegalArgumentException("gameId not exist. appId=" + appId + ", gameId=" + gameId);
            }

            // 获取游戏信息
            GameInfoBean gameInfoBean = gameInfoManager.getGameInfoBeanById(bizGameBean.getGameInfoId());
            if (gameInfoBean == null) {
                throw new IllegalArgumentException("gameInfo not exist. appId=" + appId + ", gameId=" + gameId);
            }

            // 获取渠道信息
            GameChannelBean gameChannelBean = gameChannelManager.getGameChannel(bizGameBean.getChannelId());
            if (gameChannelBean == null) {
                throw new IllegalArgumentException("channel not exist. appId=" + appId + ", gameId=" +gameId);
            }

            return createLukSdk(bizGameBean, gameChannelBean, appId);

        } catch (Exception e) {
            log.error("Error creating LUK SDK with appId: {}", appId, e);
            return null;
        }
    }

    /**
     * 获取 LUK 渠道的 SDK 实例
     *
     * @param channelId 渠道ID
     * @param gameId
     * @return SDK 实例
     */
    public Pair<SDK, GameChannelBean> createLukSdk(Long channelId, Long gameId, String appId){
        GameChannelBean gameChannel = gameChannelManager.getGameChannel(channelId);
        if(null == gameChannel){
            return null;
        }

        GameCallback gameCallback = gameCallbackManager.getGameCallback(appId, String.valueOf(gameId),
                gameCallbackManager.getCallBackTypeByGameInterface(), null
        );
        SDK sdk = new SDK(gameChannel.getAppSecret(), gameCallback.getUrl());
        return Pair.of(sdk, gameChannel);

    }

    public SDK initLukSdk(String appId, String channel) {
        GameChannelBean gameChannelBean = gameAppManager.getChannelInfoByAppIdAndChannel(appId, channel);
        return new SDK(gameChannelBean.getAppSecret(), gameChannelBean.getBaseUrl());
    }


            return lukSdk.PublishControlEvent(builder.build());

        } catch (Exception e) {
            log.error("Error executing LUK control event publish", e);
            return null;
        }
    }

}
