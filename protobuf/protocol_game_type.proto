package fm.lizhi.commons.template.datacenter.protocol;

option java_package = "fm.lizhi.ocean.seal.protocol";
option java_outer_classname = "GameTypeServiceProto";

message GameType{
    optional int64 id = 1;
    optional string typeName = 2; //名称:竞技游戏 纸牌游戏
}

// GameTypeService.java
// 获取游戏类型列表
// domain = 4302, op = 210
message RequestGetAllTypes {
}
// rcode == 0(SUCCESS) = 执行成功
// rcode == 1(FAIL) = 错误
message ResponseGetAllTypes {
    repeated GameType gameTypes = 1;
}


