package fm.lizhi.commons.template.datacenter.protocol;

option java_package = "fm.lizhi.ocean.seal.protocol";
option java_outer_classname = "SealTokenServiceProto";
option optimize_for = SPEED;

message SealHashingTokenParam{
    optional int64 userId = 1; //业务对应的标识码。使用userId作为标识码
    optional string appId = 2; //appId
    optional int64 currentTimeMillis = 3; //时间戳
    optional string hashingToken = 4; //验证token，验证的时候把它放进去
    optional bool notVerifySign = 5; // 不进行验签名.
}

message JwtUserInfo{
    optional int64 userId = 1; //用户id
    optional string userName = 2; //用户名称
    optional string role = 3;//用户角色
}

// SealTokenService.java
// 根据传入的参数，获取Seal sdk与服务端通信的短期token。这个方法与SealAuthService的区别是，这个是新版的方案jwt+缓存。userId 业务对应的标识码。建议使用userId作为标识码 hashingToken 验证token，验证的时候把它放进去
// domain = 4302, op = 100
message RequestGetSealHashingToken {
    optional SealHashingTokenParam sealHashingTokenParam = 1; // {@link SealHashingTokenParam}
}

// rcode == 0(SUCCESS) = 执行成功
// rcode == 1(FAIL) = 非法用户
// rcode == 2(ILLEGAL_PARAMS) = 参数非法
// rcode == 3(ERROR) = 服务错误
message ResponseGetSealHashingToken {
    optional string sealToken = 1; //获取sealToken
    optional int64 expireDate = 2; //长期令牌过期时间戳（毫秒）
}

// SealTokenService.java
// 验证SealToken是否正确.如果正确token,且token未过期，将会进行续命
// domain = 4302, op = 101
message RequestVerifySealToken {
    optional string sealToken = 1; //sealToken获取用户信息
    optional string appId = 2;
}

// rcode == 0(SUCCESS) = 执行成功
// rcode == 1(INVALID) = 无效Token
// rcode == 2(ERROR) = 服务错误
message ResponseVerifySealToken {
}

// SealTokenService.java
// 根据用户SealToken获取用户信息
// domain = 4302, op = 102
message RequestGetUserInfoBySealToken {
    optional string sealToken = 1; //sealToken获取用户信息
    optional string appId = 2;
}
// rcode == 0(SUCCESS) = 执行成功
// rcode == 1(FAIL) = 无效Token
// rcode == 2(ILLEGAL_PARAMS) = 参数非法
// rcode == 3(ERROR) = 服务错误
message ResponseGetUserInfoBySealToken {
    optional JwtUserInfo sealUser = 1;
}