package fm.lizhi.commons.template.datacenter.protocol;

option java_package = "fm.lizhi.ocean.seal.protocol";
option java_outer_classname = "GameChannelServiceProto";

message GameChannel{
    optional int64 id = 1;
    optional string abbreviation = 2; //简称
    optional string channelName = 3; //渠道名称
}

message EnumGameChannel{
    optional string labelChannel = 1; //渠道枚举label
    optional string valueChannel = 2; //渠道枚举value
}

// GameChannelService.java
// 获取游戏渠道列表
// domain = 4302, op = 220
message RequestGetAllGameChannels {
}
// rcode == 0(SUCCESS) = 执行成功
// rcode == 1(FAIL) = 错误
message ResponseGetAllGameChannels {
    repeated GameChannel gameChannels = 1;
}

// GameChannelService.java
// 获取游戏渠道枚举列表
// domain = 4302, op = 221
message RequestGetEnumGameChannels {
}
// rcode == 0(SUCCESS) = 执行成功
// rcode == 1(FAIL) = 错误
message ResponseGetEnumGameChannels {
    repeated EnumGameChannel enumGameChannel = 1;
}

message GetChannelTokenParam {
    required int64 userId = 1;
    required string appId = 2;
    required string channel = 3;
}

message ChannelToken {
    required string token = 1;
    required int64 expireTime = 2;
}

// GameChannelService.java
// 获取平台SDK与渠道交互的Token
// domain = 4302, op = 222
message RequestGetChannelToken {
    required GetChannelTokenParam param = 1;
}

// rcode == 1 (ILLEGAL_PARAMS) = 参数非法
// rcode == 2 (ERROR) = 内部错误
message ResponseGetChannelToken {
    required ChannelToken token = 1;
}


