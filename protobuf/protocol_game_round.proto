package fm.lizhi.commons.template.datacenter.protocol;

option java_package = "fm.lizhi.ocean.seal.protocol";
option java_outer_classname = "GameRoundServiceProto";
option optimize_for = SPEED;

message RelationGameRoundParam {
  required string channelRoundId = 1; // 渠道游戏局ID
  required string extra = 2; // 透传参数
}

// GameRoundService.java
// 关联游戏局信息
// domain = 4302, op = 81
message RequestRelationGameRound {
  optional RelationGameRoundParam param = 1; //参数
}

// rcode == 1 (ILLEGAL_PARAMS) = 参数非法
// rcode == 2 (NOT_EXISTS) = 平台局ID不存在
// rcode == 3 (ERROR) = 内部错误
message ResponseRelationGameRound {
}

/** 加载游戏参数 */
message StartGameRoundParam {
  required string appId = 1; // 业务方appId，为了兼容已有业务用string类型
  required string gameId = 2; // Seal平台游戏ID，也可以是渠道游戏ID，服务端内部通过gameId和appId组合识别
  required string groupId = 3; // 组ID
  optional string extra = 4; // 透传参数，可选值，可以根据具体场景如果有则传递
}

/** 加载游戏，生成平台游戏局 */
message GameRound {
  required int64 roundId = 2; // 游戏局ID
}

// GameRoundService.java
// 开始平台游戏局，客户端在开始游戏的时候调用
// domain = 4302, op = 82
message RequestStartGameRound {
  optional StartGameRoundParam param = 1; //参数
}

// rcode == 1 (ILLEGAL_PARAMS) = 参数非法
// rcode == 2 (NOT_EXISTS) = 游戏ID不存在
// rcode == 3 (ERROR) = 内部错误
message ResponseStartGameRound {
  optional GameRound gameRound = 1;
}

/** 结束游戏局参数 */
message EndGameRoundParam {
  required string appId = 1; // 业务方appId，为了兼容已有业务用string类型
  required string gameId = 2; // Seal平台游戏ID，也可以是渠道游戏ID，服务端内部通过gameId和appId组合识别
  required string groupId = 3; // 组ID
  required string channelRoundId = 4; // 渠道局ID
}

// GameRoundService.java
// 开始平台游戏局，客户端在开始游戏的时候调用
// domain = 4302, op = 83
message RequestEndGameRound {
  optional EndGameRoundParam param = 1; //参数
}

// rcode == 1 (ILLEGAL_PARAMS) = 参数非法
// rcode == 2 (NOT_EXISTS) = 游戏ID不存在
// rcode == 3 (ERROR) = 内部错误
message ResponseEndGameRound {
}