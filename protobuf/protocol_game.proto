package fm.lizhi.commons.template.datacenter.protocol;

option java_package = "fm.lizhi.ocean.seal.protocol";
option java_outer_classname = "GameServiceProto";
option optimize_for = SPEED;

/** 游戏详情 */
message GameDetail {
  required int64 gameId = 1; // 平台游戏ID
  required string renderType = 2; // 渲染类型，具体看：RenderType
  required string channelGameId = 3; // 渠道游戏ID
  required string channel = 4; // 渠道
  required string config = 5; // 配置信息，JSON结构
  required string gameName = 6; // 游戏名称
  required int32 captain = 7; // 是否需要队长
  optional string url = 8; // 游戏加载地址
}

/** 获取游戏详情参数 */
message GetGameDetailParam {
  required string appId = 1; // 业务方appId，为了兼容已有业务用string类型
  required string gameId = 2; // 为了兼容已有业务，可能是渠道游戏ID，也可能是平台游戏ID
}

// GameService.java
// 获取游戏详情
// domain = 4302, op = 61
message RequestGetGameDetail {
  optional GetGameDetailParam param = 1; //参数
}

// rcode == 1 (ILLEGAL_PARAMS) = 参数非法
// rcode == 2 (NOT_EXISTS) = 游戏ID不存在
// rcode == 3 (ERROR) = 内部错误
message ResponseGetGameDetail {
  optional GameDetail gameDetail = 1;
}

/** 更新游戏配置信息参数 */
message UpdateGameConfigParam {
  required string appId = 1; // 业务方appId，为了兼容已有业务用string类型
  required string gameId = 2; // 为了兼容已有业务，可能是渠道游戏ID，也可能是平台游戏ID
  required string config = 3; // 配置格式：{"minPlayer": 2, "maxPlayer": 4}
}

// GameService.java
// 更新游戏配置信息
// domain = 4302, op = 62
message RequestUpdateGameConfig {
  optional UpdateGameConfigParam param = 1; //参数
}

// rcode == 1 (ILLEGAL_PARAMS) = 参数非法
// rcode == 2 (NOT_EXISTS) = 游戏ID不存在
// rcode == 3 (ERROR) = 内部错误
message ResponseUpdateGameConfig {
}

message GameVersionParams {
  required int64 gameVersionCode = 1; // 游戏版本号，Builder号
  required string gameId = 2; // 游戏ID，可能是平台游戏ID也有可能是渠道游戏ID
}

message GameVersionInfo {
  /** 游戏版本号，Builder号 */
  required int64 gameVersionCode = 1;
  /** 平台游戏ID */
  required int64 gameId = 3;
  /** 渠道游戏ID */
  required string channelGameId = 4;
  /** 是否强制更新 */
  required bool forceUpdate = 5;
  /** 下载链接 */
  required string downloadLink = 6;
}

// GameService.java
// 获取游戏版本信息
// domain = 4302, op = 63
message RequestGetGameVersionInfo {
  required string appId = 1; // 业务方appId
  required int32 systemType = 2; // app 系统版本 0 未知 1 ios 2 android
  required int64 sdkVersionCode = 3; // SDK版本号，Builder号
  repeated GameVersionParams gameVersionParams = 4; // 获取游戏版本参数
  optional int64 userId = 5;//用户ID
}


// rcode == 1 (ILLEGAL_PARAMS) = 参数非法
// rcode == 3 (ERROR) = 内部错误
message ResponseGetGameVersionInfo {
  repeated GameVersionInfo gameVersionInfos = 1;
}

/** 游戏加载信息详情 */
message GameLoadingConfig {
  required int64 gameId = 1; // 平台游戏ID
  required string url = 2; // 加载 url
  required string gZipUrl = 3; // 压缩包 URl
}

/** 获取游戏加载配置参数 */
message GetGameLoadingConfigParam {
  required string appId = 1; // 业务方appId，为了兼容已有业务用string类型
  required string gameId = 2; // 为了兼容已有业务，可能是渠道游戏ID，也可能是平台游戏ID
}


// GameService.java
// 获取游戏加载配置
// domain = 4302, op = 64
message RequestGetGameLoadingConfig {
  optional GetGameLoadingConfigParam param = 1; //参数
}

// rcode == 1 (ILLEGAL_PARAMS) = 参数非法
// rcode == 2 (NOT_EXISTS) = 游戏ID不存在
// rcode == 3 (ERROR) = 内部错误
message ResponseGetGameLoadingConfig {
  optional GameLoadingConfig gameLoadingConfig = 1;
}
