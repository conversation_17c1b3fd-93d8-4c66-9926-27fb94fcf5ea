package fm.lizhi.commons.template.datacenter.protocol;

option java_package = "fm.lizhi.ocean.seal.protocol";
option java_outer_classname = "GameLanguageServiceProto";

message GameLanguage{
    optional int64 id = 1;
    optional string abbreviation = 2; //简称 en ar zh
    optional string languageName = 3; //中文名称
}

// GameLanguageService.java
// 获取游戏语言列表
// domain = 4302, op = 200
message RequestGetAllLanguages {
}
// rcode == 0(SUCCESS) = 执行成功
// rcode == 1(FAIL) = 错误
message ResponseGetAllLanguages {
    repeated GameLanguage gameLanguages = 1;
}


