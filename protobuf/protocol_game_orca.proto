package fm.lizhi.commons.template.datacenter.protocol;

option java_package = "fm.lizhi.ocean.seal.protocol";
option java_outer_classname = "GameOrcaApiProto";

/*渠道表*/
message GameChannelPB{
    optional int64 id = 1;//主键ID
    optional string name = 2; //渠道名称
    optional string channel = 3; //渠道代号
    optional string appId = 4; //渠道appId
    optional string appKey = 5; //渠道appKey
    optional string appSecret = 6; //渠道appSecret
    optional string extConfig = 7; //扩展参数
    optional int64 createTime = 8; //创建时间
    optional int64 modifyTime = 9; //修改时间
}

/*业务表*/
message GameAppInfoPB{
    optional int64 id = 1;
    optional string appId = 2;
    optional string appSecret = 3;
    optional string appName = 4;
    optional string appAlias = 5;
    optional string appTopic = 6;
}

/*游戏表*/
message GameInfoPB{
    optional int64 id = 1;
    optional string channelGameId = 2;
    optional string name = 3;
    optional string desc = 4;
    optional string extraJson = 5; // 扩展参数
    optional int32 renderType = 6;//客户端渲染类型，0：WEB，1：NATIVE 默认为1
    optional int32 captain = 7;//是否需要队长，1：是，0：否
    optional string channel = 8;
}

/*分配游戏给到业务*/
message GameBizGamePB{
    optional int64 id = 1;//此ID为sealGameId
    optional int64 gameAppId = 2;//业务的ID
    optional int64 gameInfoId = 3;//游戏表的ID
    optional int64 channelId = 4;//渠道表的ID
    optional string appId = 5;
    optional string channelGameId = 6;//游戏渠道ID
    optional string config = 7;//个性化参数
    optional string appName = 8;
    optional string name = 9;
    optional string channel = 10;
}

/*游戏回调配置*/
message GameCallbackPB{
    optional int64 id = 1;
    optional string appId = 2;
    optional string callbackKey = 3;
    optional int32 type = 4;//回调类型，1：用户信息回调 2 回调业务地址 3 游戏接口地址 12 线上业务地址 13线上接口地址
    optional string url = 5;
}

/*游戏镜像包管理*/
message GameVersionPB{
    optional int64 id = 1;
    optional string appId = 2;
    optional int64 gameId = 3;
    optional int32 systemType = 4;//系统版本：0 未知 1 ios 2 android
    optional int64 minSdkVersion = 5;//支持最小seal sdk 版本
    optional int64 gameVersion = 6;//游戏版本
    optional int32 forceUpdate = 7;//true：强制更新，false：非强制更新
    optional string downloadLink = 8;//游戏下载链接
    optional int32 gameFlow = 9; // 游戏流量：0%～100%，0为关闭该游戏下载，100为开放所有用户下载。用于A/B流量
    optional string extConfig = 10; // 扩展参数，可用whiteList属性明表示白名单。标志为白名单的用户ID可直接下载
    optional int64 createTime = 11;
    optional int64 modifyTime = 12;
}

message SearchVersionParam{
    optional string appId = 1;
    optional int64 gameId = 2;
    optional int32 systemType = 3;//系统版本：0 未知 1 ios 2 android
    optional int64 gameVersion = 4;//游戏版本
    optional int32 pageSize = 5;
    optional int32 pageNumber = 6;
}

// GameOrcaApiService.java
// 新增或编辑渠道
// domain = 4302, op = 500
message RequestAddOrUpdateChannel {
    optional GameChannelPB gameChannel = 1;
}

// GameOrcaApiService.java
// 根据ID查询渠道
// domain = 4302, op = 501
message RequestGetChannelById {
    optional int64 channelId = 1;
}
// rcode == 0(SUCCESS) = 执行成功
// rcode == 1(FAIL) = 错误
message ResponseGetChannelById {
    optional GameChannelPB gameChannel = 1;
}

// GameOrcaApiService.java
// 列表查询渠道
// domain = 4302, op = 502
message RequestQueryPageGameChannels {
    optional int64 channelId = 1;
    optional string appId = 2;
    optional int32 pageSize = 3;
    optional int32 pageNumber = 4;

}
// rcode == 0(SUCCESS) = 执行成功
// rcode == 1(FAIL) = 错误
message ResponseQueryPageGameChannels {
    repeated GameChannelPB gameChannels = 1;
    optional int64 total = 2;
    optional int32 pageSize = 3;
    optional int32 pageNumber = 4;
}

// GameOrcaApiService.java
// 删除渠道
// domain = 4302, op = 503
message RequestDelGameChannel {
    optional int64 id = 1;
}

// GameOrcaApiService.java
// 根据ID获取游戏信息
// domain = 4302, op = 504
message RequestGetAppInfoById {
  optional int64 id = 1;
}

// rcode == 1 (ILLEGAL_PARAMS) = 参数非法
// rcode == 2 (NOT_EXISTS) = app不存在
// rcode == 3 (ERROR) = 内部错误
message ResponseGetAppInfoById {
  optional GameAppInfoPB gameAppInfo = 1;
}

// GameOrcaApiService.java
// 新增或编辑GameApp
// domain = 4302, op = 505
message RequestAddOrUpdateGameApp {
  optional GameAppInfoPB gameAppInfo = 1;
}

// GameOrcaApiService.java
// 根据ID删除gameApp
// domain = 4302, op = 506
message RequestDelGameAppById {
  optional int64 id = 1;
}

// GameOrcaApiService.java
// 列表查询GameApp
// domain = 4302, op = 507
message RequestQueryPageGameApp {
    optional int64 id = 1;
    optional string appId = 2;
    optional int32 pageSize = 3;
    optional int32 pageNumber = 4;

}
// rcode == 0(SUCCESS) = 执行成功
// rcode == 1(FAIL) = 错误
message ResponseQueryPageGameApp {
    repeated GameAppInfoPB gameAppInfos = 1;
    optional int64 total = 2;
    optional int32 pageSize = 3;
    optional int32 pageNumber = 4;
}

// GameOrcaApiService.java
// 根据ID获取游戏信息
// domain = 4302, op = 508
message RequestGetGameById {
  optional int64 id = 1;
}

// rcode == 1 (ILLEGAL_PARAMS) = 参数非法
// rcode == 2 (NOT_EXISTS) = 不存在
// rcode == 3 (ERROR) = 内部错误
message ResponseGetGameById {
  optional GameInfoPB gameInfo = 1;
}

// GameOrcaApiService.java
// 新增或编辑
// domain = 4302, op = 509
message RequestAddOrUpdateGame {
  optional GameInfoPB gameInfo = 1;
}

// GameOrcaApiService.java
// 根据ID删除game
// domain = 4302, op = 510
message RequestDelGameById {
  optional int64 id = 1;
}

// GameOrcaApiService.java
// 列表查询Game
// domain = 4302, op = 511
message RequestQueryPageGame {
    optional int64 id = 1;
    optional string channel = 2;
    optional string name = 3;
    optional int32 pageSize = 4;
    optional int32 pageNumber = 5;

}
// rcode == 0(SUCCESS) = 执行成功
// rcode == 1(FAIL) = 错误
message ResponseQueryPageGame {
    repeated GameInfoPB gameInfoPBs = 1;
    optional int64 total = 2;
    optional int32 pageSize = 3;
    optional int32 pageNumber = 4;
}

// GameOrcaApiService.java
// 根据ID获取关联信息
// domain = 4302, op = 512
message RequestGetGameRelationById {
  optional int64 id = 1;
}

// rcode == 1 (ILLEGAL_PARAMS) = 参数非法
// rcode == 2 (NOT_EXISTS) = 不存在
// rcode == 3 (ERROR) = 内部错误
message ResponseGetGameRelationById {
  optional GameBizGamePB gameRelation = 1;
}

// GameOrcaApiService.java
// 新增或编辑
// domain = 4302, op = 513
message RequestAddOrUpdateGameRelation {
  optional GameBizGamePB gameBizGame = 1;
}

// GameOrcaApiService.java
// 根据ID删除,此处会删除兼容表
// domain = 4302, op = 514
message RequestDelGameRelationById {
  optional int64 id = 1;
}

// GameOrcaApiService.java
// 列表查询
// domain = 4302, op = 515
message RequestQueryPageGameRelation {
    optional int64 id = 1;
    optional int64 gameAppId = 2;//业务的ID
    optional int64 gameInfoId = 3;//游戏表的ID
    optional int64 channelId = 4;//渠道表的ID
    optional int32 pageSize = 5;
    optional int32 pageNumber = 6;

}
// rcode == 0(SUCCESS) = 执行成功
// rcode == 1(FAIL) = 错误
message ResponseQueryPageGameRelation {
    repeated GameBizGamePB gameBizGamePBs = 1;
    optional int64 total = 2;
    optional int32 pageSize = 3;
    optional int32 pageNumber = 4;
}

// GameOrcaApiService.java
// 根据ID获取回调配置
// domain = 4302, op = 516
message RequestGetGameCallbackById {
  optional int64 id = 1;
}

// rcode == 1 (ILLEGAL_PARAMS) = 参数非法
// rcode == 2 (NOT_EXISTS) = 不存在
// rcode == 3 (ERROR) = 内部错误
message ResponseGetGameCallbackById {
  optional GameCallbackPB gameCallbackPB = 1;
}

// GameOrcaApiService.java
// 新增或编辑
// domain = 4302, op = 517
message RequestAddOrUpdateGameCallback {
  optional GameCallbackPB gameCallbackPB = 1;
}

// GameOrcaApiService.java
// 根据ID删除
// domain = 4302, op = 518
message RequestDelGameCallBackById {
  optional int64 id = 1;
}

// GameOrcaApiService.java
// 列表查询
// domain = 4302, op = 519
message RequestQueryPageGameCallback {
    optional string appId = 1;
    optional int32 pageSize = 2;
    optional int32 pageNumber = 3;

}
// rcode == 0(SUCCESS) = 执行成功
// rcode == 1(FAIL) = 错误
message ResponseQueryPageGameCallback {
    repeated GameCallbackPB gameCallbackPBs = 1;
    optional int64 total = 2;
    optional int32 pageSize = 3;
    optional int32 pageNumber = 4;
}

// GameOrcaApiService.java
// 根据ID获取游戏镜像信息
// domain = 4302, op = 520
message RequestGetGameVersionById {
  optional int64 id = 1;
}

// rcode == 1 (ILLEGAL_PARAMS) = 参数非法
// rcode == 2 (NOT_EXISTS) = 不存在
// rcode == 3 (ERROR) = 内部错误
message ResponseGetGameVersionById {
  optional GameVersionPB gameVersionPB = 1;
}

// GameOrcaApiService.java
// 新增或编辑
// domain = 4302, op = 521
message RequestAddOrUpdateGameVersion {
  optional GameVersionPB gameVersionPB = 1;
}

// GameOrcaApiService.java
// 根据ID删除
// domain = 4302, op = 522
message RequestDelGameVersionById {
  optional int64 id = 1;
}

// GameOrcaApiService.java
// 列表查询
// domain = 4302, op = 523
message RequestQueryPageGameVersion {
    optional SearchVersionParam searchVersionParam = 1;
}
// rcode == 0(SUCCESS) = 执行成功
// rcode == 1(FAIL) = 错误
message ResponseQueryPageGameVersion {
    repeated GameVersionPB gameVersionPBs = 1;
    optional int64 total = 2;
    optional int32 pageSize = 3;
    optional int32 pageNumber = 4;
}




