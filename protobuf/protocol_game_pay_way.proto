package fm.lizhi.commons.template.datacenter.protocol;

option java_package = "fm.lizhi.ocean.seal.protocol";
option java_outer_classname = "GamePayWayServiceProto";

message GamePayWay{
    optional int32 payWayCode = 1;
    optional string payWayName = 2;
}

// GamePayWayService.java
// 后去支付方式。此处本来不需要提供接口，不过因为特殊原因，支持orca的泛化调用，所以提供接口
// domain = 4302, op = 230
message RequestGetAllGamePayWays {
}
// rcode == 0(SUCCESS) = 执行成功
// rcode == 1(FAIL) = 错误
message ResponseGetAllGamePayWays {
    repeated GamePayWay gamePayWays = 1;
}


