package fm.lizhi.commons.template.datacenter.protocol;

option java_package = "fm.lizhi.ocean.seal.protocol";
option java_outer_classname = "GameReportServiceProto";
option optimize_for = SPEED;

message GamePlayerResult{
    optional string uid = 1; //游戏用户ID
    optional bool realUser = 2; //是否是真实用户
}

message GamePlayerSettleResult{
    optional string uid = 1; //游戏用户ID
    optional bool realUser = 2; //是否是真实用户
    optional bool escaped = 3 ; //是否逃跑
    optional int32 rank = 4; //排名，从1开始，平局排名相同
    optional int32 score = 5; //得分
    optional int32 role = 6; //游戏角色
    optional int32 isWin = 7; //胜负结果
    optional int32 award = 8; //奖励
}

message GameStartResult{
    optional int64 gameId = 1; //游戏厂商ID，废弃
    optional string roomId = 2; //房间ID
    optional int32 gameMode = 3; //游戏模式
    optional string gameRoundId = 4; //本局游戏的id （可能会重复上报，使用该字段去重）
    optional int64 gameStartAtTime = 5; //游戏真正开始时间(毫秒)
    repeated GamePlayerResult gamePlayers = 6; //GamePlayer对象数组
    optional string reportGameInfoExtras = 7; //游戏上报信息扩展参数（透传），取值范围：长度不超过1024字节，超过则截断
    optional string appId = 8; //业务方appId
    optional int32 env = 9; //所属环境
    optional string channel = 10; // 游戏渠道
    optional string channelGameId = 11; // 渠道游戏ID
    optional string rawResult = 12; // 渠道原始通知消息
}

message GameSettleResult{
    optional int64 gameId = 1; //游戏厂商ID，废弃
    optional string roomId = 2; //房间ID
    optional int32 gameMode = 3; //游戏模式
    optional string gameRoundId = 4; //本局游戏的id （可能会重复上报，使用该字段去重）
    optional int64 gameStartAtTime = 5; //游戏真正开始时间(毫秒)
    optional int64 gameEndAtTime = 6; //游戏真正结束时间(毫秒)
    optional int32 gameDuration = 7; //游戏真正的进行时间(秒)
    repeated GamePlayerSettleResult gamePlayers = 8; //GamePlayer对象数组
    optional string appId = 9; //appID
    optional int32 env = 10; //所属环境
    optional string reportGameInfoExtras = 11; //游戏上报信息扩展参数（透传），取值范围：长度不超过1024字节，超过则截断
    optional string channel = 12; //游戏渠道
    optional string channelGameId = 13; // 渠道游戏ID
    optional string rawResult = 14; // 渠道原始通知消息
}

message GameResult {
    optional string gameRoundId = 1; //本局游戏的id （可能会重复上报，使用该字段去重）
    optional GameSettleResult settleResult = 2; //游戏结束信息
    optional GameStartResult startResult = 3; //游戏开始信息
}

message RoomUsersChangedResult {
    optional string roomId = 1; //房间id
    optional string mgId = 2; //游戏id
    optional int32 player_total = 3; //玩家总人数
    optional int32 ob_total = 4; //观众总人数
    optional string changed_time = 5; //变更时间戳(毫秒)
    optional string appId = 6; //业务方appId
    optional string channel = 7; // 游戏渠道
    optional string rawResult = 8; // 渠道原始通知消息
}

message RoomGameUserHeartBeat {
    optional int64 njId = 1; //房主id
    optional int64 gameId = 2; //平台分配给业务方的游戏id
    optional string appId = 3; //平台分配给业务方的appId
    optional int64 userId = 4; //用户id
    optional int64 changed_time = 5; //变更时间戳(毫秒)
}

// GameReportService.java
// 上报游戏开始信息
// domain = 4302, op = 11
message RequestGameStartReport {
    optional GameStartResult result = 1; //游戏开始信息
}

// GameReportService.java
// 上报游戏结算信息
// domain = 4302, op = 12
message RequestGameSettleReport {
    optional GameSettleResult result = 1; //游戏结算信息
}

// GameReportService.java
// 获取游戏上报数据
// domain = 4302, op = 13
message RequestGetRoomGameReportList {
    optional string roomId = 1; //房间ID
    optional string gameChannel = 2; //游戏所属渠道
    optional int32 pageNum = 3; //页码(1开始)
    optional int32 pageCount = 4; //每页条数(最大不超过10)
    optional string appId = 5; //所属appId
}

// rcode == 1(ILLEGAL_PARAMS) = 参数非法
// rcode == 2(FAIL) = 失败
// rcode == 3(NO_DATA) = 没有数据
message ResponseGetRoomGameReportList {
    repeated GameResult result = 1; //游戏数据信息
}

// GameReportService.java
// 获取游戏开始上报信息
// domain = 4302, op = 14
message RequestGetGameSettleReport {
    optional string gameRoundId = 1; //游戏开始信息
    optional string gameChannel = 2; //游戏所属渠道
    optional string appId = 3; //所属appId
}

// rcode == 1(ILLEGAL_PARAMS) = 参数非法
// rcode == 2(FAIL) = 失败
// rcode == 3(NO_DATA) = 没有数据
message ResponseGetGameSettleReport {
    optional GameSettleResult result = 1; //游戏结束信息
}

// GameReportService.java
// 上报游戏房间用户人数变更
// domain = 4302, op = 15
message RequestRoomUsersChanged {
    optional RoomUsersChangedResult result = 1; //房间用户人数变更数据
}

// GameReportService.java
// 模拟业务发送用户心跳（demo）
// domain = 4302, op = 10
message RequestSendUserHeart {
    optional RoomGameUserHeartBeat result = 1; //房间用户人数变更数据
}

