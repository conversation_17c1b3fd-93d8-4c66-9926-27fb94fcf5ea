package fm.lizhi.commons.template.datacenter.protocol;

option java_package = "fm.lizhi.ocean.seal.protocol";
option java_outer_classname = "SealAuthServiceProto";
option optimize_for = SPEED;


message SealToken{
    optional string token = 1; //长期令牌
    optional int64 expireDate = 2; //长期令牌过期时间戳（毫秒）
}

message JwtUserInfo{
    optional int64 userId = 1; //用户id
    optional string userName = 2; //用户名称
    optional string role = 3;//用户角色
}

// SealAuthService.java
// 获取短期令牌，默认时长2小时
// domain = 4302, op = 999
message RequestGetSealToken {
    optional int64 userId = 1; //业务用户Id
    optional int32 appId = 2; //appId
    optional string channel = 3; //游戏渠道
    optional string checkCode = 4;//用户校验码
}
// rcode == 0(SUCCESS) = 执行成功
// rcode == 1(FAIL) = 非法用户
// rcode == 2(ILLEGAL_PARAMS) = 参数非法
message ResponseGetSealToken {
    optional SealToken token = 1;
}


// SealAuthService.java
// 获取令牌信息
// domain = 4302, op = 1000
message RequestGetUserInfoBySealToken {
    optional string token = 1; //业务用户Id
    optional string appId = 2; //appId
    optional string channel = 3; //游戏渠道
}
// rcode == 0(SUCCESS) = 执行成功
// rcode == 1(FAIL) = 无效Token
// rcode == 2(ILLEGAL_PARAMS) = 参数非法
// rcode == 1001(TOKEN_EXPIRED) = Token过期
message ResponseGetUserInfoBySealToken {
    optional JwtUserInfo sealUser = 1;
}


// SealAuthService.java
// 获取长期令牌，默认时长2天
// domain = 4302, op = 1001
message RequestGetRefreshSealToken {
    optional string token = 1; //业务用户Id
    optional string channel = 2; //游戏渠道
}
// rcode == 0(SUCCESS) = 执行成功
// rcode == 1(FAIL) = 非法用户
// rcode == 2(ILLEGAL_PARAMS) = 参数非法
message ResponseGetRefreshSealToken {
    optional SealToken token = 1;
}