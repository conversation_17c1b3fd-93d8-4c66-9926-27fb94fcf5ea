package fm.lizhi.commons.template.datacenter.protocol;

option java_package = "fm.lizhi.ocean.seal.protocol";
option java_outer_classname = "GameInfoServiceProto";

message GameDetail{
    optional int64 id = 1;
    optional string gameName = 2;
    optional int64 channelGameId = 3; //第三方游戏的频道ID
    optional string description = 4; //描述
    optional string channel = 5;//第三方
    repeated string imageUrls = 6;
    repeated string videoUrls = 7;
    repeated string languageAbbreviation = 8;//语言简称 zh、en
    optional int32 gameAccessType = 9;//接入状态 0-已接入 1-待接入
    repeated int64 gameTypeId = 10;//游戏类型 团站、纸牌
    optional int32 gamePayWayCode = 11;//付费方式 {@link fm.lizhi.ocean.seal.constant.GamePayWayEnum}
    optional int32 lowExpense = 12;//最低消费:0没有 1有
    optional string lowExpenseValue = 13;//最低消费的值
    optional string expenseRule = 14;//计费规则
    optional string operator = 15;
}

message GetGameDetailsParam{
    optional string gameName = 1;//游戏名称
    repeated int64 gameTypes = 2;//游戏类型
    optional int32 gameAccessType = 3;//接入状态
}

message GameInfoList{
    optional int64 sealGameId = 1; //小游戏平台的游戏ID gameInfoId 业务不需要关心此ID
    optional int64 channelGameId = 2; //游戏厂商ID
    optional string name = 3; //游戏名称
    optional string desc = 4; //游戏描述
    optional string channel = 5; //游戏渠道简称
    optional int64 channelId = 6; //游戏渠道ID
    optional string configJson = 7; //对应业务游戏配置.这里有个问题，就是如果直接根据游戏厂商、渠道不能定位到具体的业务游戏配置。
    optional int64 bizGameId = 8;//对应业务游戏配置.这里有个问题，就是如果直接根据游戏厂商、渠道不能定位到具体的业务游戏配置。
    optional string channelGameIdStr = 9; //渠道游戏ID，后续都是使用这个字段。
}

message GameInfo{
    optional int64 sealGameId = 1; //小游戏平台的游戏ID gameInfoId
    optional int64 channelGameId = 2; //游戏厂商ID
    optional string name = 3; //游戏名称
    optional string desc = 4; //游戏描述
    optional string channel = 5; //游戏渠道简称
}

message GetGameListParam{
    optional string appId = 1; //appId，颁发给业务方的appId
}

message GetGameInfoParam{
    optional int64 channelGameId = 1; //游戏厂商ID
    optional string channel = 2; //游戏来源渠道
}

// GameInfoService.java
// 获取游戏列表
// domain = 4302, op = 16
message RequestGetGameList {
    optional GetGameListParam param = 1; //参数
}

// rcode == 0(SUCCESS) = 执行成功
// rcode == 1(PARAM_FAIL) = 参数错误
// rcode == 2(FAIL) = 错误
message ResponseGetGameList {
    repeated GameInfoList gameInfos = 1;
    optional string appName = 2; // 对应业务的中文名称
    optional string appTopic = 3; //对应业务 时间上报的topic
}

// GameInfoService.java
// 获取游戏信息
// domain = 4302, op = 17
message RequestGetGameInfo {
    optional GetGameInfoParam param = 1; //参数
}

message ResponseGetGameInfo {
    optional GameInfo gameInfo = 1;
}

// GameInfoService.java
// 获取游戏详细信息的列表，包含游戏类型、语言、付费方式、渠道
// domain = 4302, op = 18
message RequestGetGameDetails {
    optional GetGameDetailsParam param = 1;
}

// rcode == 0(SUCCESS) = 执行成功
// rcode == 1(FAIL) = 失败
message ResponseGetGameDetails {
    repeated GameDetail gameDetails = 1;
}

// GameInfoService.java
// 获取游戏详细信息，包含游戏类型、语言、付费方式、渠道
// domain = 4302, op = 19
message RequestGetGameDetail {
    optional int64 id = 1;
}

// rcode == 0(SUCCESS) = 执行成功
// rcode == 1(FAIL) = 失败
message ResponseGetGameDetail {
    optional GameDetail gameDetail = 1;
}

// GameInfoService.java
// 新增游戏详细信息，包含游戏类型、语言、付费方式、渠道
// domain = 4302, op = 20
message RequestAddGameDetail {
    optional GameDetail gameDetail = 1;
}

// rcode == 0(SUCCESS) = 执行成功
// rcode == 1(FAIL) = 失败
message ResponseAddGameDetail {
    optional int64 id = 1;
}

// GameInfoService.java
// 修改游戏详细信息，包含游戏类型、语言、付费方式、渠道
// domain = 4302, op = 21
message RequestModifyGameDetail {
    optional GameDetail gameDetail = 1;
}

// GameInfoService.java
// 删除游戏详细信息，包含游戏类型、语言、付费方式、渠道
// domain = 4302, op = 22
message RequestDelGameDetail {
    optional int64 id = 1;
    optional string operator = 2;
}

