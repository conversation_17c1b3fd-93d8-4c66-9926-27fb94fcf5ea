#protoc --java_out=. --include_source_info --descriptor_set_out=descriptor.desc protocol_game_round.proto > protoc_info.log 2> protoc_error.log
#protoc --java_out=. --include_source_info --descriptor_set_out=descriptor.desc protocol_game_report.proto > protoc_info.log 2> protoc_error.log
#protoc --java_out=. --include_source_info --descriptor_set_out=descriptor.desc protocol_game_channel.proto > protoc_info.log 2> protoc_error.log
#protoc --java_out=. --include_source_info --descriptor_set_out=descriptor.desc protocol_game_info.proto > protoc_info.log 2> protoc_error.log
#protoc --java_out=. --include_source_info --descriptor_set_out=descriptor.desc protocol_game_report.proto > protoc_info.log 2> protoc_error.log
#protoc --java_out=. --include_source_info --descriptor_set_out=descriptor.desc protocol_seal_auth.proto > protoc_info.log 2> protoc_error.log
#protoc --java_out=. --include_source_info --descriptor_set_out=descriptor.desc protocol_game_proxy.proto > protoc_info.log 2> protoc_error.log
#protoc --java_out=. --include_source_info --descriptor_set_out=descriptor.desc protocol_game.proto > protoc_info.log 2> protoc_error.log
#protoc --java_out=. --include_source_info --descriptor_set_out=descriptor.desc protocol_game_auth.proto > protoc_info.log 2> protoc_error.log
protoc --java_out=. --include_source_info --descriptor_set_out=descriptor.desc protocol_game_prop.proto > protoc_info.log 2> protoc_error.log