CREATE TABLE `game_user` (
  `id` bigint(20) NOT NULL,
  `user_id` bigint(20) NOT NULL COMMENT '业务用户id',
  `app_id` varchar(128)  NOT NULL DEFAULT '' COMMENT '用户所属APP',
  `status` int(11) NOT NULL DEFAULT '0' COMMENT '1-启用 0-禁用',
  `create_time` datetime NOT NULL,
  `modify_time` datetime NOT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `user_app_idx` (`user_id`,`app_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='游戏用户表';

CREATE TABLE `game_info` (
  `id` bigint(20) NOT NULL,
  `channel_game_id` bigint(20) NOT NULL COMMENT '用户id',
  `name` varchar(256) COLLATE utf8mb4_bin NOT NULL DEFAULT '' COMMENT '游戏名称',
  `desc` varchar(512) COLLATE utf8mb4_bin NOT NULL DEFAULT '' COMMENT '游戏描述',
  `extra_json` varchar(1024)  NOT NULL DEFAULT '' COMMENT '扩展参数',
  `status` int(11) NOT NULL DEFAULT '0' COMMENT '1-启用 0-禁用',
  `channel` varchar(128)  NOT NULL DEFAULT '' COMMENT '游戏来源渠道',
  `create_time` datetime NOT NULL,
  `modify_time` datetime NOT NULL,
  PRIMARY KEY (`id`),
  UNIQUE `channel_game_idx` (`channel_game_id`, `channel`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin COMMENT='游戏信息表';


-- getUser方案改造
create table `game_callback` (
    `id` bigint primary key comment '主键ID',
    `app_id` varchar(50) not null comment '业务方AppId',
    `callback_key` varchar(50) not null comment '接口回调key',
    `type` tinyint not null comment '回调类型，1：用户信息回调',
    `url` varchar(500) not null comment '回调地址',
    create_time datetime default current_timestamp,
    modify_time datetime default current_timestamp,
    index idx_app_id(`app_id`),
    unique unique_app_id_type(`app_id`, `type`)
) comment '业务回调配置表';
insert into game_callback(id, app_id, callback_key, type, url) values (1, '6518293', 'UHG843FWNF3J89', 1, 'http://pongh5.yfxn.lizhi.fm/seal/callback/getUserInfo');

-- 游戏内置下载
create table `game_version` (
    `id` bigint primary key comment '主键ID',
    `game_id` bigint not null comment 'game_biz_game表ID',
    `min_sdk_version` bigint not null comment '最小SDK版本，builder号',
    `min_app_version` bigint not null comment '最小App版本，builder号',
    `game_version` bigint not null comment '游戏版本号，builder号',
    `force_update` bool default false comment 'true：强制更新，false：非强制更新',
    `download_link` varchar(500) not null comment '游戏下载链接',
    create_time datetime default current_timestamp,
    modify_time datetime default current_timestamp
) comment '游戏版本表，用于管理内置下发版本';