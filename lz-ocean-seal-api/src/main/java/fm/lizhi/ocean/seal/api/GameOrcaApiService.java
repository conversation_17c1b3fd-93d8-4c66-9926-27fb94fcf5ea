package fm.lizhi.ocean.seal.api;

import java.util.List;
import fm.lizhi.commons.service.client.annotation.Attribute;
import fm.lizhi.commons.service.client.annotation.Return;
import fm.lizhi.commons.service.client.annotation.Service;
import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.ocean.seal.protocol.GameOrcaApiProto.RequestAddOrUpdateChannel;
import fm.lizhi.ocean.seal.protocol.GameOrcaApiProto.RequestGetChannelById;
import fm.lizhi.ocean.seal.protocol.GameOrcaApiProto.ResponseGetChannelById;
import fm.lizhi.ocean.seal.protocol.GameOrcaApiProto.RequestQueryPageGameChannels;
import fm.lizhi.ocean.seal.protocol.GameOrcaApiProto.ResponseQueryPageGameChannels;
import fm.lizhi.ocean.seal.protocol.GameOrcaApiProto.RequestDelGameChannel;
import fm.lizhi.ocean.seal.protocol.GameOrcaApiProto.RequestGetAppInfoById;
import fm.lizhi.ocean.seal.protocol.GameOrcaApiProto.ResponseGetAppInfoById;
import fm.lizhi.ocean.seal.protocol.GameOrcaApiProto.RequestAddOrUpdateGameApp;
import fm.lizhi.ocean.seal.protocol.GameOrcaApiProto.RequestDelGameAppById;
import fm.lizhi.ocean.seal.protocol.GameOrcaApiProto.RequestQueryPageGameApp;
import fm.lizhi.ocean.seal.protocol.GameOrcaApiProto.ResponseQueryPageGameApp;
import fm.lizhi.ocean.seal.protocol.GameOrcaApiProto.RequestGetGameById;
import fm.lizhi.ocean.seal.protocol.GameOrcaApiProto.ResponseGetGameById;
import fm.lizhi.ocean.seal.protocol.GameOrcaApiProto.RequestAddOrUpdateGame;
import fm.lizhi.ocean.seal.protocol.GameOrcaApiProto.RequestDelGameById;
import fm.lizhi.ocean.seal.protocol.GameOrcaApiProto.RequestQueryPageGame;
import fm.lizhi.ocean.seal.protocol.GameOrcaApiProto.ResponseQueryPageGame;
import fm.lizhi.ocean.seal.protocol.GameOrcaApiProto.RequestGetGameRelationById;
import fm.lizhi.ocean.seal.protocol.GameOrcaApiProto.ResponseGetGameRelationById;
import fm.lizhi.ocean.seal.protocol.GameOrcaApiProto.RequestAddOrUpdateGameRelation;
import fm.lizhi.ocean.seal.protocol.GameOrcaApiProto.RequestDelGameRelationById;
import fm.lizhi.ocean.seal.protocol.GameOrcaApiProto.RequestQueryPageGameRelation;
import fm.lizhi.ocean.seal.protocol.GameOrcaApiProto.ResponseQueryPageGameRelation;
import fm.lizhi.ocean.seal.protocol.GameOrcaApiProto.RequestGetGameCallbackById;
import fm.lizhi.ocean.seal.protocol.GameOrcaApiProto.ResponseGetGameCallbackById;
import fm.lizhi.ocean.seal.protocol.GameOrcaApiProto.RequestAddOrUpdateGameCallback;
import fm.lizhi.ocean.seal.protocol.GameOrcaApiProto.RequestDelGameCallBackById;
import fm.lizhi.ocean.seal.protocol.GameOrcaApiProto.RequestQueryPageGameCallback;
import fm.lizhi.ocean.seal.protocol.GameOrcaApiProto.ResponseQueryPageGameCallback;
import fm.lizhi.ocean.seal.protocol.GameOrcaApiProto.RequestGetGameVersionById;
import fm.lizhi.ocean.seal.protocol.GameOrcaApiProto.ResponseGetGameVersionById;
import fm.lizhi.ocean.seal.protocol.GameOrcaApiProto.RequestAddOrUpdateGameVersion;
import fm.lizhi.ocean.seal.protocol.GameOrcaApiProto.RequestDelGameVersionById;
import fm.lizhi.ocean.seal.protocol.GameOrcaApiProto.RequestQueryPageGameVersion;
import fm.lizhi.ocean.seal.protocol.GameOrcaApiProto.ResponseQueryPageGameVersion;
import fm.lizhi.ocean.seal.protocol.GameOrcaApiProto.GameChannelPB;
import fm.lizhi.ocean.seal.protocol.GameOrcaApiProto.GameAppInfoPB;
import fm.lizhi.ocean.seal.protocol.GameOrcaApiProto.GameInfoPB;
import fm.lizhi.ocean.seal.protocol.GameOrcaApiProto.GameBizGamePB;
import fm.lizhi.ocean.seal.protocol.GameOrcaApiProto.GameCallbackPB;
import fm.lizhi.ocean.seal.protocol.GameOrcaApiProto.GameVersionPB;
import fm.lizhi.ocean.seal.protocol.GameOrcaApiProto.SearchVersionParam;

/**
 * this file generated by autoapi-maven-plugin
 * 
 * do not edit this file manually, because this file is covered when you run autoapi-maven-plugin
 * 
 */

public interface GameOrcaApiService {
	
	
	/**
	 *  新增或编辑渠道
	 *
	 * @param gameChannel
	 *            
	 * @return 
	 */
	@Service(domain = 4302, op = 500, request = RequestAddOrUpdateChannel.class)
	@Return(resultType = Void.class)
	Result<Void> addOrUpdateChannel(@Attribute(name = "gameChannel") GameChannelPB gameChannel);
	
	
	/**
	 *  根据ID查询渠道
	 *
	 * @param channelId
	 *            
	 * @return 
	 *     //if rcode == 0 执行成功<br>
	 *     //if rcode == 1 错误<br>
	 */
	@Service(domain = 4302, op = 501, request = RequestGetChannelById.class, response = ResponseGetChannelById.class)
	@Return(resultType = ResponseGetChannelById.class)
	Result<ResponseGetChannelById> getChannelById(@Attribute(name = "channelId") long channelId);
	
	
	/**
	 *  列表查询渠道
	 *
	 * @param channelId
	 *            
	 * @param appId
	 *            
	 * @param pageSize
	 *            
	 * @param pageNumber
	 *            
	 * @return 
	 *     //if rcode == 0 执行成功<br>
	 *     //if rcode == 1 错误<br>
	 */
	@Service(domain = 4302, op = 502, request = RequestQueryPageGameChannels.class, response = ResponseQueryPageGameChannels.class)
	@Return(resultType = ResponseQueryPageGameChannels.class)
	Result<ResponseQueryPageGameChannels> queryPageGameChannels(@Attribute(name = "channelId") long channelId, @Attribute(name = "appId") String appId, @Attribute(name = "pageSize") int pageSize, @Attribute(name = "pageNumber") int pageNumber);
	
	
	/**
	 *  删除渠道
	 *
	 * @param id
	 *            
	 * @return 
	 */
	@Service(domain = 4302, op = 503, request = RequestDelGameChannel.class)
	@Return(resultType = Void.class)
	Result<Void> delGameChannel(@Attribute(name = "id") long id);
	
	
	/**
	 *  根据ID获取游戏信息
	 *
	 * @param id
	 *            
	 * @return 
	 *     //if rcode == 1 参数非法<br>
	 *     //if rcode == 2 app不存在<br>
	 *     //if rcode == 3 内部错误<br>
	 */
	@Service(domain = 4302, op = 504, request = RequestGetAppInfoById.class, response = ResponseGetAppInfoById.class)
	@Return(resultType = ResponseGetAppInfoById.class)
	Result<ResponseGetAppInfoById> getAppInfoById(@Attribute(name = "id") long id);
	
	
	/**
	 *  新增或编辑GameApp
	 *
	 * @param gameAppInfo
	 *            
	 * @return 
	 */
	@Service(domain = 4302, op = 505, request = RequestAddOrUpdateGameApp.class)
	@Return(resultType = Void.class)
	Result<Void> addOrUpdateGameApp(@Attribute(name = "gameAppInfo") GameAppInfoPB gameAppInfo);
	
	
	/**
	 *  根据ID删除gameApp
	 *
	 * @param id
	 *            
	 * @return 
	 */
	@Service(domain = 4302, op = 506, request = RequestDelGameAppById.class)
	@Return(resultType = Void.class)
	Result<Void> delGameAppById(@Attribute(name = "id") long id);
	
	
	/**
	 *  列表查询GameApp
	 *
	 * @param id
	 *            
	 * @param appId
	 *            
	 * @param pageSize
	 *            
	 * @param pageNumber
	 *            
	 * @return 
	 *     //if rcode == 0 执行成功<br>
	 *     //if rcode == 1 错误<br>
	 */
	@Service(domain = 4302, op = 507, request = RequestQueryPageGameApp.class, response = ResponseQueryPageGameApp.class)
	@Return(resultType = ResponseQueryPageGameApp.class)
	Result<ResponseQueryPageGameApp> queryPageGameApp(@Attribute(name = "id") long id, @Attribute(name = "appId") String appId, @Attribute(name = "pageSize") int pageSize, @Attribute(name = "pageNumber") int pageNumber);
	
	
	/**
	 *  根据ID获取游戏信息
	 *
	 * @param id
	 *            
	 * @return 
	 *     //if rcode == 1 参数非法<br>
	 *     //if rcode == 2 不存在<br>
	 *     //if rcode == 3 内部错误<br>
	 */
	@Service(domain = 4302, op = 508, request = RequestGetGameById.class, response = ResponseGetGameById.class)
	@Return(resultType = ResponseGetGameById.class)
	Result<ResponseGetGameById> getGameById(@Attribute(name = "id") long id);
	
	
	/**
	 *  新增或编辑
	 *
	 * @param gameInfo
	 *            
	 * @return 
	 */
	@Service(domain = 4302, op = 509, request = RequestAddOrUpdateGame.class)
	@Return(resultType = Void.class)
	Result<Void> addOrUpdateGame(@Attribute(name = "gameInfo") GameInfoPB gameInfo);
	
	
	/**
	 *  根据ID删除game
	 *
	 * @param id
	 *            
	 * @return 
	 */
	@Service(domain = 4302, op = 510, request = RequestDelGameById.class)
	@Return(resultType = Void.class)
	Result<Void> delGameById(@Attribute(name = "id") long id);
	
	
	/**
	 *  列表查询Game
	 *
	 * @param id
	 *            
	 * @param channel
	 *            
	 * @param name
	 *            
	 * @param pageSize
	 *            
	 * @param pageNumber
	 *            
	 * @return 
	 *     //if rcode == 0 执行成功<br>
	 *     //if rcode == 1 错误<br>
	 */
	@Service(domain = 4302, op = 511, request = RequestQueryPageGame.class, response = ResponseQueryPageGame.class)
	@Return(resultType = ResponseQueryPageGame.class)
	Result<ResponseQueryPageGame> queryPageGame(@Attribute(name = "id") long id, @Attribute(name = "channel") String channel, @Attribute(name = "name") String name, @Attribute(name = "pageSize") int pageSize, @Attribute(name = "pageNumber") int pageNumber);
	
	
	/**
	 *  根据ID获取关联信息
	 *
	 * @param id
	 *            
	 * @return 
	 *     //if rcode == 1 参数非法<br>
	 *     //if rcode == 2 不存在<br>
	 *     //if rcode == 3 内部错误<br>
	 */
	@Service(domain = 4302, op = 512, request = RequestGetGameRelationById.class, response = ResponseGetGameRelationById.class)
	@Return(resultType = ResponseGetGameRelationById.class)
	Result<ResponseGetGameRelationById> getGameRelationById(@Attribute(name = "id") long id);
	
	
	/**
	 *  新增或编辑
	 *
	 * @param gameBizGame
	 *            
	 * @return 
	 */
	@Service(domain = 4302, op = 513, request = RequestAddOrUpdateGameRelation.class)
	@Return(resultType = Void.class)
	Result<Void> addOrUpdateGameRelation(@Attribute(name = "gameBizGame") GameBizGamePB gameBizGame);
	
	
	/**
	 *  根据ID删除,此处会删除兼容表
	 *
	 * @param id
	 *            
	 * @return 
	 */
	@Service(domain = 4302, op = 514, request = RequestDelGameRelationById.class)
	@Return(resultType = Void.class)
	Result<Void> delGameRelationById(@Attribute(name = "id") long id);
	
	
	/**
	 *  列表查询
	 *
	 * @param id
	 *            
	 * @param gameAppId
	 *            业务的ID
	 * @param gameInfoId
	 *            游戏表的ID
	 * @param channelId
	 *            渠道表的ID
	 * @param pageSize
	 *            
	 * @param pageNumber
	 *            
	 * @return 
	 *     //if rcode == 0 执行成功<br>
	 *     //if rcode == 1 错误<br>
	 */
	@Service(domain = 4302, op = 515, request = RequestQueryPageGameRelation.class, response = ResponseQueryPageGameRelation.class)
	@Return(resultType = ResponseQueryPageGameRelation.class)
	Result<ResponseQueryPageGameRelation> queryPageGameRelation(@Attribute(name = "id") long id, @Attribute(name = "gameAppId") long gameAppId, @Attribute(name = "gameInfoId") long gameInfoId, @Attribute(name = "channelId") long channelId, @Attribute(name = "pageSize") int pageSize, @Attribute(name = "pageNumber") int pageNumber);
	
	
	/**
	 *  根据ID获取回调配置
	 *
	 * @param id
	 *            
	 * @return 
	 *     //if rcode == 1 参数非法<br>
	 *     //if rcode == 2 不存在<br>
	 *     //if rcode == 3 内部错误<br>
	 */
	@Service(domain = 4302, op = 516, request = RequestGetGameCallbackById.class, response = ResponseGetGameCallbackById.class)
	@Return(resultType = ResponseGetGameCallbackById.class)
	Result<ResponseGetGameCallbackById> getGameCallbackById(@Attribute(name = "id") long id);
	
	
	/**
	 *  新增或编辑
	 *
	 * @param gameCallbackPB
	 *            
	 * @return 
	 */
	@Service(domain = 4302, op = 517, request = RequestAddOrUpdateGameCallback.class)
	@Return(resultType = Void.class)
	Result<Void> addOrUpdateGameCallback(@Attribute(name = "gameCallbackPB") GameCallbackPB gameCallbackPB);
	
	
	/**
	 *  根据ID删除
	 *
	 * @param id
	 *            
	 * @return 
	 */
	@Service(domain = 4302, op = 518, request = RequestDelGameCallBackById.class)
	@Return(resultType = Void.class)
	Result<Void> delGameCallBackById(@Attribute(name = "id") long id);
	
	
	/**
	 *  列表查询
	 *
	 * @param appId
	 *            
	 * @param pageSize
	 *            
	 * @param pageNumber
	 *            
	 * @return 
	 *     //if rcode == 0 执行成功<br>
	 *     //if rcode == 1 错误<br>
	 */
	@Service(domain = 4302, op = 519, request = RequestQueryPageGameCallback.class, response = ResponseQueryPageGameCallback.class)
	@Return(resultType = ResponseQueryPageGameCallback.class)
	Result<ResponseQueryPageGameCallback> queryPageGameCallback(@Attribute(name = "appId") String appId, @Attribute(name = "pageSize") int pageSize, @Attribute(name = "pageNumber") int pageNumber);
	
	
	/**
	 *  根据ID获取游戏镜像信息
	 *
	 * @param id
	 *            
	 * @return 
	 *     //if rcode == 1 参数非法<br>
	 *     //if rcode == 2 不存在<br>
	 *     //if rcode == 3 内部错误<br>
	 */
	@Service(domain = 4302, op = 520, request = RequestGetGameVersionById.class, response = ResponseGetGameVersionById.class)
	@Return(resultType = ResponseGetGameVersionById.class)
	Result<ResponseGetGameVersionById> getGameVersionById(@Attribute(name = "id") long id);
	
	
	/**
	 *  新增或编辑
	 *
	 * @param gameVersionPB
	 *            
	 * @return 
	 */
	@Service(domain = 4302, op = 521, request = RequestAddOrUpdateGameVersion.class)
	@Return(resultType = Void.class)
	Result<Void> addOrUpdateGameVersion(@Attribute(name = "gameVersionPB") GameVersionPB gameVersionPB);
	
	
	/**
	 *  根据ID删除
	 *
	 * @param id
	 *            
	 * @return 
	 */
	@Service(domain = 4302, op = 522, request = RequestDelGameVersionById.class)
	@Return(resultType = Void.class)
	Result<Void> delGameVersionById(@Attribute(name = "id") long id);
	
	
	/**
	 *  列表查询
	 *
	 * @param searchVersionParam
	 *            
	 * @return 
	 *     //if rcode == 0 执行成功<br>
	 *     //if rcode == 1 错误<br>
	 */
	@Service(domain = 4302, op = 523, request = RequestQueryPageGameVersion.class, response = ResponseQueryPageGameVersion.class)
	@Return(resultType = ResponseQueryPageGameVersion.class)
	Result<ResponseQueryPageGameVersion> queryPageGameVersion(@Attribute(name = "searchVersionParam") SearchVersionParam searchVersionParam);
	
	
	public static final int GET_CHANNEL_BY_ID_SUCCESS = 0; // 执行成功
	public static final int GET_CHANNEL_BY_ID_FAIL = 1; // 错误

	public static final int QUERY_PAGE_GAME_CHANNELS_SUCCESS = 0; // 执行成功
	public static final int QUERY_PAGE_GAME_CHANNELS_FAIL = 1; // 错误

	public static final int GET_APP_INFO_BY_ID_ILLEGAL_PARAMS = 1; // 参数非法
	public static final int GET_APP_INFO_BY_ID_NOT_EXISTS = 2; // app不存在
	public static final int GET_APP_INFO_BY_ID_ERROR = 3; // 内部错误

	public static final int QUERY_PAGE_GAME_APP_SUCCESS = 0; // 执行成功
	public static final int QUERY_PAGE_GAME_APP_FAIL = 1; // 错误

	public static final int GET_GAME_BY_ID_ILLEGAL_PARAMS = 1; // 参数非法
	public static final int GET_GAME_BY_ID_NOT_EXISTS = 2; // 不存在
	public static final int GET_GAME_BY_ID_ERROR = 3; // 内部错误

	public static final int QUERY_PAGE_GAME_SUCCESS = 0; // 执行成功
	public static final int QUERY_PAGE_GAME_FAIL = 1; // 错误

	public static final int GET_GAME_RELATION_BY_ID_ILLEGAL_PARAMS = 1; // 参数非法
	public static final int GET_GAME_RELATION_BY_ID_NOT_EXISTS = 2; // 不存在
	public static final int GET_GAME_RELATION_BY_ID_ERROR = 3; // 内部错误

	public static final int QUERY_PAGE_GAME_RELATION_SUCCESS = 0; // 执行成功
	public static final int QUERY_PAGE_GAME_RELATION_FAIL = 1; // 错误

	public static final int GET_GAME_CALLBACK_BY_ID_ILLEGAL_PARAMS = 1; // 参数非法
	public static final int GET_GAME_CALLBACK_BY_ID_NOT_EXISTS = 2; // 不存在
	public static final int GET_GAME_CALLBACK_BY_ID_ERROR = 3; // 内部错误

	public static final int QUERY_PAGE_GAME_CALLBACK_SUCCESS = 0; // 执行成功
	public static final int QUERY_PAGE_GAME_CALLBACK_FAIL = 1; // 错误

	public static final int GET_GAME_VERSION_BY_ID_ILLEGAL_PARAMS = 1; // 参数非法
	public static final int GET_GAME_VERSION_BY_ID_NOT_EXISTS = 2; // 不存在
	public static final int GET_GAME_VERSION_BY_ID_ERROR = 3; // 内部错误

	public static final int QUERY_PAGE_GAME_VERSION_SUCCESS = 0; // 执行成功
	public static final int QUERY_PAGE_GAME_VERSION_FAIL = 1; // 错误


}