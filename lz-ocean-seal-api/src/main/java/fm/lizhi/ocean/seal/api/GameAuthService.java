package fm.lizhi.ocean.seal.api;

import java.util.List;
import fm.lizhi.commons.service.client.annotation.Attribute;
import fm.lizhi.commons.service.client.annotation.Return;
import fm.lizhi.commons.service.client.annotation.Service;
import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.RequestGetLoginToken;
import fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.ResponseGetLoginToken;
import fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.RequestGetServerToken;
import fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.ResponseGetServerToken;
import fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.RequestUpdateServerToken;
import fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.ResponseUpdateServerToken;
import fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.RequestGetUserByServerToken;
import fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.ResponseGetUserByServerToken;
import fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.RequestVerifyLoginToken;
import fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.ResponseVerifyLoginToken;
import fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.RequestVerifyServerToken;
import fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.ResponseVerifyServerToken;
import fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.RequestGetChannelServerToken;
import fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.ResponseGetChannelServerToken;
import fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.LoginToken;
import fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.ServerToken;
import fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.GameUser;
import fm.lizhi.ocean.seal.protocol.GameAuthServiceProto.ChannelTokenParam;

/**
 * this file generated by autoapi-maven-plugin
 * 
 * do not edit this file manually, because this file is covered when you run autoapi-maven-plugin
 * 
 */

public interface GameAuthService {
	
	
	/**
	 *  获取短期令牌，默认时长2小时
	 *
	 * @param userId
	 *            用户ID
	 * @param appId
	 *            appId
	 * @param channel
	 *            游戏渠道
	 * @return 
	 */
	@Service(domain = 4302, op = 1, request = RequestGetLoginToken.class, response = ResponseGetLoginToken.class)
	@Return(resultType = ResponseGetLoginToken.class)
	Result<ResponseGetLoginToken> getLoginToken(@Attribute(name = "userId") long userId, @Attribute(name = "appId") String appId, @Attribute(name = "channel") String channel);
	
	
	/**
	 *  获取长期令牌SSToken，SSToken为小游戏服务端与业务服务端数据交换的令牌
	 *
	 * @param loginToken
	 *            短期令牌
	 * @param channel
	 *            游戏渠道
	 * @param appId
	 *            appId
	 * @return 
	 */
	@Service(domain = 4302, op = 2, request = RequestGetServerToken.class, response = ResponseGetServerToken.class)
	@Return(resultType = ResponseGetServerToken.class)
	Result<ResponseGetServerToken> getServerToken(@Attribute(name = "loginToken") String loginToken, @Attribute(name = "channel") String channel, @Attribute(name = "appId") String appId);
	
	
	/**
	 *  更新并返回长期令牌
	 *
	 * @param token
	 *            长期令牌
	 * @param channel
	 *            游戏渠道
	 * @param appId
	 *            appId
	 * @return 
	 */
	@Service(domain = 4302, op = 3, request = RequestUpdateServerToken.class, response = ResponseUpdateServerToken.class)
	@Return(resultType = ResponseUpdateServerToken.class)
	Result<ResponseUpdateServerToken> updateServerToken(@Attribute(name = "token") String token, @Attribute(name = "channel") String channel, @Attribute(name = "appId") String appId);
	
	
	/**
	 *  通过长期令牌获得用户
	 *
	 * @param token
	 *            长期令牌
	 * @param channel
	 *            游戏渠道
	 * @param appId
	 *            appId
	 * @return 
	 */
	@Service(domain = 4302, op = 4, request = RequestGetUserByServerToken.class, response = ResponseGetUserByServerToken.class)
	@Return(resultType = ResponseGetUserByServerToken.class)
	Result<ResponseGetUserByServerToken> getUserByServerToken(@Attribute(name = "token") String token, @Attribute(name = "channel") String channel, @Attribute(name = "appId") String appId);
	
	
	/**
	 *  校验短期令牌有效性
	 *
	 * @param token
	 *            短期令牌
	 * @param channel
	 *            游戏渠道
	 * @param appId
	 *            appId
	 * @return 
	 */
	@Service(domain = 4302, op = 5, request = RequestVerifyLoginToken.class, response = ResponseVerifyLoginToken.class)
	@Return(resultType = ResponseVerifyLoginToken.class)
	Result<ResponseVerifyLoginToken> verifyLoginToken(@Attribute(name = "token") String token, @Attribute(name = "channel") String channel, @Attribute(name = "appId") String appId);
	
	
	/**
	 *  校验长期令牌有效性
	 *
	 * @param token
	 *            长期令牌
	 * @param channel
	 *            游戏渠道
	 * @param appId
	 *            appId
	 * @return 
	 */
	@Service(domain = 4302, op = 6, request = RequestVerifyServerToken.class, response = ResponseVerifyServerToken.class)
	@Return(resultType = ResponseVerifyServerToken.class)
	Result<ResponseVerifyServerToken> verifyServerToken(@Attribute(name = "token") String token, @Attribute(name = "channel") String channel, @Attribute(name = "appId") String appId);
	
	
	/**
	 *  获取长期令牌SSToken，SSToken为小游戏服务端与业务服务端数据交换的令牌，新版接口，采用对象入参，方便管理，后续废弃getServerToken，统一改用该接口
	 *
	 * @param channelTokenParam
	 *            
	 * @return 
	 */
	@Service(domain = 4302, op = 7, request = RequestGetChannelServerToken.class, response = ResponseGetChannelServerToken.class)
	@Return(resultType = ResponseGetChannelServerToken.class)
	Result<ResponseGetChannelServerToken> getChannelServerToken(@Attribute(name = "channelTokenParam") ChannelTokenParam channelTokenParam);
	
	

}