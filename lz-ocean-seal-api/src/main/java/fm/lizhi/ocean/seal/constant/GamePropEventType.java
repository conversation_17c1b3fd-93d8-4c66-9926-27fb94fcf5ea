package fm.lizhi.ocean.seal.constant;

/**
 * 游戏道具事件类型
 * <AUTHOR>
 */
public enum GamePropEventType {

    /**
     * 道具发放
     */
    GRANT_PROP("grantProp", "道具发放"),

    /**
     * 用户背包状态
     */
    USER_PROP_STATUS("userPropStatus", "用户背包状态"),

    /**
     * 查询道具发放状态
     */
    QUERY_PROP_GRANT_STATUS("queryPropGrantStatus", "查询道具发放状态");


    private final String event;
    private final String description;

    GamePropEventType(String event, String description) {
        this.event = event;
        this.description = description;
    }

    public String getEvent() {
        return event;
    }

    public String getDescription() {
        return description;
    }

    public static GamePropEventType from(String event) {
        for (GamePropEventType value : values()) {
            if (value.getEvent().equals(event)) {
                return value;
            }
        }
        return null;
    }
}
