package fm.lizhi.ocean.seal.kafka;

/**
 * Created in 2022-01-26 19:40.
 *
 * <AUTHOR>
 */
public class GamePlayer {
    /**
     * 用户ID
     */
    private long userId;
    /**
     * 是否是真实用户
     */
    private boolean realUser;

    public GamePlayer() {
    }

    public long getUserId() {
        return userId;
    }

    public void setUserId(long userId) {
        this.userId = userId;
    }

    public boolean isRealUser() {
        return realUser;
    }

    public void setRealUser(boolean realUser) {
        this.realUser = realUser;
    }

    @Override
    public String toString() {
        return "GamePlayer{" +
                "userId=" + userId +
                ", realUser=" + realUser +
                '}';
    }
}
