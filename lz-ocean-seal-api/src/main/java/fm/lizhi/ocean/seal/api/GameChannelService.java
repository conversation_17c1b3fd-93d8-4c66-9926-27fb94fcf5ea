package fm.lizhi.ocean.seal.api;

import java.util.List;
import fm.lizhi.commons.service.client.annotation.Attribute;
import fm.lizhi.commons.service.client.annotation.Return;
import fm.lizhi.commons.service.client.annotation.Service;
import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.ocean.seal.protocol.GameChannelServiceProto.RequestGetAllGameChannels;
import fm.lizhi.ocean.seal.protocol.GameChannelServiceProto.ResponseGetAllGameChannels;
import fm.lizhi.ocean.seal.protocol.GameChannelServiceProto.RequestGetEnumGameChannels;
import fm.lizhi.ocean.seal.protocol.GameChannelServiceProto.ResponseGetEnumGameChannels;
import fm.lizhi.ocean.seal.protocol.GameChannelServiceProto.RequestGetChannelToken;
import fm.lizhi.ocean.seal.protocol.GameChannelServiceProto.ResponseGetChannelToken;
import fm.lizhi.ocean.seal.protocol.GameChannelServiceProto.GameChannel;
import fm.lizhi.ocean.seal.protocol.GameChannelServiceProto.EnumGameChannel;
import fm.lizhi.ocean.seal.protocol.GameChannelServiceProto.GetChannelTokenParam;
import fm.lizhi.ocean.seal.protocol.GameChannelServiceProto.ChannelToken;

/**
 * this file generated by autoapi-maven-plugin
 * 
 * do not edit this file manually, because this file is covered when you run autoapi-maven-plugin
 * 
 */

public interface GameChannelService {
	
	
	/**
	 *  获取游戏渠道列表
	 *
	 * @return 
	 *     //if rcode == 0 执行成功<br>
	 *     //if rcode == 1 错误<br>
	 */
	@Service(domain = 4302, op = 220, request = RequestGetAllGameChannels.class, response = ResponseGetAllGameChannels.class)
	@Return(resultType = ResponseGetAllGameChannels.class)
	Result<ResponseGetAllGameChannels> getAllGameChannels();
	
	
	/**
	 *  获取游戏渠道枚举列表
	 *
	 * @return 
	 *     //if rcode == 0 执行成功<br>
	 *     //if rcode == 1 错误<br>
	 */
	@Service(domain = 4302, op = 221, request = RequestGetEnumGameChannels.class, response = ResponseGetEnumGameChannels.class)
	@Return(resultType = ResponseGetEnumGameChannels.class)
	Result<ResponseGetEnumGameChannels> getEnumGameChannels();
	
	
	/**
	 *  获取平台SDK与渠道交互的Token
	 *
	 * @param param
	 *            
	 * @return 
	 *     //if rcode == 1 参数非法<br>
	 *     //if rcode == 2 内部错误<br>
	 */
	@Service(domain = 4302, op = 222, request = RequestGetChannelToken.class, response = ResponseGetChannelToken.class)
	@Return(resultType = ResponseGetChannelToken.class)
	Result<ResponseGetChannelToken> getChannelToken(@Attribute(name = "param") GetChannelTokenParam param);
	
	
	public static final int GET_ALL_GAME_CHANNELS_SUCCESS = 0; // 执行成功
	public static final int GET_ALL_GAME_CHANNELS_FAIL = 1; // 错误

	public static final int GET_ENUM_GAME_CHANNELS_SUCCESS = 0; // 执行成功
	public static final int GET_ENUM_GAME_CHANNELS_FAIL = 1; // 错误

	public static final int GET_CHANNEL_TOKEN_ILLEGAL_PARAMS = 1; // 参数非法
	public static final int GET_CHANNEL_TOKEN_ERROR = 2; // 内部错误


}