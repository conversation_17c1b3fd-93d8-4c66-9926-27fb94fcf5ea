package fm.lizhi.ocean.seal.constant;

/**
 * 游戏道具发放状态枚举
 * <AUTHOR>
 */
public enum GamePropGrantStatus {

    /**
     * 未发放
     */
    NOT_GRANTED(0, "未发放"),

    /**
     * 发放中
     */
    GRANTING(1, "发放中"),

    /**
     * 发放成功
     */
    SUCCESS(2, "发放成功"),

    /**
     * 发放失败
     */
    FAILED(3, "发放失败");

    private final int code;
    private final String description;

    GamePropGrantStatus(int code, String description) {
        this.code = code;
        this.description = description;
    }

    public int getCode() {
        return code;
    }

    public String getDescription() {
        return description;
    }

    /**
     * 根据状态码获取枚举
     * @param code 状态码
     * @return 枚举值
     */
    public static GamePropGrantStatus fromCode(int code) {
        for (GamePropGrantStatus status : values()) {
            if (status.getCode() == code) {
                return status;
            }
        }
        throw new IllegalArgumentException("Unknown grant status code: " + code);
    }

    /**
     * 判断是否为最终状态（成功或失败）
     * @return 是否为最终状态
     */
    public boolean isFinalStatus() {
        return this == SUCCESS || this == FAILED;
    }

    /**
     * 判断是否为成功状态
     * @return 是否成功
     */
    public boolean isSuccess() {
        return this == SUCCESS;
    }

    /**
     * 判断是否为失败状态
     * @return 是否失败
     */
    public boolean isFailed() {
        return this == FAILED;
    }
}
