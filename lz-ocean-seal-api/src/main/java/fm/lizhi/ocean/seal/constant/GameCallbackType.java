package fm.lizhi.ocean.seal.constant;

import java.util.HashMap;
import java.util.Map;

/**
 * Created in 2022-04-18 18:05.
 *
 * <AUTHOR>
 */
public enum GameCallbackType {
    // 获取用户信息
    GET_USER(1, "获取用户信息"),
    // 预发接口代理回调
    BIZ_PROXY_INTERFACE(2, "回调业务地址"),
    GAME_PROXY_INTERFACE(3, "游戏接口地址"),

    THIRD_PARTY_FORWARD(4, "第三方转发"),

    //线上的代理接口回调
    PRO_BIZ_PROXY_INTERFACE(12, "回调业务地址"),
    PRO_GAME_PROXY_INTERFACE(13, "游戏接口地址");
    private int value;
    private String msg;

    private static Map<Integer, GameCallbackType> map = new HashMap<>();

    static {
        for (GameCallbackType object : GameCallbackType.values()) {
            map.put(object.getValue(), object);
        }
    }

    GameCallbackType(int value, String msg) {
        this.value = value;
        this.msg = msg;
    }

    public int getValue() {
        return value;
    }

    public String getMsg() {
        return msg;
    }

    /**
     * 根据值类型找枚举
     *
     * @param value 值
     * @return
     */
    public static GameCallbackType from(int value) {
        return map.get(value);
    }
}