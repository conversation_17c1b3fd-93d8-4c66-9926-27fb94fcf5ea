package fm.lizhi.ocean.seal.api;

import java.util.List;
import fm.lizhi.commons.service.client.annotation.Attribute;
import fm.lizhi.commons.service.client.annotation.Return;
import fm.lizhi.commons.service.client.annotation.Service;
import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.ocean.seal.protocol.GamePayWayServiceProto.RequestGetAllGamePayWays;
import fm.lizhi.ocean.seal.protocol.GamePayWayServiceProto.ResponseGetAllGamePayWays;
import fm.lizhi.ocean.seal.protocol.GamePayWayServiceProto.GamePayWay;

/**
 * this file generated by autoapi-maven-plugin
 * 
 * do not edit this file manually, because this file is covered when you run autoapi-maven-plugin
 * 
 */

public interface GamePayWayService {
	
	
	/**
	 *  后去支付方式。此处本来不需要提供接口，不过因为特殊原因，支持orca的泛化调用，所以提供接口
	 *
	 * @return 
	 *     //if rcode == 0 执行成功<br>
	 *     //if rcode == 1 错误<br>
	 */
	@Service(domain = 4302, op = 230, request = RequestGetAllGamePayWays.class, response = ResponseGetAllGamePayWays.class)
	@Return(resultType = ResponseGetAllGamePayWays.class)
	Result<ResponseGetAllGamePayWays> getAllGamePayWays();
	
	
	public static final int GET_ALL_GAME_PAY_WAYS_SUCCESS = 0; // 执行成功
	public static final int GET_ALL_GAME_PAY_WAYS_FAIL = 1; // 错误


}