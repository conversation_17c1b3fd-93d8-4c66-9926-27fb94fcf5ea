package fm.lizhi.ocean.seal.constant;

import java.util.HashMap;
import java.util.Map;

/**
 * Created in 2022-04-28 16:11.
 *
 * <AUTHOR>
 */
public enum RenderType {
    //
    WEB(0, "WEB"),
    NATIVE(1, "NATIVE"),

    ;
    private int code;
    private String type;

    private static Map<Integer, RenderType> map = new HashMap<>();

    static {
        for (RenderType object : RenderType.values()) {
            map.put(object.getCode(), object);
        }
    }

    RenderType(int code, String type) {
        this.code = code;
        this.type = type;
    }

    public int getCode() {
        return code;
    }

    public String getType() {
        return type;
    }

    /**
     * 根据枚举代码找枚举
     *
     * @param code 值
     * @return
     */
    public static RenderType from(int code) {
        return map.get(code);
    }
}