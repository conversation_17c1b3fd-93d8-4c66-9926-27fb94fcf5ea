// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: protocol_game_pay_way.proto

package fm.lizhi.ocean.seal.protocol;

public final class GamePayWayServiceProto {
  private GamePayWayServiceProto() {}
  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistry registry) {
  }
  public interface GamePayWayOrBuilder
      extends com.google.protobuf.MessageOrBuilder {

    // optional int32 payWayCode = 1;
    /**
     * <code>optional int32 payWayCode = 1;</code>
     */
    boolean hasPayWayCode();
    /**
     * <code>optional int32 payWayCode = 1;</code>
     */
    int getPayWayCode();

    // optional string payWayName = 2;
    /**
     * <code>optional string payWayName = 2;</code>
     */
    boolean hasPayWayName();
    /**
     * <code>optional string payWayName = 2;</code>
     */
    java.lang.String getPayWayName();
    /**
     * <code>optional string payWayName = 2;</code>
     */
    com.google.protobuf.ByteString
        getPayWayNameBytes();
  }
  /**
   * Protobuf type {@code fm.lizhi.commons.template.datacenter.protocol.GamePayWay}
   */
  public static final class GamePayWay extends
      com.google.protobuf.GeneratedMessage
      implements GamePayWayOrBuilder {
    // Use GamePayWay.newBuilder() to construct.
    private GamePayWay(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
      super(builder);
      this.unknownFields = builder.getUnknownFields();
    }
    private GamePayWay(boolean noInit) { this.unknownFields = com.google.protobuf.UnknownFieldSet.getDefaultInstance(); }

    private static final GamePayWay defaultInstance;
    public static GamePayWay getDefaultInstance() {
      return defaultInstance;
    }

    public GamePayWay getDefaultInstanceForType() {
      return defaultInstance;
    }

    private final com.google.protobuf.UnknownFieldSet unknownFields;
    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
        getUnknownFields() {
      return this.unknownFields;
    }
    private GamePayWay(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      initFields();
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            default: {
              if (!parseUnknownField(input, unknownFields,
                                     extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
            case 8: {
              bitField0_ |= 0x00000001;
              payWayCode_ = input.readInt32();
              break;
            }
            case 18: {
              bitField0_ |= 0x00000002;
              payWayName_ = input.readBytes();
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e.getMessage()).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return fm.lizhi.ocean.seal.protocol.GamePayWayServiceProto.internal_static_fm_lizhi_commons_template_datacenter_protocol_GamePayWay_descriptor;
    }

    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return fm.lizhi.ocean.seal.protocol.GamePayWayServiceProto.internal_static_fm_lizhi_commons_template_datacenter_protocol_GamePayWay_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              fm.lizhi.ocean.seal.protocol.GamePayWayServiceProto.GamePayWay.class, fm.lizhi.ocean.seal.protocol.GamePayWayServiceProto.GamePayWay.Builder.class);
    }

    public static com.google.protobuf.Parser<GamePayWay> PARSER =
        new com.google.protobuf.AbstractParser<GamePayWay>() {
      public GamePayWay parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new GamePayWay(input, extensionRegistry);
      }
    };

    @java.lang.Override
    public com.google.protobuf.Parser<GamePayWay> getParserForType() {
      return PARSER;
    }

    private int bitField0_;
    // optional int32 payWayCode = 1;
    public static final int PAYWAYCODE_FIELD_NUMBER = 1;
    private int payWayCode_;
    /**
     * <code>optional int32 payWayCode = 1;</code>
     */
    public boolean hasPayWayCode() {
      return ((bitField0_ & 0x00000001) == 0x00000001);
    }
    /**
     * <code>optional int32 payWayCode = 1;</code>
     */
    public int getPayWayCode() {
      return payWayCode_;
    }

    // optional string payWayName = 2;
    public static final int PAYWAYNAME_FIELD_NUMBER = 2;
    private java.lang.Object payWayName_;
    /**
     * <code>optional string payWayName = 2;</code>
     */
    public boolean hasPayWayName() {
      return ((bitField0_ & 0x00000002) == 0x00000002);
    }
    /**
     * <code>optional string payWayName = 2;</code>
     */
    public java.lang.String getPayWayName() {
      java.lang.Object ref = payWayName_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          payWayName_ = s;
        }
        return s;
      }
    }
    /**
     * <code>optional string payWayName = 2;</code>
     */
    public com.google.protobuf.ByteString
        getPayWayNameBytes() {
      java.lang.Object ref = payWayName_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        payWayName_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    private void initFields() {
      payWayCode_ = 0;
      payWayName_ = "";
    }
    private byte memoizedIsInitialized = -1;
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized != -1) return isInitialized == 1;

      memoizedIsInitialized = 1;
      return true;
    }

    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      getSerializedSize();
      if (((bitField0_ & 0x00000001) == 0x00000001)) {
        output.writeInt32(1, payWayCode_);
      }
      if (((bitField0_ & 0x00000002) == 0x00000002)) {
        output.writeBytes(2, getPayWayNameBytes());
      }
      getUnknownFields().writeTo(output);
    }

    private int memoizedSerializedSize = -1;
    public int getSerializedSize() {
      int size = memoizedSerializedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) == 0x00000001)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(1, payWayCode_);
      }
      if (((bitField0_ & 0x00000002) == 0x00000002)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(2, getPayWayNameBytes());
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSerializedSize = size;
      return size;
    }

    private static final long serialVersionUID = 0L;
    @java.lang.Override
    protected java.lang.Object writeReplace()
        throws java.io.ObjectStreamException {
      return super.writeReplace();
    }

    public static fm.lizhi.ocean.seal.protocol.GamePayWayServiceProto.GamePayWay parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static fm.lizhi.ocean.seal.protocol.GamePayWayServiceProto.GamePayWay parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static fm.lizhi.ocean.seal.protocol.GamePayWayServiceProto.GamePayWay parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static fm.lizhi.ocean.seal.protocol.GamePayWayServiceProto.GamePayWay parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static fm.lizhi.ocean.seal.protocol.GamePayWayServiceProto.GamePayWay parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return PARSER.parseFrom(input);
    }
    public static fm.lizhi.ocean.seal.protocol.GamePayWayServiceProto.GamePayWay parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseFrom(input, extensionRegistry);
    }
    public static fm.lizhi.ocean.seal.protocol.GamePayWayServiceProto.GamePayWay parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return PARSER.parseDelimitedFrom(input);
    }
    public static fm.lizhi.ocean.seal.protocol.GamePayWayServiceProto.GamePayWay parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseDelimitedFrom(input, extensionRegistry);
    }
    public static fm.lizhi.ocean.seal.protocol.GamePayWayServiceProto.GamePayWay parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return PARSER.parseFrom(input);
    }
    public static fm.lizhi.ocean.seal.protocol.GamePayWayServiceProto.GamePayWay parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseFrom(input, extensionRegistry);
    }

    public static Builder newBuilder() { return Builder.create(); }
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder(fm.lizhi.ocean.seal.protocol.GamePayWayServiceProto.GamePayWay prototype) {
      return newBuilder().mergeFrom(prototype);
    }
    public Builder toBuilder() { return newBuilder(this); }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code fm.lizhi.commons.template.datacenter.protocol.GamePayWay}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessage.Builder<Builder>
       implements fm.lizhi.ocean.seal.protocol.GamePayWayServiceProto.GamePayWayOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return fm.lizhi.ocean.seal.protocol.GamePayWayServiceProto.internal_static_fm_lizhi_commons_template_datacenter_protocol_GamePayWay_descriptor;
      }

      protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return fm.lizhi.ocean.seal.protocol.GamePayWayServiceProto.internal_static_fm_lizhi_commons_template_datacenter_protocol_GamePayWay_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                fm.lizhi.ocean.seal.protocol.GamePayWayServiceProto.GamePayWay.class, fm.lizhi.ocean.seal.protocol.GamePayWayServiceProto.GamePayWay.Builder.class);
      }

      // Construct using fm.lizhi.ocean.seal.protocol.GamePayWayServiceProto.GamePayWay.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessage.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessage.alwaysUseFieldBuilders) {
        }
      }
      private static Builder create() {
        return new Builder();
      }

      public Builder clear() {
        super.clear();
        payWayCode_ = 0;
        bitField0_ = (bitField0_ & ~0x00000001);
        payWayName_ = "";
        bitField0_ = (bitField0_ & ~0x00000002);
        return this;
      }

      public Builder clone() {
        return create().mergeFrom(buildPartial());
      }

      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return fm.lizhi.ocean.seal.protocol.GamePayWayServiceProto.internal_static_fm_lizhi_commons_template_datacenter_protocol_GamePayWay_descriptor;
      }

      public fm.lizhi.ocean.seal.protocol.GamePayWayServiceProto.GamePayWay getDefaultInstanceForType() {
        return fm.lizhi.ocean.seal.protocol.GamePayWayServiceProto.GamePayWay.getDefaultInstance();
      }

      public fm.lizhi.ocean.seal.protocol.GamePayWayServiceProto.GamePayWay build() {
        fm.lizhi.ocean.seal.protocol.GamePayWayServiceProto.GamePayWay result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      public fm.lizhi.ocean.seal.protocol.GamePayWayServiceProto.GamePayWay buildPartial() {
        fm.lizhi.ocean.seal.protocol.GamePayWayServiceProto.GamePayWay result = new fm.lizhi.ocean.seal.protocol.GamePayWayServiceProto.GamePayWay(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) == 0x00000001)) {
          to_bitField0_ |= 0x00000001;
        }
        result.payWayCode_ = payWayCode_;
        if (((from_bitField0_ & 0x00000002) == 0x00000002)) {
          to_bitField0_ |= 0x00000002;
        }
        result.payWayName_ = payWayName_;
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof fm.lizhi.ocean.seal.protocol.GamePayWayServiceProto.GamePayWay) {
          return mergeFrom((fm.lizhi.ocean.seal.protocol.GamePayWayServiceProto.GamePayWay)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(fm.lizhi.ocean.seal.protocol.GamePayWayServiceProto.GamePayWay other) {
        if (other == fm.lizhi.ocean.seal.protocol.GamePayWayServiceProto.GamePayWay.getDefaultInstance()) return this;
        if (other.hasPayWayCode()) {
          setPayWayCode(other.getPayWayCode());
        }
        if (other.hasPayWayName()) {
          bitField0_ |= 0x00000002;
          payWayName_ = other.payWayName_;
          onChanged();
        }
        this.mergeUnknownFields(other.getUnknownFields());
        return this;
      }

      public final boolean isInitialized() {
        return true;
      }

      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        fm.lizhi.ocean.seal.protocol.GamePayWayServiceProto.GamePayWay parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (fm.lizhi.ocean.seal.protocol.GamePayWayServiceProto.GamePayWay) e.getUnfinishedMessage();
          throw e;
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      // optional int32 payWayCode = 1;
      private int payWayCode_ ;
      /**
       * <code>optional int32 payWayCode = 1;</code>
       */
      public boolean hasPayWayCode() {
        return ((bitField0_ & 0x00000001) == 0x00000001);
      }
      /**
       * <code>optional int32 payWayCode = 1;</code>
       */
      public int getPayWayCode() {
        return payWayCode_;
      }
      /**
       * <code>optional int32 payWayCode = 1;</code>
       */
      public Builder setPayWayCode(int value) {
        bitField0_ |= 0x00000001;
        payWayCode_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional int32 payWayCode = 1;</code>
       */
      public Builder clearPayWayCode() {
        bitField0_ = (bitField0_ & ~0x00000001);
        payWayCode_ = 0;
        onChanged();
        return this;
      }

      // optional string payWayName = 2;
      private java.lang.Object payWayName_ = "";
      /**
       * <code>optional string payWayName = 2;</code>
       */
      public boolean hasPayWayName() {
        return ((bitField0_ & 0x00000002) == 0x00000002);
      }
      /**
       * <code>optional string payWayName = 2;</code>
       */
      public java.lang.String getPayWayName() {
        java.lang.Object ref = payWayName_;
        if (!(ref instanceof java.lang.String)) {
          java.lang.String s = ((com.google.protobuf.ByteString) ref)
              .toStringUtf8();
          payWayName_ = s;
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <code>optional string payWayName = 2;</code>
       */
      public com.google.protobuf.ByteString
          getPayWayNameBytes() {
        java.lang.Object ref = payWayName_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          payWayName_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <code>optional string payWayName = 2;</code>
       */
      public Builder setPayWayName(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000002;
        payWayName_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional string payWayName = 2;</code>
       */
      public Builder clearPayWayName() {
        bitField0_ = (bitField0_ & ~0x00000002);
        payWayName_ = getDefaultInstance().getPayWayName();
        onChanged();
        return this;
      }
      /**
       * <code>optional string payWayName = 2;</code>
       */
      public Builder setPayWayNameBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000002;
        payWayName_ = value;
        onChanged();
        return this;
      }

      // @@protoc_insertion_point(builder_scope:fm.lizhi.commons.template.datacenter.protocol.GamePayWay)
    }

    static {
      defaultInstance = new GamePayWay(true);
      defaultInstance.initFields();
    }

    // @@protoc_insertion_point(class_scope:fm.lizhi.commons.template.datacenter.protocol.GamePayWay)
  }

  public interface RequestGetAllGamePayWaysOrBuilder
      extends com.google.protobuf.MessageOrBuilder {
  }
  /**
   * Protobuf type {@code fm.lizhi.commons.template.datacenter.protocol.RequestGetAllGamePayWays}
   *
   * <pre>
   * GamePayWayService.java
   * 后去支付方式。此处本来不需要提供接口，不过因为特殊原因，支持orca的泛化调用，所以提供接口
   * domain = 4302, op = 230
   * </pre>
   */
  public static final class RequestGetAllGamePayWays extends
      com.google.protobuf.GeneratedMessage
      implements RequestGetAllGamePayWaysOrBuilder {
    // Use RequestGetAllGamePayWays.newBuilder() to construct.
    private RequestGetAllGamePayWays(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
      super(builder);
      this.unknownFields = builder.getUnknownFields();
    }
    private RequestGetAllGamePayWays(boolean noInit) { this.unknownFields = com.google.protobuf.UnknownFieldSet.getDefaultInstance(); }

    private static final RequestGetAllGamePayWays defaultInstance;
    public static RequestGetAllGamePayWays getDefaultInstance() {
      return defaultInstance;
    }

    public RequestGetAllGamePayWays getDefaultInstanceForType() {
      return defaultInstance;
    }

    private final com.google.protobuf.UnknownFieldSet unknownFields;
    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
        getUnknownFields() {
      return this.unknownFields;
    }
    private RequestGetAllGamePayWays(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      initFields();
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            default: {
              if (!parseUnknownField(input, unknownFields,
                                     extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e.getMessage()).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return fm.lizhi.ocean.seal.protocol.GamePayWayServiceProto.internal_static_fm_lizhi_commons_template_datacenter_protocol_RequestGetAllGamePayWays_descriptor;
    }

    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return fm.lizhi.ocean.seal.protocol.GamePayWayServiceProto.internal_static_fm_lizhi_commons_template_datacenter_protocol_RequestGetAllGamePayWays_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              fm.lizhi.ocean.seal.protocol.GamePayWayServiceProto.RequestGetAllGamePayWays.class, fm.lizhi.ocean.seal.protocol.GamePayWayServiceProto.RequestGetAllGamePayWays.Builder.class);
    }

    public static com.google.protobuf.Parser<RequestGetAllGamePayWays> PARSER =
        new com.google.protobuf.AbstractParser<RequestGetAllGamePayWays>() {
      public RequestGetAllGamePayWays parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new RequestGetAllGamePayWays(input, extensionRegistry);
      }
    };

    @java.lang.Override
    public com.google.protobuf.Parser<RequestGetAllGamePayWays> getParserForType() {
      return PARSER;
    }

    private void initFields() {
    }
    private byte memoizedIsInitialized = -1;
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized != -1) return isInitialized == 1;

      memoizedIsInitialized = 1;
      return true;
    }

    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      getSerializedSize();
      getUnknownFields().writeTo(output);
    }

    private int memoizedSerializedSize = -1;
    public int getSerializedSize() {
      int size = memoizedSerializedSize;
      if (size != -1) return size;

      size = 0;
      size += getUnknownFields().getSerializedSize();
      memoizedSerializedSize = size;
      return size;
    }

    private static final long serialVersionUID = 0L;
    @java.lang.Override
    protected java.lang.Object writeReplace()
        throws java.io.ObjectStreamException {
      return super.writeReplace();
    }

    public static fm.lizhi.ocean.seal.protocol.GamePayWayServiceProto.RequestGetAllGamePayWays parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static fm.lizhi.ocean.seal.protocol.GamePayWayServiceProto.RequestGetAllGamePayWays parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static fm.lizhi.ocean.seal.protocol.GamePayWayServiceProto.RequestGetAllGamePayWays parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static fm.lizhi.ocean.seal.protocol.GamePayWayServiceProto.RequestGetAllGamePayWays parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static fm.lizhi.ocean.seal.protocol.GamePayWayServiceProto.RequestGetAllGamePayWays parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return PARSER.parseFrom(input);
    }
    public static fm.lizhi.ocean.seal.protocol.GamePayWayServiceProto.RequestGetAllGamePayWays parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseFrom(input, extensionRegistry);
    }
    public static fm.lizhi.ocean.seal.protocol.GamePayWayServiceProto.RequestGetAllGamePayWays parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return PARSER.parseDelimitedFrom(input);
    }
    public static fm.lizhi.ocean.seal.protocol.GamePayWayServiceProto.RequestGetAllGamePayWays parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseDelimitedFrom(input, extensionRegistry);
    }
    public static fm.lizhi.ocean.seal.protocol.GamePayWayServiceProto.RequestGetAllGamePayWays parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return PARSER.parseFrom(input);
    }
    public static fm.lizhi.ocean.seal.protocol.GamePayWayServiceProto.RequestGetAllGamePayWays parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseFrom(input, extensionRegistry);
    }

    public static Builder newBuilder() { return Builder.create(); }
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder(fm.lizhi.ocean.seal.protocol.GamePayWayServiceProto.RequestGetAllGamePayWays prototype) {
      return newBuilder().mergeFrom(prototype);
    }
    public Builder toBuilder() { return newBuilder(this); }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code fm.lizhi.commons.template.datacenter.protocol.RequestGetAllGamePayWays}
     *
     * <pre>
     * GamePayWayService.java
     * 后去支付方式。此处本来不需要提供接口，不过因为特殊原因，支持orca的泛化调用，所以提供接口
     * domain = 4302, op = 230
     * </pre>
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessage.Builder<Builder>
       implements fm.lizhi.ocean.seal.protocol.GamePayWayServiceProto.RequestGetAllGamePayWaysOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return fm.lizhi.ocean.seal.protocol.GamePayWayServiceProto.internal_static_fm_lizhi_commons_template_datacenter_protocol_RequestGetAllGamePayWays_descriptor;
      }

      protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return fm.lizhi.ocean.seal.protocol.GamePayWayServiceProto.internal_static_fm_lizhi_commons_template_datacenter_protocol_RequestGetAllGamePayWays_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                fm.lizhi.ocean.seal.protocol.GamePayWayServiceProto.RequestGetAllGamePayWays.class, fm.lizhi.ocean.seal.protocol.GamePayWayServiceProto.RequestGetAllGamePayWays.Builder.class);
      }

      // Construct using fm.lizhi.ocean.seal.protocol.GamePayWayServiceProto.RequestGetAllGamePayWays.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessage.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessage.alwaysUseFieldBuilders) {
        }
      }
      private static Builder create() {
        return new Builder();
      }

      public Builder clear() {
        super.clear();
        return this;
      }

      public Builder clone() {
        return create().mergeFrom(buildPartial());
      }

      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return fm.lizhi.ocean.seal.protocol.GamePayWayServiceProto.internal_static_fm_lizhi_commons_template_datacenter_protocol_RequestGetAllGamePayWays_descriptor;
      }

      public fm.lizhi.ocean.seal.protocol.GamePayWayServiceProto.RequestGetAllGamePayWays getDefaultInstanceForType() {
        return fm.lizhi.ocean.seal.protocol.GamePayWayServiceProto.RequestGetAllGamePayWays.getDefaultInstance();
      }

      public fm.lizhi.ocean.seal.protocol.GamePayWayServiceProto.RequestGetAllGamePayWays build() {
        fm.lizhi.ocean.seal.protocol.GamePayWayServiceProto.RequestGetAllGamePayWays result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      public fm.lizhi.ocean.seal.protocol.GamePayWayServiceProto.RequestGetAllGamePayWays buildPartial() {
        fm.lizhi.ocean.seal.protocol.GamePayWayServiceProto.RequestGetAllGamePayWays result = new fm.lizhi.ocean.seal.protocol.GamePayWayServiceProto.RequestGetAllGamePayWays(this);
        onBuilt();
        return result;
      }

      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof fm.lizhi.ocean.seal.protocol.GamePayWayServiceProto.RequestGetAllGamePayWays) {
          return mergeFrom((fm.lizhi.ocean.seal.protocol.GamePayWayServiceProto.RequestGetAllGamePayWays)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(fm.lizhi.ocean.seal.protocol.GamePayWayServiceProto.RequestGetAllGamePayWays other) {
        if (other == fm.lizhi.ocean.seal.protocol.GamePayWayServiceProto.RequestGetAllGamePayWays.getDefaultInstance()) return this;
        this.mergeUnknownFields(other.getUnknownFields());
        return this;
      }

      public final boolean isInitialized() {
        return true;
      }

      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        fm.lizhi.ocean.seal.protocol.GamePayWayServiceProto.RequestGetAllGamePayWays parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (fm.lizhi.ocean.seal.protocol.GamePayWayServiceProto.RequestGetAllGamePayWays) e.getUnfinishedMessage();
          throw e;
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }

      // @@protoc_insertion_point(builder_scope:fm.lizhi.commons.template.datacenter.protocol.RequestGetAllGamePayWays)
    }

    static {
      defaultInstance = new RequestGetAllGamePayWays(true);
      defaultInstance.initFields();
    }

    // @@protoc_insertion_point(class_scope:fm.lizhi.commons.template.datacenter.protocol.RequestGetAllGamePayWays)
  }

  public interface ResponseGetAllGamePayWaysOrBuilder
      extends com.google.protobuf.MessageOrBuilder {

    // repeated .fm.lizhi.commons.template.datacenter.protocol.GamePayWay gamePayWays = 1;
    /**
     * <code>repeated .fm.lizhi.commons.template.datacenter.protocol.GamePayWay gamePayWays = 1;</code>
     */
    java.util.List<fm.lizhi.ocean.seal.protocol.GamePayWayServiceProto.GamePayWay> 
        getGamePayWaysList();
    /**
     * <code>repeated .fm.lizhi.commons.template.datacenter.protocol.GamePayWay gamePayWays = 1;</code>
     */
    fm.lizhi.ocean.seal.protocol.GamePayWayServiceProto.GamePayWay getGamePayWays(int index);
    /**
     * <code>repeated .fm.lizhi.commons.template.datacenter.protocol.GamePayWay gamePayWays = 1;</code>
     */
    int getGamePayWaysCount();
    /**
     * <code>repeated .fm.lizhi.commons.template.datacenter.protocol.GamePayWay gamePayWays = 1;</code>
     */
    java.util.List<? extends fm.lizhi.ocean.seal.protocol.GamePayWayServiceProto.GamePayWayOrBuilder> 
        getGamePayWaysOrBuilderList();
    /**
     * <code>repeated .fm.lizhi.commons.template.datacenter.protocol.GamePayWay gamePayWays = 1;</code>
     */
    fm.lizhi.ocean.seal.protocol.GamePayWayServiceProto.GamePayWayOrBuilder getGamePayWaysOrBuilder(
        int index);
  }
  /**
   * Protobuf type {@code fm.lizhi.commons.template.datacenter.protocol.ResponseGetAllGamePayWays}
   *
   * <pre>
   * rcode == 0(SUCCESS) = 执行成功
   * rcode == 1(FAIL) = 错误
   * </pre>
   */
  public static final class ResponseGetAllGamePayWays extends
      com.google.protobuf.GeneratedMessage
      implements ResponseGetAllGamePayWaysOrBuilder {
    // Use ResponseGetAllGamePayWays.newBuilder() to construct.
    private ResponseGetAllGamePayWays(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
      super(builder);
      this.unknownFields = builder.getUnknownFields();
    }
    private ResponseGetAllGamePayWays(boolean noInit) { this.unknownFields = com.google.protobuf.UnknownFieldSet.getDefaultInstance(); }

    private static final ResponseGetAllGamePayWays defaultInstance;
    public static ResponseGetAllGamePayWays getDefaultInstance() {
      return defaultInstance;
    }

    public ResponseGetAllGamePayWays getDefaultInstanceForType() {
      return defaultInstance;
    }

    private final com.google.protobuf.UnknownFieldSet unknownFields;
    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
        getUnknownFields() {
      return this.unknownFields;
    }
    private ResponseGetAllGamePayWays(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      initFields();
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            default: {
              if (!parseUnknownField(input, unknownFields,
                                     extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
            case 10: {
              if (!((mutable_bitField0_ & 0x00000001) == 0x00000001)) {
                gamePayWays_ = new java.util.ArrayList<fm.lizhi.ocean.seal.protocol.GamePayWayServiceProto.GamePayWay>();
                mutable_bitField0_ |= 0x00000001;
              }
              gamePayWays_.add(input.readMessage(fm.lizhi.ocean.seal.protocol.GamePayWayServiceProto.GamePayWay.PARSER, extensionRegistry));
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e.getMessage()).setUnfinishedMessage(this);
      } finally {
        if (((mutable_bitField0_ & 0x00000001) == 0x00000001)) {
          gamePayWays_ = java.util.Collections.unmodifiableList(gamePayWays_);
        }
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return fm.lizhi.ocean.seal.protocol.GamePayWayServiceProto.internal_static_fm_lizhi_commons_template_datacenter_protocol_ResponseGetAllGamePayWays_descriptor;
    }

    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return fm.lizhi.ocean.seal.protocol.GamePayWayServiceProto.internal_static_fm_lizhi_commons_template_datacenter_protocol_ResponseGetAllGamePayWays_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              fm.lizhi.ocean.seal.protocol.GamePayWayServiceProto.ResponseGetAllGamePayWays.class, fm.lizhi.ocean.seal.protocol.GamePayWayServiceProto.ResponseGetAllGamePayWays.Builder.class);
    }

    public static com.google.protobuf.Parser<ResponseGetAllGamePayWays> PARSER =
        new com.google.protobuf.AbstractParser<ResponseGetAllGamePayWays>() {
      public ResponseGetAllGamePayWays parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new ResponseGetAllGamePayWays(input, extensionRegistry);
      }
    };

    @java.lang.Override
    public com.google.protobuf.Parser<ResponseGetAllGamePayWays> getParserForType() {
      return PARSER;
    }

    // repeated .fm.lizhi.commons.template.datacenter.protocol.GamePayWay gamePayWays = 1;
    public static final int GAMEPAYWAYS_FIELD_NUMBER = 1;
    private java.util.List<fm.lizhi.ocean.seal.protocol.GamePayWayServiceProto.GamePayWay> gamePayWays_;
    /**
     * <code>repeated .fm.lizhi.commons.template.datacenter.protocol.GamePayWay gamePayWays = 1;</code>
     */
    public java.util.List<fm.lizhi.ocean.seal.protocol.GamePayWayServiceProto.GamePayWay> getGamePayWaysList() {
      return gamePayWays_;
    }
    /**
     * <code>repeated .fm.lizhi.commons.template.datacenter.protocol.GamePayWay gamePayWays = 1;</code>
     */
    public java.util.List<? extends fm.lizhi.ocean.seal.protocol.GamePayWayServiceProto.GamePayWayOrBuilder> 
        getGamePayWaysOrBuilderList() {
      return gamePayWays_;
    }
    /**
     * <code>repeated .fm.lizhi.commons.template.datacenter.protocol.GamePayWay gamePayWays = 1;</code>
     */
    public int getGamePayWaysCount() {
      return gamePayWays_.size();
    }
    /**
     * <code>repeated .fm.lizhi.commons.template.datacenter.protocol.GamePayWay gamePayWays = 1;</code>
     */
    public fm.lizhi.ocean.seal.protocol.GamePayWayServiceProto.GamePayWay getGamePayWays(int index) {
      return gamePayWays_.get(index);
    }
    /**
     * <code>repeated .fm.lizhi.commons.template.datacenter.protocol.GamePayWay gamePayWays = 1;</code>
     */
    public fm.lizhi.ocean.seal.protocol.GamePayWayServiceProto.GamePayWayOrBuilder getGamePayWaysOrBuilder(
        int index) {
      return gamePayWays_.get(index);
    }

    private void initFields() {
      gamePayWays_ = java.util.Collections.emptyList();
    }
    private byte memoizedIsInitialized = -1;
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized != -1) return isInitialized == 1;

      memoizedIsInitialized = 1;
      return true;
    }

    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      getSerializedSize();
      for (int i = 0; i < gamePayWays_.size(); i++) {
        output.writeMessage(1, gamePayWays_.get(i));
      }
      getUnknownFields().writeTo(output);
    }

    private int memoizedSerializedSize = -1;
    public int getSerializedSize() {
      int size = memoizedSerializedSize;
      if (size != -1) return size;

      size = 0;
      for (int i = 0; i < gamePayWays_.size(); i++) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(1, gamePayWays_.get(i));
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSerializedSize = size;
      return size;
    }

    private static final long serialVersionUID = 0L;
    @java.lang.Override
    protected java.lang.Object writeReplace()
        throws java.io.ObjectStreamException {
      return super.writeReplace();
    }

    public static fm.lizhi.ocean.seal.protocol.GamePayWayServiceProto.ResponseGetAllGamePayWays parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static fm.lizhi.ocean.seal.protocol.GamePayWayServiceProto.ResponseGetAllGamePayWays parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static fm.lizhi.ocean.seal.protocol.GamePayWayServiceProto.ResponseGetAllGamePayWays parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static fm.lizhi.ocean.seal.protocol.GamePayWayServiceProto.ResponseGetAllGamePayWays parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static fm.lizhi.ocean.seal.protocol.GamePayWayServiceProto.ResponseGetAllGamePayWays parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return PARSER.parseFrom(input);
    }
    public static fm.lizhi.ocean.seal.protocol.GamePayWayServiceProto.ResponseGetAllGamePayWays parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseFrom(input, extensionRegistry);
    }
    public static fm.lizhi.ocean.seal.protocol.GamePayWayServiceProto.ResponseGetAllGamePayWays parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return PARSER.parseDelimitedFrom(input);
    }
    public static fm.lizhi.ocean.seal.protocol.GamePayWayServiceProto.ResponseGetAllGamePayWays parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseDelimitedFrom(input, extensionRegistry);
    }
    public static fm.lizhi.ocean.seal.protocol.GamePayWayServiceProto.ResponseGetAllGamePayWays parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return PARSER.parseFrom(input);
    }
    public static fm.lizhi.ocean.seal.protocol.GamePayWayServiceProto.ResponseGetAllGamePayWays parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseFrom(input, extensionRegistry);
    }

    public static Builder newBuilder() { return Builder.create(); }
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder(fm.lizhi.ocean.seal.protocol.GamePayWayServiceProto.ResponseGetAllGamePayWays prototype) {
      return newBuilder().mergeFrom(prototype);
    }
    public Builder toBuilder() { return newBuilder(this); }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code fm.lizhi.commons.template.datacenter.protocol.ResponseGetAllGamePayWays}
     *
     * <pre>
     * rcode == 0(SUCCESS) = 执行成功
     * rcode == 1(FAIL) = 错误
     * </pre>
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessage.Builder<Builder>
       implements fm.lizhi.ocean.seal.protocol.GamePayWayServiceProto.ResponseGetAllGamePayWaysOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return fm.lizhi.ocean.seal.protocol.GamePayWayServiceProto.internal_static_fm_lizhi_commons_template_datacenter_protocol_ResponseGetAllGamePayWays_descriptor;
      }

      protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return fm.lizhi.ocean.seal.protocol.GamePayWayServiceProto.internal_static_fm_lizhi_commons_template_datacenter_protocol_ResponseGetAllGamePayWays_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                fm.lizhi.ocean.seal.protocol.GamePayWayServiceProto.ResponseGetAllGamePayWays.class, fm.lizhi.ocean.seal.protocol.GamePayWayServiceProto.ResponseGetAllGamePayWays.Builder.class);
      }

      // Construct using fm.lizhi.ocean.seal.protocol.GamePayWayServiceProto.ResponseGetAllGamePayWays.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessage.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessage.alwaysUseFieldBuilders) {
          getGamePayWaysFieldBuilder();
        }
      }
      private static Builder create() {
        return new Builder();
      }

      public Builder clear() {
        super.clear();
        if (gamePayWaysBuilder_ == null) {
          gamePayWays_ = java.util.Collections.emptyList();
          bitField0_ = (bitField0_ & ~0x00000001);
        } else {
          gamePayWaysBuilder_.clear();
        }
        return this;
      }

      public Builder clone() {
        return create().mergeFrom(buildPartial());
      }

      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return fm.lizhi.ocean.seal.protocol.GamePayWayServiceProto.internal_static_fm_lizhi_commons_template_datacenter_protocol_ResponseGetAllGamePayWays_descriptor;
      }

      public fm.lizhi.ocean.seal.protocol.GamePayWayServiceProto.ResponseGetAllGamePayWays getDefaultInstanceForType() {
        return fm.lizhi.ocean.seal.protocol.GamePayWayServiceProto.ResponseGetAllGamePayWays.getDefaultInstance();
      }

      public fm.lizhi.ocean.seal.protocol.GamePayWayServiceProto.ResponseGetAllGamePayWays build() {
        fm.lizhi.ocean.seal.protocol.GamePayWayServiceProto.ResponseGetAllGamePayWays result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      public fm.lizhi.ocean.seal.protocol.GamePayWayServiceProto.ResponseGetAllGamePayWays buildPartial() {
        fm.lizhi.ocean.seal.protocol.GamePayWayServiceProto.ResponseGetAllGamePayWays result = new fm.lizhi.ocean.seal.protocol.GamePayWayServiceProto.ResponseGetAllGamePayWays(this);
        int from_bitField0_ = bitField0_;
        if (gamePayWaysBuilder_ == null) {
          if (((bitField0_ & 0x00000001) == 0x00000001)) {
            gamePayWays_ = java.util.Collections.unmodifiableList(gamePayWays_);
            bitField0_ = (bitField0_ & ~0x00000001);
          }
          result.gamePayWays_ = gamePayWays_;
        } else {
          result.gamePayWays_ = gamePayWaysBuilder_.build();
        }
        onBuilt();
        return result;
      }

      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof fm.lizhi.ocean.seal.protocol.GamePayWayServiceProto.ResponseGetAllGamePayWays) {
          return mergeFrom((fm.lizhi.ocean.seal.protocol.GamePayWayServiceProto.ResponseGetAllGamePayWays)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(fm.lizhi.ocean.seal.protocol.GamePayWayServiceProto.ResponseGetAllGamePayWays other) {
        if (other == fm.lizhi.ocean.seal.protocol.GamePayWayServiceProto.ResponseGetAllGamePayWays.getDefaultInstance()) return this;
        if (gamePayWaysBuilder_ == null) {
          if (!other.gamePayWays_.isEmpty()) {
            if (gamePayWays_.isEmpty()) {
              gamePayWays_ = other.gamePayWays_;
              bitField0_ = (bitField0_ & ~0x00000001);
            } else {
              ensureGamePayWaysIsMutable();
              gamePayWays_.addAll(other.gamePayWays_);
            }
            onChanged();
          }
        } else {
          if (!other.gamePayWays_.isEmpty()) {
            if (gamePayWaysBuilder_.isEmpty()) {
              gamePayWaysBuilder_.dispose();
              gamePayWaysBuilder_ = null;
              gamePayWays_ = other.gamePayWays_;
              bitField0_ = (bitField0_ & ~0x00000001);
              gamePayWaysBuilder_ = 
                com.google.protobuf.GeneratedMessage.alwaysUseFieldBuilders ?
                   getGamePayWaysFieldBuilder() : null;
            } else {
              gamePayWaysBuilder_.addAllMessages(other.gamePayWays_);
            }
          }
        }
        this.mergeUnknownFields(other.getUnknownFields());
        return this;
      }

      public final boolean isInitialized() {
        return true;
      }

      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        fm.lizhi.ocean.seal.protocol.GamePayWayServiceProto.ResponseGetAllGamePayWays parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (fm.lizhi.ocean.seal.protocol.GamePayWayServiceProto.ResponseGetAllGamePayWays) e.getUnfinishedMessage();
          throw e;
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      // repeated .fm.lizhi.commons.template.datacenter.protocol.GamePayWay gamePayWays = 1;
      private java.util.List<fm.lizhi.ocean.seal.protocol.GamePayWayServiceProto.GamePayWay> gamePayWays_ =
        java.util.Collections.emptyList();
      private void ensureGamePayWaysIsMutable() {
        if (!((bitField0_ & 0x00000001) == 0x00000001)) {
          gamePayWays_ = new java.util.ArrayList<fm.lizhi.ocean.seal.protocol.GamePayWayServiceProto.GamePayWay>(gamePayWays_);
          bitField0_ |= 0x00000001;
         }
      }

      private com.google.protobuf.RepeatedFieldBuilder<
          fm.lizhi.ocean.seal.protocol.GamePayWayServiceProto.GamePayWay, fm.lizhi.ocean.seal.protocol.GamePayWayServiceProto.GamePayWay.Builder, fm.lizhi.ocean.seal.protocol.GamePayWayServiceProto.GamePayWayOrBuilder> gamePayWaysBuilder_;

      /**
       * <code>repeated .fm.lizhi.commons.template.datacenter.protocol.GamePayWay gamePayWays = 1;</code>
       */
      public java.util.List<fm.lizhi.ocean.seal.protocol.GamePayWayServiceProto.GamePayWay> getGamePayWaysList() {
        if (gamePayWaysBuilder_ == null) {
          return java.util.Collections.unmodifiableList(gamePayWays_);
        } else {
          return gamePayWaysBuilder_.getMessageList();
        }
      }
      /**
       * <code>repeated .fm.lizhi.commons.template.datacenter.protocol.GamePayWay gamePayWays = 1;</code>
       */
      public int getGamePayWaysCount() {
        if (gamePayWaysBuilder_ == null) {
          return gamePayWays_.size();
        } else {
          return gamePayWaysBuilder_.getCount();
        }
      }
      /**
       * <code>repeated .fm.lizhi.commons.template.datacenter.protocol.GamePayWay gamePayWays = 1;</code>
       */
      public fm.lizhi.ocean.seal.protocol.GamePayWayServiceProto.GamePayWay getGamePayWays(int index) {
        if (gamePayWaysBuilder_ == null) {
          return gamePayWays_.get(index);
        } else {
          return gamePayWaysBuilder_.getMessage(index);
        }
      }
      /**
       * <code>repeated .fm.lizhi.commons.template.datacenter.protocol.GamePayWay gamePayWays = 1;</code>
       */
      public Builder setGamePayWays(
          int index, fm.lizhi.ocean.seal.protocol.GamePayWayServiceProto.GamePayWay value) {
        if (gamePayWaysBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureGamePayWaysIsMutable();
          gamePayWays_.set(index, value);
          onChanged();
        } else {
          gamePayWaysBuilder_.setMessage(index, value);
        }
        return this;
      }
      /**
       * <code>repeated .fm.lizhi.commons.template.datacenter.protocol.GamePayWay gamePayWays = 1;</code>
       */
      public Builder setGamePayWays(
          int index, fm.lizhi.ocean.seal.protocol.GamePayWayServiceProto.GamePayWay.Builder builderForValue) {
        if (gamePayWaysBuilder_ == null) {
          ensureGamePayWaysIsMutable();
          gamePayWays_.set(index, builderForValue.build());
          onChanged();
        } else {
          gamePayWaysBuilder_.setMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .fm.lizhi.commons.template.datacenter.protocol.GamePayWay gamePayWays = 1;</code>
       */
      public Builder addGamePayWays(fm.lizhi.ocean.seal.protocol.GamePayWayServiceProto.GamePayWay value) {
        if (gamePayWaysBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureGamePayWaysIsMutable();
          gamePayWays_.add(value);
          onChanged();
        } else {
          gamePayWaysBuilder_.addMessage(value);
        }
        return this;
      }
      /**
       * <code>repeated .fm.lizhi.commons.template.datacenter.protocol.GamePayWay gamePayWays = 1;</code>
       */
      public Builder addGamePayWays(
          int index, fm.lizhi.ocean.seal.protocol.GamePayWayServiceProto.GamePayWay value) {
        if (gamePayWaysBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureGamePayWaysIsMutable();
          gamePayWays_.add(index, value);
          onChanged();
        } else {
          gamePayWaysBuilder_.addMessage(index, value);
        }
        return this;
      }
      /**
       * <code>repeated .fm.lizhi.commons.template.datacenter.protocol.GamePayWay gamePayWays = 1;</code>
       */
      public Builder addGamePayWays(
          fm.lizhi.ocean.seal.protocol.GamePayWayServiceProto.GamePayWay.Builder builderForValue) {
        if (gamePayWaysBuilder_ == null) {
          ensureGamePayWaysIsMutable();
          gamePayWays_.add(builderForValue.build());
          onChanged();
        } else {
          gamePayWaysBuilder_.addMessage(builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .fm.lizhi.commons.template.datacenter.protocol.GamePayWay gamePayWays = 1;</code>
       */
      public Builder addGamePayWays(
          int index, fm.lizhi.ocean.seal.protocol.GamePayWayServiceProto.GamePayWay.Builder builderForValue) {
        if (gamePayWaysBuilder_ == null) {
          ensureGamePayWaysIsMutable();
          gamePayWays_.add(index, builderForValue.build());
          onChanged();
        } else {
          gamePayWaysBuilder_.addMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .fm.lizhi.commons.template.datacenter.protocol.GamePayWay gamePayWays = 1;</code>
       */
      public Builder addAllGamePayWays(
          java.lang.Iterable<? extends fm.lizhi.ocean.seal.protocol.GamePayWayServiceProto.GamePayWay> values) {
        if (gamePayWaysBuilder_ == null) {
          ensureGamePayWaysIsMutable();
          super.addAll(values, gamePayWays_);
          onChanged();
        } else {
          gamePayWaysBuilder_.addAllMessages(values);
        }
        return this;
      }
      /**
       * <code>repeated .fm.lizhi.commons.template.datacenter.protocol.GamePayWay gamePayWays = 1;</code>
       */
      public Builder clearGamePayWays() {
        if (gamePayWaysBuilder_ == null) {
          gamePayWays_ = java.util.Collections.emptyList();
          bitField0_ = (bitField0_ & ~0x00000001);
          onChanged();
        } else {
          gamePayWaysBuilder_.clear();
        }
        return this;
      }
      /**
       * <code>repeated .fm.lizhi.commons.template.datacenter.protocol.GamePayWay gamePayWays = 1;</code>
       */
      public Builder removeGamePayWays(int index) {
        if (gamePayWaysBuilder_ == null) {
          ensureGamePayWaysIsMutable();
          gamePayWays_.remove(index);
          onChanged();
        } else {
          gamePayWaysBuilder_.remove(index);
        }
        return this;
      }
      /**
       * <code>repeated .fm.lizhi.commons.template.datacenter.protocol.GamePayWay gamePayWays = 1;</code>
       */
      public fm.lizhi.ocean.seal.protocol.GamePayWayServiceProto.GamePayWay.Builder getGamePayWaysBuilder(
          int index) {
        return getGamePayWaysFieldBuilder().getBuilder(index);
      }
      /**
       * <code>repeated .fm.lizhi.commons.template.datacenter.protocol.GamePayWay gamePayWays = 1;</code>
       */
      public fm.lizhi.ocean.seal.protocol.GamePayWayServiceProto.GamePayWayOrBuilder getGamePayWaysOrBuilder(
          int index) {
        if (gamePayWaysBuilder_ == null) {
          return gamePayWays_.get(index);  } else {
          return gamePayWaysBuilder_.getMessageOrBuilder(index);
        }
      }
      /**
       * <code>repeated .fm.lizhi.commons.template.datacenter.protocol.GamePayWay gamePayWays = 1;</code>
       */
      public java.util.List<? extends fm.lizhi.ocean.seal.protocol.GamePayWayServiceProto.GamePayWayOrBuilder> 
           getGamePayWaysOrBuilderList() {
        if (gamePayWaysBuilder_ != null) {
          return gamePayWaysBuilder_.getMessageOrBuilderList();
        } else {
          return java.util.Collections.unmodifiableList(gamePayWays_);
        }
      }
      /**
       * <code>repeated .fm.lizhi.commons.template.datacenter.protocol.GamePayWay gamePayWays = 1;</code>
       */
      public fm.lizhi.ocean.seal.protocol.GamePayWayServiceProto.GamePayWay.Builder addGamePayWaysBuilder() {
        return getGamePayWaysFieldBuilder().addBuilder(
            fm.lizhi.ocean.seal.protocol.GamePayWayServiceProto.GamePayWay.getDefaultInstance());
      }
      /**
       * <code>repeated .fm.lizhi.commons.template.datacenter.protocol.GamePayWay gamePayWays = 1;</code>
       */
      public fm.lizhi.ocean.seal.protocol.GamePayWayServiceProto.GamePayWay.Builder addGamePayWaysBuilder(
          int index) {
        return getGamePayWaysFieldBuilder().addBuilder(
            index, fm.lizhi.ocean.seal.protocol.GamePayWayServiceProto.GamePayWay.getDefaultInstance());
      }
      /**
       * <code>repeated .fm.lizhi.commons.template.datacenter.protocol.GamePayWay gamePayWays = 1;</code>
       */
      public java.util.List<fm.lizhi.ocean.seal.protocol.GamePayWayServiceProto.GamePayWay.Builder> 
           getGamePayWaysBuilderList() {
        return getGamePayWaysFieldBuilder().getBuilderList();
      }
      private com.google.protobuf.RepeatedFieldBuilder<
          fm.lizhi.ocean.seal.protocol.GamePayWayServiceProto.GamePayWay, fm.lizhi.ocean.seal.protocol.GamePayWayServiceProto.GamePayWay.Builder, fm.lizhi.ocean.seal.protocol.GamePayWayServiceProto.GamePayWayOrBuilder> 
          getGamePayWaysFieldBuilder() {
        if (gamePayWaysBuilder_ == null) {
          gamePayWaysBuilder_ = new com.google.protobuf.RepeatedFieldBuilder<
              fm.lizhi.ocean.seal.protocol.GamePayWayServiceProto.GamePayWay, fm.lizhi.ocean.seal.protocol.GamePayWayServiceProto.GamePayWay.Builder, fm.lizhi.ocean.seal.protocol.GamePayWayServiceProto.GamePayWayOrBuilder>(
                  gamePayWays_,
                  ((bitField0_ & 0x00000001) == 0x00000001),
                  getParentForChildren(),
                  isClean());
          gamePayWays_ = null;
        }
        return gamePayWaysBuilder_;
      }

      // @@protoc_insertion_point(builder_scope:fm.lizhi.commons.template.datacenter.protocol.ResponseGetAllGamePayWays)
    }

    static {
      defaultInstance = new ResponseGetAllGamePayWays(true);
      defaultInstance.initFields();
    }

    // @@protoc_insertion_point(class_scope:fm.lizhi.commons.template.datacenter.protocol.ResponseGetAllGamePayWays)
  }

  private static com.google.protobuf.Descriptors.Descriptor
    internal_static_fm_lizhi_commons_template_datacenter_protocol_GamePayWay_descriptor;
  private static
    com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internal_static_fm_lizhi_commons_template_datacenter_protocol_GamePayWay_fieldAccessorTable;
  private static com.google.protobuf.Descriptors.Descriptor
    internal_static_fm_lizhi_commons_template_datacenter_protocol_RequestGetAllGamePayWays_descriptor;
  private static
    com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internal_static_fm_lizhi_commons_template_datacenter_protocol_RequestGetAllGamePayWays_fieldAccessorTable;
  private static com.google.protobuf.Descriptors.Descriptor
    internal_static_fm_lizhi_commons_template_datacenter_protocol_ResponseGetAllGamePayWays_descriptor;
  private static
    com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internal_static_fm_lizhi_commons_template_datacenter_protocol_ResponseGetAllGamePayWays_fieldAccessorTable;

  public static com.google.protobuf.Descriptors.FileDescriptor
      getDescriptor() {
    return descriptor;
  }
  private static com.google.protobuf.Descriptors.FileDescriptor
      descriptor;
  static {
    java.lang.String[] descriptorData = {
      "\n\033protocol_game_pay_way.proto\022-fm.lizhi." +
      "commons.template.datacenter.protocol\"4\n\n" +
      "GamePayWay\022\022\n\npayWayCode\030\001 \001(\005\022\022\n\npayWay" +
      "Name\030\002 \001(\t\"\032\n\030RequestGetAllGamePayWays\"k" +
      "\n\031ResponseGetAllGamePayWays\022N\n\013gamePayWa" +
      "ys\030\001 \003(\01329.fm.lizhi.commons.template.dat" +
      "acenter.protocol.GamePayWayB6\n\034fm.lizhi." +
      "ocean.seal.protocolB\026GamePayWayServicePr" +
      "oto"
    };
    com.google.protobuf.Descriptors.FileDescriptor.InternalDescriptorAssigner assigner =
      new com.google.protobuf.Descriptors.FileDescriptor.InternalDescriptorAssigner() {
        public com.google.protobuf.ExtensionRegistry assignDescriptors(
            com.google.protobuf.Descriptors.FileDescriptor root) {
          descriptor = root;
          internal_static_fm_lizhi_commons_template_datacenter_protocol_GamePayWay_descriptor =
            getDescriptor().getMessageTypes().get(0);
          internal_static_fm_lizhi_commons_template_datacenter_protocol_GamePayWay_fieldAccessorTable = new
            com.google.protobuf.GeneratedMessage.FieldAccessorTable(
              internal_static_fm_lizhi_commons_template_datacenter_protocol_GamePayWay_descriptor,
              new java.lang.String[] { "PayWayCode", "PayWayName", });
          internal_static_fm_lizhi_commons_template_datacenter_protocol_RequestGetAllGamePayWays_descriptor =
            getDescriptor().getMessageTypes().get(1);
          internal_static_fm_lizhi_commons_template_datacenter_protocol_RequestGetAllGamePayWays_fieldAccessorTable = new
            com.google.protobuf.GeneratedMessage.FieldAccessorTable(
              internal_static_fm_lizhi_commons_template_datacenter_protocol_RequestGetAllGamePayWays_descriptor,
              new java.lang.String[] { });
          internal_static_fm_lizhi_commons_template_datacenter_protocol_ResponseGetAllGamePayWays_descriptor =
            getDescriptor().getMessageTypes().get(2);
          internal_static_fm_lizhi_commons_template_datacenter_protocol_ResponseGetAllGamePayWays_fieldAccessorTable = new
            com.google.protobuf.GeneratedMessage.FieldAccessorTable(
              internal_static_fm_lizhi_commons_template_datacenter_protocol_ResponseGetAllGamePayWays_descriptor,
              new java.lang.String[] { "GamePayWays", });
          return null;
        }
      };
    com.google.protobuf.Descriptors.FileDescriptor
      .internalBuildGeneratedFileFrom(descriptorData,
        new com.google.protobuf.Descriptors.FileDescriptor[] {
        }, assigner);
  }

  // @@protoc_insertion_point(outer_class_scope)
}
