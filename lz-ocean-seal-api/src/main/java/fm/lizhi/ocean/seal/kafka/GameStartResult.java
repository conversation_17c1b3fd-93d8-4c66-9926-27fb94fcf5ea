package fm.lizhi.ocean.seal.kafka;

import java.util.Arrays;

/**
 * Created in 2022-01-26 19:39.
 *
 * <AUTHOR>
 */
public class GameStartResult{
    /**
     * 游戏ID
     */
    private long gameId;
    /**
     * 房间ID
     */
    private String roomId;
    /**
     * 游戏模式
     */
    private int gameMode;
    /**
     * 本局游戏的id （可能会重复上报，使用该字段去重）
     */
    private String gameRoundId;
    /**
     * 游戏真正开始时间(毫秒)
     */
    private long gameStartAtTime;
    /**
     * GamePlayer对象数组
     */
    private GamePlayer[] gamePlayers;
    /**
     * 游戏上报信息扩展参数（透传），取值范围：长度不超过1024字节，超过则截断
     */
    private String reportGameInfoExtras;

    /**
     * 渠道原始信息
     */
    private String rawResult;

    public GameStartResult() {
    }

    public long getGameId() {
        return gameId;
    }

    public void setGameId(long gameId) {
        this.gameId = gameId;
    }

    public String getRoomId() {
        return roomId;
    }

    public void setRoomId(String roomId) {
        this.roomId = roomId;
    }

    public int getGameMode() {
        return gameMode;
    }

    public void setGameMode(int gameMode) {
        this.gameMode = gameMode;
    }

    public String getGameRoundId() {
        return gameRoundId;
    }

    public void setGameRoundId(String gameRoundId) {
        this.gameRoundId = gameRoundId;
    }

    public long getGameStartAtTime() {
        return gameStartAtTime;
    }

    public void setGameStartAtTime(long gameStartAtTime) {
        this.gameStartAtTime = gameStartAtTime;
    }

    public GamePlayer[] getGamePlayers() {
        return gamePlayers;
    }

    public void setGamePlayers(GamePlayer[] gamePlayers) {
        this.gamePlayers = gamePlayers;
    }

    public String getReportGameInfoExtras() {
        return reportGameInfoExtras;
    }

    public void setReportGameInfoExtras(String reportGameInfoExtras) {
        this.reportGameInfoExtras = reportGameInfoExtras;
    }

    public String getRawResult() {
        return rawResult;
    }

    public void setRawResult(String rawResult) {
        this.rawResult = rawResult;
    }

    @Override
    public String toString() {
        return "GameStartResult{" +
                "gameId=" + gameId +
                ", roomId='" + roomId + '\'' +
                ", gameMode=" + gameMode +
                ", gameRoundId='" + gameRoundId + '\'' +
                ", gameStartAtTime=" + gameStartAtTime +
                ", gamePlayers=" + Arrays.toString(gamePlayers) +
                ", reportGameInfoExtras='" + reportGameInfoExtras + '\'' +
                ", rawResult='" + rawResult + '\'' +
                '}';
    }
}
