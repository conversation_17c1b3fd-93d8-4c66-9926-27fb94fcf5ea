// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: protocol_game_channel.proto

package fm.lizhi.ocean.seal.protocol;

public final class GameChannelServiceProto {
  private GameChannelServiceProto() {}
  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistry registry) {
  }
  public interface GameChannelOrBuilder
      extends com.google.protobuf.MessageOrBuilder {

    // optional int64 id = 1;
    /**
     * <code>optional int64 id = 1;</code>
     */
    boolean hasId();
    /**
     * <code>optional int64 id = 1;</code>
     */
    long getId();

    // optional string abbreviation = 2;
    /**
     * <code>optional string abbreviation = 2;</code>
     *
     * <pre>
     *简称
     * </pre>
     */
    boolean hasAbbreviation();
    /**
     * <code>optional string abbreviation = 2;</code>
     *
     * <pre>
     *简称
     * </pre>
     */
    java.lang.String getAbbreviation();
    /**
     * <code>optional string abbreviation = 2;</code>
     *
     * <pre>
     *简称
     * </pre>
     */
    com.google.protobuf.ByteString
        getAbbreviationBytes();

    // optional string channelName = 3;
    /**
     * <code>optional string channelName = 3;</code>
     *
     * <pre>
     *渠道名称
     * </pre>
     */
    boolean hasChannelName();
    /**
     * <code>optional string channelName = 3;</code>
     *
     * <pre>
     *渠道名称
     * </pre>
     */
    java.lang.String getChannelName();
    /**
     * <code>optional string channelName = 3;</code>
     *
     * <pre>
     *渠道名称
     * </pre>
     */
    com.google.protobuf.ByteString
        getChannelNameBytes();
  }
  /**
   * Protobuf type {@code fm.lizhi.commons.template.datacenter.protocol.GameChannel}
   */
  public static final class GameChannel extends
      com.google.protobuf.GeneratedMessage
      implements GameChannelOrBuilder {
    // Use GameChannel.newBuilder() to construct.
    private GameChannel(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
      super(builder);
      this.unknownFields = builder.getUnknownFields();
    }
    private GameChannel(boolean noInit) { this.unknownFields = com.google.protobuf.UnknownFieldSet.getDefaultInstance(); }

    private static final GameChannel defaultInstance;
    public static GameChannel getDefaultInstance() {
      return defaultInstance;
    }

    public GameChannel getDefaultInstanceForType() {
      return defaultInstance;
    }

    private final com.google.protobuf.UnknownFieldSet unknownFields;
    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
        getUnknownFields() {
      return this.unknownFields;
    }
    private GameChannel(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      initFields();
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            default: {
              if (!parseUnknownField(input, unknownFields,
                                     extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
            case 8: {
              bitField0_ |= 0x00000001;
              id_ = input.readInt64();
              break;
            }
            case 18: {
              bitField0_ |= 0x00000002;
              abbreviation_ = input.readBytes();
              break;
            }
            case 26: {
              bitField0_ |= 0x00000004;
              channelName_ = input.readBytes();
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e.getMessage()).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return fm.lizhi.ocean.seal.protocol.GameChannelServiceProto.internal_static_fm_lizhi_commons_template_datacenter_protocol_GameChannel_descriptor;
    }

    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return fm.lizhi.ocean.seal.protocol.GameChannelServiceProto.internal_static_fm_lizhi_commons_template_datacenter_protocol_GameChannel_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              fm.lizhi.ocean.seal.protocol.GameChannelServiceProto.GameChannel.class, fm.lizhi.ocean.seal.protocol.GameChannelServiceProto.GameChannel.Builder.class);
    }

    public static com.google.protobuf.Parser<GameChannel> PARSER =
        new com.google.protobuf.AbstractParser<GameChannel>() {
      public GameChannel parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new GameChannel(input, extensionRegistry);
      }
    };

    @java.lang.Override
    public com.google.protobuf.Parser<GameChannel> getParserForType() {
      return PARSER;
    }

    private int bitField0_;
    // optional int64 id = 1;
    public static final int ID_FIELD_NUMBER = 1;
    private long id_;
    /**
     * <code>optional int64 id = 1;</code>
     */
    public boolean hasId() {
      return ((bitField0_ & 0x00000001) == 0x00000001);
    }
    /**
     * <code>optional int64 id = 1;</code>
     */
    public long getId() {
      return id_;
    }

    // optional string abbreviation = 2;
    public static final int ABBREVIATION_FIELD_NUMBER = 2;
    private java.lang.Object abbreviation_;
    /**
     * <code>optional string abbreviation = 2;</code>
     *
     * <pre>
     *简称
     * </pre>
     */
    public boolean hasAbbreviation() {
      return ((bitField0_ & 0x00000002) == 0x00000002);
    }
    /**
     * <code>optional string abbreviation = 2;</code>
     *
     * <pre>
     *简称
     * </pre>
     */
    public java.lang.String getAbbreviation() {
      java.lang.Object ref = abbreviation_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          abbreviation_ = s;
        }
        return s;
      }
    }
    /**
     * <code>optional string abbreviation = 2;</code>
     *
     * <pre>
     *简称
     * </pre>
     */
    public com.google.protobuf.ByteString
        getAbbreviationBytes() {
      java.lang.Object ref = abbreviation_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        abbreviation_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    // optional string channelName = 3;
    public static final int CHANNELNAME_FIELD_NUMBER = 3;
    private java.lang.Object channelName_;
    /**
     * <code>optional string channelName = 3;</code>
     *
     * <pre>
     *渠道名称
     * </pre>
     */
    public boolean hasChannelName() {
      return ((bitField0_ & 0x00000004) == 0x00000004);
    }
    /**
     * <code>optional string channelName = 3;</code>
     *
     * <pre>
     *渠道名称
     * </pre>
     */
    public java.lang.String getChannelName() {
      java.lang.Object ref = channelName_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          channelName_ = s;
        }
        return s;
      }
    }
    /**
     * <code>optional string channelName = 3;</code>
     *
     * <pre>
     *渠道名称
     * </pre>
     */
    public com.google.protobuf.ByteString
        getChannelNameBytes() {
      java.lang.Object ref = channelName_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        channelName_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    private void initFields() {
      id_ = 0L;
      abbreviation_ = "";
      channelName_ = "";
    }
    private byte memoizedIsInitialized = -1;
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized != -1) return isInitialized == 1;

      memoizedIsInitialized = 1;
      return true;
    }

    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      getSerializedSize();
      if (((bitField0_ & 0x00000001) == 0x00000001)) {
        output.writeInt64(1, id_);
      }
      if (((bitField0_ & 0x00000002) == 0x00000002)) {
        output.writeBytes(2, getAbbreviationBytes());
      }
      if (((bitField0_ & 0x00000004) == 0x00000004)) {
        output.writeBytes(3, getChannelNameBytes());
      }
      getUnknownFields().writeTo(output);
    }

    private int memoizedSerializedSize = -1;
    public int getSerializedSize() {
      int size = memoizedSerializedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) == 0x00000001)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt64Size(1, id_);
      }
      if (((bitField0_ & 0x00000002) == 0x00000002)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(2, getAbbreviationBytes());
      }
      if (((bitField0_ & 0x00000004) == 0x00000004)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(3, getChannelNameBytes());
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSerializedSize = size;
      return size;
    }

    private static final long serialVersionUID = 0L;
    @java.lang.Override
    protected java.lang.Object writeReplace()
        throws java.io.ObjectStreamException {
      return super.writeReplace();
    }

    public static fm.lizhi.ocean.seal.protocol.GameChannelServiceProto.GameChannel parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static fm.lizhi.ocean.seal.protocol.GameChannelServiceProto.GameChannel parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static fm.lizhi.ocean.seal.protocol.GameChannelServiceProto.GameChannel parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static fm.lizhi.ocean.seal.protocol.GameChannelServiceProto.GameChannel parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static fm.lizhi.ocean.seal.protocol.GameChannelServiceProto.GameChannel parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return PARSER.parseFrom(input);
    }
    public static fm.lizhi.ocean.seal.protocol.GameChannelServiceProto.GameChannel parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseFrom(input, extensionRegistry);
    }
    public static fm.lizhi.ocean.seal.protocol.GameChannelServiceProto.GameChannel parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return PARSER.parseDelimitedFrom(input);
    }
    public static fm.lizhi.ocean.seal.protocol.GameChannelServiceProto.GameChannel parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseDelimitedFrom(input, extensionRegistry);
    }
    public static fm.lizhi.ocean.seal.protocol.GameChannelServiceProto.GameChannel parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return PARSER.parseFrom(input);
    }
    public static fm.lizhi.ocean.seal.protocol.GameChannelServiceProto.GameChannel parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseFrom(input, extensionRegistry);
    }

    public static Builder newBuilder() { return Builder.create(); }
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder(fm.lizhi.ocean.seal.protocol.GameChannelServiceProto.GameChannel prototype) {
      return newBuilder().mergeFrom(prototype);
    }
    public Builder toBuilder() { return newBuilder(this); }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code fm.lizhi.commons.template.datacenter.protocol.GameChannel}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessage.Builder<Builder>
       implements fm.lizhi.ocean.seal.protocol.GameChannelServiceProto.GameChannelOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return fm.lizhi.ocean.seal.protocol.GameChannelServiceProto.internal_static_fm_lizhi_commons_template_datacenter_protocol_GameChannel_descriptor;
      }

      protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return fm.lizhi.ocean.seal.protocol.GameChannelServiceProto.internal_static_fm_lizhi_commons_template_datacenter_protocol_GameChannel_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                fm.lizhi.ocean.seal.protocol.GameChannelServiceProto.GameChannel.class, fm.lizhi.ocean.seal.protocol.GameChannelServiceProto.GameChannel.Builder.class);
      }

      // Construct using fm.lizhi.ocean.seal.protocol.GameChannelServiceProto.GameChannel.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessage.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessage.alwaysUseFieldBuilders) {
        }
      }
      private static Builder create() {
        return new Builder();
      }

      public Builder clear() {
        super.clear();
        id_ = 0L;
        bitField0_ = (bitField0_ & ~0x00000001);
        abbreviation_ = "";
        bitField0_ = (bitField0_ & ~0x00000002);
        channelName_ = "";
        bitField0_ = (bitField0_ & ~0x00000004);
        return this;
      }

      public Builder clone() {
        return create().mergeFrom(buildPartial());
      }

      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return fm.lizhi.ocean.seal.protocol.GameChannelServiceProto.internal_static_fm_lizhi_commons_template_datacenter_protocol_GameChannel_descriptor;
      }

      public fm.lizhi.ocean.seal.protocol.GameChannelServiceProto.GameChannel getDefaultInstanceForType() {
        return fm.lizhi.ocean.seal.protocol.GameChannelServiceProto.GameChannel.getDefaultInstance();
      }

      public fm.lizhi.ocean.seal.protocol.GameChannelServiceProto.GameChannel build() {
        fm.lizhi.ocean.seal.protocol.GameChannelServiceProto.GameChannel result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      public fm.lizhi.ocean.seal.protocol.GameChannelServiceProto.GameChannel buildPartial() {
        fm.lizhi.ocean.seal.protocol.GameChannelServiceProto.GameChannel result = new fm.lizhi.ocean.seal.protocol.GameChannelServiceProto.GameChannel(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) == 0x00000001)) {
          to_bitField0_ |= 0x00000001;
        }
        result.id_ = id_;
        if (((from_bitField0_ & 0x00000002) == 0x00000002)) {
          to_bitField0_ |= 0x00000002;
        }
        result.abbreviation_ = abbreviation_;
        if (((from_bitField0_ & 0x00000004) == 0x00000004)) {
          to_bitField0_ |= 0x00000004;
        }
        result.channelName_ = channelName_;
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof fm.lizhi.ocean.seal.protocol.GameChannelServiceProto.GameChannel) {
          return mergeFrom((fm.lizhi.ocean.seal.protocol.GameChannelServiceProto.GameChannel)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(fm.lizhi.ocean.seal.protocol.GameChannelServiceProto.GameChannel other) {
        if (other == fm.lizhi.ocean.seal.protocol.GameChannelServiceProto.GameChannel.getDefaultInstance()) return this;
        if (other.hasId()) {
          setId(other.getId());
        }
        if (other.hasAbbreviation()) {
          bitField0_ |= 0x00000002;
          abbreviation_ = other.abbreviation_;
          onChanged();
        }
        if (other.hasChannelName()) {
          bitField0_ |= 0x00000004;
          channelName_ = other.channelName_;
          onChanged();
        }
        this.mergeUnknownFields(other.getUnknownFields());
        return this;
      }

      public final boolean isInitialized() {
        return true;
      }

      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        fm.lizhi.ocean.seal.protocol.GameChannelServiceProto.GameChannel parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (fm.lizhi.ocean.seal.protocol.GameChannelServiceProto.GameChannel) e.getUnfinishedMessage();
          throw e;
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      // optional int64 id = 1;
      private long id_ ;
      /**
       * <code>optional int64 id = 1;</code>
       */
      public boolean hasId() {
        return ((bitField0_ & 0x00000001) == 0x00000001);
      }
      /**
       * <code>optional int64 id = 1;</code>
       */
      public long getId() {
        return id_;
      }
      /**
       * <code>optional int64 id = 1;</code>
       */
      public Builder setId(long value) {
        bitField0_ |= 0x00000001;
        id_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional int64 id = 1;</code>
       */
      public Builder clearId() {
        bitField0_ = (bitField0_ & ~0x00000001);
        id_ = 0L;
        onChanged();
        return this;
      }

      // optional string abbreviation = 2;
      private java.lang.Object abbreviation_ = "";
      /**
       * <code>optional string abbreviation = 2;</code>
       *
       * <pre>
       *简称
       * </pre>
       */
      public boolean hasAbbreviation() {
        return ((bitField0_ & 0x00000002) == 0x00000002);
      }
      /**
       * <code>optional string abbreviation = 2;</code>
       *
       * <pre>
       *简称
       * </pre>
       */
      public java.lang.String getAbbreviation() {
        java.lang.Object ref = abbreviation_;
        if (!(ref instanceof java.lang.String)) {
          java.lang.String s = ((com.google.protobuf.ByteString) ref)
              .toStringUtf8();
          abbreviation_ = s;
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <code>optional string abbreviation = 2;</code>
       *
       * <pre>
       *简称
       * </pre>
       */
      public com.google.protobuf.ByteString
          getAbbreviationBytes() {
        java.lang.Object ref = abbreviation_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          abbreviation_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <code>optional string abbreviation = 2;</code>
       *
       * <pre>
       *简称
       * </pre>
       */
      public Builder setAbbreviation(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000002;
        abbreviation_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional string abbreviation = 2;</code>
       *
       * <pre>
       *简称
       * </pre>
       */
      public Builder clearAbbreviation() {
        bitField0_ = (bitField0_ & ~0x00000002);
        abbreviation_ = getDefaultInstance().getAbbreviation();
        onChanged();
        return this;
      }
      /**
       * <code>optional string abbreviation = 2;</code>
       *
       * <pre>
       *简称
       * </pre>
       */
      public Builder setAbbreviationBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000002;
        abbreviation_ = value;
        onChanged();
        return this;
      }

      // optional string channelName = 3;
      private java.lang.Object channelName_ = "";
      /**
       * <code>optional string channelName = 3;</code>
       *
       * <pre>
       *渠道名称
       * </pre>
       */
      public boolean hasChannelName() {
        return ((bitField0_ & 0x00000004) == 0x00000004);
      }
      /**
       * <code>optional string channelName = 3;</code>
       *
       * <pre>
       *渠道名称
       * </pre>
       */
      public java.lang.String getChannelName() {
        java.lang.Object ref = channelName_;
        if (!(ref instanceof java.lang.String)) {
          java.lang.String s = ((com.google.protobuf.ByteString) ref)
              .toStringUtf8();
          channelName_ = s;
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <code>optional string channelName = 3;</code>
       *
       * <pre>
       *渠道名称
       * </pre>
       */
      public com.google.protobuf.ByteString
          getChannelNameBytes() {
        java.lang.Object ref = channelName_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          channelName_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <code>optional string channelName = 3;</code>
       *
       * <pre>
       *渠道名称
       * </pre>
       */
      public Builder setChannelName(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000004;
        channelName_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional string channelName = 3;</code>
       *
       * <pre>
       *渠道名称
       * </pre>
       */
      public Builder clearChannelName() {
        bitField0_ = (bitField0_ & ~0x00000004);
        channelName_ = getDefaultInstance().getChannelName();
        onChanged();
        return this;
      }
      /**
       * <code>optional string channelName = 3;</code>
       *
       * <pre>
       *渠道名称
       * </pre>
       */
      public Builder setChannelNameBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000004;
        channelName_ = value;
        onChanged();
        return this;
      }

      // @@protoc_insertion_point(builder_scope:fm.lizhi.commons.template.datacenter.protocol.GameChannel)
    }

    static {
      defaultInstance = new GameChannel(true);
      defaultInstance.initFields();
    }

    // @@protoc_insertion_point(class_scope:fm.lizhi.commons.template.datacenter.protocol.GameChannel)
  }

  public interface EnumGameChannelOrBuilder
      extends com.google.protobuf.MessageOrBuilder {

    // optional string labelChannel = 1;
    /**
     * <code>optional string labelChannel = 1;</code>
     *
     * <pre>
     *渠道枚举label
     * </pre>
     */
    boolean hasLabelChannel();
    /**
     * <code>optional string labelChannel = 1;</code>
     *
     * <pre>
     *渠道枚举label
     * </pre>
     */
    java.lang.String getLabelChannel();
    /**
     * <code>optional string labelChannel = 1;</code>
     *
     * <pre>
     *渠道枚举label
     * </pre>
     */
    com.google.protobuf.ByteString
        getLabelChannelBytes();

    // optional string valueChannel = 2;
    /**
     * <code>optional string valueChannel = 2;</code>
     *
     * <pre>
     *渠道枚举value
     * </pre>
     */
    boolean hasValueChannel();
    /**
     * <code>optional string valueChannel = 2;</code>
     *
     * <pre>
     *渠道枚举value
     * </pre>
     */
    java.lang.String getValueChannel();
    /**
     * <code>optional string valueChannel = 2;</code>
     *
     * <pre>
     *渠道枚举value
     * </pre>
     */
    com.google.protobuf.ByteString
        getValueChannelBytes();
  }
  /**
   * Protobuf type {@code fm.lizhi.commons.template.datacenter.protocol.EnumGameChannel}
   */
  public static final class EnumGameChannel extends
      com.google.protobuf.GeneratedMessage
      implements EnumGameChannelOrBuilder {
    // Use EnumGameChannel.newBuilder() to construct.
    private EnumGameChannel(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
      super(builder);
      this.unknownFields = builder.getUnknownFields();
    }
    private EnumGameChannel(boolean noInit) { this.unknownFields = com.google.protobuf.UnknownFieldSet.getDefaultInstance(); }

    private static final EnumGameChannel defaultInstance;
    public static EnumGameChannel getDefaultInstance() {
      return defaultInstance;
    }

    public EnumGameChannel getDefaultInstanceForType() {
      return defaultInstance;
    }

    private final com.google.protobuf.UnknownFieldSet unknownFields;
    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
        getUnknownFields() {
      return this.unknownFields;
    }
    private EnumGameChannel(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      initFields();
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            default: {
              if (!parseUnknownField(input, unknownFields,
                                     extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
            case 10: {
              bitField0_ |= 0x00000001;
              labelChannel_ = input.readBytes();
              break;
            }
            case 18: {
              bitField0_ |= 0x00000002;
              valueChannel_ = input.readBytes();
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e.getMessage()).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return fm.lizhi.ocean.seal.protocol.GameChannelServiceProto.internal_static_fm_lizhi_commons_template_datacenter_protocol_EnumGameChannel_descriptor;
    }

    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return fm.lizhi.ocean.seal.protocol.GameChannelServiceProto.internal_static_fm_lizhi_commons_template_datacenter_protocol_EnumGameChannel_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              fm.lizhi.ocean.seal.protocol.GameChannelServiceProto.EnumGameChannel.class, fm.lizhi.ocean.seal.protocol.GameChannelServiceProto.EnumGameChannel.Builder.class);
    }

    public static com.google.protobuf.Parser<EnumGameChannel> PARSER =
        new com.google.protobuf.AbstractParser<EnumGameChannel>() {
      public EnumGameChannel parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new EnumGameChannel(input, extensionRegistry);
      }
    };

    @java.lang.Override
    public com.google.protobuf.Parser<EnumGameChannel> getParserForType() {
      return PARSER;
    }

    private int bitField0_;
    // optional string labelChannel = 1;
    public static final int LABELCHANNEL_FIELD_NUMBER = 1;
    private java.lang.Object labelChannel_;
    /**
     * <code>optional string labelChannel = 1;</code>
     *
     * <pre>
     *渠道枚举label
     * </pre>
     */
    public boolean hasLabelChannel() {
      return ((bitField0_ & 0x00000001) == 0x00000001);
    }
    /**
     * <code>optional string labelChannel = 1;</code>
     *
     * <pre>
     *渠道枚举label
     * </pre>
     */
    public java.lang.String getLabelChannel() {
      java.lang.Object ref = labelChannel_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          labelChannel_ = s;
        }
        return s;
      }
    }
    /**
     * <code>optional string labelChannel = 1;</code>
     *
     * <pre>
     *渠道枚举label
     * </pre>
     */
    public com.google.protobuf.ByteString
        getLabelChannelBytes() {
      java.lang.Object ref = labelChannel_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        labelChannel_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    // optional string valueChannel = 2;
    public static final int VALUECHANNEL_FIELD_NUMBER = 2;
    private java.lang.Object valueChannel_;
    /**
     * <code>optional string valueChannel = 2;</code>
     *
     * <pre>
     *渠道枚举value
     * </pre>
     */
    public boolean hasValueChannel() {
      return ((bitField0_ & 0x00000002) == 0x00000002);
    }
    /**
     * <code>optional string valueChannel = 2;</code>
     *
     * <pre>
     *渠道枚举value
     * </pre>
     */
    public java.lang.String getValueChannel() {
      java.lang.Object ref = valueChannel_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          valueChannel_ = s;
        }
        return s;
      }
    }
    /**
     * <code>optional string valueChannel = 2;</code>
     *
     * <pre>
     *渠道枚举value
     * </pre>
     */
    public com.google.protobuf.ByteString
        getValueChannelBytes() {
      java.lang.Object ref = valueChannel_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        valueChannel_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    private void initFields() {
      labelChannel_ = "";
      valueChannel_ = "";
    }
    private byte memoizedIsInitialized = -1;
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized != -1) return isInitialized == 1;

      memoizedIsInitialized = 1;
      return true;
    }

    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      getSerializedSize();
      if (((bitField0_ & 0x00000001) == 0x00000001)) {
        output.writeBytes(1, getLabelChannelBytes());
      }
      if (((bitField0_ & 0x00000002) == 0x00000002)) {
        output.writeBytes(2, getValueChannelBytes());
      }
      getUnknownFields().writeTo(output);
    }

    private int memoizedSerializedSize = -1;
    public int getSerializedSize() {
      int size = memoizedSerializedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) == 0x00000001)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(1, getLabelChannelBytes());
      }
      if (((bitField0_ & 0x00000002) == 0x00000002)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(2, getValueChannelBytes());
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSerializedSize = size;
      return size;
    }

    private static final long serialVersionUID = 0L;
    @java.lang.Override
    protected java.lang.Object writeReplace()
        throws java.io.ObjectStreamException {
      return super.writeReplace();
    }

    public static fm.lizhi.ocean.seal.protocol.GameChannelServiceProto.EnumGameChannel parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static fm.lizhi.ocean.seal.protocol.GameChannelServiceProto.EnumGameChannel parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static fm.lizhi.ocean.seal.protocol.GameChannelServiceProto.EnumGameChannel parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static fm.lizhi.ocean.seal.protocol.GameChannelServiceProto.EnumGameChannel parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static fm.lizhi.ocean.seal.protocol.GameChannelServiceProto.EnumGameChannel parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return PARSER.parseFrom(input);
    }
    public static fm.lizhi.ocean.seal.protocol.GameChannelServiceProto.EnumGameChannel parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseFrom(input, extensionRegistry);
    }
    public static fm.lizhi.ocean.seal.protocol.GameChannelServiceProto.EnumGameChannel parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return PARSER.parseDelimitedFrom(input);
    }
    public static fm.lizhi.ocean.seal.protocol.GameChannelServiceProto.EnumGameChannel parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseDelimitedFrom(input, extensionRegistry);
    }
    public static fm.lizhi.ocean.seal.protocol.GameChannelServiceProto.EnumGameChannel parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return PARSER.parseFrom(input);
    }
    public static fm.lizhi.ocean.seal.protocol.GameChannelServiceProto.EnumGameChannel parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseFrom(input, extensionRegistry);
    }

    public static Builder newBuilder() { return Builder.create(); }
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder(fm.lizhi.ocean.seal.protocol.GameChannelServiceProto.EnumGameChannel prototype) {
      return newBuilder().mergeFrom(prototype);
    }
    public Builder toBuilder() { return newBuilder(this); }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code fm.lizhi.commons.template.datacenter.protocol.EnumGameChannel}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessage.Builder<Builder>
       implements fm.lizhi.ocean.seal.protocol.GameChannelServiceProto.EnumGameChannelOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return fm.lizhi.ocean.seal.protocol.GameChannelServiceProto.internal_static_fm_lizhi_commons_template_datacenter_protocol_EnumGameChannel_descriptor;
      }

      protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return fm.lizhi.ocean.seal.protocol.GameChannelServiceProto.internal_static_fm_lizhi_commons_template_datacenter_protocol_EnumGameChannel_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                fm.lizhi.ocean.seal.protocol.GameChannelServiceProto.EnumGameChannel.class, fm.lizhi.ocean.seal.protocol.GameChannelServiceProto.EnumGameChannel.Builder.class);
      }

      // Construct using fm.lizhi.ocean.seal.protocol.GameChannelServiceProto.EnumGameChannel.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessage.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessage.alwaysUseFieldBuilders) {
        }
      }
      private static Builder create() {
        return new Builder();
      }

      public Builder clear() {
        super.clear();
        labelChannel_ = "";
        bitField0_ = (bitField0_ & ~0x00000001);
        valueChannel_ = "";
        bitField0_ = (bitField0_ & ~0x00000002);
        return this;
      }

      public Builder clone() {
        return create().mergeFrom(buildPartial());
      }

      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return fm.lizhi.ocean.seal.protocol.GameChannelServiceProto.internal_static_fm_lizhi_commons_template_datacenter_protocol_EnumGameChannel_descriptor;
      }

      public fm.lizhi.ocean.seal.protocol.GameChannelServiceProto.EnumGameChannel getDefaultInstanceForType() {
        return fm.lizhi.ocean.seal.protocol.GameChannelServiceProto.EnumGameChannel.getDefaultInstance();
      }

      public fm.lizhi.ocean.seal.protocol.GameChannelServiceProto.EnumGameChannel build() {
        fm.lizhi.ocean.seal.protocol.GameChannelServiceProto.EnumGameChannel result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      public fm.lizhi.ocean.seal.protocol.GameChannelServiceProto.EnumGameChannel buildPartial() {
        fm.lizhi.ocean.seal.protocol.GameChannelServiceProto.EnumGameChannel result = new fm.lizhi.ocean.seal.protocol.GameChannelServiceProto.EnumGameChannel(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) == 0x00000001)) {
          to_bitField0_ |= 0x00000001;
        }
        result.labelChannel_ = labelChannel_;
        if (((from_bitField0_ & 0x00000002) == 0x00000002)) {
          to_bitField0_ |= 0x00000002;
        }
        result.valueChannel_ = valueChannel_;
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof fm.lizhi.ocean.seal.protocol.GameChannelServiceProto.EnumGameChannel) {
          return mergeFrom((fm.lizhi.ocean.seal.protocol.GameChannelServiceProto.EnumGameChannel)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(fm.lizhi.ocean.seal.protocol.GameChannelServiceProto.EnumGameChannel other) {
        if (other == fm.lizhi.ocean.seal.protocol.GameChannelServiceProto.EnumGameChannel.getDefaultInstance()) return this;
        if (other.hasLabelChannel()) {
          bitField0_ |= 0x00000001;
          labelChannel_ = other.labelChannel_;
          onChanged();
        }
        if (other.hasValueChannel()) {
          bitField0_ |= 0x00000002;
          valueChannel_ = other.valueChannel_;
          onChanged();
        }
        this.mergeUnknownFields(other.getUnknownFields());
        return this;
      }

      public final boolean isInitialized() {
        return true;
      }

      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        fm.lizhi.ocean.seal.protocol.GameChannelServiceProto.EnumGameChannel parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (fm.lizhi.ocean.seal.protocol.GameChannelServiceProto.EnumGameChannel) e.getUnfinishedMessage();
          throw e;
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      // optional string labelChannel = 1;
      private java.lang.Object labelChannel_ = "";
      /**
       * <code>optional string labelChannel = 1;</code>
       *
       * <pre>
       *渠道枚举label
       * </pre>
       */
      public boolean hasLabelChannel() {
        return ((bitField0_ & 0x00000001) == 0x00000001);
      }
      /**
       * <code>optional string labelChannel = 1;</code>
       *
       * <pre>
       *渠道枚举label
       * </pre>
       */
      public java.lang.String getLabelChannel() {
        java.lang.Object ref = labelChannel_;
        if (!(ref instanceof java.lang.String)) {
          java.lang.String s = ((com.google.protobuf.ByteString) ref)
              .toStringUtf8();
          labelChannel_ = s;
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <code>optional string labelChannel = 1;</code>
       *
       * <pre>
       *渠道枚举label
       * </pre>
       */
      public com.google.protobuf.ByteString
          getLabelChannelBytes() {
        java.lang.Object ref = labelChannel_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          labelChannel_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <code>optional string labelChannel = 1;</code>
       *
       * <pre>
       *渠道枚举label
       * </pre>
       */
      public Builder setLabelChannel(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000001;
        labelChannel_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional string labelChannel = 1;</code>
       *
       * <pre>
       *渠道枚举label
       * </pre>
       */
      public Builder clearLabelChannel() {
        bitField0_ = (bitField0_ & ~0x00000001);
        labelChannel_ = getDefaultInstance().getLabelChannel();
        onChanged();
        return this;
      }
      /**
       * <code>optional string labelChannel = 1;</code>
       *
       * <pre>
       *渠道枚举label
       * </pre>
       */
      public Builder setLabelChannelBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000001;
        labelChannel_ = value;
        onChanged();
        return this;
      }

      // optional string valueChannel = 2;
      private java.lang.Object valueChannel_ = "";
      /**
       * <code>optional string valueChannel = 2;</code>
       *
       * <pre>
       *渠道枚举value
       * </pre>
       */
      public boolean hasValueChannel() {
        return ((bitField0_ & 0x00000002) == 0x00000002);
      }
      /**
       * <code>optional string valueChannel = 2;</code>
       *
       * <pre>
       *渠道枚举value
       * </pre>
       */
      public java.lang.String getValueChannel() {
        java.lang.Object ref = valueChannel_;
        if (!(ref instanceof java.lang.String)) {
          java.lang.String s = ((com.google.protobuf.ByteString) ref)
              .toStringUtf8();
          valueChannel_ = s;
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <code>optional string valueChannel = 2;</code>
       *
       * <pre>
       *渠道枚举value
       * </pre>
       */
      public com.google.protobuf.ByteString
          getValueChannelBytes() {
        java.lang.Object ref = valueChannel_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          valueChannel_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <code>optional string valueChannel = 2;</code>
       *
       * <pre>
       *渠道枚举value
       * </pre>
       */
      public Builder setValueChannel(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000002;
        valueChannel_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional string valueChannel = 2;</code>
       *
       * <pre>
       *渠道枚举value
       * </pre>
       */
      public Builder clearValueChannel() {
        bitField0_ = (bitField0_ & ~0x00000002);
        valueChannel_ = getDefaultInstance().getValueChannel();
        onChanged();
        return this;
      }
      /**
       * <code>optional string valueChannel = 2;</code>
       *
       * <pre>
       *渠道枚举value
       * </pre>
       */
      public Builder setValueChannelBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000002;
        valueChannel_ = value;
        onChanged();
        return this;
      }

      // @@protoc_insertion_point(builder_scope:fm.lizhi.commons.template.datacenter.protocol.EnumGameChannel)
    }

    static {
      defaultInstance = new EnumGameChannel(true);
      defaultInstance.initFields();
    }

    // @@protoc_insertion_point(class_scope:fm.lizhi.commons.template.datacenter.protocol.EnumGameChannel)
  }

  public interface RequestGetAllGameChannelsOrBuilder
      extends com.google.protobuf.MessageOrBuilder {
  }
  /**
   * Protobuf type {@code fm.lizhi.commons.template.datacenter.protocol.RequestGetAllGameChannels}
   *
   * <pre>
   * GameChannelService.java
   * 获取游戏渠道列表
   * domain = 4302, op = 220
   * </pre>
   */
  public static final class RequestGetAllGameChannels extends
      com.google.protobuf.GeneratedMessage
      implements RequestGetAllGameChannelsOrBuilder {
    // Use RequestGetAllGameChannels.newBuilder() to construct.
    private RequestGetAllGameChannels(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
      super(builder);
      this.unknownFields = builder.getUnknownFields();
    }
    private RequestGetAllGameChannels(boolean noInit) { this.unknownFields = com.google.protobuf.UnknownFieldSet.getDefaultInstance(); }

    private static final RequestGetAllGameChannels defaultInstance;
    public static RequestGetAllGameChannels getDefaultInstance() {
      return defaultInstance;
    }

    public RequestGetAllGameChannels getDefaultInstanceForType() {
      return defaultInstance;
    }

    private final com.google.protobuf.UnknownFieldSet unknownFields;
    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
        getUnknownFields() {
      return this.unknownFields;
    }
    private RequestGetAllGameChannels(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      initFields();
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            default: {
              if (!parseUnknownField(input, unknownFields,
                                     extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e.getMessage()).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return fm.lizhi.ocean.seal.protocol.GameChannelServiceProto.internal_static_fm_lizhi_commons_template_datacenter_protocol_RequestGetAllGameChannels_descriptor;
    }

    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return fm.lizhi.ocean.seal.protocol.GameChannelServiceProto.internal_static_fm_lizhi_commons_template_datacenter_protocol_RequestGetAllGameChannels_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              fm.lizhi.ocean.seal.protocol.GameChannelServiceProto.RequestGetAllGameChannels.class, fm.lizhi.ocean.seal.protocol.GameChannelServiceProto.RequestGetAllGameChannels.Builder.class);
    }

    public static com.google.protobuf.Parser<RequestGetAllGameChannels> PARSER =
        new com.google.protobuf.AbstractParser<RequestGetAllGameChannels>() {
      public RequestGetAllGameChannels parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new RequestGetAllGameChannels(input, extensionRegistry);
      }
    };

    @java.lang.Override
    public com.google.protobuf.Parser<RequestGetAllGameChannels> getParserForType() {
      return PARSER;
    }

    private void initFields() {
    }
    private byte memoizedIsInitialized = -1;
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized != -1) return isInitialized == 1;

      memoizedIsInitialized = 1;
      return true;
    }

    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      getSerializedSize();
      getUnknownFields().writeTo(output);
    }

    private int memoizedSerializedSize = -1;
    public int getSerializedSize() {
      int size = memoizedSerializedSize;
      if (size != -1) return size;

      size = 0;
      size += getUnknownFields().getSerializedSize();
      memoizedSerializedSize = size;
      return size;
    }

    private static final long serialVersionUID = 0L;
    @java.lang.Override
    protected java.lang.Object writeReplace()
        throws java.io.ObjectStreamException {
      return super.writeReplace();
    }

    public static fm.lizhi.ocean.seal.protocol.GameChannelServiceProto.RequestGetAllGameChannels parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static fm.lizhi.ocean.seal.protocol.GameChannelServiceProto.RequestGetAllGameChannels parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static fm.lizhi.ocean.seal.protocol.GameChannelServiceProto.RequestGetAllGameChannels parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static fm.lizhi.ocean.seal.protocol.GameChannelServiceProto.RequestGetAllGameChannels parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static fm.lizhi.ocean.seal.protocol.GameChannelServiceProto.RequestGetAllGameChannels parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return PARSER.parseFrom(input);
    }
    public static fm.lizhi.ocean.seal.protocol.GameChannelServiceProto.RequestGetAllGameChannels parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseFrom(input, extensionRegistry);
    }
    public static fm.lizhi.ocean.seal.protocol.GameChannelServiceProto.RequestGetAllGameChannels parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return PARSER.parseDelimitedFrom(input);
    }
    public static fm.lizhi.ocean.seal.protocol.GameChannelServiceProto.RequestGetAllGameChannels parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseDelimitedFrom(input, extensionRegistry);
    }
    public static fm.lizhi.ocean.seal.protocol.GameChannelServiceProto.RequestGetAllGameChannels parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return PARSER.parseFrom(input);
    }
    public static fm.lizhi.ocean.seal.protocol.GameChannelServiceProto.RequestGetAllGameChannels parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseFrom(input, extensionRegistry);
    }

    public static Builder newBuilder() { return Builder.create(); }
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder(fm.lizhi.ocean.seal.protocol.GameChannelServiceProto.RequestGetAllGameChannels prototype) {
      return newBuilder().mergeFrom(prototype);
    }
    public Builder toBuilder() { return newBuilder(this); }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code fm.lizhi.commons.template.datacenter.protocol.RequestGetAllGameChannels}
     *
     * <pre>
     * GameChannelService.java
     * 获取游戏渠道列表
     * domain = 4302, op = 220
     * </pre>
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessage.Builder<Builder>
       implements fm.lizhi.ocean.seal.protocol.GameChannelServiceProto.RequestGetAllGameChannelsOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return fm.lizhi.ocean.seal.protocol.GameChannelServiceProto.internal_static_fm_lizhi_commons_template_datacenter_protocol_RequestGetAllGameChannels_descriptor;
      }

      protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return fm.lizhi.ocean.seal.protocol.GameChannelServiceProto.internal_static_fm_lizhi_commons_template_datacenter_protocol_RequestGetAllGameChannels_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                fm.lizhi.ocean.seal.protocol.GameChannelServiceProto.RequestGetAllGameChannels.class, fm.lizhi.ocean.seal.protocol.GameChannelServiceProto.RequestGetAllGameChannels.Builder.class);
      }

      // Construct using fm.lizhi.ocean.seal.protocol.GameChannelServiceProto.RequestGetAllGameChannels.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessage.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessage.alwaysUseFieldBuilders) {
        }
      }
      private static Builder create() {
        return new Builder();
      }

      public Builder clear() {
        super.clear();
        return this;
      }

      public Builder clone() {
        return create().mergeFrom(buildPartial());
      }

      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return fm.lizhi.ocean.seal.protocol.GameChannelServiceProto.internal_static_fm_lizhi_commons_template_datacenter_protocol_RequestGetAllGameChannels_descriptor;
      }

      public fm.lizhi.ocean.seal.protocol.GameChannelServiceProto.RequestGetAllGameChannels getDefaultInstanceForType() {
        return fm.lizhi.ocean.seal.protocol.GameChannelServiceProto.RequestGetAllGameChannels.getDefaultInstance();
      }

      public fm.lizhi.ocean.seal.protocol.GameChannelServiceProto.RequestGetAllGameChannels build() {
        fm.lizhi.ocean.seal.protocol.GameChannelServiceProto.RequestGetAllGameChannels result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      public fm.lizhi.ocean.seal.protocol.GameChannelServiceProto.RequestGetAllGameChannels buildPartial() {
        fm.lizhi.ocean.seal.protocol.GameChannelServiceProto.RequestGetAllGameChannels result = new fm.lizhi.ocean.seal.protocol.GameChannelServiceProto.RequestGetAllGameChannels(this);
        onBuilt();
        return result;
      }

      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof fm.lizhi.ocean.seal.protocol.GameChannelServiceProto.RequestGetAllGameChannels) {
          return mergeFrom((fm.lizhi.ocean.seal.protocol.GameChannelServiceProto.RequestGetAllGameChannels)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(fm.lizhi.ocean.seal.protocol.GameChannelServiceProto.RequestGetAllGameChannels other) {
        if (other == fm.lizhi.ocean.seal.protocol.GameChannelServiceProto.RequestGetAllGameChannels.getDefaultInstance()) return this;
        this.mergeUnknownFields(other.getUnknownFields());
        return this;
      }

      public final boolean isInitialized() {
        return true;
      }

      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        fm.lizhi.ocean.seal.protocol.GameChannelServiceProto.RequestGetAllGameChannels parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (fm.lizhi.ocean.seal.protocol.GameChannelServiceProto.RequestGetAllGameChannels) e.getUnfinishedMessage();
          throw e;
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }

      // @@protoc_insertion_point(builder_scope:fm.lizhi.commons.template.datacenter.protocol.RequestGetAllGameChannels)
    }

    static {
      defaultInstance = new RequestGetAllGameChannels(true);
      defaultInstance.initFields();
    }

    // @@protoc_insertion_point(class_scope:fm.lizhi.commons.template.datacenter.protocol.RequestGetAllGameChannels)
  }

  public interface ResponseGetAllGameChannelsOrBuilder
      extends com.google.protobuf.MessageOrBuilder {

    // repeated .fm.lizhi.commons.template.datacenter.protocol.GameChannel gameChannels = 1;
    /**
     * <code>repeated .fm.lizhi.commons.template.datacenter.protocol.GameChannel gameChannels = 1;</code>
     */
    java.util.List<fm.lizhi.ocean.seal.protocol.GameChannelServiceProto.GameChannel> 
        getGameChannelsList();
    /**
     * <code>repeated .fm.lizhi.commons.template.datacenter.protocol.GameChannel gameChannels = 1;</code>
     */
    fm.lizhi.ocean.seal.protocol.GameChannelServiceProto.GameChannel getGameChannels(int index);
    /**
     * <code>repeated .fm.lizhi.commons.template.datacenter.protocol.GameChannel gameChannels = 1;</code>
     */
    int getGameChannelsCount();
    /**
     * <code>repeated .fm.lizhi.commons.template.datacenter.protocol.GameChannel gameChannels = 1;</code>
     */
    java.util.List<? extends fm.lizhi.ocean.seal.protocol.GameChannelServiceProto.GameChannelOrBuilder> 
        getGameChannelsOrBuilderList();
    /**
     * <code>repeated .fm.lizhi.commons.template.datacenter.protocol.GameChannel gameChannels = 1;</code>
     */
    fm.lizhi.ocean.seal.protocol.GameChannelServiceProto.GameChannelOrBuilder getGameChannelsOrBuilder(
        int index);
  }
  /**
   * Protobuf type {@code fm.lizhi.commons.template.datacenter.protocol.ResponseGetAllGameChannels}
   *
   * <pre>
   * rcode == 0(SUCCESS) = 执行成功
   * rcode == 1(FAIL) = 错误
   * </pre>
   */
  public static final class ResponseGetAllGameChannels extends
      com.google.protobuf.GeneratedMessage
      implements ResponseGetAllGameChannelsOrBuilder {
    // Use ResponseGetAllGameChannels.newBuilder() to construct.
    private ResponseGetAllGameChannels(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
      super(builder);
      this.unknownFields = builder.getUnknownFields();
    }
    private ResponseGetAllGameChannels(boolean noInit) { this.unknownFields = com.google.protobuf.UnknownFieldSet.getDefaultInstance(); }

    private static final ResponseGetAllGameChannels defaultInstance;
    public static ResponseGetAllGameChannels getDefaultInstance() {
      return defaultInstance;
    }

    public ResponseGetAllGameChannels getDefaultInstanceForType() {
      return defaultInstance;
    }

    private final com.google.protobuf.UnknownFieldSet unknownFields;
    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
        getUnknownFields() {
      return this.unknownFields;
    }
    private ResponseGetAllGameChannels(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      initFields();
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            default: {
              if (!parseUnknownField(input, unknownFields,
                                     extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
            case 10: {
              if (!((mutable_bitField0_ & 0x00000001) == 0x00000001)) {
                gameChannels_ = new java.util.ArrayList<fm.lizhi.ocean.seal.protocol.GameChannelServiceProto.GameChannel>();
                mutable_bitField0_ |= 0x00000001;
              }
              gameChannels_.add(input.readMessage(fm.lizhi.ocean.seal.protocol.GameChannelServiceProto.GameChannel.PARSER, extensionRegistry));
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e.getMessage()).setUnfinishedMessage(this);
      } finally {
        if (((mutable_bitField0_ & 0x00000001) == 0x00000001)) {
          gameChannels_ = java.util.Collections.unmodifiableList(gameChannels_);
        }
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return fm.lizhi.ocean.seal.protocol.GameChannelServiceProto.internal_static_fm_lizhi_commons_template_datacenter_protocol_ResponseGetAllGameChannels_descriptor;
    }

    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return fm.lizhi.ocean.seal.protocol.GameChannelServiceProto.internal_static_fm_lizhi_commons_template_datacenter_protocol_ResponseGetAllGameChannels_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              fm.lizhi.ocean.seal.protocol.GameChannelServiceProto.ResponseGetAllGameChannels.class, fm.lizhi.ocean.seal.protocol.GameChannelServiceProto.ResponseGetAllGameChannels.Builder.class);
    }

    public static com.google.protobuf.Parser<ResponseGetAllGameChannels> PARSER =
        new com.google.protobuf.AbstractParser<ResponseGetAllGameChannels>() {
      public ResponseGetAllGameChannels parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new ResponseGetAllGameChannels(input, extensionRegistry);
      }
    };

    @java.lang.Override
    public com.google.protobuf.Parser<ResponseGetAllGameChannels> getParserForType() {
      return PARSER;
    }

    // repeated .fm.lizhi.commons.template.datacenter.protocol.GameChannel gameChannels = 1;
    public static final int GAMECHANNELS_FIELD_NUMBER = 1;
    private java.util.List<fm.lizhi.ocean.seal.protocol.GameChannelServiceProto.GameChannel> gameChannels_;
    /**
     * <code>repeated .fm.lizhi.commons.template.datacenter.protocol.GameChannel gameChannels = 1;</code>
     */
    public java.util.List<fm.lizhi.ocean.seal.protocol.GameChannelServiceProto.GameChannel> getGameChannelsList() {
      return gameChannels_;
    }
    /**
     * <code>repeated .fm.lizhi.commons.template.datacenter.protocol.GameChannel gameChannels = 1;</code>
     */
    public java.util.List<? extends fm.lizhi.ocean.seal.protocol.GameChannelServiceProto.GameChannelOrBuilder> 
        getGameChannelsOrBuilderList() {
      return gameChannels_;
    }
    /**
     * <code>repeated .fm.lizhi.commons.template.datacenter.protocol.GameChannel gameChannels = 1;</code>
     */
    public int getGameChannelsCount() {
      return gameChannels_.size();
    }
    /**
     * <code>repeated .fm.lizhi.commons.template.datacenter.protocol.GameChannel gameChannels = 1;</code>
     */
    public fm.lizhi.ocean.seal.protocol.GameChannelServiceProto.GameChannel getGameChannels(int index) {
      return gameChannels_.get(index);
    }
    /**
     * <code>repeated .fm.lizhi.commons.template.datacenter.protocol.GameChannel gameChannels = 1;</code>
     */
    public fm.lizhi.ocean.seal.protocol.GameChannelServiceProto.GameChannelOrBuilder getGameChannelsOrBuilder(
        int index) {
      return gameChannels_.get(index);
    }

    private void initFields() {
      gameChannels_ = java.util.Collections.emptyList();
    }
    private byte memoizedIsInitialized = -1;
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized != -1) return isInitialized == 1;

      memoizedIsInitialized = 1;
      return true;
    }

    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      getSerializedSize();
      for (int i = 0; i < gameChannels_.size(); i++) {
        output.writeMessage(1, gameChannels_.get(i));
      }
      getUnknownFields().writeTo(output);
    }

    private int memoizedSerializedSize = -1;
    public int getSerializedSize() {
      int size = memoizedSerializedSize;
      if (size != -1) return size;

      size = 0;
      for (int i = 0; i < gameChannels_.size(); i++) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(1, gameChannels_.get(i));
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSerializedSize = size;
      return size;
    }

    private static final long serialVersionUID = 0L;
    @java.lang.Override
    protected java.lang.Object writeReplace()
        throws java.io.ObjectStreamException {
      return super.writeReplace();
    }

    public static fm.lizhi.ocean.seal.protocol.GameChannelServiceProto.ResponseGetAllGameChannels parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static fm.lizhi.ocean.seal.protocol.GameChannelServiceProto.ResponseGetAllGameChannels parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static fm.lizhi.ocean.seal.protocol.GameChannelServiceProto.ResponseGetAllGameChannels parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static fm.lizhi.ocean.seal.protocol.GameChannelServiceProto.ResponseGetAllGameChannels parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static fm.lizhi.ocean.seal.protocol.GameChannelServiceProto.ResponseGetAllGameChannels parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return PARSER.parseFrom(input);
    }
    public static fm.lizhi.ocean.seal.protocol.GameChannelServiceProto.ResponseGetAllGameChannels parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseFrom(input, extensionRegistry);
    }
    public static fm.lizhi.ocean.seal.protocol.GameChannelServiceProto.ResponseGetAllGameChannels parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return PARSER.parseDelimitedFrom(input);
    }
    public static fm.lizhi.ocean.seal.protocol.GameChannelServiceProto.ResponseGetAllGameChannels parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseDelimitedFrom(input, extensionRegistry);
    }
    public static fm.lizhi.ocean.seal.protocol.GameChannelServiceProto.ResponseGetAllGameChannels parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return PARSER.parseFrom(input);
    }
    public static fm.lizhi.ocean.seal.protocol.GameChannelServiceProto.ResponseGetAllGameChannels parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseFrom(input, extensionRegistry);
    }

    public static Builder newBuilder() { return Builder.create(); }
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder(fm.lizhi.ocean.seal.protocol.GameChannelServiceProto.ResponseGetAllGameChannels prototype) {
      return newBuilder().mergeFrom(prototype);
    }
    public Builder toBuilder() { return newBuilder(this); }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code fm.lizhi.commons.template.datacenter.protocol.ResponseGetAllGameChannels}
     *
     * <pre>
     * rcode == 0(SUCCESS) = 执行成功
     * rcode == 1(FAIL) = 错误
     * </pre>
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessage.Builder<Builder>
       implements fm.lizhi.ocean.seal.protocol.GameChannelServiceProto.ResponseGetAllGameChannelsOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return fm.lizhi.ocean.seal.protocol.GameChannelServiceProto.internal_static_fm_lizhi_commons_template_datacenter_protocol_ResponseGetAllGameChannels_descriptor;
      }

      protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return fm.lizhi.ocean.seal.protocol.GameChannelServiceProto.internal_static_fm_lizhi_commons_template_datacenter_protocol_ResponseGetAllGameChannels_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                fm.lizhi.ocean.seal.protocol.GameChannelServiceProto.ResponseGetAllGameChannels.class, fm.lizhi.ocean.seal.protocol.GameChannelServiceProto.ResponseGetAllGameChannels.Builder.class);
      }

      // Construct using fm.lizhi.ocean.seal.protocol.GameChannelServiceProto.ResponseGetAllGameChannels.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessage.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessage.alwaysUseFieldBuilders) {
          getGameChannelsFieldBuilder();
        }
      }
      private static Builder create() {
        return new Builder();
      }

      public Builder clear() {
        super.clear();
        if (gameChannelsBuilder_ == null) {
          gameChannels_ = java.util.Collections.emptyList();
          bitField0_ = (bitField0_ & ~0x00000001);
        } else {
          gameChannelsBuilder_.clear();
        }
        return this;
      }

      public Builder clone() {
        return create().mergeFrom(buildPartial());
      }

      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return fm.lizhi.ocean.seal.protocol.GameChannelServiceProto.internal_static_fm_lizhi_commons_template_datacenter_protocol_ResponseGetAllGameChannels_descriptor;
      }

      public fm.lizhi.ocean.seal.protocol.GameChannelServiceProto.ResponseGetAllGameChannels getDefaultInstanceForType() {
        return fm.lizhi.ocean.seal.protocol.GameChannelServiceProto.ResponseGetAllGameChannels.getDefaultInstance();
      }

      public fm.lizhi.ocean.seal.protocol.GameChannelServiceProto.ResponseGetAllGameChannels build() {
        fm.lizhi.ocean.seal.protocol.GameChannelServiceProto.ResponseGetAllGameChannels result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      public fm.lizhi.ocean.seal.protocol.GameChannelServiceProto.ResponseGetAllGameChannels buildPartial() {
        fm.lizhi.ocean.seal.protocol.GameChannelServiceProto.ResponseGetAllGameChannels result = new fm.lizhi.ocean.seal.protocol.GameChannelServiceProto.ResponseGetAllGameChannels(this);
        int from_bitField0_ = bitField0_;
        if (gameChannelsBuilder_ == null) {
          if (((bitField0_ & 0x00000001) == 0x00000001)) {
            gameChannels_ = java.util.Collections.unmodifiableList(gameChannels_);
            bitField0_ = (bitField0_ & ~0x00000001);
          }
          result.gameChannels_ = gameChannels_;
        } else {
          result.gameChannels_ = gameChannelsBuilder_.build();
        }
        onBuilt();
        return result;
      }

      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof fm.lizhi.ocean.seal.protocol.GameChannelServiceProto.ResponseGetAllGameChannels) {
          return mergeFrom((fm.lizhi.ocean.seal.protocol.GameChannelServiceProto.ResponseGetAllGameChannels)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(fm.lizhi.ocean.seal.protocol.GameChannelServiceProto.ResponseGetAllGameChannels other) {
        if (other == fm.lizhi.ocean.seal.protocol.GameChannelServiceProto.ResponseGetAllGameChannels.getDefaultInstance()) return this;
        if (gameChannelsBuilder_ == null) {
          if (!other.gameChannels_.isEmpty()) {
            if (gameChannels_.isEmpty()) {
              gameChannels_ = other.gameChannels_;
              bitField0_ = (bitField0_ & ~0x00000001);
            } else {
              ensureGameChannelsIsMutable();
              gameChannels_.addAll(other.gameChannels_);
            }
            onChanged();
          }
        } else {
          if (!other.gameChannels_.isEmpty()) {
            if (gameChannelsBuilder_.isEmpty()) {
              gameChannelsBuilder_.dispose();
              gameChannelsBuilder_ = null;
              gameChannels_ = other.gameChannels_;
              bitField0_ = (bitField0_ & ~0x00000001);
              gameChannelsBuilder_ = 
                com.google.protobuf.GeneratedMessage.alwaysUseFieldBuilders ?
                   getGameChannelsFieldBuilder() : null;
            } else {
              gameChannelsBuilder_.addAllMessages(other.gameChannels_);
            }
          }
        }
        this.mergeUnknownFields(other.getUnknownFields());
        return this;
      }

      public final boolean isInitialized() {
        return true;
      }

      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        fm.lizhi.ocean.seal.protocol.GameChannelServiceProto.ResponseGetAllGameChannels parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (fm.lizhi.ocean.seal.protocol.GameChannelServiceProto.ResponseGetAllGameChannels) e.getUnfinishedMessage();
          throw e;
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      // repeated .fm.lizhi.commons.template.datacenter.protocol.GameChannel gameChannels = 1;
      private java.util.List<fm.lizhi.ocean.seal.protocol.GameChannelServiceProto.GameChannel> gameChannels_ =
        java.util.Collections.emptyList();
      private void ensureGameChannelsIsMutable() {
        if (!((bitField0_ & 0x00000001) == 0x00000001)) {
          gameChannels_ = new java.util.ArrayList<fm.lizhi.ocean.seal.protocol.GameChannelServiceProto.GameChannel>(gameChannels_);
          bitField0_ |= 0x00000001;
         }
      }

      private com.google.protobuf.RepeatedFieldBuilder<
          fm.lizhi.ocean.seal.protocol.GameChannelServiceProto.GameChannel, fm.lizhi.ocean.seal.protocol.GameChannelServiceProto.GameChannel.Builder, fm.lizhi.ocean.seal.protocol.GameChannelServiceProto.GameChannelOrBuilder> gameChannelsBuilder_;

      /**
       * <code>repeated .fm.lizhi.commons.template.datacenter.protocol.GameChannel gameChannels = 1;</code>
       */
      public java.util.List<fm.lizhi.ocean.seal.protocol.GameChannelServiceProto.GameChannel> getGameChannelsList() {
        if (gameChannelsBuilder_ == null) {
          return java.util.Collections.unmodifiableList(gameChannels_);
        } else {
          return gameChannelsBuilder_.getMessageList();
        }
      }
      /**
       * <code>repeated .fm.lizhi.commons.template.datacenter.protocol.GameChannel gameChannels = 1;</code>
       */
      public int getGameChannelsCount() {
        if (gameChannelsBuilder_ == null) {
          return gameChannels_.size();
        } else {
          return gameChannelsBuilder_.getCount();
        }
      }
      /**
       * <code>repeated .fm.lizhi.commons.template.datacenter.protocol.GameChannel gameChannels = 1;</code>
       */
      public fm.lizhi.ocean.seal.protocol.GameChannelServiceProto.GameChannel getGameChannels(int index) {
        if (gameChannelsBuilder_ == null) {
          return gameChannels_.get(index);
        } else {
          return gameChannelsBuilder_.getMessage(index);
        }
      }
      /**
       * <code>repeated .fm.lizhi.commons.template.datacenter.protocol.GameChannel gameChannels = 1;</code>
       */
      public Builder setGameChannels(
          int index, fm.lizhi.ocean.seal.protocol.GameChannelServiceProto.GameChannel value) {
        if (gameChannelsBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureGameChannelsIsMutable();
          gameChannels_.set(index, value);
          onChanged();
        } else {
          gameChannelsBuilder_.setMessage(index, value);
        }
        return this;
      }
      /**
       * <code>repeated .fm.lizhi.commons.template.datacenter.protocol.GameChannel gameChannels = 1;</code>
       */
      public Builder setGameChannels(
          int index, fm.lizhi.ocean.seal.protocol.GameChannelServiceProto.GameChannel.Builder builderForValue) {
        if (gameChannelsBuilder_ == null) {
          ensureGameChannelsIsMutable();
          gameChannels_.set(index, builderForValue.build());
          onChanged();
        } else {
          gameChannelsBuilder_.setMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .fm.lizhi.commons.template.datacenter.protocol.GameChannel gameChannels = 1;</code>
       */
      public Builder addGameChannels(fm.lizhi.ocean.seal.protocol.GameChannelServiceProto.GameChannel value) {
        if (gameChannelsBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureGameChannelsIsMutable();
          gameChannels_.add(value);
          onChanged();
        } else {
          gameChannelsBuilder_.addMessage(value);
        }
        return this;
      }
      /**
       * <code>repeated .fm.lizhi.commons.template.datacenter.protocol.GameChannel gameChannels = 1;</code>
       */
      public Builder addGameChannels(
          int index, fm.lizhi.ocean.seal.protocol.GameChannelServiceProto.GameChannel value) {
        if (gameChannelsBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureGameChannelsIsMutable();
          gameChannels_.add(index, value);
          onChanged();
        } else {
          gameChannelsBuilder_.addMessage(index, value);
        }
        return this;
      }
      /**
       * <code>repeated .fm.lizhi.commons.template.datacenter.protocol.GameChannel gameChannels = 1;</code>
       */
      public Builder addGameChannels(
          fm.lizhi.ocean.seal.protocol.GameChannelServiceProto.GameChannel.Builder builderForValue) {
        if (gameChannelsBuilder_ == null) {
          ensureGameChannelsIsMutable();
          gameChannels_.add(builderForValue.build());
          onChanged();
        } else {
          gameChannelsBuilder_.addMessage(builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .fm.lizhi.commons.template.datacenter.protocol.GameChannel gameChannels = 1;</code>
       */
      public Builder addGameChannels(
          int index, fm.lizhi.ocean.seal.protocol.GameChannelServiceProto.GameChannel.Builder builderForValue) {
        if (gameChannelsBuilder_ == null) {
          ensureGameChannelsIsMutable();
          gameChannels_.add(index, builderForValue.build());
          onChanged();
        } else {
          gameChannelsBuilder_.addMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .fm.lizhi.commons.template.datacenter.protocol.GameChannel gameChannels = 1;</code>
       */
      public Builder addAllGameChannels(
          java.lang.Iterable<? extends fm.lizhi.ocean.seal.protocol.GameChannelServiceProto.GameChannel> values) {
        if (gameChannelsBuilder_ == null) {
          ensureGameChannelsIsMutable();
          super.addAll(values, gameChannels_);
          onChanged();
        } else {
          gameChannelsBuilder_.addAllMessages(values);
        }
        return this;
      }
      /**
       * <code>repeated .fm.lizhi.commons.template.datacenter.protocol.GameChannel gameChannels = 1;</code>
       */
      public Builder clearGameChannels() {
        if (gameChannelsBuilder_ == null) {
          gameChannels_ = java.util.Collections.emptyList();
          bitField0_ = (bitField0_ & ~0x00000001);
          onChanged();
        } else {
          gameChannelsBuilder_.clear();
        }
        return this;
      }
      /**
       * <code>repeated .fm.lizhi.commons.template.datacenter.protocol.GameChannel gameChannels = 1;</code>
       */
      public Builder removeGameChannels(int index) {
        if (gameChannelsBuilder_ == null) {
          ensureGameChannelsIsMutable();
          gameChannels_.remove(index);
          onChanged();
        } else {
          gameChannelsBuilder_.remove(index);
        }
        return this;
      }
      /**
       * <code>repeated .fm.lizhi.commons.template.datacenter.protocol.GameChannel gameChannels = 1;</code>
       */
      public fm.lizhi.ocean.seal.protocol.GameChannelServiceProto.GameChannel.Builder getGameChannelsBuilder(
          int index) {
        return getGameChannelsFieldBuilder().getBuilder(index);
      }
      /**
       * <code>repeated .fm.lizhi.commons.template.datacenter.protocol.GameChannel gameChannels = 1;</code>
       */
      public fm.lizhi.ocean.seal.protocol.GameChannelServiceProto.GameChannelOrBuilder getGameChannelsOrBuilder(
          int index) {
        if (gameChannelsBuilder_ == null) {
          return gameChannels_.get(index);  } else {
          return gameChannelsBuilder_.getMessageOrBuilder(index);
        }
      }
      /**
       * <code>repeated .fm.lizhi.commons.template.datacenter.protocol.GameChannel gameChannels = 1;</code>
       */
      public java.util.List<? extends fm.lizhi.ocean.seal.protocol.GameChannelServiceProto.GameChannelOrBuilder> 
           getGameChannelsOrBuilderList() {
        if (gameChannelsBuilder_ != null) {
          return gameChannelsBuilder_.getMessageOrBuilderList();
        } else {
          return java.util.Collections.unmodifiableList(gameChannels_);
        }
      }
      /**
       * <code>repeated .fm.lizhi.commons.template.datacenter.protocol.GameChannel gameChannels = 1;</code>
       */
      public fm.lizhi.ocean.seal.protocol.GameChannelServiceProto.GameChannel.Builder addGameChannelsBuilder() {
        return getGameChannelsFieldBuilder().addBuilder(
            fm.lizhi.ocean.seal.protocol.GameChannelServiceProto.GameChannel.getDefaultInstance());
      }
      /**
       * <code>repeated .fm.lizhi.commons.template.datacenter.protocol.GameChannel gameChannels = 1;</code>
       */
      public fm.lizhi.ocean.seal.protocol.GameChannelServiceProto.GameChannel.Builder addGameChannelsBuilder(
          int index) {
        return getGameChannelsFieldBuilder().addBuilder(
            index, fm.lizhi.ocean.seal.protocol.GameChannelServiceProto.GameChannel.getDefaultInstance());
      }
      /**
       * <code>repeated .fm.lizhi.commons.template.datacenter.protocol.GameChannel gameChannels = 1;</code>
       */
      public java.util.List<fm.lizhi.ocean.seal.protocol.GameChannelServiceProto.GameChannel.Builder> 
           getGameChannelsBuilderList() {
        return getGameChannelsFieldBuilder().getBuilderList();
      }
      private com.google.protobuf.RepeatedFieldBuilder<
          fm.lizhi.ocean.seal.protocol.GameChannelServiceProto.GameChannel, fm.lizhi.ocean.seal.protocol.GameChannelServiceProto.GameChannel.Builder, fm.lizhi.ocean.seal.protocol.GameChannelServiceProto.GameChannelOrBuilder> 
          getGameChannelsFieldBuilder() {
        if (gameChannelsBuilder_ == null) {
          gameChannelsBuilder_ = new com.google.protobuf.RepeatedFieldBuilder<
              fm.lizhi.ocean.seal.protocol.GameChannelServiceProto.GameChannel, fm.lizhi.ocean.seal.protocol.GameChannelServiceProto.GameChannel.Builder, fm.lizhi.ocean.seal.protocol.GameChannelServiceProto.GameChannelOrBuilder>(
                  gameChannels_,
                  ((bitField0_ & 0x00000001) == 0x00000001),
                  getParentForChildren(),
                  isClean());
          gameChannels_ = null;
        }
        return gameChannelsBuilder_;
      }

      // @@protoc_insertion_point(builder_scope:fm.lizhi.commons.template.datacenter.protocol.ResponseGetAllGameChannels)
    }

    static {
      defaultInstance = new ResponseGetAllGameChannels(true);
      defaultInstance.initFields();
    }

    // @@protoc_insertion_point(class_scope:fm.lizhi.commons.template.datacenter.protocol.ResponseGetAllGameChannels)
  }

  public interface RequestGetEnumGameChannelsOrBuilder
      extends com.google.protobuf.MessageOrBuilder {
  }
  /**
   * Protobuf type {@code fm.lizhi.commons.template.datacenter.protocol.RequestGetEnumGameChannels}
   *
   * <pre>
   * GameChannelService.java
   * 获取游戏渠道枚举列表
   * domain = 4302, op = 221
   * </pre>
   */
  public static final class RequestGetEnumGameChannels extends
      com.google.protobuf.GeneratedMessage
      implements RequestGetEnumGameChannelsOrBuilder {
    // Use RequestGetEnumGameChannels.newBuilder() to construct.
    private RequestGetEnumGameChannels(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
      super(builder);
      this.unknownFields = builder.getUnknownFields();
    }
    private RequestGetEnumGameChannels(boolean noInit) { this.unknownFields = com.google.protobuf.UnknownFieldSet.getDefaultInstance(); }

    private static final RequestGetEnumGameChannels defaultInstance;
    public static RequestGetEnumGameChannels getDefaultInstance() {
      return defaultInstance;
    }

    public RequestGetEnumGameChannels getDefaultInstanceForType() {
      return defaultInstance;
    }

    private final com.google.protobuf.UnknownFieldSet unknownFields;
    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
        getUnknownFields() {
      return this.unknownFields;
    }
    private RequestGetEnumGameChannels(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      initFields();
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            default: {
              if (!parseUnknownField(input, unknownFields,
                                     extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e.getMessage()).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return fm.lizhi.ocean.seal.protocol.GameChannelServiceProto.internal_static_fm_lizhi_commons_template_datacenter_protocol_RequestGetEnumGameChannels_descriptor;
    }

    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return fm.lizhi.ocean.seal.protocol.GameChannelServiceProto.internal_static_fm_lizhi_commons_template_datacenter_protocol_RequestGetEnumGameChannels_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              fm.lizhi.ocean.seal.protocol.GameChannelServiceProto.RequestGetEnumGameChannels.class, fm.lizhi.ocean.seal.protocol.GameChannelServiceProto.RequestGetEnumGameChannels.Builder.class);
    }

    public static com.google.protobuf.Parser<RequestGetEnumGameChannels> PARSER =
        new com.google.protobuf.AbstractParser<RequestGetEnumGameChannels>() {
      public RequestGetEnumGameChannels parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new RequestGetEnumGameChannels(input, extensionRegistry);
      }
    };

    @java.lang.Override
    public com.google.protobuf.Parser<RequestGetEnumGameChannels> getParserForType() {
      return PARSER;
    }

    private void initFields() {
    }
    private byte memoizedIsInitialized = -1;
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized != -1) return isInitialized == 1;

      memoizedIsInitialized = 1;
      return true;
    }

    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      getSerializedSize();
      getUnknownFields().writeTo(output);
    }

    private int memoizedSerializedSize = -1;
    public int getSerializedSize() {
      int size = memoizedSerializedSize;
      if (size != -1) return size;

      size = 0;
      size += getUnknownFields().getSerializedSize();
      memoizedSerializedSize = size;
      return size;
    }

    private static final long serialVersionUID = 0L;
    @java.lang.Override
    protected java.lang.Object writeReplace()
        throws java.io.ObjectStreamException {
      return super.writeReplace();
    }

    public static fm.lizhi.ocean.seal.protocol.GameChannelServiceProto.RequestGetEnumGameChannels parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static fm.lizhi.ocean.seal.protocol.GameChannelServiceProto.RequestGetEnumGameChannels parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static fm.lizhi.ocean.seal.protocol.GameChannelServiceProto.RequestGetEnumGameChannels parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static fm.lizhi.ocean.seal.protocol.GameChannelServiceProto.RequestGetEnumGameChannels parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static fm.lizhi.ocean.seal.protocol.GameChannelServiceProto.RequestGetEnumGameChannels parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return PARSER.parseFrom(input);
    }
    public static fm.lizhi.ocean.seal.protocol.GameChannelServiceProto.RequestGetEnumGameChannels parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseFrom(input, extensionRegistry);
    }
    public static fm.lizhi.ocean.seal.protocol.GameChannelServiceProto.RequestGetEnumGameChannels parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return PARSER.parseDelimitedFrom(input);
    }
    public static fm.lizhi.ocean.seal.protocol.GameChannelServiceProto.RequestGetEnumGameChannels parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseDelimitedFrom(input, extensionRegistry);
    }
    public static fm.lizhi.ocean.seal.protocol.GameChannelServiceProto.RequestGetEnumGameChannels parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return PARSER.parseFrom(input);
    }
    public static fm.lizhi.ocean.seal.protocol.GameChannelServiceProto.RequestGetEnumGameChannels parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseFrom(input, extensionRegistry);
    }

    public static Builder newBuilder() { return Builder.create(); }
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder(fm.lizhi.ocean.seal.protocol.GameChannelServiceProto.RequestGetEnumGameChannels prototype) {
      return newBuilder().mergeFrom(prototype);
    }
    public Builder toBuilder() { return newBuilder(this); }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code fm.lizhi.commons.template.datacenter.protocol.RequestGetEnumGameChannels}
     *
     * <pre>
     * GameChannelService.java
     * 获取游戏渠道枚举列表
     * domain = 4302, op = 221
     * </pre>
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessage.Builder<Builder>
       implements fm.lizhi.ocean.seal.protocol.GameChannelServiceProto.RequestGetEnumGameChannelsOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return fm.lizhi.ocean.seal.protocol.GameChannelServiceProto.internal_static_fm_lizhi_commons_template_datacenter_protocol_RequestGetEnumGameChannels_descriptor;
      }

      protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return fm.lizhi.ocean.seal.protocol.GameChannelServiceProto.internal_static_fm_lizhi_commons_template_datacenter_protocol_RequestGetEnumGameChannels_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                fm.lizhi.ocean.seal.protocol.GameChannelServiceProto.RequestGetEnumGameChannels.class, fm.lizhi.ocean.seal.protocol.GameChannelServiceProto.RequestGetEnumGameChannels.Builder.class);
      }

      // Construct using fm.lizhi.ocean.seal.protocol.GameChannelServiceProto.RequestGetEnumGameChannels.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessage.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessage.alwaysUseFieldBuilders) {
        }
      }
      private static Builder create() {
        return new Builder();
      }

      public Builder clear() {
        super.clear();
        return this;
      }

      public Builder clone() {
        return create().mergeFrom(buildPartial());
      }

      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return fm.lizhi.ocean.seal.protocol.GameChannelServiceProto.internal_static_fm_lizhi_commons_template_datacenter_protocol_RequestGetEnumGameChannels_descriptor;
      }

      public fm.lizhi.ocean.seal.protocol.GameChannelServiceProto.RequestGetEnumGameChannels getDefaultInstanceForType() {
        return fm.lizhi.ocean.seal.protocol.GameChannelServiceProto.RequestGetEnumGameChannels.getDefaultInstance();
      }

      public fm.lizhi.ocean.seal.protocol.GameChannelServiceProto.RequestGetEnumGameChannels build() {
        fm.lizhi.ocean.seal.protocol.GameChannelServiceProto.RequestGetEnumGameChannels result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      public fm.lizhi.ocean.seal.protocol.GameChannelServiceProto.RequestGetEnumGameChannels buildPartial() {
        fm.lizhi.ocean.seal.protocol.GameChannelServiceProto.RequestGetEnumGameChannels result = new fm.lizhi.ocean.seal.protocol.GameChannelServiceProto.RequestGetEnumGameChannels(this);
        onBuilt();
        return result;
      }

      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof fm.lizhi.ocean.seal.protocol.GameChannelServiceProto.RequestGetEnumGameChannels) {
          return mergeFrom((fm.lizhi.ocean.seal.protocol.GameChannelServiceProto.RequestGetEnumGameChannels)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(fm.lizhi.ocean.seal.protocol.GameChannelServiceProto.RequestGetEnumGameChannels other) {
        if (other == fm.lizhi.ocean.seal.protocol.GameChannelServiceProto.RequestGetEnumGameChannels.getDefaultInstance()) return this;
        this.mergeUnknownFields(other.getUnknownFields());
        return this;
      }

      public final boolean isInitialized() {
        return true;
      }

      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        fm.lizhi.ocean.seal.protocol.GameChannelServiceProto.RequestGetEnumGameChannels parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (fm.lizhi.ocean.seal.protocol.GameChannelServiceProto.RequestGetEnumGameChannels) e.getUnfinishedMessage();
          throw e;
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }

      // @@protoc_insertion_point(builder_scope:fm.lizhi.commons.template.datacenter.protocol.RequestGetEnumGameChannels)
    }

    static {
      defaultInstance = new RequestGetEnumGameChannels(true);
      defaultInstance.initFields();
    }

    // @@protoc_insertion_point(class_scope:fm.lizhi.commons.template.datacenter.protocol.RequestGetEnumGameChannels)
  }

  public interface ResponseGetEnumGameChannelsOrBuilder
      extends com.google.protobuf.MessageOrBuilder {

    // repeated .fm.lizhi.commons.template.datacenter.protocol.EnumGameChannel enumGameChannel = 1;
    /**
     * <code>repeated .fm.lizhi.commons.template.datacenter.protocol.EnumGameChannel enumGameChannel = 1;</code>
     */
    java.util.List<fm.lizhi.ocean.seal.protocol.GameChannelServiceProto.EnumGameChannel> 
        getEnumGameChannelList();
    /**
     * <code>repeated .fm.lizhi.commons.template.datacenter.protocol.EnumGameChannel enumGameChannel = 1;</code>
     */
    fm.lizhi.ocean.seal.protocol.GameChannelServiceProto.EnumGameChannel getEnumGameChannel(int index);
    /**
     * <code>repeated .fm.lizhi.commons.template.datacenter.protocol.EnumGameChannel enumGameChannel = 1;</code>
     */
    int getEnumGameChannelCount();
    /**
     * <code>repeated .fm.lizhi.commons.template.datacenter.protocol.EnumGameChannel enumGameChannel = 1;</code>
     */
    java.util.List<? extends fm.lizhi.ocean.seal.protocol.GameChannelServiceProto.EnumGameChannelOrBuilder> 
        getEnumGameChannelOrBuilderList();
    /**
     * <code>repeated .fm.lizhi.commons.template.datacenter.protocol.EnumGameChannel enumGameChannel = 1;</code>
     */
    fm.lizhi.ocean.seal.protocol.GameChannelServiceProto.EnumGameChannelOrBuilder getEnumGameChannelOrBuilder(
        int index);
  }
  /**
   * Protobuf type {@code fm.lizhi.commons.template.datacenter.protocol.ResponseGetEnumGameChannels}
   *
   * <pre>
   * rcode == 0(SUCCESS) = 执行成功
   * rcode == 1(FAIL) = 错误
   * </pre>
   */
  public static final class ResponseGetEnumGameChannels extends
      com.google.protobuf.GeneratedMessage
      implements ResponseGetEnumGameChannelsOrBuilder {
    // Use ResponseGetEnumGameChannels.newBuilder() to construct.
    private ResponseGetEnumGameChannels(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
      super(builder);
      this.unknownFields = builder.getUnknownFields();
    }
    private ResponseGetEnumGameChannels(boolean noInit) { this.unknownFields = com.google.protobuf.UnknownFieldSet.getDefaultInstance(); }

    private static final ResponseGetEnumGameChannels defaultInstance;
    public static ResponseGetEnumGameChannels getDefaultInstance() {
      return defaultInstance;
    }

    public ResponseGetEnumGameChannels getDefaultInstanceForType() {
      return defaultInstance;
    }

    private final com.google.protobuf.UnknownFieldSet unknownFields;
    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
        getUnknownFields() {
      return this.unknownFields;
    }
    private ResponseGetEnumGameChannels(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      initFields();
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            default: {
              if (!parseUnknownField(input, unknownFields,
                                     extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
            case 10: {
              if (!((mutable_bitField0_ & 0x00000001) == 0x00000001)) {
                enumGameChannel_ = new java.util.ArrayList<fm.lizhi.ocean.seal.protocol.GameChannelServiceProto.EnumGameChannel>();
                mutable_bitField0_ |= 0x00000001;
              }
              enumGameChannel_.add(input.readMessage(fm.lizhi.ocean.seal.protocol.GameChannelServiceProto.EnumGameChannel.PARSER, extensionRegistry));
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e.getMessage()).setUnfinishedMessage(this);
      } finally {
        if (((mutable_bitField0_ & 0x00000001) == 0x00000001)) {
          enumGameChannel_ = java.util.Collections.unmodifiableList(enumGameChannel_);
        }
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return fm.lizhi.ocean.seal.protocol.GameChannelServiceProto.internal_static_fm_lizhi_commons_template_datacenter_protocol_ResponseGetEnumGameChannels_descriptor;
    }

    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return fm.lizhi.ocean.seal.protocol.GameChannelServiceProto.internal_static_fm_lizhi_commons_template_datacenter_protocol_ResponseGetEnumGameChannels_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              fm.lizhi.ocean.seal.protocol.GameChannelServiceProto.ResponseGetEnumGameChannels.class, fm.lizhi.ocean.seal.protocol.GameChannelServiceProto.ResponseGetEnumGameChannels.Builder.class);
    }

    public static com.google.protobuf.Parser<ResponseGetEnumGameChannels> PARSER =
        new com.google.protobuf.AbstractParser<ResponseGetEnumGameChannels>() {
      public ResponseGetEnumGameChannels parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new ResponseGetEnumGameChannels(input, extensionRegistry);
      }
    };

    @java.lang.Override
    public com.google.protobuf.Parser<ResponseGetEnumGameChannels> getParserForType() {
      return PARSER;
    }

    // repeated .fm.lizhi.commons.template.datacenter.protocol.EnumGameChannel enumGameChannel = 1;
    public static final int ENUMGAMECHANNEL_FIELD_NUMBER = 1;
    private java.util.List<fm.lizhi.ocean.seal.protocol.GameChannelServiceProto.EnumGameChannel> enumGameChannel_;
    /**
     * <code>repeated .fm.lizhi.commons.template.datacenter.protocol.EnumGameChannel enumGameChannel = 1;</code>
     */
    public java.util.List<fm.lizhi.ocean.seal.protocol.GameChannelServiceProto.EnumGameChannel> getEnumGameChannelList() {
      return enumGameChannel_;
    }
    /**
     * <code>repeated .fm.lizhi.commons.template.datacenter.protocol.EnumGameChannel enumGameChannel = 1;</code>
     */
    public java.util.List<? extends fm.lizhi.ocean.seal.protocol.GameChannelServiceProto.EnumGameChannelOrBuilder> 
        getEnumGameChannelOrBuilderList() {
      return enumGameChannel_;
    }
    /**
     * <code>repeated .fm.lizhi.commons.template.datacenter.protocol.EnumGameChannel enumGameChannel = 1;</code>
     */
    public int getEnumGameChannelCount() {
      return enumGameChannel_.size();
    }
    /**
     * <code>repeated .fm.lizhi.commons.template.datacenter.protocol.EnumGameChannel enumGameChannel = 1;</code>
     */
    public fm.lizhi.ocean.seal.protocol.GameChannelServiceProto.EnumGameChannel getEnumGameChannel(int index) {
      return enumGameChannel_.get(index);
    }
    /**
     * <code>repeated .fm.lizhi.commons.template.datacenter.protocol.EnumGameChannel enumGameChannel = 1;</code>
     */
    public fm.lizhi.ocean.seal.protocol.GameChannelServiceProto.EnumGameChannelOrBuilder getEnumGameChannelOrBuilder(
        int index) {
      return enumGameChannel_.get(index);
    }

    private void initFields() {
      enumGameChannel_ = java.util.Collections.emptyList();
    }
    private byte memoizedIsInitialized = -1;
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized != -1) return isInitialized == 1;

      memoizedIsInitialized = 1;
      return true;
    }

    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      getSerializedSize();
      for (int i = 0; i < enumGameChannel_.size(); i++) {
        output.writeMessage(1, enumGameChannel_.get(i));
      }
      getUnknownFields().writeTo(output);
    }

    private int memoizedSerializedSize = -1;
    public int getSerializedSize() {
      int size = memoizedSerializedSize;
      if (size != -1) return size;

      size = 0;
      for (int i = 0; i < enumGameChannel_.size(); i++) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(1, enumGameChannel_.get(i));
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSerializedSize = size;
      return size;
    }

    private static final long serialVersionUID = 0L;
    @java.lang.Override
    protected java.lang.Object writeReplace()
        throws java.io.ObjectStreamException {
      return super.writeReplace();
    }

    public static fm.lizhi.ocean.seal.protocol.GameChannelServiceProto.ResponseGetEnumGameChannels parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static fm.lizhi.ocean.seal.protocol.GameChannelServiceProto.ResponseGetEnumGameChannels parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static fm.lizhi.ocean.seal.protocol.GameChannelServiceProto.ResponseGetEnumGameChannels parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static fm.lizhi.ocean.seal.protocol.GameChannelServiceProto.ResponseGetEnumGameChannels parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static fm.lizhi.ocean.seal.protocol.GameChannelServiceProto.ResponseGetEnumGameChannels parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return PARSER.parseFrom(input);
    }
    public static fm.lizhi.ocean.seal.protocol.GameChannelServiceProto.ResponseGetEnumGameChannels parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseFrom(input, extensionRegistry);
    }
    public static fm.lizhi.ocean.seal.protocol.GameChannelServiceProto.ResponseGetEnumGameChannels parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return PARSER.parseDelimitedFrom(input);
    }
    public static fm.lizhi.ocean.seal.protocol.GameChannelServiceProto.ResponseGetEnumGameChannels parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseDelimitedFrom(input, extensionRegistry);
    }
    public static fm.lizhi.ocean.seal.protocol.GameChannelServiceProto.ResponseGetEnumGameChannels parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return PARSER.parseFrom(input);
    }
    public static fm.lizhi.ocean.seal.protocol.GameChannelServiceProto.ResponseGetEnumGameChannels parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseFrom(input, extensionRegistry);
    }

    public static Builder newBuilder() { return Builder.create(); }
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder(fm.lizhi.ocean.seal.protocol.GameChannelServiceProto.ResponseGetEnumGameChannels prototype) {
      return newBuilder().mergeFrom(prototype);
    }
    public Builder toBuilder() { return newBuilder(this); }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code fm.lizhi.commons.template.datacenter.protocol.ResponseGetEnumGameChannels}
     *
     * <pre>
     * rcode == 0(SUCCESS) = 执行成功
     * rcode == 1(FAIL) = 错误
     * </pre>
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessage.Builder<Builder>
       implements fm.lizhi.ocean.seal.protocol.GameChannelServiceProto.ResponseGetEnumGameChannelsOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return fm.lizhi.ocean.seal.protocol.GameChannelServiceProto.internal_static_fm_lizhi_commons_template_datacenter_protocol_ResponseGetEnumGameChannels_descriptor;
      }

      protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return fm.lizhi.ocean.seal.protocol.GameChannelServiceProto.internal_static_fm_lizhi_commons_template_datacenter_protocol_ResponseGetEnumGameChannels_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                fm.lizhi.ocean.seal.protocol.GameChannelServiceProto.ResponseGetEnumGameChannels.class, fm.lizhi.ocean.seal.protocol.GameChannelServiceProto.ResponseGetEnumGameChannels.Builder.class);
      }

      // Construct using fm.lizhi.ocean.seal.protocol.GameChannelServiceProto.ResponseGetEnumGameChannels.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessage.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessage.alwaysUseFieldBuilders) {
          getEnumGameChannelFieldBuilder();
        }
      }
      private static Builder create() {
        return new Builder();
      }

      public Builder clear() {
        super.clear();
        if (enumGameChannelBuilder_ == null) {
          enumGameChannel_ = java.util.Collections.emptyList();
          bitField0_ = (bitField0_ & ~0x00000001);
        } else {
          enumGameChannelBuilder_.clear();
        }
        return this;
      }

      public Builder clone() {
        return create().mergeFrom(buildPartial());
      }

      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return fm.lizhi.ocean.seal.protocol.GameChannelServiceProto.internal_static_fm_lizhi_commons_template_datacenter_protocol_ResponseGetEnumGameChannels_descriptor;
      }

      public fm.lizhi.ocean.seal.protocol.GameChannelServiceProto.ResponseGetEnumGameChannels getDefaultInstanceForType() {
        return fm.lizhi.ocean.seal.protocol.GameChannelServiceProto.ResponseGetEnumGameChannels.getDefaultInstance();
      }

      public fm.lizhi.ocean.seal.protocol.GameChannelServiceProto.ResponseGetEnumGameChannels build() {
        fm.lizhi.ocean.seal.protocol.GameChannelServiceProto.ResponseGetEnumGameChannels result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      public fm.lizhi.ocean.seal.protocol.GameChannelServiceProto.ResponseGetEnumGameChannels buildPartial() {
        fm.lizhi.ocean.seal.protocol.GameChannelServiceProto.ResponseGetEnumGameChannels result = new fm.lizhi.ocean.seal.protocol.GameChannelServiceProto.ResponseGetEnumGameChannels(this);
        int from_bitField0_ = bitField0_;
        if (enumGameChannelBuilder_ == null) {
          if (((bitField0_ & 0x00000001) == 0x00000001)) {
            enumGameChannel_ = java.util.Collections.unmodifiableList(enumGameChannel_);
            bitField0_ = (bitField0_ & ~0x00000001);
          }
          result.enumGameChannel_ = enumGameChannel_;
        } else {
          result.enumGameChannel_ = enumGameChannelBuilder_.build();
        }
        onBuilt();
        return result;
      }

      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof fm.lizhi.ocean.seal.protocol.GameChannelServiceProto.ResponseGetEnumGameChannels) {
          return mergeFrom((fm.lizhi.ocean.seal.protocol.GameChannelServiceProto.ResponseGetEnumGameChannels)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(fm.lizhi.ocean.seal.protocol.GameChannelServiceProto.ResponseGetEnumGameChannels other) {
        if (other == fm.lizhi.ocean.seal.protocol.GameChannelServiceProto.ResponseGetEnumGameChannels.getDefaultInstance()) return this;
        if (enumGameChannelBuilder_ == null) {
          if (!other.enumGameChannel_.isEmpty()) {
            if (enumGameChannel_.isEmpty()) {
              enumGameChannel_ = other.enumGameChannel_;
              bitField0_ = (bitField0_ & ~0x00000001);
            } else {
              ensureEnumGameChannelIsMutable();
              enumGameChannel_.addAll(other.enumGameChannel_);
            }
            onChanged();
          }
        } else {
          if (!other.enumGameChannel_.isEmpty()) {
            if (enumGameChannelBuilder_.isEmpty()) {
              enumGameChannelBuilder_.dispose();
              enumGameChannelBuilder_ = null;
              enumGameChannel_ = other.enumGameChannel_;
              bitField0_ = (bitField0_ & ~0x00000001);
              enumGameChannelBuilder_ = 
                com.google.protobuf.GeneratedMessage.alwaysUseFieldBuilders ?
                   getEnumGameChannelFieldBuilder() : null;
            } else {
              enumGameChannelBuilder_.addAllMessages(other.enumGameChannel_);
            }
          }
        }
        this.mergeUnknownFields(other.getUnknownFields());
        return this;
      }

      public final boolean isInitialized() {
        return true;
      }

      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        fm.lizhi.ocean.seal.protocol.GameChannelServiceProto.ResponseGetEnumGameChannels parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (fm.lizhi.ocean.seal.protocol.GameChannelServiceProto.ResponseGetEnumGameChannels) e.getUnfinishedMessage();
          throw e;
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      // repeated .fm.lizhi.commons.template.datacenter.protocol.EnumGameChannel enumGameChannel = 1;
      private java.util.List<fm.lizhi.ocean.seal.protocol.GameChannelServiceProto.EnumGameChannel> enumGameChannel_ =
        java.util.Collections.emptyList();
      private void ensureEnumGameChannelIsMutable() {
        if (!((bitField0_ & 0x00000001) == 0x00000001)) {
          enumGameChannel_ = new java.util.ArrayList<fm.lizhi.ocean.seal.protocol.GameChannelServiceProto.EnumGameChannel>(enumGameChannel_);
          bitField0_ |= 0x00000001;
         }
      }

      private com.google.protobuf.RepeatedFieldBuilder<
          fm.lizhi.ocean.seal.protocol.GameChannelServiceProto.EnumGameChannel, fm.lizhi.ocean.seal.protocol.GameChannelServiceProto.EnumGameChannel.Builder, fm.lizhi.ocean.seal.protocol.GameChannelServiceProto.EnumGameChannelOrBuilder> enumGameChannelBuilder_;

      /**
       * <code>repeated .fm.lizhi.commons.template.datacenter.protocol.EnumGameChannel enumGameChannel = 1;</code>
       */
      public java.util.List<fm.lizhi.ocean.seal.protocol.GameChannelServiceProto.EnumGameChannel> getEnumGameChannelList() {
        if (enumGameChannelBuilder_ == null) {
          return java.util.Collections.unmodifiableList(enumGameChannel_);
        } else {
          return enumGameChannelBuilder_.getMessageList();
        }
      }
      /**
       * <code>repeated .fm.lizhi.commons.template.datacenter.protocol.EnumGameChannel enumGameChannel = 1;</code>
       */
      public int getEnumGameChannelCount() {
        if (enumGameChannelBuilder_ == null) {
          return enumGameChannel_.size();
        } else {
          return enumGameChannelBuilder_.getCount();
        }
      }
      /**
       * <code>repeated .fm.lizhi.commons.template.datacenter.protocol.EnumGameChannel enumGameChannel = 1;</code>
       */
      public fm.lizhi.ocean.seal.protocol.GameChannelServiceProto.EnumGameChannel getEnumGameChannel(int index) {
        if (enumGameChannelBuilder_ == null) {
          return enumGameChannel_.get(index);
        } else {
          return enumGameChannelBuilder_.getMessage(index);
        }
      }
      /**
       * <code>repeated .fm.lizhi.commons.template.datacenter.protocol.EnumGameChannel enumGameChannel = 1;</code>
       */
      public Builder setEnumGameChannel(
          int index, fm.lizhi.ocean.seal.protocol.GameChannelServiceProto.EnumGameChannel value) {
        if (enumGameChannelBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureEnumGameChannelIsMutable();
          enumGameChannel_.set(index, value);
          onChanged();
        } else {
          enumGameChannelBuilder_.setMessage(index, value);
        }
        return this;
      }
      /**
       * <code>repeated .fm.lizhi.commons.template.datacenter.protocol.EnumGameChannel enumGameChannel = 1;</code>
       */
      public Builder setEnumGameChannel(
          int index, fm.lizhi.ocean.seal.protocol.GameChannelServiceProto.EnumGameChannel.Builder builderForValue) {
        if (enumGameChannelBuilder_ == null) {
          ensureEnumGameChannelIsMutable();
          enumGameChannel_.set(index, builderForValue.build());
          onChanged();
        } else {
          enumGameChannelBuilder_.setMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .fm.lizhi.commons.template.datacenter.protocol.EnumGameChannel enumGameChannel = 1;</code>
       */
      public Builder addEnumGameChannel(fm.lizhi.ocean.seal.protocol.GameChannelServiceProto.EnumGameChannel value) {
        if (enumGameChannelBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureEnumGameChannelIsMutable();
          enumGameChannel_.add(value);
          onChanged();
        } else {
          enumGameChannelBuilder_.addMessage(value);
        }
        return this;
      }
      /**
       * <code>repeated .fm.lizhi.commons.template.datacenter.protocol.EnumGameChannel enumGameChannel = 1;</code>
       */
      public Builder addEnumGameChannel(
          int index, fm.lizhi.ocean.seal.protocol.GameChannelServiceProto.EnumGameChannel value) {
        if (enumGameChannelBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureEnumGameChannelIsMutable();
          enumGameChannel_.add(index, value);
          onChanged();
        } else {
          enumGameChannelBuilder_.addMessage(index, value);
        }
        return this;
      }
      /**
       * <code>repeated .fm.lizhi.commons.template.datacenter.protocol.EnumGameChannel enumGameChannel = 1;</code>
       */
      public Builder addEnumGameChannel(
          fm.lizhi.ocean.seal.protocol.GameChannelServiceProto.EnumGameChannel.Builder builderForValue) {
        if (enumGameChannelBuilder_ == null) {
          ensureEnumGameChannelIsMutable();
          enumGameChannel_.add(builderForValue.build());
          onChanged();
        } else {
          enumGameChannelBuilder_.addMessage(builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .fm.lizhi.commons.template.datacenter.protocol.EnumGameChannel enumGameChannel = 1;</code>
       */
      public Builder addEnumGameChannel(
          int index, fm.lizhi.ocean.seal.protocol.GameChannelServiceProto.EnumGameChannel.Builder builderForValue) {
        if (enumGameChannelBuilder_ == null) {
          ensureEnumGameChannelIsMutable();
          enumGameChannel_.add(index, builderForValue.build());
          onChanged();
        } else {
          enumGameChannelBuilder_.addMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .fm.lizhi.commons.template.datacenter.protocol.EnumGameChannel enumGameChannel = 1;</code>
       */
      public Builder addAllEnumGameChannel(
          java.lang.Iterable<? extends fm.lizhi.ocean.seal.protocol.GameChannelServiceProto.EnumGameChannel> values) {
        if (enumGameChannelBuilder_ == null) {
          ensureEnumGameChannelIsMutable();
          super.addAll(values, enumGameChannel_);
          onChanged();
        } else {
          enumGameChannelBuilder_.addAllMessages(values);
        }
        return this;
      }
      /**
       * <code>repeated .fm.lizhi.commons.template.datacenter.protocol.EnumGameChannel enumGameChannel = 1;</code>
       */
      public Builder clearEnumGameChannel() {
        if (enumGameChannelBuilder_ == null) {
          enumGameChannel_ = java.util.Collections.emptyList();
          bitField0_ = (bitField0_ & ~0x00000001);
          onChanged();
        } else {
          enumGameChannelBuilder_.clear();
        }
        return this;
      }
      /**
       * <code>repeated .fm.lizhi.commons.template.datacenter.protocol.EnumGameChannel enumGameChannel = 1;</code>
       */
      public Builder removeEnumGameChannel(int index) {
        if (enumGameChannelBuilder_ == null) {
          ensureEnumGameChannelIsMutable();
          enumGameChannel_.remove(index);
          onChanged();
        } else {
          enumGameChannelBuilder_.remove(index);
        }
        return this;
      }
      /**
       * <code>repeated .fm.lizhi.commons.template.datacenter.protocol.EnumGameChannel enumGameChannel = 1;</code>
       */
      public fm.lizhi.ocean.seal.protocol.GameChannelServiceProto.EnumGameChannel.Builder getEnumGameChannelBuilder(
          int index) {
        return getEnumGameChannelFieldBuilder().getBuilder(index);
      }
      /**
       * <code>repeated .fm.lizhi.commons.template.datacenter.protocol.EnumGameChannel enumGameChannel = 1;</code>
       */
      public fm.lizhi.ocean.seal.protocol.GameChannelServiceProto.EnumGameChannelOrBuilder getEnumGameChannelOrBuilder(
          int index) {
        if (enumGameChannelBuilder_ == null) {
          return enumGameChannel_.get(index);  } else {
          return enumGameChannelBuilder_.getMessageOrBuilder(index);
        }
      }
      /**
       * <code>repeated .fm.lizhi.commons.template.datacenter.protocol.EnumGameChannel enumGameChannel = 1;</code>
       */
      public java.util.List<? extends fm.lizhi.ocean.seal.protocol.GameChannelServiceProto.EnumGameChannelOrBuilder> 
           getEnumGameChannelOrBuilderList() {
        if (enumGameChannelBuilder_ != null) {
          return enumGameChannelBuilder_.getMessageOrBuilderList();
        } else {
          return java.util.Collections.unmodifiableList(enumGameChannel_);
        }
      }
      /**
       * <code>repeated .fm.lizhi.commons.template.datacenter.protocol.EnumGameChannel enumGameChannel = 1;</code>
       */
      public fm.lizhi.ocean.seal.protocol.GameChannelServiceProto.EnumGameChannel.Builder addEnumGameChannelBuilder() {
        return getEnumGameChannelFieldBuilder().addBuilder(
            fm.lizhi.ocean.seal.protocol.GameChannelServiceProto.EnumGameChannel.getDefaultInstance());
      }
      /**
       * <code>repeated .fm.lizhi.commons.template.datacenter.protocol.EnumGameChannel enumGameChannel = 1;</code>
       */
      public fm.lizhi.ocean.seal.protocol.GameChannelServiceProto.EnumGameChannel.Builder addEnumGameChannelBuilder(
          int index) {
        return getEnumGameChannelFieldBuilder().addBuilder(
            index, fm.lizhi.ocean.seal.protocol.GameChannelServiceProto.EnumGameChannel.getDefaultInstance());
      }
      /**
       * <code>repeated .fm.lizhi.commons.template.datacenter.protocol.EnumGameChannel enumGameChannel = 1;</code>
       */
      public java.util.List<fm.lizhi.ocean.seal.protocol.GameChannelServiceProto.EnumGameChannel.Builder> 
           getEnumGameChannelBuilderList() {
        return getEnumGameChannelFieldBuilder().getBuilderList();
      }
      private com.google.protobuf.RepeatedFieldBuilder<
          fm.lizhi.ocean.seal.protocol.GameChannelServiceProto.EnumGameChannel, fm.lizhi.ocean.seal.protocol.GameChannelServiceProto.EnumGameChannel.Builder, fm.lizhi.ocean.seal.protocol.GameChannelServiceProto.EnumGameChannelOrBuilder> 
          getEnumGameChannelFieldBuilder() {
        if (enumGameChannelBuilder_ == null) {
          enumGameChannelBuilder_ = new com.google.protobuf.RepeatedFieldBuilder<
              fm.lizhi.ocean.seal.protocol.GameChannelServiceProto.EnumGameChannel, fm.lizhi.ocean.seal.protocol.GameChannelServiceProto.EnumGameChannel.Builder, fm.lizhi.ocean.seal.protocol.GameChannelServiceProto.EnumGameChannelOrBuilder>(
                  enumGameChannel_,
                  ((bitField0_ & 0x00000001) == 0x00000001),
                  getParentForChildren(),
                  isClean());
          enumGameChannel_ = null;
        }
        return enumGameChannelBuilder_;
      }

      // @@protoc_insertion_point(builder_scope:fm.lizhi.commons.template.datacenter.protocol.ResponseGetEnumGameChannels)
    }

    static {
      defaultInstance = new ResponseGetEnumGameChannels(true);
      defaultInstance.initFields();
    }

    // @@protoc_insertion_point(class_scope:fm.lizhi.commons.template.datacenter.protocol.ResponseGetEnumGameChannels)
  }

  public interface GetChannelTokenParamOrBuilder
      extends com.google.protobuf.MessageOrBuilder {

    // required int64 userId = 1;
    /**
     * <code>required int64 userId = 1;</code>
     */
    boolean hasUserId();
    /**
     * <code>required int64 userId = 1;</code>
     */
    long getUserId();

    // required string appId = 2;
    /**
     * <code>required string appId = 2;</code>
     */
    boolean hasAppId();
    /**
     * <code>required string appId = 2;</code>
     */
    java.lang.String getAppId();
    /**
     * <code>required string appId = 2;</code>
     */
    com.google.protobuf.ByteString
        getAppIdBytes();

    // required string channel = 3;
    /**
     * <code>required string channel = 3;</code>
     */
    boolean hasChannel();
    /**
     * <code>required string channel = 3;</code>
     */
    java.lang.String getChannel();
    /**
     * <code>required string channel = 3;</code>
     */
    com.google.protobuf.ByteString
        getChannelBytes();
  }
  /**
   * Protobuf type {@code fm.lizhi.commons.template.datacenter.protocol.GetChannelTokenParam}
   */
  public static final class GetChannelTokenParam extends
      com.google.protobuf.GeneratedMessage
      implements GetChannelTokenParamOrBuilder {
    // Use GetChannelTokenParam.newBuilder() to construct.
    private GetChannelTokenParam(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
      super(builder);
      this.unknownFields = builder.getUnknownFields();
    }
    private GetChannelTokenParam(boolean noInit) { this.unknownFields = com.google.protobuf.UnknownFieldSet.getDefaultInstance(); }

    private static final GetChannelTokenParam defaultInstance;
    public static GetChannelTokenParam getDefaultInstance() {
      return defaultInstance;
    }

    public GetChannelTokenParam getDefaultInstanceForType() {
      return defaultInstance;
    }

    private final com.google.protobuf.UnknownFieldSet unknownFields;
    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
        getUnknownFields() {
      return this.unknownFields;
    }
    private GetChannelTokenParam(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      initFields();
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            default: {
              if (!parseUnknownField(input, unknownFields,
                                     extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
            case 8: {
              bitField0_ |= 0x00000001;
              userId_ = input.readInt64();
              break;
            }
            case 18: {
              bitField0_ |= 0x00000002;
              appId_ = input.readBytes();
              break;
            }
            case 26: {
              bitField0_ |= 0x00000004;
              channel_ = input.readBytes();
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e.getMessage()).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return fm.lizhi.ocean.seal.protocol.GameChannelServiceProto.internal_static_fm_lizhi_commons_template_datacenter_protocol_GetChannelTokenParam_descriptor;
    }

    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return fm.lizhi.ocean.seal.protocol.GameChannelServiceProto.internal_static_fm_lizhi_commons_template_datacenter_protocol_GetChannelTokenParam_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              fm.lizhi.ocean.seal.protocol.GameChannelServiceProto.GetChannelTokenParam.class, fm.lizhi.ocean.seal.protocol.GameChannelServiceProto.GetChannelTokenParam.Builder.class);
    }

    public static com.google.protobuf.Parser<GetChannelTokenParam> PARSER =
        new com.google.protobuf.AbstractParser<GetChannelTokenParam>() {
      public GetChannelTokenParam parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new GetChannelTokenParam(input, extensionRegistry);
      }
    };

    @java.lang.Override
    public com.google.protobuf.Parser<GetChannelTokenParam> getParserForType() {
      return PARSER;
    }

    private int bitField0_;
    // required int64 userId = 1;
    public static final int USERID_FIELD_NUMBER = 1;
    private long userId_;
    /**
     * <code>required int64 userId = 1;</code>
     */
    public boolean hasUserId() {
      return ((bitField0_ & 0x00000001) == 0x00000001);
    }
    /**
     * <code>required int64 userId = 1;</code>
     */
    public long getUserId() {
      return userId_;
    }

    // required string appId = 2;
    public static final int APPID_FIELD_NUMBER = 2;
    private java.lang.Object appId_;
    /**
     * <code>required string appId = 2;</code>
     */
    public boolean hasAppId() {
      return ((bitField0_ & 0x00000002) == 0x00000002);
    }
    /**
     * <code>required string appId = 2;</code>
     */
    public java.lang.String getAppId() {
      java.lang.Object ref = appId_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          appId_ = s;
        }
        return s;
      }
    }
    /**
     * <code>required string appId = 2;</code>
     */
    public com.google.protobuf.ByteString
        getAppIdBytes() {
      java.lang.Object ref = appId_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        appId_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    // required string channel = 3;
    public static final int CHANNEL_FIELD_NUMBER = 3;
    private java.lang.Object channel_;
    /**
     * <code>required string channel = 3;</code>
     */
    public boolean hasChannel() {
      return ((bitField0_ & 0x00000004) == 0x00000004);
    }
    /**
     * <code>required string channel = 3;</code>
     */
    public java.lang.String getChannel() {
      java.lang.Object ref = channel_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          channel_ = s;
        }
        return s;
      }
    }
    /**
     * <code>required string channel = 3;</code>
     */
    public com.google.protobuf.ByteString
        getChannelBytes() {
      java.lang.Object ref = channel_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        channel_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    private void initFields() {
      userId_ = 0L;
      appId_ = "";
      channel_ = "";
    }
    private byte memoizedIsInitialized = -1;
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized != -1) return isInitialized == 1;

      if (!hasUserId()) {
        memoizedIsInitialized = 0;
        return false;
      }
      if (!hasAppId()) {
        memoizedIsInitialized = 0;
        return false;
      }
      if (!hasChannel()) {
        memoizedIsInitialized = 0;
        return false;
      }
      memoizedIsInitialized = 1;
      return true;
    }

    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      getSerializedSize();
      if (((bitField0_ & 0x00000001) == 0x00000001)) {
        output.writeInt64(1, userId_);
      }
      if (((bitField0_ & 0x00000002) == 0x00000002)) {
        output.writeBytes(2, getAppIdBytes());
      }
      if (((bitField0_ & 0x00000004) == 0x00000004)) {
        output.writeBytes(3, getChannelBytes());
      }
      getUnknownFields().writeTo(output);
    }

    private int memoizedSerializedSize = -1;
    public int getSerializedSize() {
      int size = memoizedSerializedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) == 0x00000001)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt64Size(1, userId_);
      }
      if (((bitField0_ & 0x00000002) == 0x00000002)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(2, getAppIdBytes());
      }
      if (((bitField0_ & 0x00000004) == 0x00000004)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(3, getChannelBytes());
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSerializedSize = size;
      return size;
    }

    private static final long serialVersionUID = 0L;
    @java.lang.Override
    protected java.lang.Object writeReplace()
        throws java.io.ObjectStreamException {
      return super.writeReplace();
    }

    public static fm.lizhi.ocean.seal.protocol.GameChannelServiceProto.GetChannelTokenParam parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static fm.lizhi.ocean.seal.protocol.GameChannelServiceProto.GetChannelTokenParam parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static fm.lizhi.ocean.seal.protocol.GameChannelServiceProto.GetChannelTokenParam parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static fm.lizhi.ocean.seal.protocol.GameChannelServiceProto.GetChannelTokenParam parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static fm.lizhi.ocean.seal.protocol.GameChannelServiceProto.GetChannelTokenParam parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return PARSER.parseFrom(input);
    }
    public static fm.lizhi.ocean.seal.protocol.GameChannelServiceProto.GetChannelTokenParam parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseFrom(input, extensionRegistry);
    }
    public static fm.lizhi.ocean.seal.protocol.GameChannelServiceProto.GetChannelTokenParam parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return PARSER.parseDelimitedFrom(input);
    }
    public static fm.lizhi.ocean.seal.protocol.GameChannelServiceProto.GetChannelTokenParam parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseDelimitedFrom(input, extensionRegistry);
    }
    public static fm.lizhi.ocean.seal.protocol.GameChannelServiceProto.GetChannelTokenParam parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return PARSER.parseFrom(input);
    }
    public static fm.lizhi.ocean.seal.protocol.GameChannelServiceProto.GetChannelTokenParam parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseFrom(input, extensionRegistry);
    }

    public static Builder newBuilder() { return Builder.create(); }
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder(fm.lizhi.ocean.seal.protocol.GameChannelServiceProto.GetChannelTokenParam prototype) {
      return newBuilder().mergeFrom(prototype);
    }
    public Builder toBuilder() { return newBuilder(this); }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code fm.lizhi.commons.template.datacenter.protocol.GetChannelTokenParam}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessage.Builder<Builder>
       implements fm.lizhi.ocean.seal.protocol.GameChannelServiceProto.GetChannelTokenParamOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return fm.lizhi.ocean.seal.protocol.GameChannelServiceProto.internal_static_fm_lizhi_commons_template_datacenter_protocol_GetChannelTokenParam_descriptor;
      }

      protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return fm.lizhi.ocean.seal.protocol.GameChannelServiceProto.internal_static_fm_lizhi_commons_template_datacenter_protocol_GetChannelTokenParam_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                fm.lizhi.ocean.seal.protocol.GameChannelServiceProto.GetChannelTokenParam.class, fm.lizhi.ocean.seal.protocol.GameChannelServiceProto.GetChannelTokenParam.Builder.class);
      }

      // Construct using fm.lizhi.ocean.seal.protocol.GameChannelServiceProto.GetChannelTokenParam.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessage.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessage.alwaysUseFieldBuilders) {
        }
      }
      private static Builder create() {
        return new Builder();
      }

      public Builder clear() {
        super.clear();
        userId_ = 0L;
        bitField0_ = (bitField0_ & ~0x00000001);
        appId_ = "";
        bitField0_ = (bitField0_ & ~0x00000002);
        channel_ = "";
        bitField0_ = (bitField0_ & ~0x00000004);
        return this;
      }

      public Builder clone() {
        return create().mergeFrom(buildPartial());
      }

      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return fm.lizhi.ocean.seal.protocol.GameChannelServiceProto.internal_static_fm_lizhi_commons_template_datacenter_protocol_GetChannelTokenParam_descriptor;
      }

      public fm.lizhi.ocean.seal.protocol.GameChannelServiceProto.GetChannelTokenParam getDefaultInstanceForType() {
        return fm.lizhi.ocean.seal.protocol.GameChannelServiceProto.GetChannelTokenParam.getDefaultInstance();
      }

      public fm.lizhi.ocean.seal.protocol.GameChannelServiceProto.GetChannelTokenParam build() {
        fm.lizhi.ocean.seal.protocol.GameChannelServiceProto.GetChannelTokenParam result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      public fm.lizhi.ocean.seal.protocol.GameChannelServiceProto.GetChannelTokenParam buildPartial() {
        fm.lizhi.ocean.seal.protocol.GameChannelServiceProto.GetChannelTokenParam result = new fm.lizhi.ocean.seal.protocol.GameChannelServiceProto.GetChannelTokenParam(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) == 0x00000001)) {
          to_bitField0_ |= 0x00000001;
        }
        result.userId_ = userId_;
        if (((from_bitField0_ & 0x00000002) == 0x00000002)) {
          to_bitField0_ |= 0x00000002;
        }
        result.appId_ = appId_;
        if (((from_bitField0_ & 0x00000004) == 0x00000004)) {
          to_bitField0_ |= 0x00000004;
        }
        result.channel_ = channel_;
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof fm.lizhi.ocean.seal.protocol.GameChannelServiceProto.GetChannelTokenParam) {
          return mergeFrom((fm.lizhi.ocean.seal.protocol.GameChannelServiceProto.GetChannelTokenParam)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(fm.lizhi.ocean.seal.protocol.GameChannelServiceProto.GetChannelTokenParam other) {
        if (other == fm.lizhi.ocean.seal.protocol.GameChannelServiceProto.GetChannelTokenParam.getDefaultInstance()) return this;
        if (other.hasUserId()) {
          setUserId(other.getUserId());
        }
        if (other.hasAppId()) {
          bitField0_ |= 0x00000002;
          appId_ = other.appId_;
          onChanged();
        }
        if (other.hasChannel()) {
          bitField0_ |= 0x00000004;
          channel_ = other.channel_;
          onChanged();
        }
        this.mergeUnknownFields(other.getUnknownFields());
        return this;
      }

      public final boolean isInitialized() {
        if (!hasUserId()) {
          
          return false;
        }
        if (!hasAppId()) {
          
          return false;
        }
        if (!hasChannel()) {
          
          return false;
        }
        return true;
      }

      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        fm.lizhi.ocean.seal.protocol.GameChannelServiceProto.GetChannelTokenParam parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (fm.lizhi.ocean.seal.protocol.GameChannelServiceProto.GetChannelTokenParam) e.getUnfinishedMessage();
          throw e;
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      // required int64 userId = 1;
      private long userId_ ;
      /**
       * <code>required int64 userId = 1;</code>
       */
      public boolean hasUserId() {
        return ((bitField0_ & 0x00000001) == 0x00000001);
      }
      /**
       * <code>required int64 userId = 1;</code>
       */
      public long getUserId() {
        return userId_;
      }
      /**
       * <code>required int64 userId = 1;</code>
       */
      public Builder setUserId(long value) {
        bitField0_ |= 0x00000001;
        userId_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>required int64 userId = 1;</code>
       */
      public Builder clearUserId() {
        bitField0_ = (bitField0_ & ~0x00000001);
        userId_ = 0L;
        onChanged();
        return this;
      }

      // required string appId = 2;
      private java.lang.Object appId_ = "";
      /**
       * <code>required string appId = 2;</code>
       */
      public boolean hasAppId() {
        return ((bitField0_ & 0x00000002) == 0x00000002);
      }
      /**
       * <code>required string appId = 2;</code>
       */
      public java.lang.String getAppId() {
        java.lang.Object ref = appId_;
        if (!(ref instanceof java.lang.String)) {
          java.lang.String s = ((com.google.protobuf.ByteString) ref)
              .toStringUtf8();
          appId_ = s;
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <code>required string appId = 2;</code>
       */
      public com.google.protobuf.ByteString
          getAppIdBytes() {
        java.lang.Object ref = appId_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          appId_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <code>required string appId = 2;</code>
       */
      public Builder setAppId(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000002;
        appId_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>required string appId = 2;</code>
       */
      public Builder clearAppId() {
        bitField0_ = (bitField0_ & ~0x00000002);
        appId_ = getDefaultInstance().getAppId();
        onChanged();
        return this;
      }
      /**
       * <code>required string appId = 2;</code>
       */
      public Builder setAppIdBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000002;
        appId_ = value;
        onChanged();
        return this;
      }

      // required string channel = 3;
      private java.lang.Object channel_ = "";
      /**
       * <code>required string channel = 3;</code>
       */
      public boolean hasChannel() {
        return ((bitField0_ & 0x00000004) == 0x00000004);
      }
      /**
       * <code>required string channel = 3;</code>
       */
      public java.lang.String getChannel() {
        java.lang.Object ref = channel_;
        if (!(ref instanceof java.lang.String)) {
          java.lang.String s = ((com.google.protobuf.ByteString) ref)
              .toStringUtf8();
          channel_ = s;
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <code>required string channel = 3;</code>
       */
      public com.google.protobuf.ByteString
          getChannelBytes() {
        java.lang.Object ref = channel_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          channel_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <code>required string channel = 3;</code>
       */
      public Builder setChannel(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000004;
        channel_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>required string channel = 3;</code>
       */
      public Builder clearChannel() {
        bitField0_ = (bitField0_ & ~0x00000004);
        channel_ = getDefaultInstance().getChannel();
        onChanged();
        return this;
      }
      /**
       * <code>required string channel = 3;</code>
       */
      public Builder setChannelBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000004;
        channel_ = value;
        onChanged();
        return this;
      }

      // @@protoc_insertion_point(builder_scope:fm.lizhi.commons.template.datacenter.protocol.GetChannelTokenParam)
    }

    static {
      defaultInstance = new GetChannelTokenParam(true);
      defaultInstance.initFields();
    }

    // @@protoc_insertion_point(class_scope:fm.lizhi.commons.template.datacenter.protocol.GetChannelTokenParam)
  }

  public interface ChannelTokenOrBuilder
      extends com.google.protobuf.MessageOrBuilder {

    // required string token = 1;
    /**
     * <code>required string token = 1;</code>
     */
    boolean hasToken();
    /**
     * <code>required string token = 1;</code>
     */
    java.lang.String getToken();
    /**
     * <code>required string token = 1;</code>
     */
    com.google.protobuf.ByteString
        getTokenBytes();

    // required int64 expireTime = 2;
    /**
     * <code>required int64 expireTime = 2;</code>
     */
    boolean hasExpireTime();
    /**
     * <code>required int64 expireTime = 2;</code>
     */
    long getExpireTime();
  }
  /**
   * Protobuf type {@code fm.lizhi.commons.template.datacenter.protocol.ChannelToken}
   */
  public static final class ChannelToken extends
      com.google.protobuf.GeneratedMessage
      implements ChannelTokenOrBuilder {
    // Use ChannelToken.newBuilder() to construct.
    private ChannelToken(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
      super(builder);
      this.unknownFields = builder.getUnknownFields();
    }
    private ChannelToken(boolean noInit) { this.unknownFields = com.google.protobuf.UnknownFieldSet.getDefaultInstance(); }

    private static final ChannelToken defaultInstance;
    public static ChannelToken getDefaultInstance() {
      return defaultInstance;
    }

    public ChannelToken getDefaultInstanceForType() {
      return defaultInstance;
    }

    private final com.google.protobuf.UnknownFieldSet unknownFields;
    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
        getUnknownFields() {
      return this.unknownFields;
    }
    private ChannelToken(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      initFields();
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            default: {
              if (!parseUnknownField(input, unknownFields,
                                     extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
            case 10: {
              bitField0_ |= 0x00000001;
              token_ = input.readBytes();
              break;
            }
            case 16: {
              bitField0_ |= 0x00000002;
              expireTime_ = input.readInt64();
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e.getMessage()).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return fm.lizhi.ocean.seal.protocol.GameChannelServiceProto.internal_static_fm_lizhi_commons_template_datacenter_protocol_ChannelToken_descriptor;
    }

    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return fm.lizhi.ocean.seal.protocol.GameChannelServiceProto.internal_static_fm_lizhi_commons_template_datacenter_protocol_ChannelToken_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              fm.lizhi.ocean.seal.protocol.GameChannelServiceProto.ChannelToken.class, fm.lizhi.ocean.seal.protocol.GameChannelServiceProto.ChannelToken.Builder.class);
    }

    public static com.google.protobuf.Parser<ChannelToken> PARSER =
        new com.google.protobuf.AbstractParser<ChannelToken>() {
      public ChannelToken parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new ChannelToken(input, extensionRegistry);
      }
    };

    @java.lang.Override
    public com.google.protobuf.Parser<ChannelToken> getParserForType() {
      return PARSER;
    }

    private int bitField0_;
    // required string token = 1;
    public static final int TOKEN_FIELD_NUMBER = 1;
    private java.lang.Object token_;
    /**
     * <code>required string token = 1;</code>
     */
    public boolean hasToken() {
      return ((bitField0_ & 0x00000001) == 0x00000001);
    }
    /**
     * <code>required string token = 1;</code>
     */
    public java.lang.String getToken() {
      java.lang.Object ref = token_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          token_ = s;
        }
        return s;
      }
    }
    /**
     * <code>required string token = 1;</code>
     */
    public com.google.protobuf.ByteString
        getTokenBytes() {
      java.lang.Object ref = token_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        token_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    // required int64 expireTime = 2;
    public static final int EXPIRETIME_FIELD_NUMBER = 2;
    private long expireTime_;
    /**
     * <code>required int64 expireTime = 2;</code>
     */
    public boolean hasExpireTime() {
      return ((bitField0_ & 0x00000002) == 0x00000002);
    }
    /**
     * <code>required int64 expireTime = 2;</code>
     */
    public long getExpireTime() {
      return expireTime_;
    }

    private void initFields() {
      token_ = "";
      expireTime_ = 0L;
    }
    private byte memoizedIsInitialized = -1;
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized != -1) return isInitialized == 1;

      if (!hasToken()) {
        memoizedIsInitialized = 0;
        return false;
      }
      if (!hasExpireTime()) {
        memoizedIsInitialized = 0;
        return false;
      }
      memoizedIsInitialized = 1;
      return true;
    }

    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      getSerializedSize();
      if (((bitField0_ & 0x00000001) == 0x00000001)) {
        output.writeBytes(1, getTokenBytes());
      }
      if (((bitField0_ & 0x00000002) == 0x00000002)) {
        output.writeInt64(2, expireTime_);
      }
      getUnknownFields().writeTo(output);
    }

    private int memoizedSerializedSize = -1;
    public int getSerializedSize() {
      int size = memoizedSerializedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) == 0x00000001)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(1, getTokenBytes());
      }
      if (((bitField0_ & 0x00000002) == 0x00000002)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt64Size(2, expireTime_);
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSerializedSize = size;
      return size;
    }

    private static final long serialVersionUID = 0L;
    @java.lang.Override
    protected java.lang.Object writeReplace()
        throws java.io.ObjectStreamException {
      return super.writeReplace();
    }

    public static fm.lizhi.ocean.seal.protocol.GameChannelServiceProto.ChannelToken parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static fm.lizhi.ocean.seal.protocol.GameChannelServiceProto.ChannelToken parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static fm.lizhi.ocean.seal.protocol.GameChannelServiceProto.ChannelToken parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static fm.lizhi.ocean.seal.protocol.GameChannelServiceProto.ChannelToken parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static fm.lizhi.ocean.seal.protocol.GameChannelServiceProto.ChannelToken parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return PARSER.parseFrom(input);
    }
    public static fm.lizhi.ocean.seal.protocol.GameChannelServiceProto.ChannelToken parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseFrom(input, extensionRegistry);
    }
    public static fm.lizhi.ocean.seal.protocol.GameChannelServiceProto.ChannelToken parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return PARSER.parseDelimitedFrom(input);
    }
    public static fm.lizhi.ocean.seal.protocol.GameChannelServiceProto.ChannelToken parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseDelimitedFrom(input, extensionRegistry);
    }
    public static fm.lizhi.ocean.seal.protocol.GameChannelServiceProto.ChannelToken parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return PARSER.parseFrom(input);
    }
    public static fm.lizhi.ocean.seal.protocol.GameChannelServiceProto.ChannelToken parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseFrom(input, extensionRegistry);
    }

    public static Builder newBuilder() { return Builder.create(); }
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder(fm.lizhi.ocean.seal.protocol.GameChannelServiceProto.ChannelToken prototype) {
      return newBuilder().mergeFrom(prototype);
    }
    public Builder toBuilder() { return newBuilder(this); }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code fm.lizhi.commons.template.datacenter.protocol.ChannelToken}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessage.Builder<Builder>
       implements fm.lizhi.ocean.seal.protocol.GameChannelServiceProto.ChannelTokenOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return fm.lizhi.ocean.seal.protocol.GameChannelServiceProto.internal_static_fm_lizhi_commons_template_datacenter_protocol_ChannelToken_descriptor;
      }

      protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return fm.lizhi.ocean.seal.protocol.GameChannelServiceProto.internal_static_fm_lizhi_commons_template_datacenter_protocol_ChannelToken_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                fm.lizhi.ocean.seal.protocol.GameChannelServiceProto.ChannelToken.class, fm.lizhi.ocean.seal.protocol.GameChannelServiceProto.ChannelToken.Builder.class);
      }

      // Construct using fm.lizhi.ocean.seal.protocol.GameChannelServiceProto.ChannelToken.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessage.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessage.alwaysUseFieldBuilders) {
        }
      }
      private static Builder create() {
        return new Builder();
      }

      public Builder clear() {
        super.clear();
        token_ = "";
        bitField0_ = (bitField0_ & ~0x00000001);
        expireTime_ = 0L;
        bitField0_ = (bitField0_ & ~0x00000002);
        return this;
      }

      public Builder clone() {
        return create().mergeFrom(buildPartial());
      }

      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return fm.lizhi.ocean.seal.protocol.GameChannelServiceProto.internal_static_fm_lizhi_commons_template_datacenter_protocol_ChannelToken_descriptor;
      }

      public fm.lizhi.ocean.seal.protocol.GameChannelServiceProto.ChannelToken getDefaultInstanceForType() {
        return fm.lizhi.ocean.seal.protocol.GameChannelServiceProto.ChannelToken.getDefaultInstance();
      }

      public fm.lizhi.ocean.seal.protocol.GameChannelServiceProto.ChannelToken build() {
        fm.lizhi.ocean.seal.protocol.GameChannelServiceProto.ChannelToken result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      public fm.lizhi.ocean.seal.protocol.GameChannelServiceProto.ChannelToken buildPartial() {
        fm.lizhi.ocean.seal.protocol.GameChannelServiceProto.ChannelToken result = new fm.lizhi.ocean.seal.protocol.GameChannelServiceProto.ChannelToken(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) == 0x00000001)) {
          to_bitField0_ |= 0x00000001;
        }
        result.token_ = token_;
        if (((from_bitField0_ & 0x00000002) == 0x00000002)) {
          to_bitField0_ |= 0x00000002;
        }
        result.expireTime_ = expireTime_;
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof fm.lizhi.ocean.seal.protocol.GameChannelServiceProto.ChannelToken) {
          return mergeFrom((fm.lizhi.ocean.seal.protocol.GameChannelServiceProto.ChannelToken)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(fm.lizhi.ocean.seal.protocol.GameChannelServiceProto.ChannelToken other) {
        if (other == fm.lizhi.ocean.seal.protocol.GameChannelServiceProto.ChannelToken.getDefaultInstance()) return this;
        if (other.hasToken()) {
          bitField0_ |= 0x00000001;
          token_ = other.token_;
          onChanged();
        }
        if (other.hasExpireTime()) {
          setExpireTime(other.getExpireTime());
        }
        this.mergeUnknownFields(other.getUnknownFields());
        return this;
      }

      public final boolean isInitialized() {
        if (!hasToken()) {
          
          return false;
        }
        if (!hasExpireTime()) {
          
          return false;
        }
        return true;
      }

      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        fm.lizhi.ocean.seal.protocol.GameChannelServiceProto.ChannelToken parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (fm.lizhi.ocean.seal.protocol.GameChannelServiceProto.ChannelToken) e.getUnfinishedMessage();
          throw e;
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      // required string token = 1;
      private java.lang.Object token_ = "";
      /**
       * <code>required string token = 1;</code>
       */
      public boolean hasToken() {
        return ((bitField0_ & 0x00000001) == 0x00000001);
      }
      /**
       * <code>required string token = 1;</code>
       */
      public java.lang.String getToken() {
        java.lang.Object ref = token_;
        if (!(ref instanceof java.lang.String)) {
          java.lang.String s = ((com.google.protobuf.ByteString) ref)
              .toStringUtf8();
          token_ = s;
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <code>required string token = 1;</code>
       */
      public com.google.protobuf.ByteString
          getTokenBytes() {
        java.lang.Object ref = token_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          token_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <code>required string token = 1;</code>
       */
      public Builder setToken(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000001;
        token_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>required string token = 1;</code>
       */
      public Builder clearToken() {
        bitField0_ = (bitField0_ & ~0x00000001);
        token_ = getDefaultInstance().getToken();
        onChanged();
        return this;
      }
      /**
       * <code>required string token = 1;</code>
       */
      public Builder setTokenBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000001;
        token_ = value;
        onChanged();
        return this;
      }

      // required int64 expireTime = 2;
      private long expireTime_ ;
      /**
       * <code>required int64 expireTime = 2;</code>
       */
      public boolean hasExpireTime() {
        return ((bitField0_ & 0x00000002) == 0x00000002);
      }
      /**
       * <code>required int64 expireTime = 2;</code>
       */
      public long getExpireTime() {
        return expireTime_;
      }
      /**
       * <code>required int64 expireTime = 2;</code>
       */
      public Builder setExpireTime(long value) {
        bitField0_ |= 0x00000002;
        expireTime_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>required int64 expireTime = 2;</code>
       */
      public Builder clearExpireTime() {
        bitField0_ = (bitField0_ & ~0x00000002);
        expireTime_ = 0L;
        onChanged();
        return this;
      }

      // @@protoc_insertion_point(builder_scope:fm.lizhi.commons.template.datacenter.protocol.ChannelToken)
    }

    static {
      defaultInstance = new ChannelToken(true);
      defaultInstance.initFields();
    }

    // @@protoc_insertion_point(class_scope:fm.lizhi.commons.template.datacenter.protocol.ChannelToken)
  }

  public interface RequestGetChannelTokenOrBuilder
      extends com.google.protobuf.MessageOrBuilder {

    // required .fm.lizhi.commons.template.datacenter.protocol.GetChannelTokenParam param = 1;
    /**
     * <code>required .fm.lizhi.commons.template.datacenter.protocol.GetChannelTokenParam param = 1;</code>
     */
    boolean hasParam();
    /**
     * <code>required .fm.lizhi.commons.template.datacenter.protocol.GetChannelTokenParam param = 1;</code>
     */
    fm.lizhi.ocean.seal.protocol.GameChannelServiceProto.GetChannelTokenParam getParam();
    /**
     * <code>required .fm.lizhi.commons.template.datacenter.protocol.GetChannelTokenParam param = 1;</code>
     */
    fm.lizhi.ocean.seal.protocol.GameChannelServiceProto.GetChannelTokenParamOrBuilder getParamOrBuilder();
  }
  /**
   * Protobuf type {@code fm.lizhi.commons.template.datacenter.protocol.RequestGetChannelToken}
   *
   * <pre>
   * GameChannelService.java
   * 获取平台SDK与渠道交互的Token
   * domain = 4302, op = 222
   * </pre>
   */
  public static final class RequestGetChannelToken extends
      com.google.protobuf.GeneratedMessage
      implements RequestGetChannelTokenOrBuilder {
    // Use RequestGetChannelToken.newBuilder() to construct.
    private RequestGetChannelToken(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
      super(builder);
      this.unknownFields = builder.getUnknownFields();
    }
    private RequestGetChannelToken(boolean noInit) { this.unknownFields = com.google.protobuf.UnknownFieldSet.getDefaultInstance(); }

    private static final RequestGetChannelToken defaultInstance;
    public static RequestGetChannelToken getDefaultInstance() {
      return defaultInstance;
    }

    public RequestGetChannelToken getDefaultInstanceForType() {
      return defaultInstance;
    }

    private final com.google.protobuf.UnknownFieldSet unknownFields;
    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
        getUnknownFields() {
      return this.unknownFields;
    }
    private RequestGetChannelToken(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      initFields();
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            default: {
              if (!parseUnknownField(input, unknownFields,
                                     extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
            case 10: {
              fm.lizhi.ocean.seal.protocol.GameChannelServiceProto.GetChannelTokenParam.Builder subBuilder = null;
              if (((bitField0_ & 0x00000001) == 0x00000001)) {
                subBuilder = param_.toBuilder();
              }
              param_ = input.readMessage(fm.lizhi.ocean.seal.protocol.GameChannelServiceProto.GetChannelTokenParam.PARSER, extensionRegistry);
              if (subBuilder != null) {
                subBuilder.mergeFrom(param_);
                param_ = subBuilder.buildPartial();
              }
              bitField0_ |= 0x00000001;
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e.getMessage()).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return fm.lizhi.ocean.seal.protocol.GameChannelServiceProto.internal_static_fm_lizhi_commons_template_datacenter_protocol_RequestGetChannelToken_descriptor;
    }

    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return fm.lizhi.ocean.seal.protocol.GameChannelServiceProto.internal_static_fm_lizhi_commons_template_datacenter_protocol_RequestGetChannelToken_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              fm.lizhi.ocean.seal.protocol.GameChannelServiceProto.RequestGetChannelToken.class, fm.lizhi.ocean.seal.protocol.GameChannelServiceProto.RequestGetChannelToken.Builder.class);
    }

    public static com.google.protobuf.Parser<RequestGetChannelToken> PARSER =
        new com.google.protobuf.AbstractParser<RequestGetChannelToken>() {
      public RequestGetChannelToken parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new RequestGetChannelToken(input, extensionRegistry);
      }
    };

    @java.lang.Override
    public com.google.protobuf.Parser<RequestGetChannelToken> getParserForType() {
      return PARSER;
    }

    private int bitField0_;
    // required .fm.lizhi.commons.template.datacenter.protocol.GetChannelTokenParam param = 1;
    public static final int PARAM_FIELD_NUMBER = 1;
    private fm.lizhi.ocean.seal.protocol.GameChannelServiceProto.GetChannelTokenParam param_;
    /**
     * <code>required .fm.lizhi.commons.template.datacenter.protocol.GetChannelTokenParam param = 1;</code>
     */
    public boolean hasParam() {
      return ((bitField0_ & 0x00000001) == 0x00000001);
    }
    /**
     * <code>required .fm.lizhi.commons.template.datacenter.protocol.GetChannelTokenParam param = 1;</code>
     */
    public fm.lizhi.ocean.seal.protocol.GameChannelServiceProto.GetChannelTokenParam getParam() {
      return param_;
    }
    /**
     * <code>required .fm.lizhi.commons.template.datacenter.protocol.GetChannelTokenParam param = 1;</code>
     */
    public fm.lizhi.ocean.seal.protocol.GameChannelServiceProto.GetChannelTokenParamOrBuilder getParamOrBuilder() {
      return param_;
    }

    private void initFields() {
      param_ = fm.lizhi.ocean.seal.protocol.GameChannelServiceProto.GetChannelTokenParam.getDefaultInstance();
    }
    private byte memoizedIsInitialized = -1;
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized != -1) return isInitialized == 1;

      if (!hasParam()) {
        memoizedIsInitialized = 0;
        return false;
      }
      if (!getParam().isInitialized()) {
        memoizedIsInitialized = 0;
        return false;
      }
      memoizedIsInitialized = 1;
      return true;
    }

    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      getSerializedSize();
      if (((bitField0_ & 0x00000001) == 0x00000001)) {
        output.writeMessage(1, param_);
      }
      getUnknownFields().writeTo(output);
    }

    private int memoizedSerializedSize = -1;
    public int getSerializedSize() {
      int size = memoizedSerializedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) == 0x00000001)) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(1, param_);
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSerializedSize = size;
      return size;
    }

    private static final long serialVersionUID = 0L;
    @java.lang.Override
    protected java.lang.Object writeReplace()
        throws java.io.ObjectStreamException {
      return super.writeReplace();
    }

    public static fm.lizhi.ocean.seal.protocol.GameChannelServiceProto.RequestGetChannelToken parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static fm.lizhi.ocean.seal.protocol.GameChannelServiceProto.RequestGetChannelToken parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static fm.lizhi.ocean.seal.protocol.GameChannelServiceProto.RequestGetChannelToken parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static fm.lizhi.ocean.seal.protocol.GameChannelServiceProto.RequestGetChannelToken parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static fm.lizhi.ocean.seal.protocol.GameChannelServiceProto.RequestGetChannelToken parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return PARSER.parseFrom(input);
    }
    public static fm.lizhi.ocean.seal.protocol.GameChannelServiceProto.RequestGetChannelToken parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseFrom(input, extensionRegistry);
    }
    public static fm.lizhi.ocean.seal.protocol.GameChannelServiceProto.RequestGetChannelToken parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return PARSER.parseDelimitedFrom(input);
    }
    public static fm.lizhi.ocean.seal.protocol.GameChannelServiceProto.RequestGetChannelToken parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseDelimitedFrom(input, extensionRegistry);
    }
    public static fm.lizhi.ocean.seal.protocol.GameChannelServiceProto.RequestGetChannelToken parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return PARSER.parseFrom(input);
    }
    public static fm.lizhi.ocean.seal.protocol.GameChannelServiceProto.RequestGetChannelToken parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseFrom(input, extensionRegistry);
    }

    public static Builder newBuilder() { return Builder.create(); }
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder(fm.lizhi.ocean.seal.protocol.GameChannelServiceProto.RequestGetChannelToken prototype) {
      return newBuilder().mergeFrom(prototype);
    }
    public Builder toBuilder() { return newBuilder(this); }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code fm.lizhi.commons.template.datacenter.protocol.RequestGetChannelToken}
     *
     * <pre>
     * GameChannelService.java
     * 获取平台SDK与渠道交互的Token
     * domain = 4302, op = 222
     * </pre>
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessage.Builder<Builder>
       implements fm.lizhi.ocean.seal.protocol.GameChannelServiceProto.RequestGetChannelTokenOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return fm.lizhi.ocean.seal.protocol.GameChannelServiceProto.internal_static_fm_lizhi_commons_template_datacenter_protocol_RequestGetChannelToken_descriptor;
      }

      protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return fm.lizhi.ocean.seal.protocol.GameChannelServiceProto.internal_static_fm_lizhi_commons_template_datacenter_protocol_RequestGetChannelToken_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                fm.lizhi.ocean.seal.protocol.GameChannelServiceProto.RequestGetChannelToken.class, fm.lizhi.ocean.seal.protocol.GameChannelServiceProto.RequestGetChannelToken.Builder.class);
      }

      // Construct using fm.lizhi.ocean.seal.protocol.GameChannelServiceProto.RequestGetChannelToken.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessage.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessage.alwaysUseFieldBuilders) {
          getParamFieldBuilder();
        }
      }
      private static Builder create() {
        return new Builder();
      }

      public Builder clear() {
        super.clear();
        if (paramBuilder_ == null) {
          param_ = fm.lizhi.ocean.seal.protocol.GameChannelServiceProto.GetChannelTokenParam.getDefaultInstance();
        } else {
          paramBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000001);
        return this;
      }

      public Builder clone() {
        return create().mergeFrom(buildPartial());
      }

      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return fm.lizhi.ocean.seal.protocol.GameChannelServiceProto.internal_static_fm_lizhi_commons_template_datacenter_protocol_RequestGetChannelToken_descriptor;
      }

      public fm.lizhi.ocean.seal.protocol.GameChannelServiceProto.RequestGetChannelToken getDefaultInstanceForType() {
        return fm.lizhi.ocean.seal.protocol.GameChannelServiceProto.RequestGetChannelToken.getDefaultInstance();
      }

      public fm.lizhi.ocean.seal.protocol.GameChannelServiceProto.RequestGetChannelToken build() {
        fm.lizhi.ocean.seal.protocol.GameChannelServiceProto.RequestGetChannelToken result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      public fm.lizhi.ocean.seal.protocol.GameChannelServiceProto.RequestGetChannelToken buildPartial() {
        fm.lizhi.ocean.seal.protocol.GameChannelServiceProto.RequestGetChannelToken result = new fm.lizhi.ocean.seal.protocol.GameChannelServiceProto.RequestGetChannelToken(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) == 0x00000001)) {
          to_bitField0_ |= 0x00000001;
        }
        if (paramBuilder_ == null) {
          result.param_ = param_;
        } else {
          result.param_ = paramBuilder_.build();
        }
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof fm.lizhi.ocean.seal.protocol.GameChannelServiceProto.RequestGetChannelToken) {
          return mergeFrom((fm.lizhi.ocean.seal.protocol.GameChannelServiceProto.RequestGetChannelToken)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(fm.lizhi.ocean.seal.protocol.GameChannelServiceProto.RequestGetChannelToken other) {
        if (other == fm.lizhi.ocean.seal.protocol.GameChannelServiceProto.RequestGetChannelToken.getDefaultInstance()) return this;
        if (other.hasParam()) {
          mergeParam(other.getParam());
        }
        this.mergeUnknownFields(other.getUnknownFields());
        return this;
      }

      public final boolean isInitialized() {
        if (!hasParam()) {
          
          return false;
        }
        if (!getParam().isInitialized()) {
          
          return false;
        }
        return true;
      }

      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        fm.lizhi.ocean.seal.protocol.GameChannelServiceProto.RequestGetChannelToken parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (fm.lizhi.ocean.seal.protocol.GameChannelServiceProto.RequestGetChannelToken) e.getUnfinishedMessage();
          throw e;
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      // required .fm.lizhi.commons.template.datacenter.protocol.GetChannelTokenParam param = 1;
      private fm.lizhi.ocean.seal.protocol.GameChannelServiceProto.GetChannelTokenParam param_ = fm.lizhi.ocean.seal.protocol.GameChannelServiceProto.GetChannelTokenParam.getDefaultInstance();
      private com.google.protobuf.SingleFieldBuilder<
          fm.lizhi.ocean.seal.protocol.GameChannelServiceProto.GetChannelTokenParam, fm.lizhi.ocean.seal.protocol.GameChannelServiceProto.GetChannelTokenParam.Builder, fm.lizhi.ocean.seal.protocol.GameChannelServiceProto.GetChannelTokenParamOrBuilder> paramBuilder_;
      /**
       * <code>required .fm.lizhi.commons.template.datacenter.protocol.GetChannelTokenParam param = 1;</code>
       */
      public boolean hasParam() {
        return ((bitField0_ & 0x00000001) == 0x00000001);
      }
      /**
       * <code>required .fm.lizhi.commons.template.datacenter.protocol.GetChannelTokenParam param = 1;</code>
       */
      public fm.lizhi.ocean.seal.protocol.GameChannelServiceProto.GetChannelTokenParam getParam() {
        if (paramBuilder_ == null) {
          return param_;
        } else {
          return paramBuilder_.getMessage();
        }
      }
      /**
       * <code>required .fm.lizhi.commons.template.datacenter.protocol.GetChannelTokenParam param = 1;</code>
       */
      public Builder setParam(fm.lizhi.ocean.seal.protocol.GameChannelServiceProto.GetChannelTokenParam value) {
        if (paramBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          param_ = value;
          onChanged();
        } else {
          paramBuilder_.setMessage(value);
        }
        bitField0_ |= 0x00000001;
        return this;
      }
      /**
       * <code>required .fm.lizhi.commons.template.datacenter.protocol.GetChannelTokenParam param = 1;</code>
       */
      public Builder setParam(
          fm.lizhi.ocean.seal.protocol.GameChannelServiceProto.GetChannelTokenParam.Builder builderForValue) {
        if (paramBuilder_ == null) {
          param_ = builderForValue.build();
          onChanged();
        } else {
          paramBuilder_.setMessage(builderForValue.build());
        }
        bitField0_ |= 0x00000001;
        return this;
      }
      /**
       * <code>required .fm.lizhi.commons.template.datacenter.protocol.GetChannelTokenParam param = 1;</code>
       */
      public Builder mergeParam(fm.lizhi.ocean.seal.protocol.GameChannelServiceProto.GetChannelTokenParam value) {
        if (paramBuilder_ == null) {
          if (((bitField0_ & 0x00000001) == 0x00000001) &&
              param_ != fm.lizhi.ocean.seal.protocol.GameChannelServiceProto.GetChannelTokenParam.getDefaultInstance()) {
            param_ =
              fm.lizhi.ocean.seal.protocol.GameChannelServiceProto.GetChannelTokenParam.newBuilder(param_).mergeFrom(value).buildPartial();
          } else {
            param_ = value;
          }
          onChanged();
        } else {
          paramBuilder_.mergeFrom(value);
        }
        bitField0_ |= 0x00000001;
        return this;
      }
      /**
       * <code>required .fm.lizhi.commons.template.datacenter.protocol.GetChannelTokenParam param = 1;</code>
       */
      public Builder clearParam() {
        if (paramBuilder_ == null) {
          param_ = fm.lizhi.ocean.seal.protocol.GameChannelServiceProto.GetChannelTokenParam.getDefaultInstance();
          onChanged();
        } else {
          paramBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000001);
        return this;
      }
      /**
       * <code>required .fm.lizhi.commons.template.datacenter.protocol.GetChannelTokenParam param = 1;</code>
       */
      public fm.lizhi.ocean.seal.protocol.GameChannelServiceProto.GetChannelTokenParam.Builder getParamBuilder() {
        bitField0_ |= 0x00000001;
        onChanged();
        return getParamFieldBuilder().getBuilder();
      }
      /**
       * <code>required .fm.lizhi.commons.template.datacenter.protocol.GetChannelTokenParam param = 1;</code>
       */
      public fm.lizhi.ocean.seal.protocol.GameChannelServiceProto.GetChannelTokenParamOrBuilder getParamOrBuilder() {
        if (paramBuilder_ != null) {
          return paramBuilder_.getMessageOrBuilder();
        } else {
          return param_;
        }
      }
      /**
       * <code>required .fm.lizhi.commons.template.datacenter.protocol.GetChannelTokenParam param = 1;</code>
       */
      private com.google.protobuf.SingleFieldBuilder<
          fm.lizhi.ocean.seal.protocol.GameChannelServiceProto.GetChannelTokenParam, fm.lizhi.ocean.seal.protocol.GameChannelServiceProto.GetChannelTokenParam.Builder, fm.lizhi.ocean.seal.protocol.GameChannelServiceProto.GetChannelTokenParamOrBuilder> 
          getParamFieldBuilder() {
        if (paramBuilder_ == null) {
          paramBuilder_ = new com.google.protobuf.SingleFieldBuilder<
              fm.lizhi.ocean.seal.protocol.GameChannelServiceProto.GetChannelTokenParam, fm.lizhi.ocean.seal.protocol.GameChannelServiceProto.GetChannelTokenParam.Builder, fm.lizhi.ocean.seal.protocol.GameChannelServiceProto.GetChannelTokenParamOrBuilder>(
                  param_,
                  getParentForChildren(),
                  isClean());
          param_ = null;
        }
        return paramBuilder_;
      }

      // @@protoc_insertion_point(builder_scope:fm.lizhi.commons.template.datacenter.protocol.RequestGetChannelToken)
    }

    static {
      defaultInstance = new RequestGetChannelToken(true);
      defaultInstance.initFields();
    }

    // @@protoc_insertion_point(class_scope:fm.lizhi.commons.template.datacenter.protocol.RequestGetChannelToken)
  }

  public interface ResponseGetChannelTokenOrBuilder
      extends com.google.protobuf.MessageOrBuilder {

    // required .fm.lizhi.commons.template.datacenter.protocol.ChannelToken token = 1;
    /**
     * <code>required .fm.lizhi.commons.template.datacenter.protocol.ChannelToken token = 1;</code>
     */
    boolean hasToken();
    /**
     * <code>required .fm.lizhi.commons.template.datacenter.protocol.ChannelToken token = 1;</code>
     */
    fm.lizhi.ocean.seal.protocol.GameChannelServiceProto.ChannelToken getToken();
    /**
     * <code>required .fm.lizhi.commons.template.datacenter.protocol.ChannelToken token = 1;</code>
     */
    fm.lizhi.ocean.seal.protocol.GameChannelServiceProto.ChannelTokenOrBuilder getTokenOrBuilder();
  }
  /**
   * Protobuf type {@code fm.lizhi.commons.template.datacenter.protocol.ResponseGetChannelToken}
   *
   * <pre>
   * rcode == 1 (ILLEGAL_PARAMS) = 参数非法
   * rcode == 2 (ERROR) = 内部错误
   * </pre>
   */
  public static final class ResponseGetChannelToken extends
      com.google.protobuf.GeneratedMessage
      implements ResponseGetChannelTokenOrBuilder {
    // Use ResponseGetChannelToken.newBuilder() to construct.
    private ResponseGetChannelToken(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
      super(builder);
      this.unknownFields = builder.getUnknownFields();
    }
    private ResponseGetChannelToken(boolean noInit) { this.unknownFields = com.google.protobuf.UnknownFieldSet.getDefaultInstance(); }

    private static final ResponseGetChannelToken defaultInstance;
    public static ResponseGetChannelToken getDefaultInstance() {
      return defaultInstance;
    }

    public ResponseGetChannelToken getDefaultInstanceForType() {
      return defaultInstance;
    }

    private final com.google.protobuf.UnknownFieldSet unknownFields;
    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
        getUnknownFields() {
      return this.unknownFields;
    }
    private ResponseGetChannelToken(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      initFields();
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            default: {
              if (!parseUnknownField(input, unknownFields,
                                     extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
            case 10: {
              fm.lizhi.ocean.seal.protocol.GameChannelServiceProto.ChannelToken.Builder subBuilder = null;
              if (((bitField0_ & 0x00000001) == 0x00000001)) {
                subBuilder = token_.toBuilder();
              }
              token_ = input.readMessage(fm.lizhi.ocean.seal.protocol.GameChannelServiceProto.ChannelToken.PARSER, extensionRegistry);
              if (subBuilder != null) {
                subBuilder.mergeFrom(token_);
                token_ = subBuilder.buildPartial();
              }
              bitField0_ |= 0x00000001;
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e.getMessage()).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return fm.lizhi.ocean.seal.protocol.GameChannelServiceProto.internal_static_fm_lizhi_commons_template_datacenter_protocol_ResponseGetChannelToken_descriptor;
    }

    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return fm.lizhi.ocean.seal.protocol.GameChannelServiceProto.internal_static_fm_lizhi_commons_template_datacenter_protocol_ResponseGetChannelToken_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              fm.lizhi.ocean.seal.protocol.GameChannelServiceProto.ResponseGetChannelToken.class, fm.lizhi.ocean.seal.protocol.GameChannelServiceProto.ResponseGetChannelToken.Builder.class);
    }

    public static com.google.protobuf.Parser<ResponseGetChannelToken> PARSER =
        new com.google.protobuf.AbstractParser<ResponseGetChannelToken>() {
      public ResponseGetChannelToken parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new ResponseGetChannelToken(input, extensionRegistry);
      }
    };

    @java.lang.Override
    public com.google.protobuf.Parser<ResponseGetChannelToken> getParserForType() {
      return PARSER;
    }

    private int bitField0_;
    // required .fm.lizhi.commons.template.datacenter.protocol.ChannelToken token = 1;
    public static final int TOKEN_FIELD_NUMBER = 1;
    private fm.lizhi.ocean.seal.protocol.GameChannelServiceProto.ChannelToken token_;
    /**
     * <code>required .fm.lizhi.commons.template.datacenter.protocol.ChannelToken token = 1;</code>
     */
    public boolean hasToken() {
      return ((bitField0_ & 0x00000001) == 0x00000001);
    }
    /**
     * <code>required .fm.lizhi.commons.template.datacenter.protocol.ChannelToken token = 1;</code>
     */
    public fm.lizhi.ocean.seal.protocol.GameChannelServiceProto.ChannelToken getToken() {
      return token_;
    }
    /**
     * <code>required .fm.lizhi.commons.template.datacenter.protocol.ChannelToken token = 1;</code>
     */
    public fm.lizhi.ocean.seal.protocol.GameChannelServiceProto.ChannelTokenOrBuilder getTokenOrBuilder() {
      return token_;
    }

    private void initFields() {
      token_ = fm.lizhi.ocean.seal.protocol.GameChannelServiceProto.ChannelToken.getDefaultInstance();
    }
    private byte memoizedIsInitialized = -1;
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized != -1) return isInitialized == 1;

      if (!hasToken()) {
        memoizedIsInitialized = 0;
        return false;
      }
      if (!getToken().isInitialized()) {
        memoizedIsInitialized = 0;
        return false;
      }
      memoizedIsInitialized = 1;
      return true;
    }

    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      getSerializedSize();
      if (((bitField0_ & 0x00000001) == 0x00000001)) {
        output.writeMessage(1, token_);
      }
      getUnknownFields().writeTo(output);
    }

    private int memoizedSerializedSize = -1;
    public int getSerializedSize() {
      int size = memoizedSerializedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) == 0x00000001)) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(1, token_);
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSerializedSize = size;
      return size;
    }

    private static final long serialVersionUID = 0L;
    @java.lang.Override
    protected java.lang.Object writeReplace()
        throws java.io.ObjectStreamException {
      return super.writeReplace();
    }

    public static fm.lizhi.ocean.seal.protocol.GameChannelServiceProto.ResponseGetChannelToken parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static fm.lizhi.ocean.seal.protocol.GameChannelServiceProto.ResponseGetChannelToken parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static fm.lizhi.ocean.seal.protocol.GameChannelServiceProto.ResponseGetChannelToken parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static fm.lizhi.ocean.seal.protocol.GameChannelServiceProto.ResponseGetChannelToken parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static fm.lizhi.ocean.seal.protocol.GameChannelServiceProto.ResponseGetChannelToken parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return PARSER.parseFrom(input);
    }
    public static fm.lizhi.ocean.seal.protocol.GameChannelServiceProto.ResponseGetChannelToken parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseFrom(input, extensionRegistry);
    }
    public static fm.lizhi.ocean.seal.protocol.GameChannelServiceProto.ResponseGetChannelToken parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return PARSER.parseDelimitedFrom(input);
    }
    public static fm.lizhi.ocean.seal.protocol.GameChannelServiceProto.ResponseGetChannelToken parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseDelimitedFrom(input, extensionRegistry);
    }
    public static fm.lizhi.ocean.seal.protocol.GameChannelServiceProto.ResponseGetChannelToken parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return PARSER.parseFrom(input);
    }
    public static fm.lizhi.ocean.seal.protocol.GameChannelServiceProto.ResponseGetChannelToken parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseFrom(input, extensionRegistry);
    }

    public static Builder newBuilder() { return Builder.create(); }
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder(fm.lizhi.ocean.seal.protocol.GameChannelServiceProto.ResponseGetChannelToken prototype) {
      return newBuilder().mergeFrom(prototype);
    }
    public Builder toBuilder() { return newBuilder(this); }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code fm.lizhi.commons.template.datacenter.protocol.ResponseGetChannelToken}
     *
     * <pre>
     * rcode == 1 (ILLEGAL_PARAMS) = 参数非法
     * rcode == 2 (ERROR) = 内部错误
     * </pre>
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessage.Builder<Builder>
       implements fm.lizhi.ocean.seal.protocol.GameChannelServiceProto.ResponseGetChannelTokenOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return fm.lizhi.ocean.seal.protocol.GameChannelServiceProto.internal_static_fm_lizhi_commons_template_datacenter_protocol_ResponseGetChannelToken_descriptor;
      }

      protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return fm.lizhi.ocean.seal.protocol.GameChannelServiceProto.internal_static_fm_lizhi_commons_template_datacenter_protocol_ResponseGetChannelToken_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                fm.lizhi.ocean.seal.protocol.GameChannelServiceProto.ResponseGetChannelToken.class, fm.lizhi.ocean.seal.protocol.GameChannelServiceProto.ResponseGetChannelToken.Builder.class);
      }

      // Construct using fm.lizhi.ocean.seal.protocol.GameChannelServiceProto.ResponseGetChannelToken.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessage.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessage.alwaysUseFieldBuilders) {
          getTokenFieldBuilder();
        }
      }
      private static Builder create() {
        return new Builder();
      }

      public Builder clear() {
        super.clear();
        if (tokenBuilder_ == null) {
          token_ = fm.lizhi.ocean.seal.protocol.GameChannelServiceProto.ChannelToken.getDefaultInstance();
        } else {
          tokenBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000001);
        return this;
      }

      public Builder clone() {
        return create().mergeFrom(buildPartial());
      }

      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return fm.lizhi.ocean.seal.protocol.GameChannelServiceProto.internal_static_fm_lizhi_commons_template_datacenter_protocol_ResponseGetChannelToken_descriptor;
      }

      public fm.lizhi.ocean.seal.protocol.GameChannelServiceProto.ResponseGetChannelToken getDefaultInstanceForType() {
        return fm.lizhi.ocean.seal.protocol.GameChannelServiceProto.ResponseGetChannelToken.getDefaultInstance();
      }

      public fm.lizhi.ocean.seal.protocol.GameChannelServiceProto.ResponseGetChannelToken build() {
        fm.lizhi.ocean.seal.protocol.GameChannelServiceProto.ResponseGetChannelToken result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      public fm.lizhi.ocean.seal.protocol.GameChannelServiceProto.ResponseGetChannelToken buildPartial() {
        fm.lizhi.ocean.seal.protocol.GameChannelServiceProto.ResponseGetChannelToken result = new fm.lizhi.ocean.seal.protocol.GameChannelServiceProto.ResponseGetChannelToken(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) == 0x00000001)) {
          to_bitField0_ |= 0x00000001;
        }
        if (tokenBuilder_ == null) {
          result.token_ = token_;
        } else {
          result.token_ = tokenBuilder_.build();
        }
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof fm.lizhi.ocean.seal.protocol.GameChannelServiceProto.ResponseGetChannelToken) {
          return mergeFrom((fm.lizhi.ocean.seal.protocol.GameChannelServiceProto.ResponseGetChannelToken)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(fm.lizhi.ocean.seal.protocol.GameChannelServiceProto.ResponseGetChannelToken other) {
        if (other == fm.lizhi.ocean.seal.protocol.GameChannelServiceProto.ResponseGetChannelToken.getDefaultInstance()) return this;
        if (other.hasToken()) {
          mergeToken(other.getToken());
        }
        this.mergeUnknownFields(other.getUnknownFields());
        return this;
      }

      public final boolean isInitialized() {
        if (!hasToken()) {
          
          return false;
        }
        if (!getToken().isInitialized()) {
          
          return false;
        }
        return true;
      }

      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        fm.lizhi.ocean.seal.protocol.GameChannelServiceProto.ResponseGetChannelToken parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (fm.lizhi.ocean.seal.protocol.GameChannelServiceProto.ResponseGetChannelToken) e.getUnfinishedMessage();
          throw e;
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      // required .fm.lizhi.commons.template.datacenter.protocol.ChannelToken token = 1;
      private fm.lizhi.ocean.seal.protocol.GameChannelServiceProto.ChannelToken token_ = fm.lizhi.ocean.seal.protocol.GameChannelServiceProto.ChannelToken.getDefaultInstance();
      private com.google.protobuf.SingleFieldBuilder<
          fm.lizhi.ocean.seal.protocol.GameChannelServiceProto.ChannelToken, fm.lizhi.ocean.seal.protocol.GameChannelServiceProto.ChannelToken.Builder, fm.lizhi.ocean.seal.protocol.GameChannelServiceProto.ChannelTokenOrBuilder> tokenBuilder_;
      /**
       * <code>required .fm.lizhi.commons.template.datacenter.protocol.ChannelToken token = 1;</code>
       */
      public boolean hasToken() {
        return ((bitField0_ & 0x00000001) == 0x00000001);
      }
      /**
       * <code>required .fm.lizhi.commons.template.datacenter.protocol.ChannelToken token = 1;</code>
       */
      public fm.lizhi.ocean.seal.protocol.GameChannelServiceProto.ChannelToken getToken() {
        if (tokenBuilder_ == null) {
          return token_;
        } else {
          return tokenBuilder_.getMessage();
        }
      }
      /**
       * <code>required .fm.lizhi.commons.template.datacenter.protocol.ChannelToken token = 1;</code>
       */
      public Builder setToken(fm.lizhi.ocean.seal.protocol.GameChannelServiceProto.ChannelToken value) {
        if (tokenBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          token_ = value;
          onChanged();
        } else {
          tokenBuilder_.setMessage(value);
        }
        bitField0_ |= 0x00000001;
        return this;
      }
      /**
       * <code>required .fm.lizhi.commons.template.datacenter.protocol.ChannelToken token = 1;</code>
       */
      public Builder setToken(
          fm.lizhi.ocean.seal.protocol.GameChannelServiceProto.ChannelToken.Builder builderForValue) {
        if (tokenBuilder_ == null) {
          token_ = builderForValue.build();
          onChanged();
        } else {
          tokenBuilder_.setMessage(builderForValue.build());
        }
        bitField0_ |= 0x00000001;
        return this;
      }
      /**
       * <code>required .fm.lizhi.commons.template.datacenter.protocol.ChannelToken token = 1;</code>
       */
      public Builder mergeToken(fm.lizhi.ocean.seal.protocol.GameChannelServiceProto.ChannelToken value) {
        if (tokenBuilder_ == null) {
          if (((bitField0_ & 0x00000001) == 0x00000001) &&
              token_ != fm.lizhi.ocean.seal.protocol.GameChannelServiceProto.ChannelToken.getDefaultInstance()) {
            token_ =
              fm.lizhi.ocean.seal.protocol.GameChannelServiceProto.ChannelToken.newBuilder(token_).mergeFrom(value).buildPartial();
          } else {
            token_ = value;
          }
          onChanged();
        } else {
          tokenBuilder_.mergeFrom(value);
        }
        bitField0_ |= 0x00000001;
        return this;
      }
      /**
       * <code>required .fm.lizhi.commons.template.datacenter.protocol.ChannelToken token = 1;</code>
       */
      public Builder clearToken() {
        if (tokenBuilder_ == null) {
          token_ = fm.lizhi.ocean.seal.protocol.GameChannelServiceProto.ChannelToken.getDefaultInstance();
          onChanged();
        } else {
          tokenBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000001);
        return this;
      }
      /**
       * <code>required .fm.lizhi.commons.template.datacenter.protocol.ChannelToken token = 1;</code>
       */
      public fm.lizhi.ocean.seal.protocol.GameChannelServiceProto.ChannelToken.Builder getTokenBuilder() {
        bitField0_ |= 0x00000001;
        onChanged();
        return getTokenFieldBuilder().getBuilder();
      }
      /**
       * <code>required .fm.lizhi.commons.template.datacenter.protocol.ChannelToken token = 1;</code>
       */
      public fm.lizhi.ocean.seal.protocol.GameChannelServiceProto.ChannelTokenOrBuilder getTokenOrBuilder() {
        if (tokenBuilder_ != null) {
          return tokenBuilder_.getMessageOrBuilder();
        } else {
          return token_;
        }
      }
      /**
       * <code>required .fm.lizhi.commons.template.datacenter.protocol.ChannelToken token = 1;</code>
       */
      private com.google.protobuf.SingleFieldBuilder<
          fm.lizhi.ocean.seal.protocol.GameChannelServiceProto.ChannelToken, fm.lizhi.ocean.seal.protocol.GameChannelServiceProto.ChannelToken.Builder, fm.lizhi.ocean.seal.protocol.GameChannelServiceProto.ChannelTokenOrBuilder> 
          getTokenFieldBuilder() {
        if (tokenBuilder_ == null) {
          tokenBuilder_ = new com.google.protobuf.SingleFieldBuilder<
              fm.lizhi.ocean.seal.protocol.GameChannelServiceProto.ChannelToken, fm.lizhi.ocean.seal.protocol.GameChannelServiceProto.ChannelToken.Builder, fm.lizhi.ocean.seal.protocol.GameChannelServiceProto.ChannelTokenOrBuilder>(
                  token_,
                  getParentForChildren(),
                  isClean());
          token_ = null;
        }
        return tokenBuilder_;
      }

      // @@protoc_insertion_point(builder_scope:fm.lizhi.commons.template.datacenter.protocol.ResponseGetChannelToken)
    }

    static {
      defaultInstance = new ResponseGetChannelToken(true);
      defaultInstance.initFields();
    }

    // @@protoc_insertion_point(class_scope:fm.lizhi.commons.template.datacenter.protocol.ResponseGetChannelToken)
  }

  private static com.google.protobuf.Descriptors.Descriptor
    internal_static_fm_lizhi_commons_template_datacenter_protocol_GameChannel_descriptor;
  private static
    com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internal_static_fm_lizhi_commons_template_datacenter_protocol_GameChannel_fieldAccessorTable;
  private static com.google.protobuf.Descriptors.Descriptor
    internal_static_fm_lizhi_commons_template_datacenter_protocol_EnumGameChannel_descriptor;
  private static
    com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internal_static_fm_lizhi_commons_template_datacenter_protocol_EnumGameChannel_fieldAccessorTable;
  private static com.google.protobuf.Descriptors.Descriptor
    internal_static_fm_lizhi_commons_template_datacenter_protocol_RequestGetAllGameChannels_descriptor;
  private static
    com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internal_static_fm_lizhi_commons_template_datacenter_protocol_RequestGetAllGameChannels_fieldAccessorTable;
  private static com.google.protobuf.Descriptors.Descriptor
    internal_static_fm_lizhi_commons_template_datacenter_protocol_ResponseGetAllGameChannels_descriptor;
  private static
    com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internal_static_fm_lizhi_commons_template_datacenter_protocol_ResponseGetAllGameChannels_fieldAccessorTable;
  private static com.google.protobuf.Descriptors.Descriptor
    internal_static_fm_lizhi_commons_template_datacenter_protocol_RequestGetEnumGameChannels_descriptor;
  private static
    com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internal_static_fm_lizhi_commons_template_datacenter_protocol_RequestGetEnumGameChannels_fieldAccessorTable;
  private static com.google.protobuf.Descriptors.Descriptor
    internal_static_fm_lizhi_commons_template_datacenter_protocol_ResponseGetEnumGameChannels_descriptor;
  private static
    com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internal_static_fm_lizhi_commons_template_datacenter_protocol_ResponseGetEnumGameChannels_fieldAccessorTable;
  private static com.google.protobuf.Descriptors.Descriptor
    internal_static_fm_lizhi_commons_template_datacenter_protocol_GetChannelTokenParam_descriptor;
  private static
    com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internal_static_fm_lizhi_commons_template_datacenter_protocol_GetChannelTokenParam_fieldAccessorTable;
  private static com.google.protobuf.Descriptors.Descriptor
    internal_static_fm_lizhi_commons_template_datacenter_protocol_ChannelToken_descriptor;
  private static
    com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internal_static_fm_lizhi_commons_template_datacenter_protocol_ChannelToken_fieldAccessorTable;
  private static com.google.protobuf.Descriptors.Descriptor
    internal_static_fm_lizhi_commons_template_datacenter_protocol_RequestGetChannelToken_descriptor;
  private static
    com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internal_static_fm_lizhi_commons_template_datacenter_protocol_RequestGetChannelToken_fieldAccessorTable;
  private static com.google.protobuf.Descriptors.Descriptor
    internal_static_fm_lizhi_commons_template_datacenter_protocol_ResponseGetChannelToken_descriptor;
  private static
    com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internal_static_fm_lizhi_commons_template_datacenter_protocol_ResponseGetChannelToken_fieldAccessorTable;

  public static com.google.protobuf.Descriptors.FileDescriptor
      getDescriptor() {
    return descriptor;
  }
  private static com.google.protobuf.Descriptors.FileDescriptor
      descriptor;
  static {
    java.lang.String[] descriptorData = {
      "\n\033protocol_game_channel.proto\022-fm.lizhi." +
      "commons.template.datacenter.protocol\"D\n\013" +
      "GameChannel\022\n\n\002id\030\001 \001(\003\022\024\n\014abbreviation\030" +
      "\002 \001(\t\022\023\n\013channelName\030\003 \001(\t\"=\n\017EnumGameCh" +
      "annel\022\024\n\014labelChannel\030\001 \001(\t\022\024\n\014valueChan" +
      "nel\030\002 \001(\t\"\033\n\031RequestGetAllGameChannels\"n" +
      "\n\032ResponseGetAllGameChannels\022P\n\014gameChan" +
      "nels\030\001 \003(\0132:.fm.lizhi.commons.template.d" +
      "atacenter.protocol.GameChannel\"\034\n\032Reques" +
      "tGetEnumGameChannels\"v\n\033ResponseGetEnumG",
      "ameChannels\022W\n\017enumGameChannel\030\001 \003(\0132>.f" +
      "m.lizhi.commons.template.datacenter.prot" +
      "ocol.EnumGameChannel\"F\n\024GetChannelTokenP" +
      "aram\022\016\n\006userId\030\001 \002(\003\022\r\n\005appId\030\002 \002(\t\022\017\n\007c" +
      "hannel\030\003 \002(\t\"1\n\014ChannelToken\022\r\n\005token\030\001 " +
      "\002(\t\022\022\n\nexpireTime\030\002 \002(\003\"l\n\026RequestGetCha" +
      "nnelToken\022R\n\005param\030\001 \002(\0132C.fm.lizhi.comm" +
      "ons.template.datacenter.protocol.GetChan" +
      "nelTokenParam\"e\n\027ResponseGetChannelToken" +
      "\022J\n\005token\030\001 \002(\0132;.fm.lizhi.commons.templ",
      "ate.datacenter.protocol.ChannelTokenB7\n\034" +
      "fm.lizhi.ocean.seal.protocolB\027GameChanne" +
      "lServiceProto"
    };
    com.google.protobuf.Descriptors.FileDescriptor.InternalDescriptorAssigner assigner =
      new com.google.protobuf.Descriptors.FileDescriptor.InternalDescriptorAssigner() {
        public com.google.protobuf.ExtensionRegistry assignDescriptors(
            com.google.protobuf.Descriptors.FileDescriptor root) {
          descriptor = root;
          internal_static_fm_lizhi_commons_template_datacenter_protocol_GameChannel_descriptor =
            getDescriptor().getMessageTypes().get(0);
          internal_static_fm_lizhi_commons_template_datacenter_protocol_GameChannel_fieldAccessorTable = new
            com.google.protobuf.GeneratedMessage.FieldAccessorTable(
              internal_static_fm_lizhi_commons_template_datacenter_protocol_GameChannel_descriptor,
              new java.lang.String[] { "Id", "Abbreviation", "ChannelName", });
          internal_static_fm_lizhi_commons_template_datacenter_protocol_EnumGameChannel_descriptor =
            getDescriptor().getMessageTypes().get(1);
          internal_static_fm_lizhi_commons_template_datacenter_protocol_EnumGameChannel_fieldAccessorTable = new
            com.google.protobuf.GeneratedMessage.FieldAccessorTable(
              internal_static_fm_lizhi_commons_template_datacenter_protocol_EnumGameChannel_descriptor,
              new java.lang.String[] { "LabelChannel", "ValueChannel", });
          internal_static_fm_lizhi_commons_template_datacenter_protocol_RequestGetAllGameChannels_descriptor =
            getDescriptor().getMessageTypes().get(2);
          internal_static_fm_lizhi_commons_template_datacenter_protocol_RequestGetAllGameChannels_fieldAccessorTable = new
            com.google.protobuf.GeneratedMessage.FieldAccessorTable(
              internal_static_fm_lizhi_commons_template_datacenter_protocol_RequestGetAllGameChannels_descriptor,
              new java.lang.String[] { });
          internal_static_fm_lizhi_commons_template_datacenter_protocol_ResponseGetAllGameChannels_descriptor =
            getDescriptor().getMessageTypes().get(3);
          internal_static_fm_lizhi_commons_template_datacenter_protocol_ResponseGetAllGameChannels_fieldAccessorTable = new
            com.google.protobuf.GeneratedMessage.FieldAccessorTable(
              internal_static_fm_lizhi_commons_template_datacenter_protocol_ResponseGetAllGameChannels_descriptor,
              new java.lang.String[] { "GameChannels", });
          internal_static_fm_lizhi_commons_template_datacenter_protocol_RequestGetEnumGameChannels_descriptor =
            getDescriptor().getMessageTypes().get(4);
          internal_static_fm_lizhi_commons_template_datacenter_protocol_RequestGetEnumGameChannels_fieldAccessorTable = new
            com.google.protobuf.GeneratedMessage.FieldAccessorTable(
              internal_static_fm_lizhi_commons_template_datacenter_protocol_RequestGetEnumGameChannels_descriptor,
              new java.lang.String[] { });
          internal_static_fm_lizhi_commons_template_datacenter_protocol_ResponseGetEnumGameChannels_descriptor =
            getDescriptor().getMessageTypes().get(5);
          internal_static_fm_lizhi_commons_template_datacenter_protocol_ResponseGetEnumGameChannels_fieldAccessorTable = new
            com.google.protobuf.GeneratedMessage.FieldAccessorTable(
              internal_static_fm_lizhi_commons_template_datacenter_protocol_ResponseGetEnumGameChannels_descriptor,
              new java.lang.String[] { "EnumGameChannel", });
          internal_static_fm_lizhi_commons_template_datacenter_protocol_GetChannelTokenParam_descriptor =
            getDescriptor().getMessageTypes().get(6);
          internal_static_fm_lizhi_commons_template_datacenter_protocol_GetChannelTokenParam_fieldAccessorTable = new
            com.google.protobuf.GeneratedMessage.FieldAccessorTable(
              internal_static_fm_lizhi_commons_template_datacenter_protocol_GetChannelTokenParam_descriptor,
              new java.lang.String[] { "UserId", "AppId", "Channel", });
          internal_static_fm_lizhi_commons_template_datacenter_protocol_ChannelToken_descriptor =
            getDescriptor().getMessageTypes().get(7);
          internal_static_fm_lizhi_commons_template_datacenter_protocol_ChannelToken_fieldAccessorTable = new
            com.google.protobuf.GeneratedMessage.FieldAccessorTable(
              internal_static_fm_lizhi_commons_template_datacenter_protocol_ChannelToken_descriptor,
              new java.lang.String[] { "Token", "ExpireTime", });
          internal_static_fm_lizhi_commons_template_datacenter_protocol_RequestGetChannelToken_descriptor =
            getDescriptor().getMessageTypes().get(8);
          internal_static_fm_lizhi_commons_template_datacenter_protocol_RequestGetChannelToken_fieldAccessorTable = new
            com.google.protobuf.GeneratedMessage.FieldAccessorTable(
              internal_static_fm_lizhi_commons_template_datacenter_protocol_RequestGetChannelToken_descriptor,
              new java.lang.String[] { "Param", });
          internal_static_fm_lizhi_commons_template_datacenter_protocol_ResponseGetChannelToken_descriptor =
            getDescriptor().getMessageTypes().get(9);
          internal_static_fm_lizhi_commons_template_datacenter_protocol_ResponseGetChannelToken_fieldAccessorTable = new
            com.google.protobuf.GeneratedMessage.FieldAccessorTable(
              internal_static_fm_lizhi_commons_template_datacenter_protocol_ResponseGetChannelToken_descriptor,
              new java.lang.String[] { "Token", });
          return null;
        }
      };
    com.google.protobuf.Descriptors.FileDescriptor
      .internalBuildGeneratedFileFrom(descriptorData,
        new com.google.protobuf.Descriptors.FileDescriptor[] {
        }, assigner);
  }

  // @@protoc_insertion_point(outer_class_scope)
}
