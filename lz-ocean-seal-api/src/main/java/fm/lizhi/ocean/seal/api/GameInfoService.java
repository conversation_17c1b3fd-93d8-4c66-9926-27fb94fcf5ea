package fm.lizhi.ocean.seal.api;

import java.util.List;
import fm.lizhi.commons.service.client.annotation.Attribute;
import fm.lizhi.commons.service.client.annotation.Return;
import fm.lizhi.commons.service.client.annotation.Service;
import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.ocean.seal.protocol.GameInfoServiceProto.RequestGetGameList;
import fm.lizhi.ocean.seal.protocol.GameInfoServiceProto.ResponseGetGameList;
import fm.lizhi.ocean.seal.protocol.GameInfoServiceProto.RequestGetGameInfo;
import fm.lizhi.ocean.seal.protocol.GameInfoServiceProto.ResponseGetGameInfo;
import fm.lizhi.ocean.seal.protocol.GameInfoServiceProto.RequestGetGameDetails;
import fm.lizhi.ocean.seal.protocol.GameInfoServiceProto.ResponseGetGameDetails;
import fm.lizhi.ocean.seal.protocol.GameInfoServiceProto.RequestGetGameDetail;
import fm.lizhi.ocean.seal.protocol.GameInfoServiceProto.ResponseGetGameDetail;
import fm.lizhi.ocean.seal.protocol.GameInfoServiceProto.RequestAddGameDetail;
import fm.lizhi.ocean.seal.protocol.GameInfoServiceProto.ResponseAddGameDetail;
import fm.lizhi.ocean.seal.protocol.GameInfoServiceProto.RequestModifyGameDetail;
import fm.lizhi.ocean.seal.protocol.GameInfoServiceProto.RequestDelGameDetail;
import fm.lizhi.ocean.seal.protocol.GameInfoServiceProto.GameDetail;
import fm.lizhi.ocean.seal.protocol.GameInfoServiceProto.GetGameDetailsParam;
import fm.lizhi.ocean.seal.protocol.GameInfoServiceProto.GameInfoList;
import fm.lizhi.ocean.seal.protocol.GameInfoServiceProto.GameInfo;
import fm.lizhi.ocean.seal.protocol.GameInfoServiceProto.GetGameListParam;
import fm.lizhi.ocean.seal.protocol.GameInfoServiceProto.GetGameInfoParam;

/**
 * this file generated by autoapi-maven-plugin
 * 
 * do not edit this file manually, because this file is covered when you run autoapi-maven-plugin
 * 
 */

public interface GameInfoService {
	
	
	/**
	 *  获取游戏列表
	 *
	 * @param param
	 *            参数
	 * @return 
	 *     //if rcode == 0 执行成功<br>
	 *     //if rcode == 1 参数错误<br>
	 *     //if rcode == 2 错误<br>
	 */
	@Service(domain = 4302, op = 16, request = RequestGetGameList.class, response = ResponseGetGameList.class)
	@Return(resultType = ResponseGetGameList.class)
	Result<ResponseGetGameList> getGameList(@Attribute(name = "param") GetGameListParam param);
	
	
	/**
	 *  获取游戏信息
	 *
	 * @param param
	 *            参数
	 * @return 
	 */
	@Service(domain = 4302, op = 17, request = RequestGetGameInfo.class, response = ResponseGetGameInfo.class)
	@Return(resultType = ResponseGetGameInfo.class)
	Result<ResponseGetGameInfo> getGameInfo(@Attribute(name = "param") GetGameInfoParam param);
	
	
	/**
	 *  获取游戏详细信息的列表，包含游戏类型、语言、付费方式、渠道
	 *
	 * @param param
	 *            
	 * @return 
	 *     //if rcode == 0 执行成功<br>
	 *     //if rcode == 1 失败<br>
	 */
	@Service(domain = 4302, op = 18, request = RequestGetGameDetails.class, response = ResponseGetGameDetails.class)
	@Return(resultType = ResponseGetGameDetails.class)
	Result<ResponseGetGameDetails> getGameDetails(@Attribute(name = "param") GetGameDetailsParam param);
	
	
	/**
	 *  获取游戏详细信息，包含游戏类型、语言、付费方式、渠道
	 *
	 * @param id
	 *            
	 * @return 
	 *     //if rcode == 0 执行成功<br>
	 *     //if rcode == 1 失败<br>
	 */
	@Service(domain = 4302, op = 19, request = RequestGetGameDetail.class, response = ResponseGetGameDetail.class)
	@Return(resultType = ResponseGetGameDetail.class)
	Result<ResponseGetGameDetail> getGameDetail(@Attribute(name = "id") long id);
	
	
	/**
	 *  新增游戏详细信息，包含游戏类型、语言、付费方式、渠道
	 *
	 * @param gameDetail
	 *            
	 * @return 
	 *     //if rcode == 0 执行成功<br>
	 *     //if rcode == 1 失败<br>
	 */
	@Service(domain = 4302, op = 20, request = RequestAddGameDetail.class, response = ResponseAddGameDetail.class)
	@Return(resultType = ResponseAddGameDetail.class)
	Result<ResponseAddGameDetail> addGameDetail(@Attribute(name = "gameDetail") GameDetail gameDetail);
	
	
	/**
	 *  修改游戏详细信息，包含游戏类型、语言、付费方式、渠道
	 *
	 * @param gameDetail
	 *            
	 * @return 
	 */
	@Service(domain = 4302, op = 21, request = RequestModifyGameDetail.class)
	@Return(resultType = Void.class)
	Result<Void> modifyGameDetail(@Attribute(name = "gameDetail") GameDetail gameDetail);
	
	
	/**
	 *  删除游戏详细信息，包含游戏类型、语言、付费方式、渠道
	 *
	 * @param id
	 *            
	 * @param operator
	 *            
	 * @return 
	 */
	@Service(domain = 4302, op = 22, request = RequestDelGameDetail.class)
	@Return(resultType = Void.class)
	Result<Void> delGameDetail(@Attribute(name = "id") long id, @Attribute(name = "operator") String operator);
	
	
	public static final int GET_GAME_LIST_SUCCESS = 0; // 执行成功
	public static final int GET_GAME_LIST_PARAM_FAIL = 1; // 参数错误
	public static final int GET_GAME_LIST_FAIL = 2; // 错误

	public static final int GET_GAME_DETAILS_SUCCESS = 0; // 执行成功
	public static final int GET_GAME_DETAILS_FAIL = 1; // 失败

	public static final int GET_GAME_DETAIL_SUCCESS = 0; // 执行成功
	public static final int GET_GAME_DETAIL_FAIL = 1; // 失败

	public static final int ADD_GAME_DETAIL_SUCCESS = 0; // 执行成功
	public static final int ADD_GAME_DETAIL_FAIL = 1; // 失败


}