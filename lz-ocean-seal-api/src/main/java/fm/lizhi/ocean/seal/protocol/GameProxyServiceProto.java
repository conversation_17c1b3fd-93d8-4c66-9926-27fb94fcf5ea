// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: protocol_game_proxy.proto

package fm.lizhi.ocean.seal.protocol;

public final class GameProxyServiceProto {
  private GameProxyServiceProto() {}
  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistry registry) {
  }
  public interface InvokeTargetParamsOrBuilder
      extends com.google.protobuf.MessageOrBuilder {

    // required string eventName = 1;
    /**
     * <code>required string eventName = 1;</code>
     *
     * <pre>
     * 事件名，对应SUD的event参数
     * </pre>
     */
    boolean hasEventName();
    /**
     * <code>required string eventName = 1;</code>
     *
     * <pre>
     * 事件名，对应SUD的event参数
     * </pre>
     */
    java.lang.String getEventName();
    /**
     * <code>required string eventName = 1;</code>
     *
     * <pre>
     * 事件名，对应SUD的event参数
     * </pre>
     */
    com.google.protobuf.ByteString
        getEventNameBytes();

    // required string channel = 2;
    /**
     * <code>required string channel = 2;</code>
     *
     * <pre>
     * 渠道(sud)
     * </pre>
     */
    boolean hasChannel();
    /**
     * <code>required string channel = 2;</code>
     *
     * <pre>
     * 渠道(sud)
     * </pre>
     */
    java.lang.String getChannel();
    /**
     * <code>required string channel = 2;</code>
     *
     * <pre>
     * 渠道(sud)
     * </pre>
     */
    com.google.protobuf.ByteString
        getChannelBytes();

    // required int32 env = 3;
    /**
     * <code>required int32 env = 3;</code>
     *
     * <pre>
     *环境
     * </pre>
     */
    boolean hasEnv();
    /**
     * <code>required int32 env = 3;</code>
     *
     * <pre>
     *环境
     * </pre>
     */
    int getEnv();

    // required string appId = 4;
    /**
     * <code>required string appId = 4;</code>
     *
     * <pre>
     * appId，Seal服务端根据appId配置业务服务端域名
     * </pre>
     */
    boolean hasAppId();
    /**
     * <code>required string appId = 4;</code>
     *
     * <pre>
     * appId，Seal服务端根据appId配置业务服务端域名
     * </pre>
     */
    java.lang.String getAppId();
    /**
     * <code>required string appId = 4;</code>
     *
     * <pre>
     * appId，Seal服务端根据appId配置业务服务端域名
     * </pre>
     */
    com.google.protobuf.ByteString
        getAppIdBytes();

    // required string gameId = 5;
    /**
     * <code>required string gameId = 5;</code>
     *
     * <pre>
     * 渠道游戏ID
     * </pre>
     */
    boolean hasGameId();
    /**
     * <code>required string gameId = 5;</code>
     *
     * <pre>
     * 渠道游戏ID
     * </pre>
     */
    java.lang.String getGameId();
    /**
     * <code>required string gameId = 5;</code>
     *
     * <pre>
     * 渠道游戏ID
     * </pre>
     */
    com.google.protobuf.ByteString
        getGameIdBytes();

    // required string dataJson = 6;
    /**
     * <code>required string dataJson = 6;</code>
     *
     * <pre>
     * 转发到渠道的参数
     * </pre>
     */
    boolean hasDataJson();
    /**
     * <code>required string dataJson = 6;</code>
     *
     * <pre>
     * 转发到渠道的参数
     * </pre>
     */
    java.lang.String getDataJson();
    /**
     * <code>required string dataJson = 6;</code>
     *
     * <pre>
     * 转发到渠道的参数
     * </pre>
     */
    com.google.protobuf.ByteString
        getDataJsonBytes();

    // optional string roomId = 7;
    /**
     * <code>optional string roomId = 7;</code>
     *
     * <pre>
     * 房间ID
     * </pre>
     */
    boolean hasRoomId();
    /**
     * <code>optional string roomId = 7;</code>
     *
     * <pre>
     * 房间ID
     * </pre>
     */
    java.lang.String getRoomId();
    /**
     * <code>optional string roomId = 7;</code>
     *
     * <pre>
     * 房间ID
     * </pre>
     */
    com.google.protobuf.ByteString
        getRoomIdBytes();
  }
  /**
   * Protobuf type {@code fm.lizhi.commons.template.datacenter.protocol.InvokeTargetParams}
   *
   * <pre>
   *调用目标接口的参数
   * </pre>
   */
  public static final class InvokeTargetParams extends
      com.google.protobuf.GeneratedMessage
      implements InvokeTargetParamsOrBuilder {
    // Use InvokeTargetParams.newBuilder() to construct.
    private InvokeTargetParams(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
      super(builder);
      this.unknownFields = builder.getUnknownFields();
    }
    private InvokeTargetParams(boolean noInit) { this.unknownFields = com.google.protobuf.UnknownFieldSet.getDefaultInstance(); }

    private static final InvokeTargetParams defaultInstance;
    public static InvokeTargetParams getDefaultInstance() {
      return defaultInstance;
    }

    public InvokeTargetParams getDefaultInstanceForType() {
      return defaultInstance;
    }

    private final com.google.protobuf.UnknownFieldSet unknownFields;
    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
        getUnknownFields() {
      return this.unknownFields;
    }
    private InvokeTargetParams(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      initFields();
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            default: {
              if (!parseUnknownField(input, unknownFields,
                                     extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
            case 10: {
              bitField0_ |= 0x00000001;
              eventName_ = input.readBytes();
              break;
            }
            case 18: {
              bitField0_ |= 0x00000002;
              channel_ = input.readBytes();
              break;
            }
            case 24: {
              bitField0_ |= 0x00000004;
              env_ = input.readInt32();
              break;
            }
            case 34: {
              bitField0_ |= 0x00000008;
              appId_ = input.readBytes();
              break;
            }
            case 42: {
              bitField0_ |= 0x00000010;
              gameId_ = input.readBytes();
              break;
            }
            case 50: {
              bitField0_ |= 0x00000020;
              dataJson_ = input.readBytes();
              break;
            }
            case 58: {
              bitField0_ |= 0x00000040;
              roomId_ = input.readBytes();
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e.getMessage()).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return fm.lizhi.ocean.seal.protocol.GameProxyServiceProto.internal_static_fm_lizhi_commons_template_datacenter_protocol_InvokeTargetParams_descriptor;
    }

    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return fm.lizhi.ocean.seal.protocol.GameProxyServiceProto.internal_static_fm_lizhi_commons_template_datacenter_protocol_InvokeTargetParams_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              fm.lizhi.ocean.seal.protocol.GameProxyServiceProto.InvokeTargetParams.class, fm.lizhi.ocean.seal.protocol.GameProxyServiceProto.InvokeTargetParams.Builder.class);
    }

    public static com.google.protobuf.Parser<InvokeTargetParams> PARSER =
        new com.google.protobuf.AbstractParser<InvokeTargetParams>() {
      public InvokeTargetParams parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new InvokeTargetParams(input, extensionRegistry);
      }
    };

    @java.lang.Override
    public com.google.protobuf.Parser<InvokeTargetParams> getParserForType() {
      return PARSER;
    }

    private int bitField0_;
    // required string eventName = 1;
    public static final int EVENTNAME_FIELD_NUMBER = 1;
    private java.lang.Object eventName_;
    /**
     * <code>required string eventName = 1;</code>
     *
     * <pre>
     * 事件名，对应SUD的event参数
     * </pre>
     */
    public boolean hasEventName() {
      return ((bitField0_ & 0x00000001) == 0x00000001);
    }
    /**
     * <code>required string eventName = 1;</code>
     *
     * <pre>
     * 事件名，对应SUD的event参数
     * </pre>
     */
    public java.lang.String getEventName() {
      java.lang.Object ref = eventName_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          eventName_ = s;
        }
        return s;
      }
    }
    /**
     * <code>required string eventName = 1;</code>
     *
     * <pre>
     * 事件名，对应SUD的event参数
     * </pre>
     */
    public com.google.protobuf.ByteString
        getEventNameBytes() {
      java.lang.Object ref = eventName_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        eventName_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    // required string channel = 2;
    public static final int CHANNEL_FIELD_NUMBER = 2;
    private java.lang.Object channel_;
    /**
     * <code>required string channel = 2;</code>
     *
     * <pre>
     * 渠道(sud)
     * </pre>
     */
    public boolean hasChannel() {
      return ((bitField0_ & 0x00000002) == 0x00000002);
    }
    /**
     * <code>required string channel = 2;</code>
     *
     * <pre>
     * 渠道(sud)
     * </pre>
     */
    public java.lang.String getChannel() {
      java.lang.Object ref = channel_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          channel_ = s;
        }
        return s;
      }
    }
    /**
     * <code>required string channel = 2;</code>
     *
     * <pre>
     * 渠道(sud)
     * </pre>
     */
    public com.google.protobuf.ByteString
        getChannelBytes() {
      java.lang.Object ref = channel_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        channel_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    // required int32 env = 3;
    public static final int ENV_FIELD_NUMBER = 3;
    private int env_;
    /**
     * <code>required int32 env = 3;</code>
     *
     * <pre>
     *环境
     * </pre>
     */
    public boolean hasEnv() {
      return ((bitField0_ & 0x00000004) == 0x00000004);
    }
    /**
     * <code>required int32 env = 3;</code>
     *
     * <pre>
     *环境
     * </pre>
     */
    public int getEnv() {
      return env_;
    }

    // required string appId = 4;
    public static final int APPID_FIELD_NUMBER = 4;
    private java.lang.Object appId_;
    /**
     * <code>required string appId = 4;</code>
     *
     * <pre>
     * appId，Seal服务端根据appId配置业务服务端域名
     * </pre>
     */
    public boolean hasAppId() {
      return ((bitField0_ & 0x00000008) == 0x00000008);
    }
    /**
     * <code>required string appId = 4;</code>
     *
     * <pre>
     * appId，Seal服务端根据appId配置业务服务端域名
     * </pre>
     */
    public java.lang.String getAppId() {
      java.lang.Object ref = appId_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          appId_ = s;
        }
        return s;
      }
    }
    /**
     * <code>required string appId = 4;</code>
     *
     * <pre>
     * appId，Seal服务端根据appId配置业务服务端域名
     * </pre>
     */
    public com.google.protobuf.ByteString
        getAppIdBytes() {
      java.lang.Object ref = appId_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        appId_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    // required string gameId = 5;
    public static final int GAMEID_FIELD_NUMBER = 5;
    private java.lang.Object gameId_;
    /**
     * <code>required string gameId = 5;</code>
     *
     * <pre>
     * 渠道游戏ID
     * </pre>
     */
    public boolean hasGameId() {
      return ((bitField0_ & 0x00000010) == 0x00000010);
    }
    /**
     * <code>required string gameId = 5;</code>
     *
     * <pre>
     * 渠道游戏ID
     * </pre>
     */
    public java.lang.String getGameId() {
      java.lang.Object ref = gameId_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          gameId_ = s;
        }
        return s;
      }
    }
    /**
     * <code>required string gameId = 5;</code>
     *
     * <pre>
     * 渠道游戏ID
     * </pre>
     */
    public com.google.protobuf.ByteString
        getGameIdBytes() {
      java.lang.Object ref = gameId_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        gameId_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    // required string dataJson = 6;
    public static final int DATAJSON_FIELD_NUMBER = 6;
    private java.lang.Object dataJson_;
    /**
     * <code>required string dataJson = 6;</code>
     *
     * <pre>
     * 转发到渠道的参数
     * </pre>
     */
    public boolean hasDataJson() {
      return ((bitField0_ & 0x00000020) == 0x00000020);
    }
    /**
     * <code>required string dataJson = 6;</code>
     *
     * <pre>
     * 转发到渠道的参数
     * </pre>
     */
    public java.lang.String getDataJson() {
      java.lang.Object ref = dataJson_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          dataJson_ = s;
        }
        return s;
      }
    }
    /**
     * <code>required string dataJson = 6;</code>
     *
     * <pre>
     * 转发到渠道的参数
     * </pre>
     */
    public com.google.protobuf.ByteString
        getDataJsonBytes() {
      java.lang.Object ref = dataJson_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        dataJson_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    // optional string roomId = 7;
    public static final int ROOMID_FIELD_NUMBER = 7;
    private java.lang.Object roomId_;
    /**
     * <code>optional string roomId = 7;</code>
     *
     * <pre>
     * 房间ID
     * </pre>
     */
    public boolean hasRoomId() {
      return ((bitField0_ & 0x00000040) == 0x00000040);
    }
    /**
     * <code>optional string roomId = 7;</code>
     *
     * <pre>
     * 房间ID
     * </pre>
     */
    public java.lang.String getRoomId() {
      java.lang.Object ref = roomId_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          roomId_ = s;
        }
        return s;
      }
    }
    /**
     * <code>optional string roomId = 7;</code>
     *
     * <pre>
     * 房间ID
     * </pre>
     */
    public com.google.protobuf.ByteString
        getRoomIdBytes() {
      java.lang.Object ref = roomId_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        roomId_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    private void initFields() {
      eventName_ = "";
      channel_ = "";
      env_ = 0;
      appId_ = "";
      gameId_ = "";
      dataJson_ = "";
      roomId_ = "";
    }
    private byte memoizedIsInitialized = -1;
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized != -1) return isInitialized == 1;

      if (!hasEventName()) {
        memoizedIsInitialized = 0;
        return false;
      }
      if (!hasChannel()) {
        memoizedIsInitialized = 0;
        return false;
      }
      if (!hasEnv()) {
        memoizedIsInitialized = 0;
        return false;
      }
      if (!hasAppId()) {
        memoizedIsInitialized = 0;
        return false;
      }
      if (!hasGameId()) {
        memoizedIsInitialized = 0;
        return false;
      }
      if (!hasDataJson()) {
        memoizedIsInitialized = 0;
        return false;
      }
      memoizedIsInitialized = 1;
      return true;
    }

    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      getSerializedSize();
      if (((bitField0_ & 0x00000001) == 0x00000001)) {
        output.writeBytes(1, getEventNameBytes());
      }
      if (((bitField0_ & 0x00000002) == 0x00000002)) {
        output.writeBytes(2, getChannelBytes());
      }
      if (((bitField0_ & 0x00000004) == 0x00000004)) {
        output.writeInt32(3, env_);
      }
      if (((bitField0_ & 0x00000008) == 0x00000008)) {
        output.writeBytes(4, getAppIdBytes());
      }
      if (((bitField0_ & 0x00000010) == 0x00000010)) {
        output.writeBytes(5, getGameIdBytes());
      }
      if (((bitField0_ & 0x00000020) == 0x00000020)) {
        output.writeBytes(6, getDataJsonBytes());
      }
      if (((bitField0_ & 0x00000040) == 0x00000040)) {
        output.writeBytes(7, getRoomIdBytes());
      }
      getUnknownFields().writeTo(output);
    }

    private int memoizedSerializedSize = -1;
    public int getSerializedSize() {
      int size = memoizedSerializedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) == 0x00000001)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(1, getEventNameBytes());
      }
      if (((bitField0_ & 0x00000002) == 0x00000002)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(2, getChannelBytes());
      }
      if (((bitField0_ & 0x00000004) == 0x00000004)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(3, env_);
      }
      if (((bitField0_ & 0x00000008) == 0x00000008)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(4, getAppIdBytes());
      }
      if (((bitField0_ & 0x00000010) == 0x00000010)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(5, getGameIdBytes());
      }
      if (((bitField0_ & 0x00000020) == 0x00000020)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(6, getDataJsonBytes());
      }
      if (((bitField0_ & 0x00000040) == 0x00000040)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(7, getRoomIdBytes());
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSerializedSize = size;
      return size;
    }

    private static final long serialVersionUID = 0L;
    @java.lang.Override
    protected java.lang.Object writeReplace()
        throws java.io.ObjectStreamException {
      return super.writeReplace();
    }

    public static fm.lizhi.ocean.seal.protocol.GameProxyServiceProto.InvokeTargetParams parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static fm.lizhi.ocean.seal.protocol.GameProxyServiceProto.InvokeTargetParams parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static fm.lizhi.ocean.seal.protocol.GameProxyServiceProto.InvokeTargetParams parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static fm.lizhi.ocean.seal.protocol.GameProxyServiceProto.InvokeTargetParams parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static fm.lizhi.ocean.seal.protocol.GameProxyServiceProto.InvokeTargetParams parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return PARSER.parseFrom(input);
    }
    public static fm.lizhi.ocean.seal.protocol.GameProxyServiceProto.InvokeTargetParams parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseFrom(input, extensionRegistry);
    }
    public static fm.lizhi.ocean.seal.protocol.GameProxyServiceProto.InvokeTargetParams parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return PARSER.parseDelimitedFrom(input);
    }
    public static fm.lizhi.ocean.seal.protocol.GameProxyServiceProto.InvokeTargetParams parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseDelimitedFrom(input, extensionRegistry);
    }
    public static fm.lizhi.ocean.seal.protocol.GameProxyServiceProto.InvokeTargetParams parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return PARSER.parseFrom(input);
    }
    public static fm.lizhi.ocean.seal.protocol.GameProxyServiceProto.InvokeTargetParams parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseFrom(input, extensionRegistry);
    }

    public static Builder newBuilder() { return Builder.create(); }
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder(fm.lizhi.ocean.seal.protocol.GameProxyServiceProto.InvokeTargetParams prototype) {
      return newBuilder().mergeFrom(prototype);
    }
    public Builder toBuilder() { return newBuilder(this); }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code fm.lizhi.commons.template.datacenter.protocol.InvokeTargetParams}
     *
     * <pre>
     *调用目标接口的参数
     * </pre>
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessage.Builder<Builder>
       implements fm.lizhi.ocean.seal.protocol.GameProxyServiceProto.InvokeTargetParamsOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return fm.lizhi.ocean.seal.protocol.GameProxyServiceProto.internal_static_fm_lizhi_commons_template_datacenter_protocol_InvokeTargetParams_descriptor;
      }

      protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return fm.lizhi.ocean.seal.protocol.GameProxyServiceProto.internal_static_fm_lizhi_commons_template_datacenter_protocol_InvokeTargetParams_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                fm.lizhi.ocean.seal.protocol.GameProxyServiceProto.InvokeTargetParams.class, fm.lizhi.ocean.seal.protocol.GameProxyServiceProto.InvokeTargetParams.Builder.class);
      }

      // Construct using fm.lizhi.ocean.seal.protocol.GameProxyServiceProto.InvokeTargetParams.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessage.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessage.alwaysUseFieldBuilders) {
        }
      }
      private static Builder create() {
        return new Builder();
      }

      public Builder clear() {
        super.clear();
        eventName_ = "";
        bitField0_ = (bitField0_ & ~0x00000001);
        channel_ = "";
        bitField0_ = (bitField0_ & ~0x00000002);
        env_ = 0;
        bitField0_ = (bitField0_ & ~0x00000004);
        appId_ = "";
        bitField0_ = (bitField0_ & ~0x00000008);
        gameId_ = "";
        bitField0_ = (bitField0_ & ~0x00000010);
        dataJson_ = "";
        bitField0_ = (bitField0_ & ~0x00000020);
        roomId_ = "";
        bitField0_ = (bitField0_ & ~0x00000040);
        return this;
      }

      public Builder clone() {
        return create().mergeFrom(buildPartial());
      }

      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return fm.lizhi.ocean.seal.protocol.GameProxyServiceProto.internal_static_fm_lizhi_commons_template_datacenter_protocol_InvokeTargetParams_descriptor;
      }

      public fm.lizhi.ocean.seal.protocol.GameProxyServiceProto.InvokeTargetParams getDefaultInstanceForType() {
        return fm.lizhi.ocean.seal.protocol.GameProxyServiceProto.InvokeTargetParams.getDefaultInstance();
      }

      public fm.lizhi.ocean.seal.protocol.GameProxyServiceProto.InvokeTargetParams build() {
        fm.lizhi.ocean.seal.protocol.GameProxyServiceProto.InvokeTargetParams result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      public fm.lizhi.ocean.seal.protocol.GameProxyServiceProto.InvokeTargetParams buildPartial() {
        fm.lizhi.ocean.seal.protocol.GameProxyServiceProto.InvokeTargetParams result = new fm.lizhi.ocean.seal.protocol.GameProxyServiceProto.InvokeTargetParams(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) == 0x00000001)) {
          to_bitField0_ |= 0x00000001;
        }
        result.eventName_ = eventName_;
        if (((from_bitField0_ & 0x00000002) == 0x00000002)) {
          to_bitField0_ |= 0x00000002;
        }
        result.channel_ = channel_;
        if (((from_bitField0_ & 0x00000004) == 0x00000004)) {
          to_bitField0_ |= 0x00000004;
        }
        result.env_ = env_;
        if (((from_bitField0_ & 0x00000008) == 0x00000008)) {
          to_bitField0_ |= 0x00000008;
        }
        result.appId_ = appId_;
        if (((from_bitField0_ & 0x00000010) == 0x00000010)) {
          to_bitField0_ |= 0x00000010;
        }
        result.gameId_ = gameId_;
        if (((from_bitField0_ & 0x00000020) == 0x00000020)) {
          to_bitField0_ |= 0x00000020;
        }
        result.dataJson_ = dataJson_;
        if (((from_bitField0_ & 0x00000040) == 0x00000040)) {
          to_bitField0_ |= 0x00000040;
        }
        result.roomId_ = roomId_;
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof fm.lizhi.ocean.seal.protocol.GameProxyServiceProto.InvokeTargetParams) {
          return mergeFrom((fm.lizhi.ocean.seal.protocol.GameProxyServiceProto.InvokeTargetParams)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(fm.lizhi.ocean.seal.protocol.GameProxyServiceProto.InvokeTargetParams other) {
        if (other == fm.lizhi.ocean.seal.protocol.GameProxyServiceProto.InvokeTargetParams.getDefaultInstance()) return this;
        if (other.hasEventName()) {
          bitField0_ |= 0x00000001;
          eventName_ = other.eventName_;
          onChanged();
        }
        if (other.hasChannel()) {
          bitField0_ |= 0x00000002;
          channel_ = other.channel_;
          onChanged();
        }
        if (other.hasEnv()) {
          setEnv(other.getEnv());
        }
        if (other.hasAppId()) {
          bitField0_ |= 0x00000008;
          appId_ = other.appId_;
          onChanged();
        }
        if (other.hasGameId()) {
          bitField0_ |= 0x00000010;
          gameId_ = other.gameId_;
          onChanged();
        }
        if (other.hasDataJson()) {
          bitField0_ |= 0x00000020;
          dataJson_ = other.dataJson_;
          onChanged();
        }
        if (other.hasRoomId()) {
          bitField0_ |= 0x00000040;
          roomId_ = other.roomId_;
          onChanged();
        }
        this.mergeUnknownFields(other.getUnknownFields());
        return this;
      }

      public final boolean isInitialized() {
        if (!hasEventName()) {
          
          return false;
        }
        if (!hasChannel()) {
          
          return false;
        }
        if (!hasEnv()) {
          
          return false;
        }
        if (!hasAppId()) {
          
          return false;
        }
        if (!hasGameId()) {
          
          return false;
        }
        if (!hasDataJson()) {
          
          return false;
        }
        return true;
      }

      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        fm.lizhi.ocean.seal.protocol.GameProxyServiceProto.InvokeTargetParams parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (fm.lizhi.ocean.seal.protocol.GameProxyServiceProto.InvokeTargetParams) e.getUnfinishedMessage();
          throw e;
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      // required string eventName = 1;
      private java.lang.Object eventName_ = "";
      /**
       * <code>required string eventName = 1;</code>
       *
       * <pre>
       * 事件名，对应SUD的event参数
       * </pre>
       */
      public boolean hasEventName() {
        return ((bitField0_ & 0x00000001) == 0x00000001);
      }
      /**
       * <code>required string eventName = 1;</code>
       *
       * <pre>
       * 事件名，对应SUD的event参数
       * </pre>
       */
      public java.lang.String getEventName() {
        java.lang.Object ref = eventName_;
        if (!(ref instanceof java.lang.String)) {
          java.lang.String s = ((com.google.protobuf.ByteString) ref)
              .toStringUtf8();
          eventName_ = s;
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <code>required string eventName = 1;</code>
       *
       * <pre>
       * 事件名，对应SUD的event参数
       * </pre>
       */
      public com.google.protobuf.ByteString
          getEventNameBytes() {
        java.lang.Object ref = eventName_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          eventName_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <code>required string eventName = 1;</code>
       *
       * <pre>
       * 事件名，对应SUD的event参数
       * </pre>
       */
      public Builder setEventName(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000001;
        eventName_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>required string eventName = 1;</code>
       *
       * <pre>
       * 事件名，对应SUD的event参数
       * </pre>
       */
      public Builder clearEventName() {
        bitField0_ = (bitField0_ & ~0x00000001);
        eventName_ = getDefaultInstance().getEventName();
        onChanged();
        return this;
      }
      /**
       * <code>required string eventName = 1;</code>
       *
       * <pre>
       * 事件名，对应SUD的event参数
       * </pre>
       */
      public Builder setEventNameBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000001;
        eventName_ = value;
        onChanged();
        return this;
      }

      // required string channel = 2;
      private java.lang.Object channel_ = "";
      /**
       * <code>required string channel = 2;</code>
       *
       * <pre>
       * 渠道(sud)
       * </pre>
       */
      public boolean hasChannel() {
        return ((bitField0_ & 0x00000002) == 0x00000002);
      }
      /**
       * <code>required string channel = 2;</code>
       *
       * <pre>
       * 渠道(sud)
       * </pre>
       */
      public java.lang.String getChannel() {
        java.lang.Object ref = channel_;
        if (!(ref instanceof java.lang.String)) {
          java.lang.String s = ((com.google.protobuf.ByteString) ref)
              .toStringUtf8();
          channel_ = s;
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <code>required string channel = 2;</code>
       *
       * <pre>
       * 渠道(sud)
       * </pre>
       */
      public com.google.protobuf.ByteString
          getChannelBytes() {
        java.lang.Object ref = channel_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          channel_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <code>required string channel = 2;</code>
       *
       * <pre>
       * 渠道(sud)
       * </pre>
       */
      public Builder setChannel(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000002;
        channel_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>required string channel = 2;</code>
       *
       * <pre>
       * 渠道(sud)
       * </pre>
       */
      public Builder clearChannel() {
        bitField0_ = (bitField0_ & ~0x00000002);
        channel_ = getDefaultInstance().getChannel();
        onChanged();
        return this;
      }
      /**
       * <code>required string channel = 2;</code>
       *
       * <pre>
       * 渠道(sud)
       * </pre>
       */
      public Builder setChannelBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000002;
        channel_ = value;
        onChanged();
        return this;
      }

      // required int32 env = 3;
      private int env_ ;
      /**
       * <code>required int32 env = 3;</code>
       *
       * <pre>
       *环境
       * </pre>
       */
      public boolean hasEnv() {
        return ((bitField0_ & 0x00000004) == 0x00000004);
      }
      /**
       * <code>required int32 env = 3;</code>
       *
       * <pre>
       *环境
       * </pre>
       */
      public int getEnv() {
        return env_;
      }
      /**
       * <code>required int32 env = 3;</code>
       *
       * <pre>
       *环境
       * </pre>
       */
      public Builder setEnv(int value) {
        bitField0_ |= 0x00000004;
        env_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>required int32 env = 3;</code>
       *
       * <pre>
       *环境
       * </pre>
       */
      public Builder clearEnv() {
        bitField0_ = (bitField0_ & ~0x00000004);
        env_ = 0;
        onChanged();
        return this;
      }

      // required string appId = 4;
      private java.lang.Object appId_ = "";
      /**
       * <code>required string appId = 4;</code>
       *
       * <pre>
       * appId，Seal服务端根据appId配置业务服务端域名
       * </pre>
       */
      public boolean hasAppId() {
        return ((bitField0_ & 0x00000008) == 0x00000008);
      }
      /**
       * <code>required string appId = 4;</code>
       *
       * <pre>
       * appId，Seal服务端根据appId配置业务服务端域名
       * </pre>
       */
      public java.lang.String getAppId() {
        java.lang.Object ref = appId_;
        if (!(ref instanceof java.lang.String)) {
          java.lang.String s = ((com.google.protobuf.ByteString) ref)
              .toStringUtf8();
          appId_ = s;
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <code>required string appId = 4;</code>
       *
       * <pre>
       * appId，Seal服务端根据appId配置业务服务端域名
       * </pre>
       */
      public com.google.protobuf.ByteString
          getAppIdBytes() {
        java.lang.Object ref = appId_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          appId_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <code>required string appId = 4;</code>
       *
       * <pre>
       * appId，Seal服务端根据appId配置业务服务端域名
       * </pre>
       */
      public Builder setAppId(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000008;
        appId_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>required string appId = 4;</code>
       *
       * <pre>
       * appId，Seal服务端根据appId配置业务服务端域名
       * </pre>
       */
      public Builder clearAppId() {
        bitField0_ = (bitField0_ & ~0x00000008);
        appId_ = getDefaultInstance().getAppId();
        onChanged();
        return this;
      }
      /**
       * <code>required string appId = 4;</code>
       *
       * <pre>
       * appId，Seal服务端根据appId配置业务服务端域名
       * </pre>
       */
      public Builder setAppIdBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000008;
        appId_ = value;
        onChanged();
        return this;
      }

      // required string gameId = 5;
      private java.lang.Object gameId_ = "";
      /**
       * <code>required string gameId = 5;</code>
       *
       * <pre>
       * 渠道游戏ID
       * </pre>
       */
      public boolean hasGameId() {
        return ((bitField0_ & 0x00000010) == 0x00000010);
      }
      /**
       * <code>required string gameId = 5;</code>
       *
       * <pre>
       * 渠道游戏ID
       * </pre>
       */
      public java.lang.String getGameId() {
        java.lang.Object ref = gameId_;
        if (!(ref instanceof java.lang.String)) {
          java.lang.String s = ((com.google.protobuf.ByteString) ref)
              .toStringUtf8();
          gameId_ = s;
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <code>required string gameId = 5;</code>
       *
       * <pre>
       * 渠道游戏ID
       * </pre>
       */
      public com.google.protobuf.ByteString
          getGameIdBytes() {
        java.lang.Object ref = gameId_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          gameId_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <code>required string gameId = 5;</code>
       *
       * <pre>
       * 渠道游戏ID
       * </pre>
       */
      public Builder setGameId(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000010;
        gameId_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>required string gameId = 5;</code>
       *
       * <pre>
       * 渠道游戏ID
       * </pre>
       */
      public Builder clearGameId() {
        bitField0_ = (bitField0_ & ~0x00000010);
        gameId_ = getDefaultInstance().getGameId();
        onChanged();
        return this;
      }
      /**
       * <code>required string gameId = 5;</code>
       *
       * <pre>
       * 渠道游戏ID
       * </pre>
       */
      public Builder setGameIdBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000010;
        gameId_ = value;
        onChanged();
        return this;
      }

      // required string dataJson = 6;
      private java.lang.Object dataJson_ = "";
      /**
       * <code>required string dataJson = 6;</code>
       *
       * <pre>
       * 转发到渠道的参数
       * </pre>
       */
      public boolean hasDataJson() {
        return ((bitField0_ & 0x00000020) == 0x00000020);
      }
      /**
       * <code>required string dataJson = 6;</code>
       *
       * <pre>
       * 转发到渠道的参数
       * </pre>
       */
      public java.lang.String getDataJson() {
        java.lang.Object ref = dataJson_;
        if (!(ref instanceof java.lang.String)) {
          java.lang.String s = ((com.google.protobuf.ByteString) ref)
              .toStringUtf8();
          dataJson_ = s;
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <code>required string dataJson = 6;</code>
       *
       * <pre>
       * 转发到渠道的参数
       * </pre>
       */
      public com.google.protobuf.ByteString
          getDataJsonBytes() {
        java.lang.Object ref = dataJson_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          dataJson_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <code>required string dataJson = 6;</code>
       *
       * <pre>
       * 转发到渠道的参数
       * </pre>
       */
      public Builder setDataJson(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000020;
        dataJson_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>required string dataJson = 6;</code>
       *
       * <pre>
       * 转发到渠道的参数
       * </pre>
       */
      public Builder clearDataJson() {
        bitField0_ = (bitField0_ & ~0x00000020);
        dataJson_ = getDefaultInstance().getDataJson();
        onChanged();
        return this;
      }
      /**
       * <code>required string dataJson = 6;</code>
       *
       * <pre>
       * 转发到渠道的参数
       * </pre>
       */
      public Builder setDataJsonBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000020;
        dataJson_ = value;
        onChanged();
        return this;
      }

      // optional string roomId = 7;
      private java.lang.Object roomId_ = "";
      /**
       * <code>optional string roomId = 7;</code>
       *
       * <pre>
       * 房间ID
       * </pre>
       */
      public boolean hasRoomId() {
        return ((bitField0_ & 0x00000040) == 0x00000040);
      }
      /**
       * <code>optional string roomId = 7;</code>
       *
       * <pre>
       * 房间ID
       * </pre>
       */
      public java.lang.String getRoomId() {
        java.lang.Object ref = roomId_;
        if (!(ref instanceof java.lang.String)) {
          java.lang.String s = ((com.google.protobuf.ByteString) ref)
              .toStringUtf8();
          roomId_ = s;
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <code>optional string roomId = 7;</code>
       *
       * <pre>
       * 房间ID
       * </pre>
       */
      public com.google.protobuf.ByteString
          getRoomIdBytes() {
        java.lang.Object ref = roomId_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          roomId_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <code>optional string roomId = 7;</code>
       *
       * <pre>
       * 房间ID
       * </pre>
       */
      public Builder setRoomId(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000040;
        roomId_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional string roomId = 7;</code>
       *
       * <pre>
       * 房间ID
       * </pre>
       */
      public Builder clearRoomId() {
        bitField0_ = (bitField0_ & ~0x00000040);
        roomId_ = getDefaultInstance().getRoomId();
        onChanged();
        return this;
      }
      /**
       * <code>optional string roomId = 7;</code>
       *
       * <pre>
       * 房间ID
       * </pre>
       */
      public Builder setRoomIdBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000040;
        roomId_ = value;
        onChanged();
        return this;
      }

      // @@protoc_insertion_point(builder_scope:fm.lizhi.commons.template.datacenter.protocol.InvokeTargetParams)
    }

    static {
      defaultInstance = new InvokeTargetParams(true);
      defaultInstance.initFields();
    }

    // @@protoc_insertion_point(class_scope:fm.lizhi.commons.template.datacenter.protocol.InvokeTargetParams)
  }

  public interface GetAppIdAppSecretParamOrBuilder
      extends com.google.protobuf.MessageOrBuilder {

    // optional string appId = 1;
    /**
     * <code>optional string appId = 1;</code>
     *
     * <pre>
     * appId，Seal服务端根据appId配置业务服务端域名
     * </pre>
     */
    boolean hasAppId();
    /**
     * <code>optional string appId = 1;</code>
     *
     * <pre>
     * appId，Seal服务端根据appId配置业务服务端域名
     * </pre>
     */
    java.lang.String getAppId();
    /**
     * <code>optional string appId = 1;</code>
     *
     * <pre>
     * appId，Seal服务端根据appId配置业务服务端域名
     * </pre>
     */
    com.google.protobuf.ByteString
        getAppIdBytes();

    // optional string gameId = 2;
    /**
     * <code>optional string gameId = 2;</code>
     *
     * <pre>
     * 游戏ID，Seal服务端根据gameId配置游戏服务端域名
     * </pre>
     */
    boolean hasGameId();
    /**
     * <code>optional string gameId = 2;</code>
     *
     * <pre>
     * 游戏ID，Seal服务端根据gameId配置游戏服务端域名
     * </pre>
     */
    java.lang.String getGameId();
    /**
     * <code>optional string gameId = 2;</code>
     *
     * <pre>
     * 游戏ID，Seal服务端根据gameId配置游戏服务端域名
     * </pre>
     */
    com.google.protobuf.ByteString
        getGameIdBytes();

    // optional int32 type = 3;
    /**
     * <code>optional int32 type = 3;</code>
     *
     * <pre>
     * 请求类型，1：获取业务类型appId，2：获取游戏的appId
     * </pre>
     */
    boolean hasType();
    /**
     * <code>optional int32 type = 3;</code>
     *
     * <pre>
     * 请求类型，1：获取业务类型appId，2：获取游戏的appId
     * </pre>
     */
    int getType();
  }
  /**
   * Protobuf type {@code fm.lizhi.commons.template.datacenter.protocol.GetAppIdAppSecretParam}
   */
  public static final class GetAppIdAppSecretParam extends
      com.google.protobuf.GeneratedMessage
      implements GetAppIdAppSecretParamOrBuilder {
    // Use GetAppIdAppSecretParam.newBuilder() to construct.
    private GetAppIdAppSecretParam(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
      super(builder);
      this.unknownFields = builder.getUnknownFields();
    }
    private GetAppIdAppSecretParam(boolean noInit) { this.unknownFields = com.google.protobuf.UnknownFieldSet.getDefaultInstance(); }

    private static final GetAppIdAppSecretParam defaultInstance;
    public static GetAppIdAppSecretParam getDefaultInstance() {
      return defaultInstance;
    }

    public GetAppIdAppSecretParam getDefaultInstanceForType() {
      return defaultInstance;
    }

    private final com.google.protobuf.UnknownFieldSet unknownFields;
    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
        getUnknownFields() {
      return this.unknownFields;
    }
    private GetAppIdAppSecretParam(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      initFields();
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            default: {
              if (!parseUnknownField(input, unknownFields,
                                     extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
            case 10: {
              bitField0_ |= 0x00000001;
              appId_ = input.readBytes();
              break;
            }
            case 18: {
              bitField0_ |= 0x00000002;
              gameId_ = input.readBytes();
              break;
            }
            case 24: {
              bitField0_ |= 0x00000004;
              type_ = input.readInt32();
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e.getMessage()).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return fm.lizhi.ocean.seal.protocol.GameProxyServiceProto.internal_static_fm_lizhi_commons_template_datacenter_protocol_GetAppIdAppSecretParam_descriptor;
    }

    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return fm.lizhi.ocean.seal.protocol.GameProxyServiceProto.internal_static_fm_lizhi_commons_template_datacenter_protocol_GetAppIdAppSecretParam_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              fm.lizhi.ocean.seal.protocol.GameProxyServiceProto.GetAppIdAppSecretParam.class, fm.lizhi.ocean.seal.protocol.GameProxyServiceProto.GetAppIdAppSecretParam.Builder.class);
    }

    public static com.google.protobuf.Parser<GetAppIdAppSecretParam> PARSER =
        new com.google.protobuf.AbstractParser<GetAppIdAppSecretParam>() {
      public GetAppIdAppSecretParam parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new GetAppIdAppSecretParam(input, extensionRegistry);
      }
    };

    @java.lang.Override
    public com.google.protobuf.Parser<GetAppIdAppSecretParam> getParserForType() {
      return PARSER;
    }

    private int bitField0_;
    // optional string appId = 1;
    public static final int APPID_FIELD_NUMBER = 1;
    private java.lang.Object appId_;
    /**
     * <code>optional string appId = 1;</code>
     *
     * <pre>
     * appId，Seal服务端根据appId配置业务服务端域名
     * </pre>
     */
    public boolean hasAppId() {
      return ((bitField0_ & 0x00000001) == 0x00000001);
    }
    /**
     * <code>optional string appId = 1;</code>
     *
     * <pre>
     * appId，Seal服务端根据appId配置业务服务端域名
     * </pre>
     */
    public java.lang.String getAppId() {
      java.lang.Object ref = appId_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          appId_ = s;
        }
        return s;
      }
    }
    /**
     * <code>optional string appId = 1;</code>
     *
     * <pre>
     * appId，Seal服务端根据appId配置业务服务端域名
     * </pre>
     */
    public com.google.protobuf.ByteString
        getAppIdBytes() {
      java.lang.Object ref = appId_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        appId_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    // optional string gameId = 2;
    public static final int GAMEID_FIELD_NUMBER = 2;
    private java.lang.Object gameId_;
    /**
     * <code>optional string gameId = 2;</code>
     *
     * <pre>
     * 游戏ID，Seal服务端根据gameId配置游戏服务端域名
     * </pre>
     */
    public boolean hasGameId() {
      return ((bitField0_ & 0x00000002) == 0x00000002);
    }
    /**
     * <code>optional string gameId = 2;</code>
     *
     * <pre>
     * 游戏ID，Seal服务端根据gameId配置游戏服务端域名
     * </pre>
     */
    public java.lang.String getGameId() {
      java.lang.Object ref = gameId_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          gameId_ = s;
        }
        return s;
      }
    }
    /**
     * <code>optional string gameId = 2;</code>
     *
     * <pre>
     * 游戏ID，Seal服务端根据gameId配置游戏服务端域名
     * </pre>
     */
    public com.google.protobuf.ByteString
        getGameIdBytes() {
      java.lang.Object ref = gameId_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        gameId_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    // optional int32 type = 3;
    public static final int TYPE_FIELD_NUMBER = 3;
    private int type_;
    /**
     * <code>optional int32 type = 3;</code>
     *
     * <pre>
     * 请求类型，1：获取业务类型appId，2：获取游戏的appId
     * </pre>
     */
    public boolean hasType() {
      return ((bitField0_ & 0x00000004) == 0x00000004);
    }
    /**
     * <code>optional int32 type = 3;</code>
     *
     * <pre>
     * 请求类型，1：获取业务类型appId，2：获取游戏的appId
     * </pre>
     */
    public int getType() {
      return type_;
    }

    private void initFields() {
      appId_ = "";
      gameId_ = "";
      type_ = 0;
    }
    private byte memoizedIsInitialized = -1;
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized != -1) return isInitialized == 1;

      memoizedIsInitialized = 1;
      return true;
    }

    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      getSerializedSize();
      if (((bitField0_ & 0x00000001) == 0x00000001)) {
        output.writeBytes(1, getAppIdBytes());
      }
      if (((bitField0_ & 0x00000002) == 0x00000002)) {
        output.writeBytes(2, getGameIdBytes());
      }
      if (((bitField0_ & 0x00000004) == 0x00000004)) {
        output.writeInt32(3, type_);
      }
      getUnknownFields().writeTo(output);
    }

    private int memoizedSerializedSize = -1;
    public int getSerializedSize() {
      int size = memoizedSerializedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) == 0x00000001)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(1, getAppIdBytes());
      }
      if (((bitField0_ & 0x00000002) == 0x00000002)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(2, getGameIdBytes());
      }
      if (((bitField0_ & 0x00000004) == 0x00000004)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(3, type_);
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSerializedSize = size;
      return size;
    }

    private static final long serialVersionUID = 0L;
    @java.lang.Override
    protected java.lang.Object writeReplace()
        throws java.io.ObjectStreamException {
      return super.writeReplace();
    }

    public static fm.lizhi.ocean.seal.protocol.GameProxyServiceProto.GetAppIdAppSecretParam parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static fm.lizhi.ocean.seal.protocol.GameProxyServiceProto.GetAppIdAppSecretParam parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static fm.lizhi.ocean.seal.protocol.GameProxyServiceProto.GetAppIdAppSecretParam parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static fm.lizhi.ocean.seal.protocol.GameProxyServiceProto.GetAppIdAppSecretParam parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static fm.lizhi.ocean.seal.protocol.GameProxyServiceProto.GetAppIdAppSecretParam parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return PARSER.parseFrom(input);
    }
    public static fm.lizhi.ocean.seal.protocol.GameProxyServiceProto.GetAppIdAppSecretParam parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseFrom(input, extensionRegistry);
    }
    public static fm.lizhi.ocean.seal.protocol.GameProxyServiceProto.GetAppIdAppSecretParam parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return PARSER.parseDelimitedFrom(input);
    }
    public static fm.lizhi.ocean.seal.protocol.GameProxyServiceProto.GetAppIdAppSecretParam parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseDelimitedFrom(input, extensionRegistry);
    }
    public static fm.lizhi.ocean.seal.protocol.GameProxyServiceProto.GetAppIdAppSecretParam parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return PARSER.parseFrom(input);
    }
    public static fm.lizhi.ocean.seal.protocol.GameProxyServiceProto.GetAppIdAppSecretParam parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseFrom(input, extensionRegistry);
    }

    public static Builder newBuilder() { return Builder.create(); }
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder(fm.lizhi.ocean.seal.protocol.GameProxyServiceProto.GetAppIdAppSecretParam prototype) {
      return newBuilder().mergeFrom(prototype);
    }
    public Builder toBuilder() { return newBuilder(this); }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code fm.lizhi.commons.template.datacenter.protocol.GetAppIdAppSecretParam}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessage.Builder<Builder>
       implements fm.lizhi.ocean.seal.protocol.GameProxyServiceProto.GetAppIdAppSecretParamOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return fm.lizhi.ocean.seal.protocol.GameProxyServiceProto.internal_static_fm_lizhi_commons_template_datacenter_protocol_GetAppIdAppSecretParam_descriptor;
      }

      protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return fm.lizhi.ocean.seal.protocol.GameProxyServiceProto.internal_static_fm_lizhi_commons_template_datacenter_protocol_GetAppIdAppSecretParam_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                fm.lizhi.ocean.seal.protocol.GameProxyServiceProto.GetAppIdAppSecretParam.class, fm.lizhi.ocean.seal.protocol.GameProxyServiceProto.GetAppIdAppSecretParam.Builder.class);
      }

      // Construct using fm.lizhi.ocean.seal.protocol.GameProxyServiceProto.GetAppIdAppSecretParam.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessage.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessage.alwaysUseFieldBuilders) {
        }
      }
      private static Builder create() {
        return new Builder();
      }

      public Builder clear() {
        super.clear();
        appId_ = "";
        bitField0_ = (bitField0_ & ~0x00000001);
        gameId_ = "";
        bitField0_ = (bitField0_ & ~0x00000002);
        type_ = 0;
        bitField0_ = (bitField0_ & ~0x00000004);
        return this;
      }

      public Builder clone() {
        return create().mergeFrom(buildPartial());
      }

      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return fm.lizhi.ocean.seal.protocol.GameProxyServiceProto.internal_static_fm_lizhi_commons_template_datacenter_protocol_GetAppIdAppSecretParam_descriptor;
      }

      public fm.lizhi.ocean.seal.protocol.GameProxyServiceProto.GetAppIdAppSecretParam getDefaultInstanceForType() {
        return fm.lizhi.ocean.seal.protocol.GameProxyServiceProto.GetAppIdAppSecretParam.getDefaultInstance();
      }

      public fm.lizhi.ocean.seal.protocol.GameProxyServiceProto.GetAppIdAppSecretParam build() {
        fm.lizhi.ocean.seal.protocol.GameProxyServiceProto.GetAppIdAppSecretParam result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      public fm.lizhi.ocean.seal.protocol.GameProxyServiceProto.GetAppIdAppSecretParam buildPartial() {
        fm.lizhi.ocean.seal.protocol.GameProxyServiceProto.GetAppIdAppSecretParam result = new fm.lizhi.ocean.seal.protocol.GameProxyServiceProto.GetAppIdAppSecretParam(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) == 0x00000001)) {
          to_bitField0_ |= 0x00000001;
        }
        result.appId_ = appId_;
        if (((from_bitField0_ & 0x00000002) == 0x00000002)) {
          to_bitField0_ |= 0x00000002;
        }
        result.gameId_ = gameId_;
        if (((from_bitField0_ & 0x00000004) == 0x00000004)) {
          to_bitField0_ |= 0x00000004;
        }
        result.type_ = type_;
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof fm.lizhi.ocean.seal.protocol.GameProxyServiceProto.GetAppIdAppSecretParam) {
          return mergeFrom((fm.lizhi.ocean.seal.protocol.GameProxyServiceProto.GetAppIdAppSecretParam)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(fm.lizhi.ocean.seal.protocol.GameProxyServiceProto.GetAppIdAppSecretParam other) {
        if (other == fm.lizhi.ocean.seal.protocol.GameProxyServiceProto.GetAppIdAppSecretParam.getDefaultInstance()) return this;
        if (other.hasAppId()) {
          bitField0_ |= 0x00000001;
          appId_ = other.appId_;
          onChanged();
        }
        if (other.hasGameId()) {
          bitField0_ |= 0x00000002;
          gameId_ = other.gameId_;
          onChanged();
        }
        if (other.hasType()) {
          setType(other.getType());
        }
        this.mergeUnknownFields(other.getUnknownFields());
        return this;
      }

      public final boolean isInitialized() {
        return true;
      }

      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        fm.lizhi.ocean.seal.protocol.GameProxyServiceProto.GetAppIdAppSecretParam parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (fm.lizhi.ocean.seal.protocol.GameProxyServiceProto.GetAppIdAppSecretParam) e.getUnfinishedMessage();
          throw e;
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      // optional string appId = 1;
      private java.lang.Object appId_ = "";
      /**
       * <code>optional string appId = 1;</code>
       *
       * <pre>
       * appId，Seal服务端根据appId配置业务服务端域名
       * </pre>
       */
      public boolean hasAppId() {
        return ((bitField0_ & 0x00000001) == 0x00000001);
      }
      /**
       * <code>optional string appId = 1;</code>
       *
       * <pre>
       * appId，Seal服务端根据appId配置业务服务端域名
       * </pre>
       */
      public java.lang.String getAppId() {
        java.lang.Object ref = appId_;
        if (!(ref instanceof java.lang.String)) {
          java.lang.String s = ((com.google.protobuf.ByteString) ref)
              .toStringUtf8();
          appId_ = s;
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <code>optional string appId = 1;</code>
       *
       * <pre>
       * appId，Seal服务端根据appId配置业务服务端域名
       * </pre>
       */
      public com.google.protobuf.ByteString
          getAppIdBytes() {
        java.lang.Object ref = appId_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          appId_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <code>optional string appId = 1;</code>
       *
       * <pre>
       * appId，Seal服务端根据appId配置业务服务端域名
       * </pre>
       */
      public Builder setAppId(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000001;
        appId_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional string appId = 1;</code>
       *
       * <pre>
       * appId，Seal服务端根据appId配置业务服务端域名
       * </pre>
       */
      public Builder clearAppId() {
        bitField0_ = (bitField0_ & ~0x00000001);
        appId_ = getDefaultInstance().getAppId();
        onChanged();
        return this;
      }
      /**
       * <code>optional string appId = 1;</code>
       *
       * <pre>
       * appId，Seal服务端根据appId配置业务服务端域名
       * </pre>
       */
      public Builder setAppIdBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000001;
        appId_ = value;
        onChanged();
        return this;
      }

      // optional string gameId = 2;
      private java.lang.Object gameId_ = "";
      /**
       * <code>optional string gameId = 2;</code>
       *
       * <pre>
       * 游戏ID，Seal服务端根据gameId配置游戏服务端域名
       * </pre>
       */
      public boolean hasGameId() {
        return ((bitField0_ & 0x00000002) == 0x00000002);
      }
      /**
       * <code>optional string gameId = 2;</code>
       *
       * <pre>
       * 游戏ID，Seal服务端根据gameId配置游戏服务端域名
       * </pre>
       */
      public java.lang.String getGameId() {
        java.lang.Object ref = gameId_;
        if (!(ref instanceof java.lang.String)) {
          java.lang.String s = ((com.google.protobuf.ByteString) ref)
              .toStringUtf8();
          gameId_ = s;
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <code>optional string gameId = 2;</code>
       *
       * <pre>
       * 游戏ID，Seal服务端根据gameId配置游戏服务端域名
       * </pre>
       */
      public com.google.protobuf.ByteString
          getGameIdBytes() {
        java.lang.Object ref = gameId_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          gameId_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <code>optional string gameId = 2;</code>
       *
       * <pre>
       * 游戏ID，Seal服务端根据gameId配置游戏服务端域名
       * </pre>
       */
      public Builder setGameId(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000002;
        gameId_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional string gameId = 2;</code>
       *
       * <pre>
       * 游戏ID，Seal服务端根据gameId配置游戏服务端域名
       * </pre>
       */
      public Builder clearGameId() {
        bitField0_ = (bitField0_ & ~0x00000002);
        gameId_ = getDefaultInstance().getGameId();
        onChanged();
        return this;
      }
      /**
       * <code>optional string gameId = 2;</code>
       *
       * <pre>
       * 游戏ID，Seal服务端根据gameId配置游戏服务端域名
       * </pre>
       */
      public Builder setGameIdBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000002;
        gameId_ = value;
        onChanged();
        return this;
      }

      // optional int32 type = 3;
      private int type_ ;
      /**
       * <code>optional int32 type = 3;</code>
       *
       * <pre>
       * 请求类型，1：获取业务类型appId，2：获取游戏的appId
       * </pre>
       */
      public boolean hasType() {
        return ((bitField0_ & 0x00000004) == 0x00000004);
      }
      /**
       * <code>optional int32 type = 3;</code>
       *
       * <pre>
       * 请求类型，1：获取业务类型appId，2：获取游戏的appId
       * </pre>
       */
      public int getType() {
        return type_;
      }
      /**
       * <code>optional int32 type = 3;</code>
       *
       * <pre>
       * 请求类型，1：获取业务类型appId，2：获取游戏的appId
       * </pre>
       */
      public Builder setType(int value) {
        bitField0_ |= 0x00000004;
        type_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional int32 type = 3;</code>
       *
       * <pre>
       * 请求类型，1：获取业务类型appId，2：获取游戏的appId
       * </pre>
       */
      public Builder clearType() {
        bitField0_ = (bitField0_ & ~0x00000004);
        type_ = 0;
        onChanged();
        return this;
      }

      // @@protoc_insertion_point(builder_scope:fm.lizhi.commons.template.datacenter.protocol.GetAppIdAppSecretParam)
    }

    static {
      defaultInstance = new GetAppIdAppSecretParam(true);
      defaultInstance.initFields();
    }

    // @@protoc_insertion_point(class_scope:fm.lizhi.commons.template.datacenter.protocol.GetAppIdAppSecretParam)
  }

  public interface RequestInvokeTargetOrBuilder
      extends com.google.protobuf.MessageOrBuilder {

    // optional .fm.lizhi.commons.template.datacenter.protocol.InvokeTargetParams param = 1;
    /**
     * <code>optional .fm.lizhi.commons.template.datacenter.protocol.InvokeTargetParams param = 1;</code>
     *
     * <pre>
     *参数
     * </pre>
     */
    boolean hasParam();
    /**
     * <code>optional .fm.lizhi.commons.template.datacenter.protocol.InvokeTargetParams param = 1;</code>
     *
     * <pre>
     *参数
     * </pre>
     */
    fm.lizhi.ocean.seal.protocol.GameProxyServiceProto.InvokeTargetParams getParam();
    /**
     * <code>optional .fm.lizhi.commons.template.datacenter.protocol.InvokeTargetParams param = 1;</code>
     *
     * <pre>
     *参数
     * </pre>
     */
    fm.lizhi.ocean.seal.protocol.GameProxyServiceProto.InvokeTargetParamsOrBuilder getParamOrBuilder();
  }
  /**
   * Protobuf type {@code fm.lizhi.commons.template.datacenter.protocol.RequestInvokeTarget}
   *
   * <pre>
   * GameProxyService.java
   * 获取App回调配置信息
   * domain = 4302, op = 240
   * </pre>
   */
  public static final class RequestInvokeTarget extends
      com.google.protobuf.GeneratedMessage
      implements RequestInvokeTargetOrBuilder {
    // Use RequestInvokeTarget.newBuilder() to construct.
    private RequestInvokeTarget(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
      super(builder);
      this.unknownFields = builder.getUnknownFields();
    }
    private RequestInvokeTarget(boolean noInit) { this.unknownFields = com.google.protobuf.UnknownFieldSet.getDefaultInstance(); }

    private static final RequestInvokeTarget defaultInstance;
    public static RequestInvokeTarget getDefaultInstance() {
      return defaultInstance;
    }

    public RequestInvokeTarget getDefaultInstanceForType() {
      return defaultInstance;
    }

    private final com.google.protobuf.UnknownFieldSet unknownFields;
    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
        getUnknownFields() {
      return this.unknownFields;
    }
    private RequestInvokeTarget(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      initFields();
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            default: {
              if (!parseUnknownField(input, unknownFields,
                                     extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
            case 10: {
              fm.lizhi.ocean.seal.protocol.GameProxyServiceProto.InvokeTargetParams.Builder subBuilder = null;
              if (((bitField0_ & 0x00000001) == 0x00000001)) {
                subBuilder = param_.toBuilder();
              }
              param_ = input.readMessage(fm.lizhi.ocean.seal.protocol.GameProxyServiceProto.InvokeTargetParams.PARSER, extensionRegistry);
              if (subBuilder != null) {
                subBuilder.mergeFrom(param_);
                param_ = subBuilder.buildPartial();
              }
              bitField0_ |= 0x00000001;
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e.getMessage()).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return fm.lizhi.ocean.seal.protocol.GameProxyServiceProto.internal_static_fm_lizhi_commons_template_datacenter_protocol_RequestInvokeTarget_descriptor;
    }

    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return fm.lizhi.ocean.seal.protocol.GameProxyServiceProto.internal_static_fm_lizhi_commons_template_datacenter_protocol_RequestInvokeTarget_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              fm.lizhi.ocean.seal.protocol.GameProxyServiceProto.RequestInvokeTarget.class, fm.lizhi.ocean.seal.protocol.GameProxyServiceProto.RequestInvokeTarget.Builder.class);
    }

    public static com.google.protobuf.Parser<RequestInvokeTarget> PARSER =
        new com.google.protobuf.AbstractParser<RequestInvokeTarget>() {
      public RequestInvokeTarget parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new RequestInvokeTarget(input, extensionRegistry);
      }
    };

    @java.lang.Override
    public com.google.protobuf.Parser<RequestInvokeTarget> getParserForType() {
      return PARSER;
    }

    private int bitField0_;
    // optional .fm.lizhi.commons.template.datacenter.protocol.InvokeTargetParams param = 1;
    public static final int PARAM_FIELD_NUMBER = 1;
    private fm.lizhi.ocean.seal.protocol.GameProxyServiceProto.InvokeTargetParams param_;
    /**
     * <code>optional .fm.lizhi.commons.template.datacenter.protocol.InvokeTargetParams param = 1;</code>
     *
     * <pre>
     *参数
     * </pre>
     */
    public boolean hasParam() {
      return ((bitField0_ & 0x00000001) == 0x00000001);
    }
    /**
     * <code>optional .fm.lizhi.commons.template.datacenter.protocol.InvokeTargetParams param = 1;</code>
     *
     * <pre>
     *参数
     * </pre>
     */
    public fm.lizhi.ocean.seal.protocol.GameProxyServiceProto.InvokeTargetParams getParam() {
      return param_;
    }
    /**
     * <code>optional .fm.lizhi.commons.template.datacenter.protocol.InvokeTargetParams param = 1;</code>
     *
     * <pre>
     *参数
     * </pre>
     */
    public fm.lizhi.ocean.seal.protocol.GameProxyServiceProto.InvokeTargetParamsOrBuilder getParamOrBuilder() {
      return param_;
    }

    private void initFields() {
      param_ = fm.lizhi.ocean.seal.protocol.GameProxyServiceProto.InvokeTargetParams.getDefaultInstance();
    }
    private byte memoizedIsInitialized = -1;
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized != -1) return isInitialized == 1;

      if (hasParam()) {
        if (!getParam().isInitialized()) {
          memoizedIsInitialized = 0;
          return false;
        }
      }
      memoizedIsInitialized = 1;
      return true;
    }

    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      getSerializedSize();
      if (((bitField0_ & 0x00000001) == 0x00000001)) {
        output.writeMessage(1, param_);
      }
      getUnknownFields().writeTo(output);
    }

    private int memoizedSerializedSize = -1;
    public int getSerializedSize() {
      int size = memoizedSerializedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) == 0x00000001)) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(1, param_);
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSerializedSize = size;
      return size;
    }

    private static final long serialVersionUID = 0L;
    @java.lang.Override
    protected java.lang.Object writeReplace()
        throws java.io.ObjectStreamException {
      return super.writeReplace();
    }

    public static fm.lizhi.ocean.seal.protocol.GameProxyServiceProto.RequestInvokeTarget parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static fm.lizhi.ocean.seal.protocol.GameProxyServiceProto.RequestInvokeTarget parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static fm.lizhi.ocean.seal.protocol.GameProxyServiceProto.RequestInvokeTarget parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static fm.lizhi.ocean.seal.protocol.GameProxyServiceProto.RequestInvokeTarget parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static fm.lizhi.ocean.seal.protocol.GameProxyServiceProto.RequestInvokeTarget parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return PARSER.parseFrom(input);
    }
    public static fm.lizhi.ocean.seal.protocol.GameProxyServiceProto.RequestInvokeTarget parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseFrom(input, extensionRegistry);
    }
    public static fm.lizhi.ocean.seal.protocol.GameProxyServiceProto.RequestInvokeTarget parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return PARSER.parseDelimitedFrom(input);
    }
    public static fm.lizhi.ocean.seal.protocol.GameProxyServiceProto.RequestInvokeTarget parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseDelimitedFrom(input, extensionRegistry);
    }
    public static fm.lizhi.ocean.seal.protocol.GameProxyServiceProto.RequestInvokeTarget parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return PARSER.parseFrom(input);
    }
    public static fm.lizhi.ocean.seal.protocol.GameProxyServiceProto.RequestInvokeTarget parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseFrom(input, extensionRegistry);
    }

    public static Builder newBuilder() { return Builder.create(); }
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder(fm.lizhi.ocean.seal.protocol.GameProxyServiceProto.RequestInvokeTarget prototype) {
      return newBuilder().mergeFrom(prototype);
    }
    public Builder toBuilder() { return newBuilder(this); }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code fm.lizhi.commons.template.datacenter.protocol.RequestInvokeTarget}
     *
     * <pre>
     * GameProxyService.java
     * 获取App回调配置信息
     * domain = 4302, op = 240
     * </pre>
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessage.Builder<Builder>
       implements fm.lizhi.ocean.seal.protocol.GameProxyServiceProto.RequestInvokeTargetOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return fm.lizhi.ocean.seal.protocol.GameProxyServiceProto.internal_static_fm_lizhi_commons_template_datacenter_protocol_RequestInvokeTarget_descriptor;
      }

      protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return fm.lizhi.ocean.seal.protocol.GameProxyServiceProto.internal_static_fm_lizhi_commons_template_datacenter_protocol_RequestInvokeTarget_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                fm.lizhi.ocean.seal.protocol.GameProxyServiceProto.RequestInvokeTarget.class, fm.lizhi.ocean.seal.protocol.GameProxyServiceProto.RequestInvokeTarget.Builder.class);
      }

      // Construct using fm.lizhi.ocean.seal.protocol.GameProxyServiceProto.RequestInvokeTarget.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessage.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessage.alwaysUseFieldBuilders) {
          getParamFieldBuilder();
        }
      }
      private static Builder create() {
        return new Builder();
      }

      public Builder clear() {
        super.clear();
        if (paramBuilder_ == null) {
          param_ = fm.lizhi.ocean.seal.protocol.GameProxyServiceProto.InvokeTargetParams.getDefaultInstance();
        } else {
          paramBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000001);
        return this;
      }

      public Builder clone() {
        return create().mergeFrom(buildPartial());
      }

      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return fm.lizhi.ocean.seal.protocol.GameProxyServiceProto.internal_static_fm_lizhi_commons_template_datacenter_protocol_RequestInvokeTarget_descriptor;
      }

      public fm.lizhi.ocean.seal.protocol.GameProxyServiceProto.RequestInvokeTarget getDefaultInstanceForType() {
        return fm.lizhi.ocean.seal.protocol.GameProxyServiceProto.RequestInvokeTarget.getDefaultInstance();
      }

      public fm.lizhi.ocean.seal.protocol.GameProxyServiceProto.RequestInvokeTarget build() {
        fm.lizhi.ocean.seal.protocol.GameProxyServiceProto.RequestInvokeTarget result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      public fm.lizhi.ocean.seal.protocol.GameProxyServiceProto.RequestInvokeTarget buildPartial() {
        fm.lizhi.ocean.seal.protocol.GameProxyServiceProto.RequestInvokeTarget result = new fm.lizhi.ocean.seal.protocol.GameProxyServiceProto.RequestInvokeTarget(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) == 0x00000001)) {
          to_bitField0_ |= 0x00000001;
        }
        if (paramBuilder_ == null) {
          result.param_ = param_;
        } else {
          result.param_ = paramBuilder_.build();
        }
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof fm.lizhi.ocean.seal.protocol.GameProxyServiceProto.RequestInvokeTarget) {
          return mergeFrom((fm.lizhi.ocean.seal.protocol.GameProxyServiceProto.RequestInvokeTarget)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(fm.lizhi.ocean.seal.protocol.GameProxyServiceProto.RequestInvokeTarget other) {
        if (other == fm.lizhi.ocean.seal.protocol.GameProxyServiceProto.RequestInvokeTarget.getDefaultInstance()) return this;
        if (other.hasParam()) {
          mergeParam(other.getParam());
        }
        this.mergeUnknownFields(other.getUnknownFields());
        return this;
      }

      public final boolean isInitialized() {
        if (hasParam()) {
          if (!getParam().isInitialized()) {
            
            return false;
          }
        }
        return true;
      }

      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        fm.lizhi.ocean.seal.protocol.GameProxyServiceProto.RequestInvokeTarget parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (fm.lizhi.ocean.seal.protocol.GameProxyServiceProto.RequestInvokeTarget) e.getUnfinishedMessage();
          throw e;
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      // optional .fm.lizhi.commons.template.datacenter.protocol.InvokeTargetParams param = 1;
      private fm.lizhi.ocean.seal.protocol.GameProxyServiceProto.InvokeTargetParams param_ = fm.lizhi.ocean.seal.protocol.GameProxyServiceProto.InvokeTargetParams.getDefaultInstance();
      private com.google.protobuf.SingleFieldBuilder<
          fm.lizhi.ocean.seal.protocol.GameProxyServiceProto.InvokeTargetParams, fm.lizhi.ocean.seal.protocol.GameProxyServiceProto.InvokeTargetParams.Builder, fm.lizhi.ocean.seal.protocol.GameProxyServiceProto.InvokeTargetParamsOrBuilder> paramBuilder_;
      /**
       * <code>optional .fm.lizhi.commons.template.datacenter.protocol.InvokeTargetParams param = 1;</code>
       *
       * <pre>
       *参数
       * </pre>
       */
      public boolean hasParam() {
        return ((bitField0_ & 0x00000001) == 0x00000001);
      }
      /**
       * <code>optional .fm.lizhi.commons.template.datacenter.protocol.InvokeTargetParams param = 1;</code>
       *
       * <pre>
       *参数
       * </pre>
       */
      public fm.lizhi.ocean.seal.protocol.GameProxyServiceProto.InvokeTargetParams getParam() {
        if (paramBuilder_ == null) {
          return param_;
        } else {
          return paramBuilder_.getMessage();
        }
      }
      /**
       * <code>optional .fm.lizhi.commons.template.datacenter.protocol.InvokeTargetParams param = 1;</code>
       *
       * <pre>
       *参数
       * </pre>
       */
      public Builder setParam(fm.lizhi.ocean.seal.protocol.GameProxyServiceProto.InvokeTargetParams value) {
        if (paramBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          param_ = value;
          onChanged();
        } else {
          paramBuilder_.setMessage(value);
        }
        bitField0_ |= 0x00000001;
        return this;
      }
      /**
       * <code>optional .fm.lizhi.commons.template.datacenter.protocol.InvokeTargetParams param = 1;</code>
       *
       * <pre>
       *参数
       * </pre>
       */
      public Builder setParam(
          fm.lizhi.ocean.seal.protocol.GameProxyServiceProto.InvokeTargetParams.Builder builderForValue) {
        if (paramBuilder_ == null) {
          param_ = builderForValue.build();
          onChanged();
        } else {
          paramBuilder_.setMessage(builderForValue.build());
        }
        bitField0_ |= 0x00000001;
        return this;
      }
      /**
       * <code>optional .fm.lizhi.commons.template.datacenter.protocol.InvokeTargetParams param = 1;</code>
       *
       * <pre>
       *参数
       * </pre>
       */
      public Builder mergeParam(fm.lizhi.ocean.seal.protocol.GameProxyServiceProto.InvokeTargetParams value) {
        if (paramBuilder_ == null) {
          if (((bitField0_ & 0x00000001) == 0x00000001) &&
              param_ != fm.lizhi.ocean.seal.protocol.GameProxyServiceProto.InvokeTargetParams.getDefaultInstance()) {
            param_ =
              fm.lizhi.ocean.seal.protocol.GameProxyServiceProto.InvokeTargetParams.newBuilder(param_).mergeFrom(value).buildPartial();
          } else {
            param_ = value;
          }
          onChanged();
        } else {
          paramBuilder_.mergeFrom(value);
        }
        bitField0_ |= 0x00000001;
        return this;
      }
      /**
       * <code>optional .fm.lizhi.commons.template.datacenter.protocol.InvokeTargetParams param = 1;</code>
       *
       * <pre>
       *参数
       * </pre>
       */
      public Builder clearParam() {
        if (paramBuilder_ == null) {
          param_ = fm.lizhi.ocean.seal.protocol.GameProxyServiceProto.InvokeTargetParams.getDefaultInstance();
          onChanged();
        } else {
          paramBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000001);
        return this;
      }
      /**
       * <code>optional .fm.lizhi.commons.template.datacenter.protocol.InvokeTargetParams param = 1;</code>
       *
       * <pre>
       *参数
       * </pre>
       */
      public fm.lizhi.ocean.seal.protocol.GameProxyServiceProto.InvokeTargetParams.Builder getParamBuilder() {
        bitField0_ |= 0x00000001;
        onChanged();
        return getParamFieldBuilder().getBuilder();
      }
      /**
       * <code>optional .fm.lizhi.commons.template.datacenter.protocol.InvokeTargetParams param = 1;</code>
       *
       * <pre>
       *参数
       * </pre>
       */
      public fm.lizhi.ocean.seal.protocol.GameProxyServiceProto.InvokeTargetParamsOrBuilder getParamOrBuilder() {
        if (paramBuilder_ != null) {
          return paramBuilder_.getMessageOrBuilder();
        } else {
          return param_;
        }
      }
      /**
       * <code>optional .fm.lizhi.commons.template.datacenter.protocol.InvokeTargetParams param = 1;</code>
       *
       * <pre>
       *参数
       * </pre>
       */
      private com.google.protobuf.SingleFieldBuilder<
          fm.lizhi.ocean.seal.protocol.GameProxyServiceProto.InvokeTargetParams, fm.lizhi.ocean.seal.protocol.GameProxyServiceProto.InvokeTargetParams.Builder, fm.lizhi.ocean.seal.protocol.GameProxyServiceProto.InvokeTargetParamsOrBuilder> 
          getParamFieldBuilder() {
        if (paramBuilder_ == null) {
          paramBuilder_ = new com.google.protobuf.SingleFieldBuilder<
              fm.lizhi.ocean.seal.protocol.GameProxyServiceProto.InvokeTargetParams, fm.lizhi.ocean.seal.protocol.GameProxyServiceProto.InvokeTargetParams.Builder, fm.lizhi.ocean.seal.protocol.GameProxyServiceProto.InvokeTargetParamsOrBuilder>(
                  param_,
                  getParentForChildren(),
                  isClean());
          param_ = null;
        }
        return paramBuilder_;
      }

      // @@protoc_insertion_point(builder_scope:fm.lizhi.commons.template.datacenter.protocol.RequestInvokeTarget)
    }

    static {
      defaultInstance = new RequestInvokeTarget(true);
      defaultInstance.initFields();
    }

    // @@protoc_insertion_point(class_scope:fm.lizhi.commons.template.datacenter.protocol.RequestInvokeTarget)
  }

  public interface ResponseInvokeTargetOrBuilder
      extends com.google.protobuf.MessageOrBuilder {

    // required int32 bizCode = 1;
    /**
     * <code>required int32 bizCode = 1;</code>
     *
     * <pre>
     * 业务错误码
     * </pre>
     */
    boolean hasBizCode();
    /**
     * <code>required int32 bizCode = 1;</code>
     *
     * <pre>
     * 业务错误码
     * </pre>
     */
    int getBizCode();

    // required string msg = 2;
    /**
     * <code>required string msg = 2;</code>
     *
     * <pre>
     * 错误消息
     * </pre>
     */
    boolean hasMsg();
    /**
     * <code>required string msg = 2;</code>
     *
     * <pre>
     * 错误消息
     * </pre>
     */
    java.lang.String getMsg();
    /**
     * <code>required string msg = 2;</code>
     *
     * <pre>
     * 错误消息
     * </pre>
     */
    com.google.protobuf.ByteString
        getMsgBytes();

    // required string data = 3;
    /**
     * <code>required string data = 3;</code>
     *
     * <pre>
     * 数据
     * </pre>
     */
    boolean hasData();
    /**
     * <code>required string data = 3;</code>
     *
     * <pre>
     * 数据
     * </pre>
     */
    java.lang.String getData();
    /**
     * <code>required string data = 3;</code>
     *
     * <pre>
     * 数据
     * </pre>
     */
    com.google.protobuf.ByteString
        getDataBytes();
  }
  /**
   * Protobuf type {@code fm.lizhi.commons.template.datacenter.protocol.ResponseInvokeTarget}
   *
   * <pre>
   * rcode == 1 (ILLEGAL_PARAMS) = 参数非法
   * rcode == 2 (NOT_EXISTS) = 映射不存在
   * rcode == 3 (ERROR_TARGET) = 目标服务错误
   * rcode == 4 (ERROR) = 内部错误
   * </pre>
   */
  public static final class ResponseInvokeTarget extends
      com.google.protobuf.GeneratedMessage
      implements ResponseInvokeTargetOrBuilder {
    // Use ResponseInvokeTarget.newBuilder() to construct.
    private ResponseInvokeTarget(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
      super(builder);
      this.unknownFields = builder.getUnknownFields();
    }
    private ResponseInvokeTarget(boolean noInit) { this.unknownFields = com.google.protobuf.UnknownFieldSet.getDefaultInstance(); }

    private static final ResponseInvokeTarget defaultInstance;
    public static ResponseInvokeTarget getDefaultInstance() {
      return defaultInstance;
    }

    public ResponseInvokeTarget getDefaultInstanceForType() {
      return defaultInstance;
    }

    private final com.google.protobuf.UnknownFieldSet unknownFields;
    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
        getUnknownFields() {
      return this.unknownFields;
    }
    private ResponseInvokeTarget(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      initFields();
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            default: {
              if (!parseUnknownField(input, unknownFields,
                                     extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
            case 8: {
              bitField0_ |= 0x00000001;
              bizCode_ = input.readInt32();
              break;
            }
            case 18: {
              bitField0_ |= 0x00000002;
              msg_ = input.readBytes();
              break;
            }
            case 26: {
              bitField0_ |= 0x00000004;
              data_ = input.readBytes();
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e.getMessage()).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return fm.lizhi.ocean.seal.protocol.GameProxyServiceProto.internal_static_fm_lizhi_commons_template_datacenter_protocol_ResponseInvokeTarget_descriptor;
    }

    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return fm.lizhi.ocean.seal.protocol.GameProxyServiceProto.internal_static_fm_lizhi_commons_template_datacenter_protocol_ResponseInvokeTarget_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              fm.lizhi.ocean.seal.protocol.GameProxyServiceProto.ResponseInvokeTarget.class, fm.lizhi.ocean.seal.protocol.GameProxyServiceProto.ResponseInvokeTarget.Builder.class);
    }

    public static com.google.protobuf.Parser<ResponseInvokeTarget> PARSER =
        new com.google.protobuf.AbstractParser<ResponseInvokeTarget>() {
      public ResponseInvokeTarget parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new ResponseInvokeTarget(input, extensionRegistry);
      }
    };

    @java.lang.Override
    public com.google.protobuf.Parser<ResponseInvokeTarget> getParserForType() {
      return PARSER;
    }

    private int bitField0_;
    // required int32 bizCode = 1;
    public static final int BIZCODE_FIELD_NUMBER = 1;
    private int bizCode_;
    /**
     * <code>required int32 bizCode = 1;</code>
     *
     * <pre>
     * 业务错误码
     * </pre>
     */
    public boolean hasBizCode() {
      return ((bitField0_ & 0x00000001) == 0x00000001);
    }
    /**
     * <code>required int32 bizCode = 1;</code>
     *
     * <pre>
     * 业务错误码
     * </pre>
     */
    public int getBizCode() {
      return bizCode_;
    }

    // required string msg = 2;
    public static final int MSG_FIELD_NUMBER = 2;
    private java.lang.Object msg_;
    /**
     * <code>required string msg = 2;</code>
     *
     * <pre>
     * 错误消息
     * </pre>
     */
    public boolean hasMsg() {
      return ((bitField0_ & 0x00000002) == 0x00000002);
    }
    /**
     * <code>required string msg = 2;</code>
     *
     * <pre>
     * 错误消息
     * </pre>
     */
    public java.lang.String getMsg() {
      java.lang.Object ref = msg_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          msg_ = s;
        }
        return s;
      }
    }
    /**
     * <code>required string msg = 2;</code>
     *
     * <pre>
     * 错误消息
     * </pre>
     */
    public com.google.protobuf.ByteString
        getMsgBytes() {
      java.lang.Object ref = msg_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        msg_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    // required string data = 3;
    public static final int DATA_FIELD_NUMBER = 3;
    private java.lang.Object data_;
    /**
     * <code>required string data = 3;</code>
     *
     * <pre>
     * 数据
     * </pre>
     */
    public boolean hasData() {
      return ((bitField0_ & 0x00000004) == 0x00000004);
    }
    /**
     * <code>required string data = 3;</code>
     *
     * <pre>
     * 数据
     * </pre>
     */
    public java.lang.String getData() {
      java.lang.Object ref = data_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          data_ = s;
        }
        return s;
      }
    }
    /**
     * <code>required string data = 3;</code>
     *
     * <pre>
     * 数据
     * </pre>
     */
    public com.google.protobuf.ByteString
        getDataBytes() {
      java.lang.Object ref = data_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        data_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    private void initFields() {
      bizCode_ = 0;
      msg_ = "";
      data_ = "";
    }
    private byte memoizedIsInitialized = -1;
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized != -1) return isInitialized == 1;

      if (!hasBizCode()) {
        memoizedIsInitialized = 0;
        return false;
      }
      if (!hasMsg()) {
        memoizedIsInitialized = 0;
        return false;
      }
      if (!hasData()) {
        memoizedIsInitialized = 0;
        return false;
      }
      memoizedIsInitialized = 1;
      return true;
    }

    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      getSerializedSize();
      if (((bitField0_ & 0x00000001) == 0x00000001)) {
        output.writeInt32(1, bizCode_);
      }
      if (((bitField0_ & 0x00000002) == 0x00000002)) {
        output.writeBytes(2, getMsgBytes());
      }
      if (((bitField0_ & 0x00000004) == 0x00000004)) {
        output.writeBytes(3, getDataBytes());
      }
      getUnknownFields().writeTo(output);
    }

    private int memoizedSerializedSize = -1;
    public int getSerializedSize() {
      int size = memoizedSerializedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) == 0x00000001)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(1, bizCode_);
      }
      if (((bitField0_ & 0x00000002) == 0x00000002)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(2, getMsgBytes());
      }
      if (((bitField0_ & 0x00000004) == 0x00000004)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(3, getDataBytes());
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSerializedSize = size;
      return size;
    }

    private static final long serialVersionUID = 0L;
    @java.lang.Override
    protected java.lang.Object writeReplace()
        throws java.io.ObjectStreamException {
      return super.writeReplace();
    }

    public static fm.lizhi.ocean.seal.protocol.GameProxyServiceProto.ResponseInvokeTarget parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static fm.lizhi.ocean.seal.protocol.GameProxyServiceProto.ResponseInvokeTarget parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static fm.lizhi.ocean.seal.protocol.GameProxyServiceProto.ResponseInvokeTarget parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static fm.lizhi.ocean.seal.protocol.GameProxyServiceProto.ResponseInvokeTarget parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static fm.lizhi.ocean.seal.protocol.GameProxyServiceProto.ResponseInvokeTarget parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return PARSER.parseFrom(input);
    }
    public static fm.lizhi.ocean.seal.protocol.GameProxyServiceProto.ResponseInvokeTarget parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseFrom(input, extensionRegistry);
    }
    public static fm.lizhi.ocean.seal.protocol.GameProxyServiceProto.ResponseInvokeTarget parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return PARSER.parseDelimitedFrom(input);
    }
    public static fm.lizhi.ocean.seal.protocol.GameProxyServiceProto.ResponseInvokeTarget parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseDelimitedFrom(input, extensionRegistry);
    }
    public static fm.lizhi.ocean.seal.protocol.GameProxyServiceProto.ResponseInvokeTarget parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return PARSER.parseFrom(input);
    }
    public static fm.lizhi.ocean.seal.protocol.GameProxyServiceProto.ResponseInvokeTarget parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseFrom(input, extensionRegistry);
    }

    public static Builder newBuilder() { return Builder.create(); }
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder(fm.lizhi.ocean.seal.protocol.GameProxyServiceProto.ResponseInvokeTarget prototype) {
      return newBuilder().mergeFrom(prototype);
    }
    public Builder toBuilder() { return newBuilder(this); }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code fm.lizhi.commons.template.datacenter.protocol.ResponseInvokeTarget}
     *
     * <pre>
     * rcode == 1 (ILLEGAL_PARAMS) = 参数非法
     * rcode == 2 (NOT_EXISTS) = 映射不存在
     * rcode == 3 (ERROR_TARGET) = 目标服务错误
     * rcode == 4 (ERROR) = 内部错误
     * </pre>
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessage.Builder<Builder>
       implements fm.lizhi.ocean.seal.protocol.GameProxyServiceProto.ResponseInvokeTargetOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return fm.lizhi.ocean.seal.protocol.GameProxyServiceProto.internal_static_fm_lizhi_commons_template_datacenter_protocol_ResponseInvokeTarget_descriptor;
      }

      protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return fm.lizhi.ocean.seal.protocol.GameProxyServiceProto.internal_static_fm_lizhi_commons_template_datacenter_protocol_ResponseInvokeTarget_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                fm.lizhi.ocean.seal.protocol.GameProxyServiceProto.ResponseInvokeTarget.class, fm.lizhi.ocean.seal.protocol.GameProxyServiceProto.ResponseInvokeTarget.Builder.class);
      }

      // Construct using fm.lizhi.ocean.seal.protocol.GameProxyServiceProto.ResponseInvokeTarget.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessage.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessage.alwaysUseFieldBuilders) {
        }
      }
      private static Builder create() {
        return new Builder();
      }

      public Builder clear() {
        super.clear();
        bizCode_ = 0;
        bitField0_ = (bitField0_ & ~0x00000001);
        msg_ = "";
        bitField0_ = (bitField0_ & ~0x00000002);
        data_ = "";
        bitField0_ = (bitField0_ & ~0x00000004);
        return this;
      }

      public Builder clone() {
        return create().mergeFrom(buildPartial());
      }

      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return fm.lizhi.ocean.seal.protocol.GameProxyServiceProto.internal_static_fm_lizhi_commons_template_datacenter_protocol_ResponseInvokeTarget_descriptor;
      }

      public fm.lizhi.ocean.seal.protocol.GameProxyServiceProto.ResponseInvokeTarget getDefaultInstanceForType() {
        return fm.lizhi.ocean.seal.protocol.GameProxyServiceProto.ResponseInvokeTarget.getDefaultInstance();
      }

      public fm.lizhi.ocean.seal.protocol.GameProxyServiceProto.ResponseInvokeTarget build() {
        fm.lizhi.ocean.seal.protocol.GameProxyServiceProto.ResponseInvokeTarget result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      public fm.lizhi.ocean.seal.protocol.GameProxyServiceProto.ResponseInvokeTarget buildPartial() {
        fm.lizhi.ocean.seal.protocol.GameProxyServiceProto.ResponseInvokeTarget result = new fm.lizhi.ocean.seal.protocol.GameProxyServiceProto.ResponseInvokeTarget(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) == 0x00000001)) {
          to_bitField0_ |= 0x00000001;
        }
        result.bizCode_ = bizCode_;
        if (((from_bitField0_ & 0x00000002) == 0x00000002)) {
          to_bitField0_ |= 0x00000002;
        }
        result.msg_ = msg_;
        if (((from_bitField0_ & 0x00000004) == 0x00000004)) {
          to_bitField0_ |= 0x00000004;
        }
        result.data_ = data_;
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof fm.lizhi.ocean.seal.protocol.GameProxyServiceProto.ResponseInvokeTarget) {
          return mergeFrom((fm.lizhi.ocean.seal.protocol.GameProxyServiceProto.ResponseInvokeTarget)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(fm.lizhi.ocean.seal.protocol.GameProxyServiceProto.ResponseInvokeTarget other) {
        if (other == fm.lizhi.ocean.seal.protocol.GameProxyServiceProto.ResponseInvokeTarget.getDefaultInstance()) return this;
        if (other.hasBizCode()) {
          setBizCode(other.getBizCode());
        }
        if (other.hasMsg()) {
          bitField0_ |= 0x00000002;
          msg_ = other.msg_;
          onChanged();
        }
        if (other.hasData()) {
          bitField0_ |= 0x00000004;
          data_ = other.data_;
          onChanged();
        }
        this.mergeUnknownFields(other.getUnknownFields());
        return this;
      }

      public final boolean isInitialized() {
        if (!hasBizCode()) {
          
          return false;
        }
        if (!hasMsg()) {
          
          return false;
        }
        if (!hasData()) {
          
          return false;
        }
        return true;
      }

      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        fm.lizhi.ocean.seal.protocol.GameProxyServiceProto.ResponseInvokeTarget parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (fm.lizhi.ocean.seal.protocol.GameProxyServiceProto.ResponseInvokeTarget) e.getUnfinishedMessage();
          throw e;
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      // required int32 bizCode = 1;
      private int bizCode_ ;
      /**
       * <code>required int32 bizCode = 1;</code>
       *
       * <pre>
       * 业务错误码
       * </pre>
       */
      public boolean hasBizCode() {
        return ((bitField0_ & 0x00000001) == 0x00000001);
      }
      /**
       * <code>required int32 bizCode = 1;</code>
       *
       * <pre>
       * 业务错误码
       * </pre>
       */
      public int getBizCode() {
        return bizCode_;
      }
      /**
       * <code>required int32 bizCode = 1;</code>
       *
       * <pre>
       * 业务错误码
       * </pre>
       */
      public Builder setBizCode(int value) {
        bitField0_ |= 0x00000001;
        bizCode_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>required int32 bizCode = 1;</code>
       *
       * <pre>
       * 业务错误码
       * </pre>
       */
      public Builder clearBizCode() {
        bitField0_ = (bitField0_ & ~0x00000001);
        bizCode_ = 0;
        onChanged();
        return this;
      }

      // required string msg = 2;
      private java.lang.Object msg_ = "";
      /**
       * <code>required string msg = 2;</code>
       *
       * <pre>
       * 错误消息
       * </pre>
       */
      public boolean hasMsg() {
        return ((bitField0_ & 0x00000002) == 0x00000002);
      }
      /**
       * <code>required string msg = 2;</code>
       *
       * <pre>
       * 错误消息
       * </pre>
       */
      public java.lang.String getMsg() {
        java.lang.Object ref = msg_;
        if (!(ref instanceof java.lang.String)) {
          java.lang.String s = ((com.google.protobuf.ByteString) ref)
              .toStringUtf8();
          msg_ = s;
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <code>required string msg = 2;</code>
       *
       * <pre>
       * 错误消息
       * </pre>
       */
      public com.google.protobuf.ByteString
          getMsgBytes() {
        java.lang.Object ref = msg_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          msg_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <code>required string msg = 2;</code>
       *
       * <pre>
       * 错误消息
       * </pre>
       */
      public Builder setMsg(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000002;
        msg_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>required string msg = 2;</code>
       *
       * <pre>
       * 错误消息
       * </pre>
       */
      public Builder clearMsg() {
        bitField0_ = (bitField0_ & ~0x00000002);
        msg_ = getDefaultInstance().getMsg();
        onChanged();
        return this;
      }
      /**
       * <code>required string msg = 2;</code>
       *
       * <pre>
       * 错误消息
       * </pre>
       */
      public Builder setMsgBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000002;
        msg_ = value;
        onChanged();
        return this;
      }

      // required string data = 3;
      private java.lang.Object data_ = "";
      /**
       * <code>required string data = 3;</code>
       *
       * <pre>
       * 数据
       * </pre>
       */
      public boolean hasData() {
        return ((bitField0_ & 0x00000004) == 0x00000004);
      }
      /**
       * <code>required string data = 3;</code>
       *
       * <pre>
       * 数据
       * </pre>
       */
      public java.lang.String getData() {
        java.lang.Object ref = data_;
        if (!(ref instanceof java.lang.String)) {
          java.lang.String s = ((com.google.protobuf.ByteString) ref)
              .toStringUtf8();
          data_ = s;
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <code>required string data = 3;</code>
       *
       * <pre>
       * 数据
       * </pre>
       */
      public com.google.protobuf.ByteString
          getDataBytes() {
        java.lang.Object ref = data_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          data_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <code>required string data = 3;</code>
       *
       * <pre>
       * 数据
       * </pre>
       */
      public Builder setData(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000004;
        data_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>required string data = 3;</code>
       *
       * <pre>
       * 数据
       * </pre>
       */
      public Builder clearData() {
        bitField0_ = (bitField0_ & ~0x00000004);
        data_ = getDefaultInstance().getData();
        onChanged();
        return this;
      }
      /**
       * <code>required string data = 3;</code>
       *
       * <pre>
       * 数据
       * </pre>
       */
      public Builder setDataBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000004;
        data_ = value;
        onChanged();
        return this;
      }

      // @@protoc_insertion_point(builder_scope:fm.lizhi.commons.template.datacenter.protocol.ResponseInvokeTarget)
    }

    static {
      defaultInstance = new ResponseInvokeTarget(true);
      defaultInstance.initFields();
    }

    // @@protoc_insertion_point(class_scope:fm.lizhi.commons.template.datacenter.protocol.ResponseInvokeTarget)
  }

  public interface RequestGetAppIdAppSecretByParamOrBuilder
      extends com.google.protobuf.MessageOrBuilder {

    // optional .fm.lizhi.commons.template.datacenter.protocol.GetAppIdAppSecretParam param = 1;
    /**
     * <code>optional .fm.lizhi.commons.template.datacenter.protocol.GetAppIdAppSecretParam param = 1;</code>
     *
     * <pre>
     *参数
     * </pre>
     */
    boolean hasParam();
    /**
     * <code>optional .fm.lizhi.commons.template.datacenter.protocol.GetAppIdAppSecretParam param = 1;</code>
     *
     * <pre>
     *参数
     * </pre>
     */
    fm.lizhi.ocean.seal.protocol.GameProxyServiceProto.GetAppIdAppSecretParam getParam();
    /**
     * <code>optional .fm.lizhi.commons.template.datacenter.protocol.GetAppIdAppSecretParam param = 1;</code>
     *
     * <pre>
     *参数
     * </pre>
     */
    fm.lizhi.ocean.seal.protocol.GameProxyServiceProto.GetAppIdAppSecretParamOrBuilder getParamOrBuilder();
  }
  /**
   * Protobuf type {@code fm.lizhi.commons.template.datacenter.protocol.RequestGetAppIdAppSecretByParam}
   *
   * <pre>
   * GameProxyService.java
   * 根据传入的appId、gameId、type 获取对应的appId、appSecret
   * domain = 4302, op = 241
   * </pre>
   */
  public static final class RequestGetAppIdAppSecretByParam extends
      com.google.protobuf.GeneratedMessage
      implements RequestGetAppIdAppSecretByParamOrBuilder {
    // Use RequestGetAppIdAppSecretByParam.newBuilder() to construct.
    private RequestGetAppIdAppSecretByParam(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
      super(builder);
      this.unknownFields = builder.getUnknownFields();
    }
    private RequestGetAppIdAppSecretByParam(boolean noInit) { this.unknownFields = com.google.protobuf.UnknownFieldSet.getDefaultInstance(); }

    private static final RequestGetAppIdAppSecretByParam defaultInstance;
    public static RequestGetAppIdAppSecretByParam getDefaultInstance() {
      return defaultInstance;
    }

    public RequestGetAppIdAppSecretByParam getDefaultInstanceForType() {
      return defaultInstance;
    }

    private final com.google.protobuf.UnknownFieldSet unknownFields;
    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
        getUnknownFields() {
      return this.unknownFields;
    }
    private RequestGetAppIdAppSecretByParam(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      initFields();
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            default: {
              if (!parseUnknownField(input, unknownFields,
                                     extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
            case 10: {
              fm.lizhi.ocean.seal.protocol.GameProxyServiceProto.GetAppIdAppSecretParam.Builder subBuilder = null;
              if (((bitField0_ & 0x00000001) == 0x00000001)) {
                subBuilder = param_.toBuilder();
              }
              param_ = input.readMessage(fm.lizhi.ocean.seal.protocol.GameProxyServiceProto.GetAppIdAppSecretParam.PARSER, extensionRegistry);
              if (subBuilder != null) {
                subBuilder.mergeFrom(param_);
                param_ = subBuilder.buildPartial();
              }
              bitField0_ |= 0x00000001;
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e.getMessage()).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return fm.lizhi.ocean.seal.protocol.GameProxyServiceProto.internal_static_fm_lizhi_commons_template_datacenter_protocol_RequestGetAppIdAppSecretByParam_descriptor;
    }

    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return fm.lizhi.ocean.seal.protocol.GameProxyServiceProto.internal_static_fm_lizhi_commons_template_datacenter_protocol_RequestGetAppIdAppSecretByParam_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              fm.lizhi.ocean.seal.protocol.GameProxyServiceProto.RequestGetAppIdAppSecretByParam.class, fm.lizhi.ocean.seal.protocol.GameProxyServiceProto.RequestGetAppIdAppSecretByParam.Builder.class);
    }

    public static com.google.protobuf.Parser<RequestGetAppIdAppSecretByParam> PARSER =
        new com.google.protobuf.AbstractParser<RequestGetAppIdAppSecretByParam>() {
      public RequestGetAppIdAppSecretByParam parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new RequestGetAppIdAppSecretByParam(input, extensionRegistry);
      }
    };

    @java.lang.Override
    public com.google.protobuf.Parser<RequestGetAppIdAppSecretByParam> getParserForType() {
      return PARSER;
    }

    private int bitField0_;
    // optional .fm.lizhi.commons.template.datacenter.protocol.GetAppIdAppSecretParam param = 1;
    public static final int PARAM_FIELD_NUMBER = 1;
    private fm.lizhi.ocean.seal.protocol.GameProxyServiceProto.GetAppIdAppSecretParam param_;
    /**
     * <code>optional .fm.lizhi.commons.template.datacenter.protocol.GetAppIdAppSecretParam param = 1;</code>
     *
     * <pre>
     *参数
     * </pre>
     */
    public boolean hasParam() {
      return ((bitField0_ & 0x00000001) == 0x00000001);
    }
    /**
     * <code>optional .fm.lizhi.commons.template.datacenter.protocol.GetAppIdAppSecretParam param = 1;</code>
     *
     * <pre>
     *参数
     * </pre>
     */
    public fm.lizhi.ocean.seal.protocol.GameProxyServiceProto.GetAppIdAppSecretParam getParam() {
      return param_;
    }
    /**
     * <code>optional .fm.lizhi.commons.template.datacenter.protocol.GetAppIdAppSecretParam param = 1;</code>
     *
     * <pre>
     *参数
     * </pre>
     */
    public fm.lizhi.ocean.seal.protocol.GameProxyServiceProto.GetAppIdAppSecretParamOrBuilder getParamOrBuilder() {
      return param_;
    }

    private void initFields() {
      param_ = fm.lizhi.ocean.seal.protocol.GameProxyServiceProto.GetAppIdAppSecretParam.getDefaultInstance();
    }
    private byte memoizedIsInitialized = -1;
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized != -1) return isInitialized == 1;

      memoizedIsInitialized = 1;
      return true;
    }

    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      getSerializedSize();
      if (((bitField0_ & 0x00000001) == 0x00000001)) {
        output.writeMessage(1, param_);
      }
      getUnknownFields().writeTo(output);
    }

    private int memoizedSerializedSize = -1;
    public int getSerializedSize() {
      int size = memoizedSerializedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) == 0x00000001)) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(1, param_);
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSerializedSize = size;
      return size;
    }

    private static final long serialVersionUID = 0L;
    @java.lang.Override
    protected java.lang.Object writeReplace()
        throws java.io.ObjectStreamException {
      return super.writeReplace();
    }

    public static fm.lizhi.ocean.seal.protocol.GameProxyServiceProto.RequestGetAppIdAppSecretByParam parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static fm.lizhi.ocean.seal.protocol.GameProxyServiceProto.RequestGetAppIdAppSecretByParam parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static fm.lizhi.ocean.seal.protocol.GameProxyServiceProto.RequestGetAppIdAppSecretByParam parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static fm.lizhi.ocean.seal.protocol.GameProxyServiceProto.RequestGetAppIdAppSecretByParam parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static fm.lizhi.ocean.seal.protocol.GameProxyServiceProto.RequestGetAppIdAppSecretByParam parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return PARSER.parseFrom(input);
    }
    public static fm.lizhi.ocean.seal.protocol.GameProxyServiceProto.RequestGetAppIdAppSecretByParam parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseFrom(input, extensionRegistry);
    }
    public static fm.lizhi.ocean.seal.protocol.GameProxyServiceProto.RequestGetAppIdAppSecretByParam parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return PARSER.parseDelimitedFrom(input);
    }
    public static fm.lizhi.ocean.seal.protocol.GameProxyServiceProto.RequestGetAppIdAppSecretByParam parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseDelimitedFrom(input, extensionRegistry);
    }
    public static fm.lizhi.ocean.seal.protocol.GameProxyServiceProto.RequestGetAppIdAppSecretByParam parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return PARSER.parseFrom(input);
    }
    public static fm.lizhi.ocean.seal.protocol.GameProxyServiceProto.RequestGetAppIdAppSecretByParam parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseFrom(input, extensionRegistry);
    }

    public static Builder newBuilder() { return Builder.create(); }
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder(fm.lizhi.ocean.seal.protocol.GameProxyServiceProto.RequestGetAppIdAppSecretByParam prototype) {
      return newBuilder().mergeFrom(prototype);
    }
    public Builder toBuilder() { return newBuilder(this); }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code fm.lizhi.commons.template.datacenter.protocol.RequestGetAppIdAppSecretByParam}
     *
     * <pre>
     * GameProxyService.java
     * 根据传入的appId、gameId、type 获取对应的appId、appSecret
     * domain = 4302, op = 241
     * </pre>
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessage.Builder<Builder>
       implements fm.lizhi.ocean.seal.protocol.GameProxyServiceProto.RequestGetAppIdAppSecretByParamOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return fm.lizhi.ocean.seal.protocol.GameProxyServiceProto.internal_static_fm_lizhi_commons_template_datacenter_protocol_RequestGetAppIdAppSecretByParam_descriptor;
      }

      protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return fm.lizhi.ocean.seal.protocol.GameProxyServiceProto.internal_static_fm_lizhi_commons_template_datacenter_protocol_RequestGetAppIdAppSecretByParam_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                fm.lizhi.ocean.seal.protocol.GameProxyServiceProto.RequestGetAppIdAppSecretByParam.class, fm.lizhi.ocean.seal.protocol.GameProxyServiceProto.RequestGetAppIdAppSecretByParam.Builder.class);
      }

      // Construct using fm.lizhi.ocean.seal.protocol.GameProxyServiceProto.RequestGetAppIdAppSecretByParam.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessage.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessage.alwaysUseFieldBuilders) {
          getParamFieldBuilder();
        }
      }
      private static Builder create() {
        return new Builder();
      }

      public Builder clear() {
        super.clear();
        if (paramBuilder_ == null) {
          param_ = fm.lizhi.ocean.seal.protocol.GameProxyServiceProto.GetAppIdAppSecretParam.getDefaultInstance();
        } else {
          paramBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000001);
        return this;
      }

      public Builder clone() {
        return create().mergeFrom(buildPartial());
      }

      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return fm.lizhi.ocean.seal.protocol.GameProxyServiceProto.internal_static_fm_lizhi_commons_template_datacenter_protocol_RequestGetAppIdAppSecretByParam_descriptor;
      }

      public fm.lizhi.ocean.seal.protocol.GameProxyServiceProto.RequestGetAppIdAppSecretByParam getDefaultInstanceForType() {
        return fm.lizhi.ocean.seal.protocol.GameProxyServiceProto.RequestGetAppIdAppSecretByParam.getDefaultInstance();
      }

      public fm.lizhi.ocean.seal.protocol.GameProxyServiceProto.RequestGetAppIdAppSecretByParam build() {
        fm.lizhi.ocean.seal.protocol.GameProxyServiceProto.RequestGetAppIdAppSecretByParam result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      public fm.lizhi.ocean.seal.protocol.GameProxyServiceProto.RequestGetAppIdAppSecretByParam buildPartial() {
        fm.lizhi.ocean.seal.protocol.GameProxyServiceProto.RequestGetAppIdAppSecretByParam result = new fm.lizhi.ocean.seal.protocol.GameProxyServiceProto.RequestGetAppIdAppSecretByParam(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) == 0x00000001)) {
          to_bitField0_ |= 0x00000001;
        }
        if (paramBuilder_ == null) {
          result.param_ = param_;
        } else {
          result.param_ = paramBuilder_.build();
        }
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof fm.lizhi.ocean.seal.protocol.GameProxyServiceProto.RequestGetAppIdAppSecretByParam) {
          return mergeFrom((fm.lizhi.ocean.seal.protocol.GameProxyServiceProto.RequestGetAppIdAppSecretByParam)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(fm.lizhi.ocean.seal.protocol.GameProxyServiceProto.RequestGetAppIdAppSecretByParam other) {
        if (other == fm.lizhi.ocean.seal.protocol.GameProxyServiceProto.RequestGetAppIdAppSecretByParam.getDefaultInstance()) return this;
        if (other.hasParam()) {
          mergeParam(other.getParam());
        }
        this.mergeUnknownFields(other.getUnknownFields());
        return this;
      }

      public final boolean isInitialized() {
        return true;
      }

      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        fm.lizhi.ocean.seal.protocol.GameProxyServiceProto.RequestGetAppIdAppSecretByParam parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (fm.lizhi.ocean.seal.protocol.GameProxyServiceProto.RequestGetAppIdAppSecretByParam) e.getUnfinishedMessage();
          throw e;
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      // optional .fm.lizhi.commons.template.datacenter.protocol.GetAppIdAppSecretParam param = 1;
      private fm.lizhi.ocean.seal.protocol.GameProxyServiceProto.GetAppIdAppSecretParam param_ = fm.lizhi.ocean.seal.protocol.GameProxyServiceProto.GetAppIdAppSecretParam.getDefaultInstance();
      private com.google.protobuf.SingleFieldBuilder<
          fm.lizhi.ocean.seal.protocol.GameProxyServiceProto.GetAppIdAppSecretParam, fm.lizhi.ocean.seal.protocol.GameProxyServiceProto.GetAppIdAppSecretParam.Builder, fm.lizhi.ocean.seal.protocol.GameProxyServiceProto.GetAppIdAppSecretParamOrBuilder> paramBuilder_;
      /**
       * <code>optional .fm.lizhi.commons.template.datacenter.protocol.GetAppIdAppSecretParam param = 1;</code>
       *
       * <pre>
       *参数
       * </pre>
       */
      public boolean hasParam() {
        return ((bitField0_ & 0x00000001) == 0x00000001);
      }
      /**
       * <code>optional .fm.lizhi.commons.template.datacenter.protocol.GetAppIdAppSecretParam param = 1;</code>
       *
       * <pre>
       *参数
       * </pre>
       */
      public fm.lizhi.ocean.seal.protocol.GameProxyServiceProto.GetAppIdAppSecretParam getParam() {
        if (paramBuilder_ == null) {
          return param_;
        } else {
          return paramBuilder_.getMessage();
        }
      }
      /**
       * <code>optional .fm.lizhi.commons.template.datacenter.protocol.GetAppIdAppSecretParam param = 1;</code>
       *
       * <pre>
       *参数
       * </pre>
       */
      public Builder setParam(fm.lizhi.ocean.seal.protocol.GameProxyServiceProto.GetAppIdAppSecretParam value) {
        if (paramBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          param_ = value;
          onChanged();
        } else {
          paramBuilder_.setMessage(value);
        }
        bitField0_ |= 0x00000001;
        return this;
      }
      /**
       * <code>optional .fm.lizhi.commons.template.datacenter.protocol.GetAppIdAppSecretParam param = 1;</code>
       *
       * <pre>
       *参数
       * </pre>
       */
      public Builder setParam(
          fm.lizhi.ocean.seal.protocol.GameProxyServiceProto.GetAppIdAppSecretParam.Builder builderForValue) {
        if (paramBuilder_ == null) {
          param_ = builderForValue.build();
          onChanged();
        } else {
          paramBuilder_.setMessage(builderForValue.build());
        }
        bitField0_ |= 0x00000001;
        return this;
      }
      /**
       * <code>optional .fm.lizhi.commons.template.datacenter.protocol.GetAppIdAppSecretParam param = 1;</code>
       *
       * <pre>
       *参数
       * </pre>
       */
      public Builder mergeParam(fm.lizhi.ocean.seal.protocol.GameProxyServiceProto.GetAppIdAppSecretParam value) {
        if (paramBuilder_ == null) {
          if (((bitField0_ & 0x00000001) == 0x00000001) &&
              param_ != fm.lizhi.ocean.seal.protocol.GameProxyServiceProto.GetAppIdAppSecretParam.getDefaultInstance()) {
            param_ =
              fm.lizhi.ocean.seal.protocol.GameProxyServiceProto.GetAppIdAppSecretParam.newBuilder(param_).mergeFrom(value).buildPartial();
          } else {
            param_ = value;
          }
          onChanged();
        } else {
          paramBuilder_.mergeFrom(value);
        }
        bitField0_ |= 0x00000001;
        return this;
      }
      /**
       * <code>optional .fm.lizhi.commons.template.datacenter.protocol.GetAppIdAppSecretParam param = 1;</code>
       *
       * <pre>
       *参数
       * </pre>
       */
      public Builder clearParam() {
        if (paramBuilder_ == null) {
          param_ = fm.lizhi.ocean.seal.protocol.GameProxyServiceProto.GetAppIdAppSecretParam.getDefaultInstance();
          onChanged();
        } else {
          paramBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000001);
        return this;
      }
      /**
       * <code>optional .fm.lizhi.commons.template.datacenter.protocol.GetAppIdAppSecretParam param = 1;</code>
       *
       * <pre>
       *参数
       * </pre>
       */
      public fm.lizhi.ocean.seal.protocol.GameProxyServiceProto.GetAppIdAppSecretParam.Builder getParamBuilder() {
        bitField0_ |= 0x00000001;
        onChanged();
        return getParamFieldBuilder().getBuilder();
      }
      /**
       * <code>optional .fm.lizhi.commons.template.datacenter.protocol.GetAppIdAppSecretParam param = 1;</code>
       *
       * <pre>
       *参数
       * </pre>
       */
      public fm.lizhi.ocean.seal.protocol.GameProxyServiceProto.GetAppIdAppSecretParamOrBuilder getParamOrBuilder() {
        if (paramBuilder_ != null) {
          return paramBuilder_.getMessageOrBuilder();
        } else {
          return param_;
        }
      }
      /**
       * <code>optional .fm.lizhi.commons.template.datacenter.protocol.GetAppIdAppSecretParam param = 1;</code>
       *
       * <pre>
       *参数
       * </pre>
       */
      private com.google.protobuf.SingleFieldBuilder<
          fm.lizhi.ocean.seal.protocol.GameProxyServiceProto.GetAppIdAppSecretParam, fm.lizhi.ocean.seal.protocol.GameProxyServiceProto.GetAppIdAppSecretParam.Builder, fm.lizhi.ocean.seal.protocol.GameProxyServiceProto.GetAppIdAppSecretParamOrBuilder> 
          getParamFieldBuilder() {
        if (paramBuilder_ == null) {
          paramBuilder_ = new com.google.protobuf.SingleFieldBuilder<
              fm.lizhi.ocean.seal.protocol.GameProxyServiceProto.GetAppIdAppSecretParam, fm.lizhi.ocean.seal.protocol.GameProxyServiceProto.GetAppIdAppSecretParam.Builder, fm.lizhi.ocean.seal.protocol.GameProxyServiceProto.GetAppIdAppSecretParamOrBuilder>(
                  param_,
                  getParentForChildren(),
                  isClean());
          param_ = null;
        }
        return paramBuilder_;
      }

      // @@protoc_insertion_point(builder_scope:fm.lizhi.commons.template.datacenter.protocol.RequestGetAppIdAppSecretByParam)
    }

    static {
      defaultInstance = new RequestGetAppIdAppSecretByParam(true);
      defaultInstance.initFields();
    }

    // @@protoc_insertion_point(class_scope:fm.lizhi.commons.template.datacenter.protocol.RequestGetAppIdAppSecretByParam)
  }

  public interface ResponseGetAppIdAppSecretByParamOrBuilder
      extends com.google.protobuf.MessageOrBuilder {

    // optional string appId = 1;
    /**
     * <code>optional string appId = 1;</code>
     */
    boolean hasAppId();
    /**
     * <code>optional string appId = 1;</code>
     */
    java.lang.String getAppId();
    /**
     * <code>optional string appId = 1;</code>
     */
    com.google.protobuf.ByteString
        getAppIdBytes();

    // optional string appSecret = 2;
    /**
     * <code>optional string appSecret = 2;</code>
     */
    boolean hasAppSecret();
    /**
     * <code>optional string appSecret = 2;</code>
     */
    java.lang.String getAppSecret();
    /**
     * <code>optional string appSecret = 2;</code>
     */
    com.google.protobuf.ByteString
        getAppSecretBytes();
  }
  /**
   * Protobuf type {@code fm.lizhi.commons.template.datacenter.protocol.ResponseGetAppIdAppSecretByParam}
   *
   * <pre>
   * rcode == 1 (ILLEGAL_PARAMS) = 参数非法
   * rcode == 2 (NOT_EXISTS) = 映射不存在
   * rcode == 3 (ERROR) = 内部错误
   * </pre>
   */
  public static final class ResponseGetAppIdAppSecretByParam extends
      com.google.protobuf.GeneratedMessage
      implements ResponseGetAppIdAppSecretByParamOrBuilder {
    // Use ResponseGetAppIdAppSecretByParam.newBuilder() to construct.
    private ResponseGetAppIdAppSecretByParam(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
      super(builder);
      this.unknownFields = builder.getUnknownFields();
    }
    private ResponseGetAppIdAppSecretByParam(boolean noInit) { this.unknownFields = com.google.protobuf.UnknownFieldSet.getDefaultInstance(); }

    private static final ResponseGetAppIdAppSecretByParam defaultInstance;
    public static ResponseGetAppIdAppSecretByParam getDefaultInstance() {
      return defaultInstance;
    }

    public ResponseGetAppIdAppSecretByParam getDefaultInstanceForType() {
      return defaultInstance;
    }

    private final com.google.protobuf.UnknownFieldSet unknownFields;
    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
        getUnknownFields() {
      return this.unknownFields;
    }
    private ResponseGetAppIdAppSecretByParam(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      initFields();
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            default: {
              if (!parseUnknownField(input, unknownFields,
                                     extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
            case 10: {
              bitField0_ |= 0x00000001;
              appId_ = input.readBytes();
              break;
            }
            case 18: {
              bitField0_ |= 0x00000002;
              appSecret_ = input.readBytes();
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e.getMessage()).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return fm.lizhi.ocean.seal.protocol.GameProxyServiceProto.internal_static_fm_lizhi_commons_template_datacenter_protocol_ResponseGetAppIdAppSecretByParam_descriptor;
    }

    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return fm.lizhi.ocean.seal.protocol.GameProxyServiceProto.internal_static_fm_lizhi_commons_template_datacenter_protocol_ResponseGetAppIdAppSecretByParam_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              fm.lizhi.ocean.seal.protocol.GameProxyServiceProto.ResponseGetAppIdAppSecretByParam.class, fm.lizhi.ocean.seal.protocol.GameProxyServiceProto.ResponseGetAppIdAppSecretByParam.Builder.class);
    }

    public static com.google.protobuf.Parser<ResponseGetAppIdAppSecretByParam> PARSER =
        new com.google.protobuf.AbstractParser<ResponseGetAppIdAppSecretByParam>() {
      public ResponseGetAppIdAppSecretByParam parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new ResponseGetAppIdAppSecretByParam(input, extensionRegistry);
      }
    };

    @java.lang.Override
    public com.google.protobuf.Parser<ResponseGetAppIdAppSecretByParam> getParserForType() {
      return PARSER;
    }

    private int bitField0_;
    // optional string appId = 1;
    public static final int APPID_FIELD_NUMBER = 1;
    private java.lang.Object appId_;
    /**
     * <code>optional string appId = 1;</code>
     */
    public boolean hasAppId() {
      return ((bitField0_ & 0x00000001) == 0x00000001);
    }
    /**
     * <code>optional string appId = 1;</code>
     */
    public java.lang.String getAppId() {
      java.lang.Object ref = appId_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          appId_ = s;
        }
        return s;
      }
    }
    /**
     * <code>optional string appId = 1;</code>
     */
    public com.google.protobuf.ByteString
        getAppIdBytes() {
      java.lang.Object ref = appId_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        appId_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    // optional string appSecret = 2;
    public static final int APPSECRET_FIELD_NUMBER = 2;
    private java.lang.Object appSecret_;
    /**
     * <code>optional string appSecret = 2;</code>
     */
    public boolean hasAppSecret() {
      return ((bitField0_ & 0x00000002) == 0x00000002);
    }
    /**
     * <code>optional string appSecret = 2;</code>
     */
    public java.lang.String getAppSecret() {
      java.lang.Object ref = appSecret_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          appSecret_ = s;
        }
        return s;
      }
    }
    /**
     * <code>optional string appSecret = 2;</code>
     */
    public com.google.protobuf.ByteString
        getAppSecretBytes() {
      java.lang.Object ref = appSecret_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        appSecret_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    private void initFields() {
      appId_ = "";
      appSecret_ = "";
    }
    private byte memoizedIsInitialized = -1;
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized != -1) return isInitialized == 1;

      memoizedIsInitialized = 1;
      return true;
    }

    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      getSerializedSize();
      if (((bitField0_ & 0x00000001) == 0x00000001)) {
        output.writeBytes(1, getAppIdBytes());
      }
      if (((bitField0_ & 0x00000002) == 0x00000002)) {
        output.writeBytes(2, getAppSecretBytes());
      }
      getUnknownFields().writeTo(output);
    }

    private int memoizedSerializedSize = -1;
    public int getSerializedSize() {
      int size = memoizedSerializedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) == 0x00000001)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(1, getAppIdBytes());
      }
      if (((bitField0_ & 0x00000002) == 0x00000002)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(2, getAppSecretBytes());
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSerializedSize = size;
      return size;
    }

    private static final long serialVersionUID = 0L;
    @java.lang.Override
    protected java.lang.Object writeReplace()
        throws java.io.ObjectStreamException {
      return super.writeReplace();
    }

    public static fm.lizhi.ocean.seal.protocol.GameProxyServiceProto.ResponseGetAppIdAppSecretByParam parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static fm.lizhi.ocean.seal.protocol.GameProxyServiceProto.ResponseGetAppIdAppSecretByParam parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static fm.lizhi.ocean.seal.protocol.GameProxyServiceProto.ResponseGetAppIdAppSecretByParam parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static fm.lizhi.ocean.seal.protocol.GameProxyServiceProto.ResponseGetAppIdAppSecretByParam parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static fm.lizhi.ocean.seal.protocol.GameProxyServiceProto.ResponseGetAppIdAppSecretByParam parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return PARSER.parseFrom(input);
    }
    public static fm.lizhi.ocean.seal.protocol.GameProxyServiceProto.ResponseGetAppIdAppSecretByParam parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseFrom(input, extensionRegistry);
    }
    public static fm.lizhi.ocean.seal.protocol.GameProxyServiceProto.ResponseGetAppIdAppSecretByParam parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return PARSER.parseDelimitedFrom(input);
    }
    public static fm.lizhi.ocean.seal.protocol.GameProxyServiceProto.ResponseGetAppIdAppSecretByParam parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseDelimitedFrom(input, extensionRegistry);
    }
    public static fm.lizhi.ocean.seal.protocol.GameProxyServiceProto.ResponseGetAppIdAppSecretByParam parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return PARSER.parseFrom(input);
    }
    public static fm.lizhi.ocean.seal.protocol.GameProxyServiceProto.ResponseGetAppIdAppSecretByParam parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseFrom(input, extensionRegistry);
    }

    public static Builder newBuilder() { return Builder.create(); }
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder(fm.lizhi.ocean.seal.protocol.GameProxyServiceProto.ResponseGetAppIdAppSecretByParam prototype) {
      return newBuilder().mergeFrom(prototype);
    }
    public Builder toBuilder() { return newBuilder(this); }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code fm.lizhi.commons.template.datacenter.protocol.ResponseGetAppIdAppSecretByParam}
     *
     * <pre>
     * rcode == 1 (ILLEGAL_PARAMS) = 参数非法
     * rcode == 2 (NOT_EXISTS) = 映射不存在
     * rcode == 3 (ERROR) = 内部错误
     * </pre>
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessage.Builder<Builder>
       implements fm.lizhi.ocean.seal.protocol.GameProxyServiceProto.ResponseGetAppIdAppSecretByParamOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return fm.lizhi.ocean.seal.protocol.GameProxyServiceProto.internal_static_fm_lizhi_commons_template_datacenter_protocol_ResponseGetAppIdAppSecretByParam_descriptor;
      }

      protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return fm.lizhi.ocean.seal.protocol.GameProxyServiceProto.internal_static_fm_lizhi_commons_template_datacenter_protocol_ResponseGetAppIdAppSecretByParam_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                fm.lizhi.ocean.seal.protocol.GameProxyServiceProto.ResponseGetAppIdAppSecretByParam.class, fm.lizhi.ocean.seal.protocol.GameProxyServiceProto.ResponseGetAppIdAppSecretByParam.Builder.class);
      }

      // Construct using fm.lizhi.ocean.seal.protocol.GameProxyServiceProto.ResponseGetAppIdAppSecretByParam.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessage.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessage.alwaysUseFieldBuilders) {
        }
      }
      private static Builder create() {
        return new Builder();
      }

      public Builder clear() {
        super.clear();
        appId_ = "";
        bitField0_ = (bitField0_ & ~0x00000001);
        appSecret_ = "";
        bitField0_ = (bitField0_ & ~0x00000002);
        return this;
      }

      public Builder clone() {
        return create().mergeFrom(buildPartial());
      }

      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return fm.lizhi.ocean.seal.protocol.GameProxyServiceProto.internal_static_fm_lizhi_commons_template_datacenter_protocol_ResponseGetAppIdAppSecretByParam_descriptor;
      }

      public fm.lizhi.ocean.seal.protocol.GameProxyServiceProto.ResponseGetAppIdAppSecretByParam getDefaultInstanceForType() {
        return fm.lizhi.ocean.seal.protocol.GameProxyServiceProto.ResponseGetAppIdAppSecretByParam.getDefaultInstance();
      }

      public fm.lizhi.ocean.seal.protocol.GameProxyServiceProto.ResponseGetAppIdAppSecretByParam build() {
        fm.lizhi.ocean.seal.protocol.GameProxyServiceProto.ResponseGetAppIdAppSecretByParam result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      public fm.lizhi.ocean.seal.protocol.GameProxyServiceProto.ResponseGetAppIdAppSecretByParam buildPartial() {
        fm.lizhi.ocean.seal.protocol.GameProxyServiceProto.ResponseGetAppIdAppSecretByParam result = new fm.lizhi.ocean.seal.protocol.GameProxyServiceProto.ResponseGetAppIdAppSecretByParam(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) == 0x00000001)) {
          to_bitField0_ |= 0x00000001;
        }
        result.appId_ = appId_;
        if (((from_bitField0_ & 0x00000002) == 0x00000002)) {
          to_bitField0_ |= 0x00000002;
        }
        result.appSecret_ = appSecret_;
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof fm.lizhi.ocean.seal.protocol.GameProxyServiceProto.ResponseGetAppIdAppSecretByParam) {
          return mergeFrom((fm.lizhi.ocean.seal.protocol.GameProxyServiceProto.ResponseGetAppIdAppSecretByParam)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(fm.lizhi.ocean.seal.protocol.GameProxyServiceProto.ResponseGetAppIdAppSecretByParam other) {
        if (other == fm.lizhi.ocean.seal.protocol.GameProxyServiceProto.ResponseGetAppIdAppSecretByParam.getDefaultInstance()) return this;
        if (other.hasAppId()) {
          bitField0_ |= 0x00000001;
          appId_ = other.appId_;
          onChanged();
        }
        if (other.hasAppSecret()) {
          bitField0_ |= 0x00000002;
          appSecret_ = other.appSecret_;
          onChanged();
        }
        this.mergeUnknownFields(other.getUnknownFields());
        return this;
      }

      public final boolean isInitialized() {
        return true;
      }

      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        fm.lizhi.ocean.seal.protocol.GameProxyServiceProto.ResponseGetAppIdAppSecretByParam parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (fm.lizhi.ocean.seal.protocol.GameProxyServiceProto.ResponseGetAppIdAppSecretByParam) e.getUnfinishedMessage();
          throw e;
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      // optional string appId = 1;
      private java.lang.Object appId_ = "";
      /**
       * <code>optional string appId = 1;</code>
       */
      public boolean hasAppId() {
        return ((bitField0_ & 0x00000001) == 0x00000001);
      }
      /**
       * <code>optional string appId = 1;</code>
       */
      public java.lang.String getAppId() {
        java.lang.Object ref = appId_;
        if (!(ref instanceof java.lang.String)) {
          java.lang.String s = ((com.google.protobuf.ByteString) ref)
              .toStringUtf8();
          appId_ = s;
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <code>optional string appId = 1;</code>
       */
      public com.google.protobuf.ByteString
          getAppIdBytes() {
        java.lang.Object ref = appId_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          appId_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <code>optional string appId = 1;</code>
       */
      public Builder setAppId(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000001;
        appId_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional string appId = 1;</code>
       */
      public Builder clearAppId() {
        bitField0_ = (bitField0_ & ~0x00000001);
        appId_ = getDefaultInstance().getAppId();
        onChanged();
        return this;
      }
      /**
       * <code>optional string appId = 1;</code>
       */
      public Builder setAppIdBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000001;
        appId_ = value;
        onChanged();
        return this;
      }

      // optional string appSecret = 2;
      private java.lang.Object appSecret_ = "";
      /**
       * <code>optional string appSecret = 2;</code>
       */
      public boolean hasAppSecret() {
        return ((bitField0_ & 0x00000002) == 0x00000002);
      }
      /**
       * <code>optional string appSecret = 2;</code>
       */
      public java.lang.String getAppSecret() {
        java.lang.Object ref = appSecret_;
        if (!(ref instanceof java.lang.String)) {
          java.lang.String s = ((com.google.protobuf.ByteString) ref)
              .toStringUtf8();
          appSecret_ = s;
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <code>optional string appSecret = 2;</code>
       */
      public com.google.protobuf.ByteString
          getAppSecretBytes() {
        java.lang.Object ref = appSecret_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          appSecret_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <code>optional string appSecret = 2;</code>
       */
      public Builder setAppSecret(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000002;
        appSecret_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional string appSecret = 2;</code>
       */
      public Builder clearAppSecret() {
        bitField0_ = (bitField0_ & ~0x00000002);
        appSecret_ = getDefaultInstance().getAppSecret();
        onChanged();
        return this;
      }
      /**
       * <code>optional string appSecret = 2;</code>
       */
      public Builder setAppSecretBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000002;
        appSecret_ = value;
        onChanged();
        return this;
      }

      // @@protoc_insertion_point(builder_scope:fm.lizhi.commons.template.datacenter.protocol.ResponseGetAppIdAppSecretByParam)
    }

    static {
      defaultInstance = new ResponseGetAppIdAppSecretByParam(true);
      defaultInstance.initFields();
    }

    // @@protoc_insertion_point(class_scope:fm.lizhi.commons.template.datacenter.protocol.ResponseGetAppIdAppSecretByParam)
  }

  private static com.google.protobuf.Descriptors.Descriptor
    internal_static_fm_lizhi_commons_template_datacenter_protocol_InvokeTargetParams_descriptor;
  private static
    com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internal_static_fm_lizhi_commons_template_datacenter_protocol_InvokeTargetParams_fieldAccessorTable;
  private static com.google.protobuf.Descriptors.Descriptor
    internal_static_fm_lizhi_commons_template_datacenter_protocol_GetAppIdAppSecretParam_descriptor;
  private static
    com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internal_static_fm_lizhi_commons_template_datacenter_protocol_GetAppIdAppSecretParam_fieldAccessorTable;
  private static com.google.protobuf.Descriptors.Descriptor
    internal_static_fm_lizhi_commons_template_datacenter_protocol_RequestInvokeTarget_descriptor;
  private static
    com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internal_static_fm_lizhi_commons_template_datacenter_protocol_RequestInvokeTarget_fieldAccessorTable;
  private static com.google.protobuf.Descriptors.Descriptor
    internal_static_fm_lizhi_commons_template_datacenter_protocol_ResponseInvokeTarget_descriptor;
  private static
    com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internal_static_fm_lizhi_commons_template_datacenter_protocol_ResponseInvokeTarget_fieldAccessorTable;
  private static com.google.protobuf.Descriptors.Descriptor
    internal_static_fm_lizhi_commons_template_datacenter_protocol_RequestGetAppIdAppSecretByParam_descriptor;
  private static
    com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internal_static_fm_lizhi_commons_template_datacenter_protocol_RequestGetAppIdAppSecretByParam_fieldAccessorTable;
  private static com.google.protobuf.Descriptors.Descriptor
    internal_static_fm_lizhi_commons_template_datacenter_protocol_ResponseGetAppIdAppSecretByParam_descriptor;
  private static
    com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internal_static_fm_lizhi_commons_template_datacenter_protocol_ResponseGetAppIdAppSecretByParam_fieldAccessorTable;

  public static com.google.protobuf.Descriptors.FileDescriptor
      getDescriptor() {
    return descriptor;
  }
  private static com.google.protobuf.Descriptors.FileDescriptor
      descriptor;
  static {
    java.lang.String[] descriptorData = {
      "\n\031protocol_game_proxy.proto\022-fm.lizhi.co" +
      "mmons.template.datacenter.protocol\"\206\001\n\022I" +
      "nvokeTargetParams\022\021\n\teventName\030\001 \002(\t\022\017\n\007" +
      "channel\030\002 \002(\t\022\013\n\003env\030\003 \002(\005\022\r\n\005appId\030\004 \002(" +
      "\t\022\016\n\006gameId\030\005 \002(\t\022\020\n\010dataJson\030\006 \002(\t\022\016\n\006r" +
      "oomId\030\007 \001(\t\"E\n\026GetAppIdAppSecretParam\022\r\n" +
      "\005appId\030\001 \001(\t\022\016\n\006gameId\030\002 \001(\t\022\014\n\004type\030\003 \001" +
      "(\005\"g\n\023RequestInvokeTarget\022P\n\005param\030\001 \001(\013" +
      "2A.fm.lizhi.commons.template.datacenter." +
      "protocol.InvokeTargetParams\"B\n\024ResponseI",
      "nvokeTarget\022\017\n\007bizCode\030\001 \002(\005\022\013\n\003msg\030\002 \002(" +
      "\t\022\014\n\004data\030\003 \002(\t\"w\n\037RequestGetAppIdAppSec" +
      "retByParam\022T\n\005param\030\001 \001(\0132E.fm.lizhi.com" +
      "mons.template.datacenter.protocol.GetApp" +
      "IdAppSecretParam\"D\n ResponseGetAppIdAppS" +
      "ecretByParam\022\r\n\005appId\030\001 \001(\t\022\021\n\tappSecret" +
      "\030\002 \001(\tB5\n\034fm.lizhi.ocean.seal.protocolB\025" +
      "GameProxyServiceProto"
    };
    com.google.protobuf.Descriptors.FileDescriptor.InternalDescriptorAssigner assigner =
      new com.google.protobuf.Descriptors.FileDescriptor.InternalDescriptorAssigner() {
        public com.google.protobuf.ExtensionRegistry assignDescriptors(
            com.google.protobuf.Descriptors.FileDescriptor root) {
          descriptor = root;
          internal_static_fm_lizhi_commons_template_datacenter_protocol_InvokeTargetParams_descriptor =
            getDescriptor().getMessageTypes().get(0);
          internal_static_fm_lizhi_commons_template_datacenter_protocol_InvokeTargetParams_fieldAccessorTable = new
            com.google.protobuf.GeneratedMessage.FieldAccessorTable(
              internal_static_fm_lizhi_commons_template_datacenter_protocol_InvokeTargetParams_descriptor,
              new java.lang.String[] { "EventName", "Channel", "Env", "AppId", "GameId", "DataJson", "RoomId", });
          internal_static_fm_lizhi_commons_template_datacenter_protocol_GetAppIdAppSecretParam_descriptor =
            getDescriptor().getMessageTypes().get(1);
          internal_static_fm_lizhi_commons_template_datacenter_protocol_GetAppIdAppSecretParam_fieldAccessorTable = new
            com.google.protobuf.GeneratedMessage.FieldAccessorTable(
              internal_static_fm_lizhi_commons_template_datacenter_protocol_GetAppIdAppSecretParam_descriptor,
              new java.lang.String[] { "AppId", "GameId", "Type", });
          internal_static_fm_lizhi_commons_template_datacenter_protocol_RequestInvokeTarget_descriptor =
            getDescriptor().getMessageTypes().get(2);
          internal_static_fm_lizhi_commons_template_datacenter_protocol_RequestInvokeTarget_fieldAccessorTable = new
            com.google.protobuf.GeneratedMessage.FieldAccessorTable(
              internal_static_fm_lizhi_commons_template_datacenter_protocol_RequestInvokeTarget_descriptor,
              new java.lang.String[] { "Param", });
          internal_static_fm_lizhi_commons_template_datacenter_protocol_ResponseInvokeTarget_descriptor =
            getDescriptor().getMessageTypes().get(3);
          internal_static_fm_lizhi_commons_template_datacenter_protocol_ResponseInvokeTarget_fieldAccessorTable = new
            com.google.protobuf.GeneratedMessage.FieldAccessorTable(
              internal_static_fm_lizhi_commons_template_datacenter_protocol_ResponseInvokeTarget_descriptor,
              new java.lang.String[] { "BizCode", "Msg", "Data", });
          internal_static_fm_lizhi_commons_template_datacenter_protocol_RequestGetAppIdAppSecretByParam_descriptor =
            getDescriptor().getMessageTypes().get(4);
          internal_static_fm_lizhi_commons_template_datacenter_protocol_RequestGetAppIdAppSecretByParam_fieldAccessorTable = new
            com.google.protobuf.GeneratedMessage.FieldAccessorTable(
              internal_static_fm_lizhi_commons_template_datacenter_protocol_RequestGetAppIdAppSecretByParam_descriptor,
              new java.lang.String[] { "Param", });
          internal_static_fm_lizhi_commons_template_datacenter_protocol_ResponseGetAppIdAppSecretByParam_descriptor =
            getDescriptor().getMessageTypes().get(5);
          internal_static_fm_lizhi_commons_template_datacenter_protocol_ResponseGetAppIdAppSecretByParam_fieldAccessorTable = new
            com.google.protobuf.GeneratedMessage.FieldAccessorTable(
              internal_static_fm_lizhi_commons_template_datacenter_protocol_ResponseGetAppIdAppSecretByParam_descriptor,
              new java.lang.String[] { "AppId", "AppSecret", });
          return null;
        }
      };
    com.google.protobuf.Descriptors.FileDescriptor
      .internalBuildGeneratedFileFrom(descriptorData,
        new com.google.protobuf.Descriptors.FileDescriptor[] {
        }, assigner);
  }

  // @@protoc_insertion_point(outer_class_scope)
}
