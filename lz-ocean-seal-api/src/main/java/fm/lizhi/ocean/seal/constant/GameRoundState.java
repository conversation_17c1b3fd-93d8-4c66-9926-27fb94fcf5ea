package fm.lizhi.ocean.seal.constant;

import java.util.HashMap;
import java.util.Map;

/**
 * 游戏局状态
 * <p>
 * Created in 2022-04-27 18:14.
 *
 * <AUTHOR>
 */
public enum GameRoundState {
    //
    LOAD_GAME_WAIT_PAGE(0, "加载等待页"),
    LOAD_GAME(1, "加载游戏"),
    GAME_IN(2, "游戏中"),
    GAME_END(3, "游戏结束"),
    ;
    private int value;
    private String msg;

    private static Map<Integer, GameRoundState> map = new HashMap<>();

    static {
        for (GameRoundState object : GameRoundState.values()) {
            map.put(object.getValue(), object);
        }
    }

    GameRoundState(int value, String msg) {
        this.value = value;
        this.msg = msg;
    }

    public int getValue() {
        return value;
    }

    public String getMsg() {
        return msg;
    }

    /**
     * 根据值类型找枚举
     *
     * @param value 值
     * @return
     */
    public static GameRoundState from(int value) {
        return map.get(value);
    }
}