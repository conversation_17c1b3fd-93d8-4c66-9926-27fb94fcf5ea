// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: protocol_game_round.proto

package fm.lizhi.ocean.seal.protocol;

public final class GameRoundServiceProto {
  private GameRoundServiceProto() {}
  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistry registry) {
  }
  public interface RelationGameRoundParamOrBuilder
      extends com.google.protobuf.MessageOrBuilder {

    // required string channelRoundId = 1;
    /**
     * <code>required string channelRoundId = 1;</code>
     *
     * <pre>
     * 渠道游戏局ID
     * </pre>
     */
    boolean hasChannelRoundId();
    /**
     * <code>required string channelRoundId = 1;</code>
     *
     * <pre>
     * 渠道游戏局ID
     * </pre>
     */
    java.lang.String getChannelRoundId();
    /**
     * <code>required string channelRoundId = 1;</code>
     *
     * <pre>
     * 渠道游戏局ID
     * </pre>
     */
    com.google.protobuf.ByteString
        getChannelRoundIdBytes();

    // required string extra = 2;
    /**
     * <code>required string extra = 2;</code>
     *
     * <pre>
     * 透传参数
     * </pre>
     */
    boolean hasExtra();
    /**
     * <code>required string extra = 2;</code>
     *
     * <pre>
     * 透传参数
     * </pre>
     */
    java.lang.String getExtra();
    /**
     * <code>required string extra = 2;</code>
     *
     * <pre>
     * 透传参数
     * </pre>
     */
    com.google.protobuf.ByteString
        getExtraBytes();
  }
  /**
   * Protobuf type {@code fm.lizhi.commons.template.datacenter.protocol.RelationGameRoundParam}
   */
  public static final class RelationGameRoundParam extends
      com.google.protobuf.GeneratedMessage
      implements RelationGameRoundParamOrBuilder {
    // Use RelationGameRoundParam.newBuilder() to construct.
    private RelationGameRoundParam(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
      super(builder);
      this.unknownFields = builder.getUnknownFields();
    }
    private RelationGameRoundParam(boolean noInit) { this.unknownFields = com.google.protobuf.UnknownFieldSet.getDefaultInstance(); }

    private static final RelationGameRoundParam defaultInstance;
    public static RelationGameRoundParam getDefaultInstance() {
      return defaultInstance;
    }

    public RelationGameRoundParam getDefaultInstanceForType() {
      return defaultInstance;
    }

    private final com.google.protobuf.UnknownFieldSet unknownFields;
    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
        getUnknownFields() {
      return this.unknownFields;
    }
    private RelationGameRoundParam(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      initFields();
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            default: {
              if (!parseUnknownField(input, unknownFields,
                                     extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
            case 10: {
              bitField0_ |= 0x00000001;
              channelRoundId_ = input.readBytes();
              break;
            }
            case 18: {
              bitField0_ |= 0x00000002;
              extra_ = input.readBytes();
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e.getMessage()).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return fm.lizhi.ocean.seal.protocol.GameRoundServiceProto.internal_static_fm_lizhi_commons_template_datacenter_protocol_RelationGameRoundParam_descriptor;
    }

    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return fm.lizhi.ocean.seal.protocol.GameRoundServiceProto.internal_static_fm_lizhi_commons_template_datacenter_protocol_RelationGameRoundParam_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              fm.lizhi.ocean.seal.protocol.GameRoundServiceProto.RelationGameRoundParam.class, fm.lizhi.ocean.seal.protocol.GameRoundServiceProto.RelationGameRoundParam.Builder.class);
    }

    public static com.google.protobuf.Parser<RelationGameRoundParam> PARSER =
        new com.google.protobuf.AbstractParser<RelationGameRoundParam>() {
      public RelationGameRoundParam parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new RelationGameRoundParam(input, extensionRegistry);
      }
    };

    @java.lang.Override
    public com.google.protobuf.Parser<RelationGameRoundParam> getParserForType() {
      return PARSER;
    }

    private int bitField0_;
    // required string channelRoundId = 1;
    public static final int CHANNELROUNDID_FIELD_NUMBER = 1;
    private java.lang.Object channelRoundId_;
    /**
     * <code>required string channelRoundId = 1;</code>
     *
     * <pre>
     * 渠道游戏局ID
     * </pre>
     */
    public boolean hasChannelRoundId() {
      return ((bitField0_ & 0x00000001) == 0x00000001);
    }
    /**
     * <code>required string channelRoundId = 1;</code>
     *
     * <pre>
     * 渠道游戏局ID
     * </pre>
     */
    public java.lang.String getChannelRoundId() {
      java.lang.Object ref = channelRoundId_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          channelRoundId_ = s;
        }
        return s;
      }
    }
    /**
     * <code>required string channelRoundId = 1;</code>
     *
     * <pre>
     * 渠道游戏局ID
     * </pre>
     */
    public com.google.protobuf.ByteString
        getChannelRoundIdBytes() {
      java.lang.Object ref = channelRoundId_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        channelRoundId_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    // required string extra = 2;
    public static final int EXTRA_FIELD_NUMBER = 2;
    private java.lang.Object extra_;
    /**
     * <code>required string extra = 2;</code>
     *
     * <pre>
     * 透传参数
     * </pre>
     */
    public boolean hasExtra() {
      return ((bitField0_ & 0x00000002) == 0x00000002);
    }
    /**
     * <code>required string extra = 2;</code>
     *
     * <pre>
     * 透传参数
     * </pre>
     */
    public java.lang.String getExtra() {
      java.lang.Object ref = extra_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          extra_ = s;
        }
        return s;
      }
    }
    /**
     * <code>required string extra = 2;</code>
     *
     * <pre>
     * 透传参数
     * </pre>
     */
    public com.google.protobuf.ByteString
        getExtraBytes() {
      java.lang.Object ref = extra_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        extra_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    private void initFields() {
      channelRoundId_ = "";
      extra_ = "";
    }
    private byte memoizedIsInitialized = -1;
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized != -1) return isInitialized == 1;

      if (!hasChannelRoundId()) {
        memoizedIsInitialized = 0;
        return false;
      }
      if (!hasExtra()) {
        memoizedIsInitialized = 0;
        return false;
      }
      memoizedIsInitialized = 1;
      return true;
    }

    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      getSerializedSize();
      if (((bitField0_ & 0x00000001) == 0x00000001)) {
        output.writeBytes(1, getChannelRoundIdBytes());
      }
      if (((bitField0_ & 0x00000002) == 0x00000002)) {
        output.writeBytes(2, getExtraBytes());
      }
      getUnknownFields().writeTo(output);
    }

    private int memoizedSerializedSize = -1;
    public int getSerializedSize() {
      int size = memoizedSerializedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) == 0x00000001)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(1, getChannelRoundIdBytes());
      }
      if (((bitField0_ & 0x00000002) == 0x00000002)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(2, getExtraBytes());
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSerializedSize = size;
      return size;
    }

    private static final long serialVersionUID = 0L;
    @java.lang.Override
    protected java.lang.Object writeReplace()
        throws java.io.ObjectStreamException {
      return super.writeReplace();
    }

    public static fm.lizhi.ocean.seal.protocol.GameRoundServiceProto.RelationGameRoundParam parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static fm.lizhi.ocean.seal.protocol.GameRoundServiceProto.RelationGameRoundParam parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static fm.lizhi.ocean.seal.protocol.GameRoundServiceProto.RelationGameRoundParam parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static fm.lizhi.ocean.seal.protocol.GameRoundServiceProto.RelationGameRoundParam parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static fm.lizhi.ocean.seal.protocol.GameRoundServiceProto.RelationGameRoundParam parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return PARSER.parseFrom(input);
    }
    public static fm.lizhi.ocean.seal.protocol.GameRoundServiceProto.RelationGameRoundParam parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseFrom(input, extensionRegistry);
    }
    public static fm.lizhi.ocean.seal.protocol.GameRoundServiceProto.RelationGameRoundParam parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return PARSER.parseDelimitedFrom(input);
    }
    public static fm.lizhi.ocean.seal.protocol.GameRoundServiceProto.RelationGameRoundParam parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseDelimitedFrom(input, extensionRegistry);
    }
    public static fm.lizhi.ocean.seal.protocol.GameRoundServiceProto.RelationGameRoundParam parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return PARSER.parseFrom(input);
    }
    public static fm.lizhi.ocean.seal.protocol.GameRoundServiceProto.RelationGameRoundParam parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseFrom(input, extensionRegistry);
    }

    public static Builder newBuilder() { return Builder.create(); }
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder(fm.lizhi.ocean.seal.protocol.GameRoundServiceProto.RelationGameRoundParam prototype) {
      return newBuilder().mergeFrom(prototype);
    }
    public Builder toBuilder() { return newBuilder(this); }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code fm.lizhi.commons.template.datacenter.protocol.RelationGameRoundParam}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessage.Builder<Builder>
       implements fm.lizhi.ocean.seal.protocol.GameRoundServiceProto.RelationGameRoundParamOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return fm.lizhi.ocean.seal.protocol.GameRoundServiceProto.internal_static_fm_lizhi_commons_template_datacenter_protocol_RelationGameRoundParam_descriptor;
      }

      protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return fm.lizhi.ocean.seal.protocol.GameRoundServiceProto.internal_static_fm_lizhi_commons_template_datacenter_protocol_RelationGameRoundParam_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                fm.lizhi.ocean.seal.protocol.GameRoundServiceProto.RelationGameRoundParam.class, fm.lizhi.ocean.seal.protocol.GameRoundServiceProto.RelationGameRoundParam.Builder.class);
      }

      // Construct using fm.lizhi.ocean.seal.protocol.GameRoundServiceProto.RelationGameRoundParam.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessage.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessage.alwaysUseFieldBuilders) {
        }
      }
      private static Builder create() {
        return new Builder();
      }

      public Builder clear() {
        super.clear();
        channelRoundId_ = "";
        bitField0_ = (bitField0_ & ~0x00000001);
        extra_ = "";
        bitField0_ = (bitField0_ & ~0x00000002);
        return this;
      }

      public Builder clone() {
        return create().mergeFrom(buildPartial());
      }

      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return fm.lizhi.ocean.seal.protocol.GameRoundServiceProto.internal_static_fm_lizhi_commons_template_datacenter_protocol_RelationGameRoundParam_descriptor;
      }

      public fm.lizhi.ocean.seal.protocol.GameRoundServiceProto.RelationGameRoundParam getDefaultInstanceForType() {
        return fm.lizhi.ocean.seal.protocol.GameRoundServiceProto.RelationGameRoundParam.getDefaultInstance();
      }

      public fm.lizhi.ocean.seal.protocol.GameRoundServiceProto.RelationGameRoundParam build() {
        fm.lizhi.ocean.seal.protocol.GameRoundServiceProto.RelationGameRoundParam result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      public fm.lizhi.ocean.seal.protocol.GameRoundServiceProto.RelationGameRoundParam buildPartial() {
        fm.lizhi.ocean.seal.protocol.GameRoundServiceProto.RelationGameRoundParam result = new fm.lizhi.ocean.seal.protocol.GameRoundServiceProto.RelationGameRoundParam(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) == 0x00000001)) {
          to_bitField0_ |= 0x00000001;
        }
        result.channelRoundId_ = channelRoundId_;
        if (((from_bitField0_ & 0x00000002) == 0x00000002)) {
          to_bitField0_ |= 0x00000002;
        }
        result.extra_ = extra_;
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof fm.lizhi.ocean.seal.protocol.GameRoundServiceProto.RelationGameRoundParam) {
          return mergeFrom((fm.lizhi.ocean.seal.protocol.GameRoundServiceProto.RelationGameRoundParam)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(fm.lizhi.ocean.seal.protocol.GameRoundServiceProto.RelationGameRoundParam other) {
        if (other == fm.lizhi.ocean.seal.protocol.GameRoundServiceProto.RelationGameRoundParam.getDefaultInstance()) return this;
        if (other.hasChannelRoundId()) {
          bitField0_ |= 0x00000001;
          channelRoundId_ = other.channelRoundId_;
          onChanged();
        }
        if (other.hasExtra()) {
          bitField0_ |= 0x00000002;
          extra_ = other.extra_;
          onChanged();
        }
        this.mergeUnknownFields(other.getUnknownFields());
        return this;
      }

      public final boolean isInitialized() {
        if (!hasChannelRoundId()) {
          
          return false;
        }
        if (!hasExtra()) {
          
          return false;
        }
        return true;
      }

      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        fm.lizhi.ocean.seal.protocol.GameRoundServiceProto.RelationGameRoundParam parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (fm.lizhi.ocean.seal.protocol.GameRoundServiceProto.RelationGameRoundParam) e.getUnfinishedMessage();
          throw e;
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      // required string channelRoundId = 1;
      private java.lang.Object channelRoundId_ = "";
      /**
       * <code>required string channelRoundId = 1;</code>
       *
       * <pre>
       * 渠道游戏局ID
       * </pre>
       */
      public boolean hasChannelRoundId() {
        return ((bitField0_ & 0x00000001) == 0x00000001);
      }
      /**
       * <code>required string channelRoundId = 1;</code>
       *
       * <pre>
       * 渠道游戏局ID
       * </pre>
       */
      public java.lang.String getChannelRoundId() {
        java.lang.Object ref = channelRoundId_;
        if (!(ref instanceof java.lang.String)) {
          java.lang.String s = ((com.google.protobuf.ByteString) ref)
              .toStringUtf8();
          channelRoundId_ = s;
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <code>required string channelRoundId = 1;</code>
       *
       * <pre>
       * 渠道游戏局ID
       * </pre>
       */
      public com.google.protobuf.ByteString
          getChannelRoundIdBytes() {
        java.lang.Object ref = channelRoundId_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          channelRoundId_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <code>required string channelRoundId = 1;</code>
       *
       * <pre>
       * 渠道游戏局ID
       * </pre>
       */
      public Builder setChannelRoundId(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000001;
        channelRoundId_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>required string channelRoundId = 1;</code>
       *
       * <pre>
       * 渠道游戏局ID
       * </pre>
       */
      public Builder clearChannelRoundId() {
        bitField0_ = (bitField0_ & ~0x00000001);
        channelRoundId_ = getDefaultInstance().getChannelRoundId();
        onChanged();
        return this;
      }
      /**
       * <code>required string channelRoundId = 1;</code>
       *
       * <pre>
       * 渠道游戏局ID
       * </pre>
       */
      public Builder setChannelRoundIdBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000001;
        channelRoundId_ = value;
        onChanged();
        return this;
      }

      // required string extra = 2;
      private java.lang.Object extra_ = "";
      /**
       * <code>required string extra = 2;</code>
       *
       * <pre>
       * 透传参数
       * </pre>
       */
      public boolean hasExtra() {
        return ((bitField0_ & 0x00000002) == 0x00000002);
      }
      /**
       * <code>required string extra = 2;</code>
       *
       * <pre>
       * 透传参数
       * </pre>
       */
      public java.lang.String getExtra() {
        java.lang.Object ref = extra_;
        if (!(ref instanceof java.lang.String)) {
          java.lang.String s = ((com.google.protobuf.ByteString) ref)
              .toStringUtf8();
          extra_ = s;
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <code>required string extra = 2;</code>
       *
       * <pre>
       * 透传参数
       * </pre>
       */
      public com.google.protobuf.ByteString
          getExtraBytes() {
        java.lang.Object ref = extra_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          extra_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <code>required string extra = 2;</code>
       *
       * <pre>
       * 透传参数
       * </pre>
       */
      public Builder setExtra(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000002;
        extra_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>required string extra = 2;</code>
       *
       * <pre>
       * 透传参数
       * </pre>
       */
      public Builder clearExtra() {
        bitField0_ = (bitField0_ & ~0x00000002);
        extra_ = getDefaultInstance().getExtra();
        onChanged();
        return this;
      }
      /**
       * <code>required string extra = 2;</code>
       *
       * <pre>
       * 透传参数
       * </pre>
       */
      public Builder setExtraBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000002;
        extra_ = value;
        onChanged();
        return this;
      }

      // @@protoc_insertion_point(builder_scope:fm.lizhi.commons.template.datacenter.protocol.RelationGameRoundParam)
    }

    static {
      defaultInstance = new RelationGameRoundParam(true);
      defaultInstance.initFields();
    }

    // @@protoc_insertion_point(class_scope:fm.lizhi.commons.template.datacenter.protocol.RelationGameRoundParam)
  }

  public interface RequestRelationGameRoundOrBuilder
      extends com.google.protobuf.MessageOrBuilder {

    // optional .fm.lizhi.commons.template.datacenter.protocol.RelationGameRoundParam param = 1;
    /**
     * <code>optional .fm.lizhi.commons.template.datacenter.protocol.RelationGameRoundParam param = 1;</code>
     *
     * <pre>
     *参数
     * </pre>
     */
    boolean hasParam();
    /**
     * <code>optional .fm.lizhi.commons.template.datacenter.protocol.RelationGameRoundParam param = 1;</code>
     *
     * <pre>
     *参数
     * </pre>
     */
    fm.lizhi.ocean.seal.protocol.GameRoundServiceProto.RelationGameRoundParam getParam();
    /**
     * <code>optional .fm.lizhi.commons.template.datacenter.protocol.RelationGameRoundParam param = 1;</code>
     *
     * <pre>
     *参数
     * </pre>
     */
    fm.lizhi.ocean.seal.protocol.GameRoundServiceProto.RelationGameRoundParamOrBuilder getParamOrBuilder();
  }
  /**
   * Protobuf type {@code fm.lizhi.commons.template.datacenter.protocol.RequestRelationGameRound}
   *
   * <pre>
   * GameRoundService.java
   * 关联游戏局信息
   * domain = 4302, op = 81
   * </pre>
   */
  public static final class RequestRelationGameRound extends
      com.google.protobuf.GeneratedMessage
      implements RequestRelationGameRoundOrBuilder {
    // Use RequestRelationGameRound.newBuilder() to construct.
    private RequestRelationGameRound(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
      super(builder);
      this.unknownFields = builder.getUnknownFields();
    }
    private RequestRelationGameRound(boolean noInit) { this.unknownFields = com.google.protobuf.UnknownFieldSet.getDefaultInstance(); }

    private static final RequestRelationGameRound defaultInstance;
    public static RequestRelationGameRound getDefaultInstance() {
      return defaultInstance;
    }

    public RequestRelationGameRound getDefaultInstanceForType() {
      return defaultInstance;
    }

    private final com.google.protobuf.UnknownFieldSet unknownFields;
    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
        getUnknownFields() {
      return this.unknownFields;
    }
    private RequestRelationGameRound(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      initFields();
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            default: {
              if (!parseUnknownField(input, unknownFields,
                                     extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
            case 10: {
              fm.lizhi.ocean.seal.protocol.GameRoundServiceProto.RelationGameRoundParam.Builder subBuilder = null;
              if (((bitField0_ & 0x00000001) == 0x00000001)) {
                subBuilder = param_.toBuilder();
              }
              param_ = input.readMessage(fm.lizhi.ocean.seal.protocol.GameRoundServiceProto.RelationGameRoundParam.PARSER, extensionRegistry);
              if (subBuilder != null) {
                subBuilder.mergeFrom(param_);
                param_ = subBuilder.buildPartial();
              }
              bitField0_ |= 0x00000001;
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e.getMessage()).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return fm.lizhi.ocean.seal.protocol.GameRoundServiceProto.internal_static_fm_lizhi_commons_template_datacenter_protocol_RequestRelationGameRound_descriptor;
    }

    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return fm.lizhi.ocean.seal.protocol.GameRoundServiceProto.internal_static_fm_lizhi_commons_template_datacenter_protocol_RequestRelationGameRound_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              fm.lizhi.ocean.seal.protocol.GameRoundServiceProto.RequestRelationGameRound.class, fm.lizhi.ocean.seal.protocol.GameRoundServiceProto.RequestRelationGameRound.Builder.class);
    }

    public static com.google.protobuf.Parser<RequestRelationGameRound> PARSER =
        new com.google.protobuf.AbstractParser<RequestRelationGameRound>() {
      public RequestRelationGameRound parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new RequestRelationGameRound(input, extensionRegistry);
      }
    };

    @java.lang.Override
    public com.google.protobuf.Parser<RequestRelationGameRound> getParserForType() {
      return PARSER;
    }

    private int bitField0_;
    // optional .fm.lizhi.commons.template.datacenter.protocol.RelationGameRoundParam param = 1;
    public static final int PARAM_FIELD_NUMBER = 1;
    private fm.lizhi.ocean.seal.protocol.GameRoundServiceProto.RelationGameRoundParam param_;
    /**
     * <code>optional .fm.lizhi.commons.template.datacenter.protocol.RelationGameRoundParam param = 1;</code>
     *
     * <pre>
     *参数
     * </pre>
     */
    public boolean hasParam() {
      return ((bitField0_ & 0x00000001) == 0x00000001);
    }
    /**
     * <code>optional .fm.lizhi.commons.template.datacenter.protocol.RelationGameRoundParam param = 1;</code>
     *
     * <pre>
     *参数
     * </pre>
     */
    public fm.lizhi.ocean.seal.protocol.GameRoundServiceProto.RelationGameRoundParam getParam() {
      return param_;
    }
    /**
     * <code>optional .fm.lizhi.commons.template.datacenter.protocol.RelationGameRoundParam param = 1;</code>
     *
     * <pre>
     *参数
     * </pre>
     */
    public fm.lizhi.ocean.seal.protocol.GameRoundServiceProto.RelationGameRoundParamOrBuilder getParamOrBuilder() {
      return param_;
    }

    private void initFields() {
      param_ = fm.lizhi.ocean.seal.protocol.GameRoundServiceProto.RelationGameRoundParam.getDefaultInstance();
    }
    private byte memoizedIsInitialized = -1;
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized != -1) return isInitialized == 1;

      if (hasParam()) {
        if (!getParam().isInitialized()) {
          memoizedIsInitialized = 0;
          return false;
        }
      }
      memoizedIsInitialized = 1;
      return true;
    }

    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      getSerializedSize();
      if (((bitField0_ & 0x00000001) == 0x00000001)) {
        output.writeMessage(1, param_);
      }
      getUnknownFields().writeTo(output);
    }

    private int memoizedSerializedSize = -1;
    public int getSerializedSize() {
      int size = memoizedSerializedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) == 0x00000001)) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(1, param_);
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSerializedSize = size;
      return size;
    }

    private static final long serialVersionUID = 0L;
    @java.lang.Override
    protected java.lang.Object writeReplace()
        throws java.io.ObjectStreamException {
      return super.writeReplace();
    }

    public static fm.lizhi.ocean.seal.protocol.GameRoundServiceProto.RequestRelationGameRound parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static fm.lizhi.ocean.seal.protocol.GameRoundServiceProto.RequestRelationGameRound parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static fm.lizhi.ocean.seal.protocol.GameRoundServiceProto.RequestRelationGameRound parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static fm.lizhi.ocean.seal.protocol.GameRoundServiceProto.RequestRelationGameRound parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static fm.lizhi.ocean.seal.protocol.GameRoundServiceProto.RequestRelationGameRound parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return PARSER.parseFrom(input);
    }
    public static fm.lizhi.ocean.seal.protocol.GameRoundServiceProto.RequestRelationGameRound parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseFrom(input, extensionRegistry);
    }
    public static fm.lizhi.ocean.seal.protocol.GameRoundServiceProto.RequestRelationGameRound parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return PARSER.parseDelimitedFrom(input);
    }
    public static fm.lizhi.ocean.seal.protocol.GameRoundServiceProto.RequestRelationGameRound parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseDelimitedFrom(input, extensionRegistry);
    }
    public static fm.lizhi.ocean.seal.protocol.GameRoundServiceProto.RequestRelationGameRound parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return PARSER.parseFrom(input);
    }
    public static fm.lizhi.ocean.seal.protocol.GameRoundServiceProto.RequestRelationGameRound parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseFrom(input, extensionRegistry);
    }

    public static Builder newBuilder() { return Builder.create(); }
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder(fm.lizhi.ocean.seal.protocol.GameRoundServiceProto.RequestRelationGameRound prototype) {
      return newBuilder().mergeFrom(prototype);
    }
    public Builder toBuilder() { return newBuilder(this); }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code fm.lizhi.commons.template.datacenter.protocol.RequestRelationGameRound}
     *
     * <pre>
     * GameRoundService.java
     * 关联游戏局信息
     * domain = 4302, op = 81
     * </pre>
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessage.Builder<Builder>
       implements fm.lizhi.ocean.seal.protocol.GameRoundServiceProto.RequestRelationGameRoundOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return fm.lizhi.ocean.seal.protocol.GameRoundServiceProto.internal_static_fm_lizhi_commons_template_datacenter_protocol_RequestRelationGameRound_descriptor;
      }

      protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return fm.lizhi.ocean.seal.protocol.GameRoundServiceProto.internal_static_fm_lizhi_commons_template_datacenter_protocol_RequestRelationGameRound_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                fm.lizhi.ocean.seal.protocol.GameRoundServiceProto.RequestRelationGameRound.class, fm.lizhi.ocean.seal.protocol.GameRoundServiceProto.RequestRelationGameRound.Builder.class);
      }

      // Construct using fm.lizhi.ocean.seal.protocol.GameRoundServiceProto.RequestRelationGameRound.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessage.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessage.alwaysUseFieldBuilders) {
          getParamFieldBuilder();
        }
      }
      private static Builder create() {
        return new Builder();
      }

      public Builder clear() {
        super.clear();
        if (paramBuilder_ == null) {
          param_ = fm.lizhi.ocean.seal.protocol.GameRoundServiceProto.RelationGameRoundParam.getDefaultInstance();
        } else {
          paramBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000001);
        return this;
      }

      public Builder clone() {
        return create().mergeFrom(buildPartial());
      }

      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return fm.lizhi.ocean.seal.protocol.GameRoundServiceProto.internal_static_fm_lizhi_commons_template_datacenter_protocol_RequestRelationGameRound_descriptor;
      }

      public fm.lizhi.ocean.seal.protocol.GameRoundServiceProto.RequestRelationGameRound getDefaultInstanceForType() {
        return fm.lizhi.ocean.seal.protocol.GameRoundServiceProto.RequestRelationGameRound.getDefaultInstance();
      }

      public fm.lizhi.ocean.seal.protocol.GameRoundServiceProto.RequestRelationGameRound build() {
        fm.lizhi.ocean.seal.protocol.GameRoundServiceProto.RequestRelationGameRound result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      public fm.lizhi.ocean.seal.protocol.GameRoundServiceProto.RequestRelationGameRound buildPartial() {
        fm.lizhi.ocean.seal.protocol.GameRoundServiceProto.RequestRelationGameRound result = new fm.lizhi.ocean.seal.protocol.GameRoundServiceProto.RequestRelationGameRound(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) == 0x00000001)) {
          to_bitField0_ |= 0x00000001;
        }
        if (paramBuilder_ == null) {
          result.param_ = param_;
        } else {
          result.param_ = paramBuilder_.build();
        }
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof fm.lizhi.ocean.seal.protocol.GameRoundServiceProto.RequestRelationGameRound) {
          return mergeFrom((fm.lizhi.ocean.seal.protocol.GameRoundServiceProto.RequestRelationGameRound)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(fm.lizhi.ocean.seal.protocol.GameRoundServiceProto.RequestRelationGameRound other) {
        if (other == fm.lizhi.ocean.seal.protocol.GameRoundServiceProto.RequestRelationGameRound.getDefaultInstance()) return this;
        if (other.hasParam()) {
          mergeParam(other.getParam());
        }
        this.mergeUnknownFields(other.getUnknownFields());
        return this;
      }

      public final boolean isInitialized() {
        if (hasParam()) {
          if (!getParam().isInitialized()) {
            
            return false;
          }
        }
        return true;
      }

      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        fm.lizhi.ocean.seal.protocol.GameRoundServiceProto.RequestRelationGameRound parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (fm.lizhi.ocean.seal.protocol.GameRoundServiceProto.RequestRelationGameRound) e.getUnfinishedMessage();
          throw e;
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      // optional .fm.lizhi.commons.template.datacenter.protocol.RelationGameRoundParam param = 1;
      private fm.lizhi.ocean.seal.protocol.GameRoundServiceProto.RelationGameRoundParam param_ = fm.lizhi.ocean.seal.protocol.GameRoundServiceProto.RelationGameRoundParam.getDefaultInstance();
      private com.google.protobuf.SingleFieldBuilder<
          fm.lizhi.ocean.seal.protocol.GameRoundServiceProto.RelationGameRoundParam, fm.lizhi.ocean.seal.protocol.GameRoundServiceProto.RelationGameRoundParam.Builder, fm.lizhi.ocean.seal.protocol.GameRoundServiceProto.RelationGameRoundParamOrBuilder> paramBuilder_;
      /**
       * <code>optional .fm.lizhi.commons.template.datacenter.protocol.RelationGameRoundParam param = 1;</code>
       *
       * <pre>
       *参数
       * </pre>
       */
      public boolean hasParam() {
        return ((bitField0_ & 0x00000001) == 0x00000001);
      }
      /**
       * <code>optional .fm.lizhi.commons.template.datacenter.protocol.RelationGameRoundParam param = 1;</code>
       *
       * <pre>
       *参数
       * </pre>
       */
      public fm.lizhi.ocean.seal.protocol.GameRoundServiceProto.RelationGameRoundParam getParam() {
        if (paramBuilder_ == null) {
          return param_;
        } else {
          return paramBuilder_.getMessage();
        }
      }
      /**
       * <code>optional .fm.lizhi.commons.template.datacenter.protocol.RelationGameRoundParam param = 1;</code>
       *
       * <pre>
       *参数
       * </pre>
       */
      public Builder setParam(fm.lizhi.ocean.seal.protocol.GameRoundServiceProto.RelationGameRoundParam value) {
        if (paramBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          param_ = value;
          onChanged();
        } else {
          paramBuilder_.setMessage(value);
        }
        bitField0_ |= 0x00000001;
        return this;
      }
      /**
       * <code>optional .fm.lizhi.commons.template.datacenter.protocol.RelationGameRoundParam param = 1;</code>
       *
       * <pre>
       *参数
       * </pre>
       */
      public Builder setParam(
          fm.lizhi.ocean.seal.protocol.GameRoundServiceProto.RelationGameRoundParam.Builder builderForValue) {
        if (paramBuilder_ == null) {
          param_ = builderForValue.build();
          onChanged();
        } else {
          paramBuilder_.setMessage(builderForValue.build());
        }
        bitField0_ |= 0x00000001;
        return this;
      }
      /**
       * <code>optional .fm.lizhi.commons.template.datacenter.protocol.RelationGameRoundParam param = 1;</code>
       *
       * <pre>
       *参数
       * </pre>
       */
      public Builder mergeParam(fm.lizhi.ocean.seal.protocol.GameRoundServiceProto.RelationGameRoundParam value) {
        if (paramBuilder_ == null) {
          if (((bitField0_ & 0x00000001) == 0x00000001) &&
              param_ != fm.lizhi.ocean.seal.protocol.GameRoundServiceProto.RelationGameRoundParam.getDefaultInstance()) {
            param_ =
              fm.lizhi.ocean.seal.protocol.GameRoundServiceProto.RelationGameRoundParam.newBuilder(param_).mergeFrom(value).buildPartial();
          } else {
            param_ = value;
          }
          onChanged();
        } else {
          paramBuilder_.mergeFrom(value);
        }
        bitField0_ |= 0x00000001;
        return this;
      }
      /**
       * <code>optional .fm.lizhi.commons.template.datacenter.protocol.RelationGameRoundParam param = 1;</code>
       *
       * <pre>
       *参数
       * </pre>
       */
      public Builder clearParam() {
        if (paramBuilder_ == null) {
          param_ = fm.lizhi.ocean.seal.protocol.GameRoundServiceProto.RelationGameRoundParam.getDefaultInstance();
          onChanged();
        } else {
          paramBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000001);
        return this;
      }
      /**
       * <code>optional .fm.lizhi.commons.template.datacenter.protocol.RelationGameRoundParam param = 1;</code>
       *
       * <pre>
       *参数
       * </pre>
       */
      public fm.lizhi.ocean.seal.protocol.GameRoundServiceProto.RelationGameRoundParam.Builder getParamBuilder() {
        bitField0_ |= 0x00000001;
        onChanged();
        return getParamFieldBuilder().getBuilder();
      }
      /**
       * <code>optional .fm.lizhi.commons.template.datacenter.protocol.RelationGameRoundParam param = 1;</code>
       *
       * <pre>
       *参数
       * </pre>
       */
      public fm.lizhi.ocean.seal.protocol.GameRoundServiceProto.RelationGameRoundParamOrBuilder getParamOrBuilder() {
        if (paramBuilder_ != null) {
          return paramBuilder_.getMessageOrBuilder();
        } else {
          return param_;
        }
      }
      /**
       * <code>optional .fm.lizhi.commons.template.datacenter.protocol.RelationGameRoundParam param = 1;</code>
       *
       * <pre>
       *参数
       * </pre>
       */
      private com.google.protobuf.SingleFieldBuilder<
          fm.lizhi.ocean.seal.protocol.GameRoundServiceProto.RelationGameRoundParam, fm.lizhi.ocean.seal.protocol.GameRoundServiceProto.RelationGameRoundParam.Builder, fm.lizhi.ocean.seal.protocol.GameRoundServiceProto.RelationGameRoundParamOrBuilder> 
          getParamFieldBuilder() {
        if (paramBuilder_ == null) {
          paramBuilder_ = new com.google.protobuf.SingleFieldBuilder<
              fm.lizhi.ocean.seal.protocol.GameRoundServiceProto.RelationGameRoundParam, fm.lizhi.ocean.seal.protocol.GameRoundServiceProto.RelationGameRoundParam.Builder, fm.lizhi.ocean.seal.protocol.GameRoundServiceProto.RelationGameRoundParamOrBuilder>(
                  param_,
                  getParentForChildren(),
                  isClean());
          param_ = null;
        }
        return paramBuilder_;
      }

      // @@protoc_insertion_point(builder_scope:fm.lizhi.commons.template.datacenter.protocol.RequestRelationGameRound)
    }

    static {
      defaultInstance = new RequestRelationGameRound(true);
      defaultInstance.initFields();
    }

    // @@protoc_insertion_point(class_scope:fm.lizhi.commons.template.datacenter.protocol.RequestRelationGameRound)
  }

  public interface ResponseRelationGameRoundOrBuilder
      extends com.google.protobuf.MessageOrBuilder {
  }
  /**
   * Protobuf type {@code fm.lizhi.commons.template.datacenter.protocol.ResponseRelationGameRound}
   *
   * <pre>
   * rcode == 1 (ILLEGAL_PARAMS) = 参数非法
   * rcode == 2 (NOT_EXISTS) = 平台局ID不存在
   * rcode == 3 (ERROR) = 内部错误
   * </pre>
   */
  public static final class ResponseRelationGameRound extends
      com.google.protobuf.GeneratedMessage
      implements ResponseRelationGameRoundOrBuilder {
    // Use ResponseRelationGameRound.newBuilder() to construct.
    private ResponseRelationGameRound(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
      super(builder);
      this.unknownFields = builder.getUnknownFields();
    }
    private ResponseRelationGameRound(boolean noInit) { this.unknownFields = com.google.protobuf.UnknownFieldSet.getDefaultInstance(); }

    private static final ResponseRelationGameRound defaultInstance;
    public static ResponseRelationGameRound getDefaultInstance() {
      return defaultInstance;
    }

    public ResponseRelationGameRound getDefaultInstanceForType() {
      return defaultInstance;
    }

    private final com.google.protobuf.UnknownFieldSet unknownFields;
    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
        getUnknownFields() {
      return this.unknownFields;
    }
    private ResponseRelationGameRound(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      initFields();
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            default: {
              if (!parseUnknownField(input, unknownFields,
                                     extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e.getMessage()).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return fm.lizhi.ocean.seal.protocol.GameRoundServiceProto.internal_static_fm_lizhi_commons_template_datacenter_protocol_ResponseRelationGameRound_descriptor;
    }

    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return fm.lizhi.ocean.seal.protocol.GameRoundServiceProto.internal_static_fm_lizhi_commons_template_datacenter_protocol_ResponseRelationGameRound_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              fm.lizhi.ocean.seal.protocol.GameRoundServiceProto.ResponseRelationGameRound.class, fm.lizhi.ocean.seal.protocol.GameRoundServiceProto.ResponseRelationGameRound.Builder.class);
    }

    public static com.google.protobuf.Parser<ResponseRelationGameRound> PARSER =
        new com.google.protobuf.AbstractParser<ResponseRelationGameRound>() {
      public ResponseRelationGameRound parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new ResponseRelationGameRound(input, extensionRegistry);
      }
    };

    @java.lang.Override
    public com.google.protobuf.Parser<ResponseRelationGameRound> getParserForType() {
      return PARSER;
    }

    private void initFields() {
    }
    private byte memoizedIsInitialized = -1;
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized != -1) return isInitialized == 1;

      memoizedIsInitialized = 1;
      return true;
    }

    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      getSerializedSize();
      getUnknownFields().writeTo(output);
    }

    private int memoizedSerializedSize = -1;
    public int getSerializedSize() {
      int size = memoizedSerializedSize;
      if (size != -1) return size;

      size = 0;
      size += getUnknownFields().getSerializedSize();
      memoizedSerializedSize = size;
      return size;
    }

    private static final long serialVersionUID = 0L;
    @java.lang.Override
    protected java.lang.Object writeReplace()
        throws java.io.ObjectStreamException {
      return super.writeReplace();
    }

    public static fm.lizhi.ocean.seal.protocol.GameRoundServiceProto.ResponseRelationGameRound parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static fm.lizhi.ocean.seal.protocol.GameRoundServiceProto.ResponseRelationGameRound parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static fm.lizhi.ocean.seal.protocol.GameRoundServiceProto.ResponseRelationGameRound parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static fm.lizhi.ocean.seal.protocol.GameRoundServiceProto.ResponseRelationGameRound parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static fm.lizhi.ocean.seal.protocol.GameRoundServiceProto.ResponseRelationGameRound parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return PARSER.parseFrom(input);
    }
    public static fm.lizhi.ocean.seal.protocol.GameRoundServiceProto.ResponseRelationGameRound parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseFrom(input, extensionRegistry);
    }
    public static fm.lizhi.ocean.seal.protocol.GameRoundServiceProto.ResponseRelationGameRound parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return PARSER.parseDelimitedFrom(input);
    }
    public static fm.lizhi.ocean.seal.protocol.GameRoundServiceProto.ResponseRelationGameRound parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseDelimitedFrom(input, extensionRegistry);
    }
    public static fm.lizhi.ocean.seal.protocol.GameRoundServiceProto.ResponseRelationGameRound parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return PARSER.parseFrom(input);
    }
    public static fm.lizhi.ocean.seal.protocol.GameRoundServiceProto.ResponseRelationGameRound parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseFrom(input, extensionRegistry);
    }

    public static Builder newBuilder() { return Builder.create(); }
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder(fm.lizhi.ocean.seal.protocol.GameRoundServiceProto.ResponseRelationGameRound prototype) {
      return newBuilder().mergeFrom(prototype);
    }
    public Builder toBuilder() { return newBuilder(this); }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code fm.lizhi.commons.template.datacenter.protocol.ResponseRelationGameRound}
     *
     * <pre>
     * rcode == 1 (ILLEGAL_PARAMS) = 参数非法
     * rcode == 2 (NOT_EXISTS) = 平台局ID不存在
     * rcode == 3 (ERROR) = 内部错误
     * </pre>
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessage.Builder<Builder>
       implements fm.lizhi.ocean.seal.protocol.GameRoundServiceProto.ResponseRelationGameRoundOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return fm.lizhi.ocean.seal.protocol.GameRoundServiceProto.internal_static_fm_lizhi_commons_template_datacenter_protocol_ResponseRelationGameRound_descriptor;
      }

      protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return fm.lizhi.ocean.seal.protocol.GameRoundServiceProto.internal_static_fm_lizhi_commons_template_datacenter_protocol_ResponseRelationGameRound_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                fm.lizhi.ocean.seal.protocol.GameRoundServiceProto.ResponseRelationGameRound.class, fm.lizhi.ocean.seal.protocol.GameRoundServiceProto.ResponseRelationGameRound.Builder.class);
      }

      // Construct using fm.lizhi.ocean.seal.protocol.GameRoundServiceProto.ResponseRelationGameRound.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessage.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessage.alwaysUseFieldBuilders) {
        }
      }
      private static Builder create() {
        return new Builder();
      }

      public Builder clear() {
        super.clear();
        return this;
      }

      public Builder clone() {
        return create().mergeFrom(buildPartial());
      }

      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return fm.lizhi.ocean.seal.protocol.GameRoundServiceProto.internal_static_fm_lizhi_commons_template_datacenter_protocol_ResponseRelationGameRound_descriptor;
      }

      public fm.lizhi.ocean.seal.protocol.GameRoundServiceProto.ResponseRelationGameRound getDefaultInstanceForType() {
        return fm.lizhi.ocean.seal.protocol.GameRoundServiceProto.ResponseRelationGameRound.getDefaultInstance();
      }

      public fm.lizhi.ocean.seal.protocol.GameRoundServiceProto.ResponseRelationGameRound build() {
        fm.lizhi.ocean.seal.protocol.GameRoundServiceProto.ResponseRelationGameRound result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      public fm.lizhi.ocean.seal.protocol.GameRoundServiceProto.ResponseRelationGameRound buildPartial() {
        fm.lizhi.ocean.seal.protocol.GameRoundServiceProto.ResponseRelationGameRound result = new fm.lizhi.ocean.seal.protocol.GameRoundServiceProto.ResponseRelationGameRound(this);
        onBuilt();
        return result;
      }

      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof fm.lizhi.ocean.seal.protocol.GameRoundServiceProto.ResponseRelationGameRound) {
          return mergeFrom((fm.lizhi.ocean.seal.protocol.GameRoundServiceProto.ResponseRelationGameRound)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(fm.lizhi.ocean.seal.protocol.GameRoundServiceProto.ResponseRelationGameRound other) {
        if (other == fm.lizhi.ocean.seal.protocol.GameRoundServiceProto.ResponseRelationGameRound.getDefaultInstance()) return this;
        this.mergeUnknownFields(other.getUnknownFields());
        return this;
      }

      public final boolean isInitialized() {
        return true;
      }

      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        fm.lizhi.ocean.seal.protocol.GameRoundServiceProto.ResponseRelationGameRound parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (fm.lizhi.ocean.seal.protocol.GameRoundServiceProto.ResponseRelationGameRound) e.getUnfinishedMessage();
          throw e;
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }

      // @@protoc_insertion_point(builder_scope:fm.lizhi.commons.template.datacenter.protocol.ResponseRelationGameRound)
    }

    static {
      defaultInstance = new ResponseRelationGameRound(true);
      defaultInstance.initFields();
    }

    // @@protoc_insertion_point(class_scope:fm.lizhi.commons.template.datacenter.protocol.ResponseRelationGameRound)
  }

  public interface StartGameRoundParamOrBuilder
      extends com.google.protobuf.MessageOrBuilder {

    // required string appId = 1;
    /**
     * <code>required string appId = 1;</code>
     *
     * <pre>
     * 业务方appId，为了兼容已有业务用string类型
     * </pre>
     */
    boolean hasAppId();
    /**
     * <code>required string appId = 1;</code>
     *
     * <pre>
     * 业务方appId，为了兼容已有业务用string类型
     * </pre>
     */
    java.lang.String getAppId();
    /**
     * <code>required string appId = 1;</code>
     *
     * <pre>
     * 业务方appId，为了兼容已有业务用string类型
     * </pre>
     */
    com.google.protobuf.ByteString
        getAppIdBytes();

    // required string gameId = 2;
    /**
     * <code>required string gameId = 2;</code>
     *
     * <pre>
     * Seal平台游戏ID，也可以是渠道游戏ID，服务端内部通过gameId和appId组合识别
     * </pre>
     */
    boolean hasGameId();
    /**
     * <code>required string gameId = 2;</code>
     *
     * <pre>
     * Seal平台游戏ID，也可以是渠道游戏ID，服务端内部通过gameId和appId组合识别
     * </pre>
     */
    java.lang.String getGameId();
    /**
     * <code>required string gameId = 2;</code>
     *
     * <pre>
     * Seal平台游戏ID，也可以是渠道游戏ID，服务端内部通过gameId和appId组合识别
     * </pre>
     */
    com.google.protobuf.ByteString
        getGameIdBytes();

    // required string groupId = 3;
    /**
     * <code>required string groupId = 3;</code>
     *
     * <pre>
     * 组ID
     * </pre>
     */
    boolean hasGroupId();
    /**
     * <code>required string groupId = 3;</code>
     *
     * <pre>
     * 组ID
     * </pre>
     */
    java.lang.String getGroupId();
    /**
     * <code>required string groupId = 3;</code>
     *
     * <pre>
     * 组ID
     * </pre>
     */
    com.google.protobuf.ByteString
        getGroupIdBytes();

    // optional string extra = 4;
    /**
     * <code>optional string extra = 4;</code>
     *
     * <pre>
     * 透传参数，可选值，可以根据具体场景如果有则传递
     * </pre>
     */
    boolean hasExtra();
    /**
     * <code>optional string extra = 4;</code>
     *
     * <pre>
     * 透传参数，可选值，可以根据具体场景如果有则传递
     * </pre>
     */
    java.lang.String getExtra();
    /**
     * <code>optional string extra = 4;</code>
     *
     * <pre>
     * 透传参数，可选值，可以根据具体场景如果有则传递
     * </pre>
     */
    com.google.protobuf.ByteString
        getExtraBytes();
  }
  /**
   * Protobuf type {@code fm.lizhi.commons.template.datacenter.protocol.StartGameRoundParam}
   *
   * <pre>
   ** 加载游戏参数 
   * </pre>
   */
  public static final class StartGameRoundParam extends
      com.google.protobuf.GeneratedMessage
      implements StartGameRoundParamOrBuilder {
    // Use StartGameRoundParam.newBuilder() to construct.
    private StartGameRoundParam(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
      super(builder);
      this.unknownFields = builder.getUnknownFields();
    }
    private StartGameRoundParam(boolean noInit) { this.unknownFields = com.google.protobuf.UnknownFieldSet.getDefaultInstance(); }

    private static final StartGameRoundParam defaultInstance;
    public static StartGameRoundParam getDefaultInstance() {
      return defaultInstance;
    }

    public StartGameRoundParam getDefaultInstanceForType() {
      return defaultInstance;
    }

    private final com.google.protobuf.UnknownFieldSet unknownFields;
    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
        getUnknownFields() {
      return this.unknownFields;
    }
    private StartGameRoundParam(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      initFields();
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            default: {
              if (!parseUnknownField(input, unknownFields,
                                     extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
            case 10: {
              bitField0_ |= 0x00000001;
              appId_ = input.readBytes();
              break;
            }
            case 18: {
              bitField0_ |= 0x00000002;
              gameId_ = input.readBytes();
              break;
            }
            case 26: {
              bitField0_ |= 0x00000004;
              groupId_ = input.readBytes();
              break;
            }
            case 34: {
              bitField0_ |= 0x00000008;
              extra_ = input.readBytes();
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e.getMessage()).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return fm.lizhi.ocean.seal.protocol.GameRoundServiceProto.internal_static_fm_lizhi_commons_template_datacenter_protocol_StartGameRoundParam_descriptor;
    }

    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return fm.lizhi.ocean.seal.protocol.GameRoundServiceProto.internal_static_fm_lizhi_commons_template_datacenter_protocol_StartGameRoundParam_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              fm.lizhi.ocean.seal.protocol.GameRoundServiceProto.StartGameRoundParam.class, fm.lizhi.ocean.seal.protocol.GameRoundServiceProto.StartGameRoundParam.Builder.class);
    }

    public static com.google.protobuf.Parser<StartGameRoundParam> PARSER =
        new com.google.protobuf.AbstractParser<StartGameRoundParam>() {
      public StartGameRoundParam parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new StartGameRoundParam(input, extensionRegistry);
      }
    };

    @java.lang.Override
    public com.google.protobuf.Parser<StartGameRoundParam> getParserForType() {
      return PARSER;
    }

    private int bitField0_;
    // required string appId = 1;
    public static final int APPID_FIELD_NUMBER = 1;
    private java.lang.Object appId_;
    /**
     * <code>required string appId = 1;</code>
     *
     * <pre>
     * 业务方appId，为了兼容已有业务用string类型
     * </pre>
     */
    public boolean hasAppId() {
      return ((bitField0_ & 0x00000001) == 0x00000001);
    }
    /**
     * <code>required string appId = 1;</code>
     *
     * <pre>
     * 业务方appId，为了兼容已有业务用string类型
     * </pre>
     */
    public java.lang.String getAppId() {
      java.lang.Object ref = appId_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          appId_ = s;
        }
        return s;
      }
    }
    /**
     * <code>required string appId = 1;</code>
     *
     * <pre>
     * 业务方appId，为了兼容已有业务用string类型
     * </pre>
     */
    public com.google.protobuf.ByteString
        getAppIdBytes() {
      java.lang.Object ref = appId_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        appId_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    // required string gameId = 2;
    public static final int GAMEID_FIELD_NUMBER = 2;
    private java.lang.Object gameId_;
    /**
     * <code>required string gameId = 2;</code>
     *
     * <pre>
     * Seal平台游戏ID，也可以是渠道游戏ID，服务端内部通过gameId和appId组合识别
     * </pre>
     */
    public boolean hasGameId() {
      return ((bitField0_ & 0x00000002) == 0x00000002);
    }
    /**
     * <code>required string gameId = 2;</code>
     *
     * <pre>
     * Seal平台游戏ID，也可以是渠道游戏ID，服务端内部通过gameId和appId组合识别
     * </pre>
     */
    public java.lang.String getGameId() {
      java.lang.Object ref = gameId_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          gameId_ = s;
        }
        return s;
      }
    }
    /**
     * <code>required string gameId = 2;</code>
     *
     * <pre>
     * Seal平台游戏ID，也可以是渠道游戏ID，服务端内部通过gameId和appId组合识别
     * </pre>
     */
    public com.google.protobuf.ByteString
        getGameIdBytes() {
      java.lang.Object ref = gameId_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        gameId_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    // required string groupId = 3;
    public static final int GROUPID_FIELD_NUMBER = 3;
    private java.lang.Object groupId_;
    /**
     * <code>required string groupId = 3;</code>
     *
     * <pre>
     * 组ID
     * </pre>
     */
    public boolean hasGroupId() {
      return ((bitField0_ & 0x00000004) == 0x00000004);
    }
    /**
     * <code>required string groupId = 3;</code>
     *
     * <pre>
     * 组ID
     * </pre>
     */
    public java.lang.String getGroupId() {
      java.lang.Object ref = groupId_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          groupId_ = s;
        }
        return s;
      }
    }
    /**
     * <code>required string groupId = 3;</code>
     *
     * <pre>
     * 组ID
     * </pre>
     */
    public com.google.protobuf.ByteString
        getGroupIdBytes() {
      java.lang.Object ref = groupId_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        groupId_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    // optional string extra = 4;
    public static final int EXTRA_FIELD_NUMBER = 4;
    private java.lang.Object extra_;
    /**
     * <code>optional string extra = 4;</code>
     *
     * <pre>
     * 透传参数，可选值，可以根据具体场景如果有则传递
     * </pre>
     */
    public boolean hasExtra() {
      return ((bitField0_ & 0x00000008) == 0x00000008);
    }
    /**
     * <code>optional string extra = 4;</code>
     *
     * <pre>
     * 透传参数，可选值，可以根据具体场景如果有则传递
     * </pre>
     */
    public java.lang.String getExtra() {
      java.lang.Object ref = extra_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          extra_ = s;
        }
        return s;
      }
    }
    /**
     * <code>optional string extra = 4;</code>
     *
     * <pre>
     * 透传参数，可选值，可以根据具体场景如果有则传递
     * </pre>
     */
    public com.google.protobuf.ByteString
        getExtraBytes() {
      java.lang.Object ref = extra_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        extra_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    private void initFields() {
      appId_ = "";
      gameId_ = "";
      groupId_ = "";
      extra_ = "";
    }
    private byte memoizedIsInitialized = -1;
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized != -1) return isInitialized == 1;

      if (!hasAppId()) {
        memoizedIsInitialized = 0;
        return false;
      }
      if (!hasGameId()) {
        memoizedIsInitialized = 0;
        return false;
      }
      if (!hasGroupId()) {
        memoizedIsInitialized = 0;
        return false;
      }
      memoizedIsInitialized = 1;
      return true;
    }

    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      getSerializedSize();
      if (((bitField0_ & 0x00000001) == 0x00000001)) {
        output.writeBytes(1, getAppIdBytes());
      }
      if (((bitField0_ & 0x00000002) == 0x00000002)) {
        output.writeBytes(2, getGameIdBytes());
      }
      if (((bitField0_ & 0x00000004) == 0x00000004)) {
        output.writeBytes(3, getGroupIdBytes());
      }
      if (((bitField0_ & 0x00000008) == 0x00000008)) {
        output.writeBytes(4, getExtraBytes());
      }
      getUnknownFields().writeTo(output);
    }

    private int memoizedSerializedSize = -1;
    public int getSerializedSize() {
      int size = memoizedSerializedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) == 0x00000001)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(1, getAppIdBytes());
      }
      if (((bitField0_ & 0x00000002) == 0x00000002)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(2, getGameIdBytes());
      }
      if (((bitField0_ & 0x00000004) == 0x00000004)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(3, getGroupIdBytes());
      }
      if (((bitField0_ & 0x00000008) == 0x00000008)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(4, getExtraBytes());
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSerializedSize = size;
      return size;
    }

    private static final long serialVersionUID = 0L;
    @java.lang.Override
    protected java.lang.Object writeReplace()
        throws java.io.ObjectStreamException {
      return super.writeReplace();
    }

    public static fm.lizhi.ocean.seal.protocol.GameRoundServiceProto.StartGameRoundParam parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static fm.lizhi.ocean.seal.protocol.GameRoundServiceProto.StartGameRoundParam parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static fm.lizhi.ocean.seal.protocol.GameRoundServiceProto.StartGameRoundParam parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static fm.lizhi.ocean.seal.protocol.GameRoundServiceProto.StartGameRoundParam parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static fm.lizhi.ocean.seal.protocol.GameRoundServiceProto.StartGameRoundParam parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return PARSER.parseFrom(input);
    }
    public static fm.lizhi.ocean.seal.protocol.GameRoundServiceProto.StartGameRoundParam parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseFrom(input, extensionRegistry);
    }
    public static fm.lizhi.ocean.seal.protocol.GameRoundServiceProto.StartGameRoundParam parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return PARSER.parseDelimitedFrom(input);
    }
    public static fm.lizhi.ocean.seal.protocol.GameRoundServiceProto.StartGameRoundParam parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseDelimitedFrom(input, extensionRegistry);
    }
    public static fm.lizhi.ocean.seal.protocol.GameRoundServiceProto.StartGameRoundParam parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return PARSER.parseFrom(input);
    }
    public static fm.lizhi.ocean.seal.protocol.GameRoundServiceProto.StartGameRoundParam parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseFrom(input, extensionRegistry);
    }

    public static Builder newBuilder() { return Builder.create(); }
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder(fm.lizhi.ocean.seal.protocol.GameRoundServiceProto.StartGameRoundParam prototype) {
      return newBuilder().mergeFrom(prototype);
    }
    public Builder toBuilder() { return newBuilder(this); }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code fm.lizhi.commons.template.datacenter.protocol.StartGameRoundParam}
     *
     * <pre>
     ** 加载游戏参数 
     * </pre>
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessage.Builder<Builder>
       implements fm.lizhi.ocean.seal.protocol.GameRoundServiceProto.StartGameRoundParamOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return fm.lizhi.ocean.seal.protocol.GameRoundServiceProto.internal_static_fm_lizhi_commons_template_datacenter_protocol_StartGameRoundParam_descriptor;
      }

      protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return fm.lizhi.ocean.seal.protocol.GameRoundServiceProto.internal_static_fm_lizhi_commons_template_datacenter_protocol_StartGameRoundParam_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                fm.lizhi.ocean.seal.protocol.GameRoundServiceProto.StartGameRoundParam.class, fm.lizhi.ocean.seal.protocol.GameRoundServiceProto.StartGameRoundParam.Builder.class);
      }

      // Construct using fm.lizhi.ocean.seal.protocol.GameRoundServiceProto.StartGameRoundParam.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessage.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessage.alwaysUseFieldBuilders) {
        }
      }
      private static Builder create() {
        return new Builder();
      }

      public Builder clear() {
        super.clear();
        appId_ = "";
        bitField0_ = (bitField0_ & ~0x00000001);
        gameId_ = "";
        bitField0_ = (bitField0_ & ~0x00000002);
        groupId_ = "";
        bitField0_ = (bitField0_ & ~0x00000004);
        extra_ = "";
        bitField0_ = (bitField0_ & ~0x00000008);
        return this;
      }

      public Builder clone() {
        return create().mergeFrom(buildPartial());
      }

      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return fm.lizhi.ocean.seal.protocol.GameRoundServiceProto.internal_static_fm_lizhi_commons_template_datacenter_protocol_StartGameRoundParam_descriptor;
      }

      public fm.lizhi.ocean.seal.protocol.GameRoundServiceProto.StartGameRoundParam getDefaultInstanceForType() {
        return fm.lizhi.ocean.seal.protocol.GameRoundServiceProto.StartGameRoundParam.getDefaultInstance();
      }

      public fm.lizhi.ocean.seal.protocol.GameRoundServiceProto.StartGameRoundParam build() {
        fm.lizhi.ocean.seal.protocol.GameRoundServiceProto.StartGameRoundParam result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      public fm.lizhi.ocean.seal.protocol.GameRoundServiceProto.StartGameRoundParam buildPartial() {
        fm.lizhi.ocean.seal.protocol.GameRoundServiceProto.StartGameRoundParam result = new fm.lizhi.ocean.seal.protocol.GameRoundServiceProto.StartGameRoundParam(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) == 0x00000001)) {
          to_bitField0_ |= 0x00000001;
        }
        result.appId_ = appId_;
        if (((from_bitField0_ & 0x00000002) == 0x00000002)) {
          to_bitField0_ |= 0x00000002;
        }
        result.gameId_ = gameId_;
        if (((from_bitField0_ & 0x00000004) == 0x00000004)) {
          to_bitField0_ |= 0x00000004;
        }
        result.groupId_ = groupId_;
        if (((from_bitField0_ & 0x00000008) == 0x00000008)) {
          to_bitField0_ |= 0x00000008;
        }
        result.extra_ = extra_;
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof fm.lizhi.ocean.seal.protocol.GameRoundServiceProto.StartGameRoundParam) {
          return mergeFrom((fm.lizhi.ocean.seal.protocol.GameRoundServiceProto.StartGameRoundParam)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(fm.lizhi.ocean.seal.protocol.GameRoundServiceProto.StartGameRoundParam other) {
        if (other == fm.lizhi.ocean.seal.protocol.GameRoundServiceProto.StartGameRoundParam.getDefaultInstance()) return this;
        if (other.hasAppId()) {
          bitField0_ |= 0x00000001;
          appId_ = other.appId_;
          onChanged();
        }
        if (other.hasGameId()) {
          bitField0_ |= 0x00000002;
          gameId_ = other.gameId_;
          onChanged();
        }
        if (other.hasGroupId()) {
          bitField0_ |= 0x00000004;
          groupId_ = other.groupId_;
          onChanged();
        }
        if (other.hasExtra()) {
          bitField0_ |= 0x00000008;
          extra_ = other.extra_;
          onChanged();
        }
        this.mergeUnknownFields(other.getUnknownFields());
        return this;
      }

      public final boolean isInitialized() {
        if (!hasAppId()) {
          
          return false;
        }
        if (!hasGameId()) {
          
          return false;
        }
        if (!hasGroupId()) {
          
          return false;
        }
        return true;
      }

      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        fm.lizhi.ocean.seal.protocol.GameRoundServiceProto.StartGameRoundParam parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (fm.lizhi.ocean.seal.protocol.GameRoundServiceProto.StartGameRoundParam) e.getUnfinishedMessage();
          throw e;
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      // required string appId = 1;
      private java.lang.Object appId_ = "";
      /**
       * <code>required string appId = 1;</code>
       *
       * <pre>
       * 业务方appId，为了兼容已有业务用string类型
       * </pre>
       */
      public boolean hasAppId() {
        return ((bitField0_ & 0x00000001) == 0x00000001);
      }
      /**
       * <code>required string appId = 1;</code>
       *
       * <pre>
       * 业务方appId，为了兼容已有业务用string类型
       * </pre>
       */
      public java.lang.String getAppId() {
        java.lang.Object ref = appId_;
        if (!(ref instanceof java.lang.String)) {
          java.lang.String s = ((com.google.protobuf.ByteString) ref)
              .toStringUtf8();
          appId_ = s;
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <code>required string appId = 1;</code>
       *
       * <pre>
       * 业务方appId，为了兼容已有业务用string类型
       * </pre>
       */
      public com.google.protobuf.ByteString
          getAppIdBytes() {
        java.lang.Object ref = appId_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          appId_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <code>required string appId = 1;</code>
       *
       * <pre>
       * 业务方appId，为了兼容已有业务用string类型
       * </pre>
       */
      public Builder setAppId(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000001;
        appId_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>required string appId = 1;</code>
       *
       * <pre>
       * 业务方appId，为了兼容已有业务用string类型
       * </pre>
       */
      public Builder clearAppId() {
        bitField0_ = (bitField0_ & ~0x00000001);
        appId_ = getDefaultInstance().getAppId();
        onChanged();
        return this;
      }
      /**
       * <code>required string appId = 1;</code>
       *
       * <pre>
       * 业务方appId，为了兼容已有业务用string类型
       * </pre>
       */
      public Builder setAppIdBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000001;
        appId_ = value;
        onChanged();
        return this;
      }

      // required string gameId = 2;
      private java.lang.Object gameId_ = "";
      /**
       * <code>required string gameId = 2;</code>
       *
       * <pre>
       * Seal平台游戏ID，也可以是渠道游戏ID，服务端内部通过gameId和appId组合识别
       * </pre>
       */
      public boolean hasGameId() {
        return ((bitField0_ & 0x00000002) == 0x00000002);
      }
      /**
       * <code>required string gameId = 2;</code>
       *
       * <pre>
       * Seal平台游戏ID，也可以是渠道游戏ID，服务端内部通过gameId和appId组合识别
       * </pre>
       */
      public java.lang.String getGameId() {
        java.lang.Object ref = gameId_;
        if (!(ref instanceof java.lang.String)) {
          java.lang.String s = ((com.google.protobuf.ByteString) ref)
              .toStringUtf8();
          gameId_ = s;
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <code>required string gameId = 2;</code>
       *
       * <pre>
       * Seal平台游戏ID，也可以是渠道游戏ID，服务端内部通过gameId和appId组合识别
       * </pre>
       */
      public com.google.protobuf.ByteString
          getGameIdBytes() {
        java.lang.Object ref = gameId_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          gameId_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <code>required string gameId = 2;</code>
       *
       * <pre>
       * Seal平台游戏ID，也可以是渠道游戏ID，服务端内部通过gameId和appId组合识别
       * </pre>
       */
      public Builder setGameId(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000002;
        gameId_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>required string gameId = 2;</code>
       *
       * <pre>
       * Seal平台游戏ID，也可以是渠道游戏ID，服务端内部通过gameId和appId组合识别
       * </pre>
       */
      public Builder clearGameId() {
        bitField0_ = (bitField0_ & ~0x00000002);
        gameId_ = getDefaultInstance().getGameId();
        onChanged();
        return this;
      }
      /**
       * <code>required string gameId = 2;</code>
       *
       * <pre>
       * Seal平台游戏ID，也可以是渠道游戏ID，服务端内部通过gameId和appId组合识别
       * </pre>
       */
      public Builder setGameIdBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000002;
        gameId_ = value;
        onChanged();
        return this;
      }

      // required string groupId = 3;
      private java.lang.Object groupId_ = "";
      /**
       * <code>required string groupId = 3;</code>
       *
       * <pre>
       * 组ID
       * </pre>
       */
      public boolean hasGroupId() {
        return ((bitField0_ & 0x00000004) == 0x00000004);
      }
      /**
       * <code>required string groupId = 3;</code>
       *
       * <pre>
       * 组ID
       * </pre>
       */
      public java.lang.String getGroupId() {
        java.lang.Object ref = groupId_;
        if (!(ref instanceof java.lang.String)) {
          java.lang.String s = ((com.google.protobuf.ByteString) ref)
              .toStringUtf8();
          groupId_ = s;
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <code>required string groupId = 3;</code>
       *
       * <pre>
       * 组ID
       * </pre>
       */
      public com.google.protobuf.ByteString
          getGroupIdBytes() {
        java.lang.Object ref = groupId_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          groupId_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <code>required string groupId = 3;</code>
       *
       * <pre>
       * 组ID
       * </pre>
       */
      public Builder setGroupId(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000004;
        groupId_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>required string groupId = 3;</code>
       *
       * <pre>
       * 组ID
       * </pre>
       */
      public Builder clearGroupId() {
        bitField0_ = (bitField0_ & ~0x00000004);
        groupId_ = getDefaultInstance().getGroupId();
        onChanged();
        return this;
      }
      /**
       * <code>required string groupId = 3;</code>
       *
       * <pre>
       * 组ID
       * </pre>
       */
      public Builder setGroupIdBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000004;
        groupId_ = value;
        onChanged();
        return this;
      }

      // optional string extra = 4;
      private java.lang.Object extra_ = "";
      /**
       * <code>optional string extra = 4;</code>
       *
       * <pre>
       * 透传参数，可选值，可以根据具体场景如果有则传递
       * </pre>
       */
      public boolean hasExtra() {
        return ((bitField0_ & 0x00000008) == 0x00000008);
      }
      /**
       * <code>optional string extra = 4;</code>
       *
       * <pre>
       * 透传参数，可选值，可以根据具体场景如果有则传递
       * </pre>
       */
      public java.lang.String getExtra() {
        java.lang.Object ref = extra_;
        if (!(ref instanceof java.lang.String)) {
          java.lang.String s = ((com.google.protobuf.ByteString) ref)
              .toStringUtf8();
          extra_ = s;
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <code>optional string extra = 4;</code>
       *
       * <pre>
       * 透传参数，可选值，可以根据具体场景如果有则传递
       * </pre>
       */
      public com.google.protobuf.ByteString
          getExtraBytes() {
        java.lang.Object ref = extra_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          extra_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <code>optional string extra = 4;</code>
       *
       * <pre>
       * 透传参数，可选值，可以根据具体场景如果有则传递
       * </pre>
       */
      public Builder setExtra(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000008;
        extra_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional string extra = 4;</code>
       *
       * <pre>
       * 透传参数，可选值，可以根据具体场景如果有则传递
       * </pre>
       */
      public Builder clearExtra() {
        bitField0_ = (bitField0_ & ~0x00000008);
        extra_ = getDefaultInstance().getExtra();
        onChanged();
        return this;
      }
      /**
       * <code>optional string extra = 4;</code>
       *
       * <pre>
       * 透传参数，可选值，可以根据具体场景如果有则传递
       * </pre>
       */
      public Builder setExtraBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000008;
        extra_ = value;
        onChanged();
        return this;
      }

      // @@protoc_insertion_point(builder_scope:fm.lizhi.commons.template.datacenter.protocol.StartGameRoundParam)
    }

    static {
      defaultInstance = new StartGameRoundParam(true);
      defaultInstance.initFields();
    }

    // @@protoc_insertion_point(class_scope:fm.lizhi.commons.template.datacenter.protocol.StartGameRoundParam)
  }

  public interface GameRoundOrBuilder
      extends com.google.protobuf.MessageOrBuilder {

    // required int64 roundId = 2;
    /**
     * <code>required int64 roundId = 2;</code>
     *
     * <pre>
     * 游戏局ID
     * </pre>
     */
    boolean hasRoundId();
    /**
     * <code>required int64 roundId = 2;</code>
     *
     * <pre>
     * 游戏局ID
     * </pre>
     */
    long getRoundId();
  }
  /**
   * Protobuf type {@code fm.lizhi.commons.template.datacenter.protocol.GameRound}
   *
   * <pre>
   ** 加载游戏，生成平台游戏局 
   * </pre>
   */
  public static final class GameRound extends
      com.google.protobuf.GeneratedMessage
      implements GameRoundOrBuilder {
    // Use GameRound.newBuilder() to construct.
    private GameRound(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
      super(builder);
      this.unknownFields = builder.getUnknownFields();
    }
    private GameRound(boolean noInit) { this.unknownFields = com.google.protobuf.UnknownFieldSet.getDefaultInstance(); }

    private static final GameRound defaultInstance;
    public static GameRound getDefaultInstance() {
      return defaultInstance;
    }

    public GameRound getDefaultInstanceForType() {
      return defaultInstance;
    }

    private final com.google.protobuf.UnknownFieldSet unknownFields;
    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
        getUnknownFields() {
      return this.unknownFields;
    }
    private GameRound(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      initFields();
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            default: {
              if (!parseUnknownField(input, unknownFields,
                                     extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
            case 16: {
              bitField0_ |= 0x00000001;
              roundId_ = input.readInt64();
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e.getMessage()).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return fm.lizhi.ocean.seal.protocol.GameRoundServiceProto.internal_static_fm_lizhi_commons_template_datacenter_protocol_GameRound_descriptor;
    }

    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return fm.lizhi.ocean.seal.protocol.GameRoundServiceProto.internal_static_fm_lizhi_commons_template_datacenter_protocol_GameRound_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              fm.lizhi.ocean.seal.protocol.GameRoundServiceProto.GameRound.class, fm.lizhi.ocean.seal.protocol.GameRoundServiceProto.GameRound.Builder.class);
    }

    public static com.google.protobuf.Parser<GameRound> PARSER =
        new com.google.protobuf.AbstractParser<GameRound>() {
      public GameRound parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new GameRound(input, extensionRegistry);
      }
    };

    @java.lang.Override
    public com.google.protobuf.Parser<GameRound> getParserForType() {
      return PARSER;
    }

    private int bitField0_;
    // required int64 roundId = 2;
    public static final int ROUNDID_FIELD_NUMBER = 2;
    private long roundId_;
    /**
     * <code>required int64 roundId = 2;</code>
     *
     * <pre>
     * 游戏局ID
     * </pre>
     */
    public boolean hasRoundId() {
      return ((bitField0_ & 0x00000001) == 0x00000001);
    }
    /**
     * <code>required int64 roundId = 2;</code>
     *
     * <pre>
     * 游戏局ID
     * </pre>
     */
    public long getRoundId() {
      return roundId_;
    }

    private void initFields() {
      roundId_ = 0L;
    }
    private byte memoizedIsInitialized = -1;
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized != -1) return isInitialized == 1;

      if (!hasRoundId()) {
        memoizedIsInitialized = 0;
        return false;
      }
      memoizedIsInitialized = 1;
      return true;
    }

    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      getSerializedSize();
      if (((bitField0_ & 0x00000001) == 0x00000001)) {
        output.writeInt64(2, roundId_);
      }
      getUnknownFields().writeTo(output);
    }

    private int memoizedSerializedSize = -1;
    public int getSerializedSize() {
      int size = memoizedSerializedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) == 0x00000001)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt64Size(2, roundId_);
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSerializedSize = size;
      return size;
    }

    private static final long serialVersionUID = 0L;
    @java.lang.Override
    protected java.lang.Object writeReplace()
        throws java.io.ObjectStreamException {
      return super.writeReplace();
    }

    public static fm.lizhi.ocean.seal.protocol.GameRoundServiceProto.GameRound parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static fm.lizhi.ocean.seal.protocol.GameRoundServiceProto.GameRound parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static fm.lizhi.ocean.seal.protocol.GameRoundServiceProto.GameRound parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static fm.lizhi.ocean.seal.protocol.GameRoundServiceProto.GameRound parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static fm.lizhi.ocean.seal.protocol.GameRoundServiceProto.GameRound parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return PARSER.parseFrom(input);
    }
    public static fm.lizhi.ocean.seal.protocol.GameRoundServiceProto.GameRound parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseFrom(input, extensionRegistry);
    }
    public static fm.lizhi.ocean.seal.protocol.GameRoundServiceProto.GameRound parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return PARSER.parseDelimitedFrom(input);
    }
    public static fm.lizhi.ocean.seal.protocol.GameRoundServiceProto.GameRound parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseDelimitedFrom(input, extensionRegistry);
    }
    public static fm.lizhi.ocean.seal.protocol.GameRoundServiceProto.GameRound parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return PARSER.parseFrom(input);
    }
    public static fm.lizhi.ocean.seal.protocol.GameRoundServiceProto.GameRound parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseFrom(input, extensionRegistry);
    }

    public static Builder newBuilder() { return Builder.create(); }
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder(fm.lizhi.ocean.seal.protocol.GameRoundServiceProto.GameRound prototype) {
      return newBuilder().mergeFrom(prototype);
    }
    public Builder toBuilder() { return newBuilder(this); }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code fm.lizhi.commons.template.datacenter.protocol.GameRound}
     *
     * <pre>
     ** 加载游戏，生成平台游戏局 
     * </pre>
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessage.Builder<Builder>
       implements fm.lizhi.ocean.seal.protocol.GameRoundServiceProto.GameRoundOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return fm.lizhi.ocean.seal.protocol.GameRoundServiceProto.internal_static_fm_lizhi_commons_template_datacenter_protocol_GameRound_descriptor;
      }

      protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return fm.lizhi.ocean.seal.protocol.GameRoundServiceProto.internal_static_fm_lizhi_commons_template_datacenter_protocol_GameRound_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                fm.lizhi.ocean.seal.protocol.GameRoundServiceProto.GameRound.class, fm.lizhi.ocean.seal.protocol.GameRoundServiceProto.GameRound.Builder.class);
      }

      // Construct using fm.lizhi.ocean.seal.protocol.GameRoundServiceProto.GameRound.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessage.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessage.alwaysUseFieldBuilders) {
        }
      }
      private static Builder create() {
        return new Builder();
      }

      public Builder clear() {
        super.clear();
        roundId_ = 0L;
        bitField0_ = (bitField0_ & ~0x00000001);
        return this;
      }

      public Builder clone() {
        return create().mergeFrom(buildPartial());
      }

      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return fm.lizhi.ocean.seal.protocol.GameRoundServiceProto.internal_static_fm_lizhi_commons_template_datacenter_protocol_GameRound_descriptor;
      }

      public fm.lizhi.ocean.seal.protocol.GameRoundServiceProto.GameRound getDefaultInstanceForType() {
        return fm.lizhi.ocean.seal.protocol.GameRoundServiceProto.GameRound.getDefaultInstance();
      }

      public fm.lizhi.ocean.seal.protocol.GameRoundServiceProto.GameRound build() {
        fm.lizhi.ocean.seal.protocol.GameRoundServiceProto.GameRound result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      public fm.lizhi.ocean.seal.protocol.GameRoundServiceProto.GameRound buildPartial() {
        fm.lizhi.ocean.seal.protocol.GameRoundServiceProto.GameRound result = new fm.lizhi.ocean.seal.protocol.GameRoundServiceProto.GameRound(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) == 0x00000001)) {
          to_bitField0_ |= 0x00000001;
        }
        result.roundId_ = roundId_;
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof fm.lizhi.ocean.seal.protocol.GameRoundServiceProto.GameRound) {
          return mergeFrom((fm.lizhi.ocean.seal.protocol.GameRoundServiceProto.GameRound)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(fm.lizhi.ocean.seal.protocol.GameRoundServiceProto.GameRound other) {
        if (other == fm.lizhi.ocean.seal.protocol.GameRoundServiceProto.GameRound.getDefaultInstance()) return this;
        if (other.hasRoundId()) {
          setRoundId(other.getRoundId());
        }
        this.mergeUnknownFields(other.getUnknownFields());
        return this;
      }

      public final boolean isInitialized() {
        if (!hasRoundId()) {
          
          return false;
        }
        return true;
      }

      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        fm.lizhi.ocean.seal.protocol.GameRoundServiceProto.GameRound parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (fm.lizhi.ocean.seal.protocol.GameRoundServiceProto.GameRound) e.getUnfinishedMessage();
          throw e;
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      // required int64 roundId = 2;
      private long roundId_ ;
      /**
       * <code>required int64 roundId = 2;</code>
       *
       * <pre>
       * 游戏局ID
       * </pre>
       */
      public boolean hasRoundId() {
        return ((bitField0_ & 0x00000001) == 0x00000001);
      }
      /**
       * <code>required int64 roundId = 2;</code>
       *
       * <pre>
       * 游戏局ID
       * </pre>
       */
      public long getRoundId() {
        return roundId_;
      }
      /**
       * <code>required int64 roundId = 2;</code>
       *
       * <pre>
       * 游戏局ID
       * </pre>
       */
      public Builder setRoundId(long value) {
        bitField0_ |= 0x00000001;
        roundId_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>required int64 roundId = 2;</code>
       *
       * <pre>
       * 游戏局ID
       * </pre>
       */
      public Builder clearRoundId() {
        bitField0_ = (bitField0_ & ~0x00000001);
        roundId_ = 0L;
        onChanged();
        return this;
      }

      // @@protoc_insertion_point(builder_scope:fm.lizhi.commons.template.datacenter.protocol.GameRound)
    }

    static {
      defaultInstance = new GameRound(true);
      defaultInstance.initFields();
    }

    // @@protoc_insertion_point(class_scope:fm.lizhi.commons.template.datacenter.protocol.GameRound)
  }

  public interface RequestStartGameRoundOrBuilder
      extends com.google.protobuf.MessageOrBuilder {

    // optional .fm.lizhi.commons.template.datacenter.protocol.StartGameRoundParam param = 1;
    /**
     * <code>optional .fm.lizhi.commons.template.datacenter.protocol.StartGameRoundParam param = 1;</code>
     *
     * <pre>
     *参数
     * </pre>
     */
    boolean hasParam();
    /**
     * <code>optional .fm.lizhi.commons.template.datacenter.protocol.StartGameRoundParam param = 1;</code>
     *
     * <pre>
     *参数
     * </pre>
     */
    fm.lizhi.ocean.seal.protocol.GameRoundServiceProto.StartGameRoundParam getParam();
    /**
     * <code>optional .fm.lizhi.commons.template.datacenter.protocol.StartGameRoundParam param = 1;</code>
     *
     * <pre>
     *参数
     * </pre>
     */
    fm.lizhi.ocean.seal.protocol.GameRoundServiceProto.StartGameRoundParamOrBuilder getParamOrBuilder();
  }
  /**
   * Protobuf type {@code fm.lizhi.commons.template.datacenter.protocol.RequestStartGameRound}
   *
   * <pre>
   * GameRoundService.java
   * 开始平台游戏局，客户端在开始游戏的时候调用
   * domain = 4302, op = 82
   * </pre>
   */
  public static final class RequestStartGameRound extends
      com.google.protobuf.GeneratedMessage
      implements RequestStartGameRoundOrBuilder {
    // Use RequestStartGameRound.newBuilder() to construct.
    private RequestStartGameRound(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
      super(builder);
      this.unknownFields = builder.getUnknownFields();
    }
    private RequestStartGameRound(boolean noInit) { this.unknownFields = com.google.protobuf.UnknownFieldSet.getDefaultInstance(); }

    private static final RequestStartGameRound defaultInstance;
    public static RequestStartGameRound getDefaultInstance() {
      return defaultInstance;
    }

    public RequestStartGameRound getDefaultInstanceForType() {
      return defaultInstance;
    }

    private final com.google.protobuf.UnknownFieldSet unknownFields;
    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
        getUnknownFields() {
      return this.unknownFields;
    }
    private RequestStartGameRound(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      initFields();
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            default: {
              if (!parseUnknownField(input, unknownFields,
                                     extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
            case 10: {
              fm.lizhi.ocean.seal.protocol.GameRoundServiceProto.StartGameRoundParam.Builder subBuilder = null;
              if (((bitField0_ & 0x00000001) == 0x00000001)) {
                subBuilder = param_.toBuilder();
              }
              param_ = input.readMessage(fm.lizhi.ocean.seal.protocol.GameRoundServiceProto.StartGameRoundParam.PARSER, extensionRegistry);
              if (subBuilder != null) {
                subBuilder.mergeFrom(param_);
                param_ = subBuilder.buildPartial();
              }
              bitField0_ |= 0x00000001;
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e.getMessage()).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return fm.lizhi.ocean.seal.protocol.GameRoundServiceProto.internal_static_fm_lizhi_commons_template_datacenter_protocol_RequestStartGameRound_descriptor;
    }

    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return fm.lizhi.ocean.seal.protocol.GameRoundServiceProto.internal_static_fm_lizhi_commons_template_datacenter_protocol_RequestStartGameRound_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              fm.lizhi.ocean.seal.protocol.GameRoundServiceProto.RequestStartGameRound.class, fm.lizhi.ocean.seal.protocol.GameRoundServiceProto.RequestStartGameRound.Builder.class);
    }

    public static com.google.protobuf.Parser<RequestStartGameRound> PARSER =
        new com.google.protobuf.AbstractParser<RequestStartGameRound>() {
      public RequestStartGameRound parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new RequestStartGameRound(input, extensionRegistry);
      }
    };

    @java.lang.Override
    public com.google.protobuf.Parser<RequestStartGameRound> getParserForType() {
      return PARSER;
    }

    private int bitField0_;
    // optional .fm.lizhi.commons.template.datacenter.protocol.StartGameRoundParam param = 1;
    public static final int PARAM_FIELD_NUMBER = 1;
    private fm.lizhi.ocean.seal.protocol.GameRoundServiceProto.StartGameRoundParam param_;
    /**
     * <code>optional .fm.lizhi.commons.template.datacenter.protocol.StartGameRoundParam param = 1;</code>
     *
     * <pre>
     *参数
     * </pre>
     */
    public boolean hasParam() {
      return ((bitField0_ & 0x00000001) == 0x00000001);
    }
    /**
     * <code>optional .fm.lizhi.commons.template.datacenter.protocol.StartGameRoundParam param = 1;</code>
     *
     * <pre>
     *参数
     * </pre>
     */
    public fm.lizhi.ocean.seal.protocol.GameRoundServiceProto.StartGameRoundParam getParam() {
      return param_;
    }
    /**
     * <code>optional .fm.lizhi.commons.template.datacenter.protocol.StartGameRoundParam param = 1;</code>
     *
     * <pre>
     *参数
     * </pre>
     */
    public fm.lizhi.ocean.seal.protocol.GameRoundServiceProto.StartGameRoundParamOrBuilder getParamOrBuilder() {
      return param_;
    }

    private void initFields() {
      param_ = fm.lizhi.ocean.seal.protocol.GameRoundServiceProto.StartGameRoundParam.getDefaultInstance();
    }
    private byte memoizedIsInitialized = -1;
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized != -1) return isInitialized == 1;

      if (hasParam()) {
        if (!getParam().isInitialized()) {
          memoizedIsInitialized = 0;
          return false;
        }
      }
      memoizedIsInitialized = 1;
      return true;
    }

    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      getSerializedSize();
      if (((bitField0_ & 0x00000001) == 0x00000001)) {
        output.writeMessage(1, param_);
      }
      getUnknownFields().writeTo(output);
    }

    private int memoizedSerializedSize = -1;
    public int getSerializedSize() {
      int size = memoizedSerializedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) == 0x00000001)) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(1, param_);
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSerializedSize = size;
      return size;
    }

    private static final long serialVersionUID = 0L;
    @java.lang.Override
    protected java.lang.Object writeReplace()
        throws java.io.ObjectStreamException {
      return super.writeReplace();
    }

    public static fm.lizhi.ocean.seal.protocol.GameRoundServiceProto.RequestStartGameRound parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static fm.lizhi.ocean.seal.protocol.GameRoundServiceProto.RequestStartGameRound parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static fm.lizhi.ocean.seal.protocol.GameRoundServiceProto.RequestStartGameRound parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static fm.lizhi.ocean.seal.protocol.GameRoundServiceProto.RequestStartGameRound parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static fm.lizhi.ocean.seal.protocol.GameRoundServiceProto.RequestStartGameRound parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return PARSER.parseFrom(input);
    }
    public static fm.lizhi.ocean.seal.protocol.GameRoundServiceProto.RequestStartGameRound parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseFrom(input, extensionRegistry);
    }
    public static fm.lizhi.ocean.seal.protocol.GameRoundServiceProto.RequestStartGameRound parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return PARSER.parseDelimitedFrom(input);
    }
    public static fm.lizhi.ocean.seal.protocol.GameRoundServiceProto.RequestStartGameRound parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseDelimitedFrom(input, extensionRegistry);
    }
    public static fm.lizhi.ocean.seal.protocol.GameRoundServiceProto.RequestStartGameRound parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return PARSER.parseFrom(input);
    }
    public static fm.lizhi.ocean.seal.protocol.GameRoundServiceProto.RequestStartGameRound parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseFrom(input, extensionRegistry);
    }

    public static Builder newBuilder() { return Builder.create(); }
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder(fm.lizhi.ocean.seal.protocol.GameRoundServiceProto.RequestStartGameRound prototype) {
      return newBuilder().mergeFrom(prototype);
    }
    public Builder toBuilder() { return newBuilder(this); }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code fm.lizhi.commons.template.datacenter.protocol.RequestStartGameRound}
     *
     * <pre>
     * GameRoundService.java
     * 开始平台游戏局，客户端在开始游戏的时候调用
     * domain = 4302, op = 82
     * </pre>
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessage.Builder<Builder>
       implements fm.lizhi.ocean.seal.protocol.GameRoundServiceProto.RequestStartGameRoundOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return fm.lizhi.ocean.seal.protocol.GameRoundServiceProto.internal_static_fm_lizhi_commons_template_datacenter_protocol_RequestStartGameRound_descriptor;
      }

      protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return fm.lizhi.ocean.seal.protocol.GameRoundServiceProto.internal_static_fm_lizhi_commons_template_datacenter_protocol_RequestStartGameRound_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                fm.lizhi.ocean.seal.protocol.GameRoundServiceProto.RequestStartGameRound.class, fm.lizhi.ocean.seal.protocol.GameRoundServiceProto.RequestStartGameRound.Builder.class);
      }

      // Construct using fm.lizhi.ocean.seal.protocol.GameRoundServiceProto.RequestStartGameRound.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessage.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessage.alwaysUseFieldBuilders) {
          getParamFieldBuilder();
        }
      }
      private static Builder create() {
        return new Builder();
      }

      public Builder clear() {
        super.clear();
        if (paramBuilder_ == null) {
          param_ = fm.lizhi.ocean.seal.protocol.GameRoundServiceProto.StartGameRoundParam.getDefaultInstance();
        } else {
          paramBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000001);
        return this;
      }

      public Builder clone() {
        return create().mergeFrom(buildPartial());
      }

      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return fm.lizhi.ocean.seal.protocol.GameRoundServiceProto.internal_static_fm_lizhi_commons_template_datacenter_protocol_RequestStartGameRound_descriptor;
      }

      public fm.lizhi.ocean.seal.protocol.GameRoundServiceProto.RequestStartGameRound getDefaultInstanceForType() {
        return fm.lizhi.ocean.seal.protocol.GameRoundServiceProto.RequestStartGameRound.getDefaultInstance();
      }

      public fm.lizhi.ocean.seal.protocol.GameRoundServiceProto.RequestStartGameRound build() {
        fm.lizhi.ocean.seal.protocol.GameRoundServiceProto.RequestStartGameRound result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      public fm.lizhi.ocean.seal.protocol.GameRoundServiceProto.RequestStartGameRound buildPartial() {
        fm.lizhi.ocean.seal.protocol.GameRoundServiceProto.RequestStartGameRound result = new fm.lizhi.ocean.seal.protocol.GameRoundServiceProto.RequestStartGameRound(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) == 0x00000001)) {
          to_bitField0_ |= 0x00000001;
        }
        if (paramBuilder_ == null) {
          result.param_ = param_;
        } else {
          result.param_ = paramBuilder_.build();
        }
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof fm.lizhi.ocean.seal.protocol.GameRoundServiceProto.RequestStartGameRound) {
          return mergeFrom((fm.lizhi.ocean.seal.protocol.GameRoundServiceProto.RequestStartGameRound)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(fm.lizhi.ocean.seal.protocol.GameRoundServiceProto.RequestStartGameRound other) {
        if (other == fm.lizhi.ocean.seal.protocol.GameRoundServiceProto.RequestStartGameRound.getDefaultInstance()) return this;
        if (other.hasParam()) {
          mergeParam(other.getParam());
        }
        this.mergeUnknownFields(other.getUnknownFields());
        return this;
      }

      public final boolean isInitialized() {
        if (hasParam()) {
          if (!getParam().isInitialized()) {
            
            return false;
          }
        }
        return true;
      }

      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        fm.lizhi.ocean.seal.protocol.GameRoundServiceProto.RequestStartGameRound parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (fm.lizhi.ocean.seal.protocol.GameRoundServiceProto.RequestStartGameRound) e.getUnfinishedMessage();
          throw e;
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      // optional .fm.lizhi.commons.template.datacenter.protocol.StartGameRoundParam param = 1;
      private fm.lizhi.ocean.seal.protocol.GameRoundServiceProto.StartGameRoundParam param_ = fm.lizhi.ocean.seal.protocol.GameRoundServiceProto.StartGameRoundParam.getDefaultInstance();
      private com.google.protobuf.SingleFieldBuilder<
          fm.lizhi.ocean.seal.protocol.GameRoundServiceProto.StartGameRoundParam, fm.lizhi.ocean.seal.protocol.GameRoundServiceProto.StartGameRoundParam.Builder, fm.lizhi.ocean.seal.protocol.GameRoundServiceProto.StartGameRoundParamOrBuilder> paramBuilder_;
      /**
       * <code>optional .fm.lizhi.commons.template.datacenter.protocol.StartGameRoundParam param = 1;</code>
       *
       * <pre>
       *参数
       * </pre>
       */
      public boolean hasParam() {
        return ((bitField0_ & 0x00000001) == 0x00000001);
      }
      /**
       * <code>optional .fm.lizhi.commons.template.datacenter.protocol.StartGameRoundParam param = 1;</code>
       *
       * <pre>
       *参数
       * </pre>
       */
      public fm.lizhi.ocean.seal.protocol.GameRoundServiceProto.StartGameRoundParam getParam() {
        if (paramBuilder_ == null) {
          return param_;
        } else {
          return paramBuilder_.getMessage();
        }
      }
      /**
       * <code>optional .fm.lizhi.commons.template.datacenter.protocol.StartGameRoundParam param = 1;</code>
       *
       * <pre>
       *参数
       * </pre>
       */
      public Builder setParam(fm.lizhi.ocean.seal.protocol.GameRoundServiceProto.StartGameRoundParam value) {
        if (paramBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          param_ = value;
          onChanged();
        } else {
          paramBuilder_.setMessage(value);
        }
        bitField0_ |= 0x00000001;
        return this;
      }
      /**
       * <code>optional .fm.lizhi.commons.template.datacenter.protocol.StartGameRoundParam param = 1;</code>
       *
       * <pre>
       *参数
       * </pre>
       */
      public Builder setParam(
          fm.lizhi.ocean.seal.protocol.GameRoundServiceProto.StartGameRoundParam.Builder builderForValue) {
        if (paramBuilder_ == null) {
          param_ = builderForValue.build();
          onChanged();
        } else {
          paramBuilder_.setMessage(builderForValue.build());
        }
        bitField0_ |= 0x00000001;
        return this;
      }
      /**
       * <code>optional .fm.lizhi.commons.template.datacenter.protocol.StartGameRoundParam param = 1;</code>
       *
       * <pre>
       *参数
       * </pre>
       */
      public Builder mergeParam(fm.lizhi.ocean.seal.protocol.GameRoundServiceProto.StartGameRoundParam value) {
        if (paramBuilder_ == null) {
          if (((bitField0_ & 0x00000001) == 0x00000001) &&
              param_ != fm.lizhi.ocean.seal.protocol.GameRoundServiceProto.StartGameRoundParam.getDefaultInstance()) {
            param_ =
              fm.lizhi.ocean.seal.protocol.GameRoundServiceProto.StartGameRoundParam.newBuilder(param_).mergeFrom(value).buildPartial();
          } else {
            param_ = value;
          }
          onChanged();
        } else {
          paramBuilder_.mergeFrom(value);
        }
        bitField0_ |= 0x00000001;
        return this;
      }
      /**
       * <code>optional .fm.lizhi.commons.template.datacenter.protocol.StartGameRoundParam param = 1;</code>
       *
       * <pre>
       *参数
       * </pre>
       */
      public Builder clearParam() {
        if (paramBuilder_ == null) {
          param_ = fm.lizhi.ocean.seal.protocol.GameRoundServiceProto.StartGameRoundParam.getDefaultInstance();
          onChanged();
        } else {
          paramBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000001);
        return this;
      }
      /**
       * <code>optional .fm.lizhi.commons.template.datacenter.protocol.StartGameRoundParam param = 1;</code>
       *
       * <pre>
       *参数
       * </pre>
       */
      public fm.lizhi.ocean.seal.protocol.GameRoundServiceProto.StartGameRoundParam.Builder getParamBuilder() {
        bitField0_ |= 0x00000001;
        onChanged();
        return getParamFieldBuilder().getBuilder();
      }
      /**
       * <code>optional .fm.lizhi.commons.template.datacenter.protocol.StartGameRoundParam param = 1;</code>
       *
       * <pre>
       *参数
       * </pre>
       */
      public fm.lizhi.ocean.seal.protocol.GameRoundServiceProto.StartGameRoundParamOrBuilder getParamOrBuilder() {
        if (paramBuilder_ != null) {
          return paramBuilder_.getMessageOrBuilder();
        } else {
          return param_;
        }
      }
      /**
       * <code>optional .fm.lizhi.commons.template.datacenter.protocol.StartGameRoundParam param = 1;</code>
       *
       * <pre>
       *参数
       * </pre>
       */
      private com.google.protobuf.SingleFieldBuilder<
          fm.lizhi.ocean.seal.protocol.GameRoundServiceProto.StartGameRoundParam, fm.lizhi.ocean.seal.protocol.GameRoundServiceProto.StartGameRoundParam.Builder, fm.lizhi.ocean.seal.protocol.GameRoundServiceProto.StartGameRoundParamOrBuilder> 
          getParamFieldBuilder() {
        if (paramBuilder_ == null) {
          paramBuilder_ = new com.google.protobuf.SingleFieldBuilder<
              fm.lizhi.ocean.seal.protocol.GameRoundServiceProto.StartGameRoundParam, fm.lizhi.ocean.seal.protocol.GameRoundServiceProto.StartGameRoundParam.Builder, fm.lizhi.ocean.seal.protocol.GameRoundServiceProto.StartGameRoundParamOrBuilder>(
                  param_,
                  getParentForChildren(),
                  isClean());
          param_ = null;
        }
        return paramBuilder_;
      }

      // @@protoc_insertion_point(builder_scope:fm.lizhi.commons.template.datacenter.protocol.RequestStartGameRound)
    }

    static {
      defaultInstance = new RequestStartGameRound(true);
      defaultInstance.initFields();
    }

    // @@protoc_insertion_point(class_scope:fm.lizhi.commons.template.datacenter.protocol.RequestStartGameRound)
  }

  public interface ResponseStartGameRoundOrBuilder
      extends com.google.protobuf.MessageOrBuilder {

    // optional .fm.lizhi.commons.template.datacenter.protocol.GameRound gameRound = 1;
    /**
     * <code>optional .fm.lizhi.commons.template.datacenter.protocol.GameRound gameRound = 1;</code>
     */
    boolean hasGameRound();
    /**
     * <code>optional .fm.lizhi.commons.template.datacenter.protocol.GameRound gameRound = 1;</code>
     */
    fm.lizhi.ocean.seal.protocol.GameRoundServiceProto.GameRound getGameRound();
    /**
     * <code>optional .fm.lizhi.commons.template.datacenter.protocol.GameRound gameRound = 1;</code>
     */
    fm.lizhi.ocean.seal.protocol.GameRoundServiceProto.GameRoundOrBuilder getGameRoundOrBuilder();
  }
  /**
   * Protobuf type {@code fm.lizhi.commons.template.datacenter.protocol.ResponseStartGameRound}
   *
   * <pre>
   * rcode == 1 (ILLEGAL_PARAMS) = 参数非法
   * rcode == 2 (NOT_EXISTS) = 游戏ID不存在
   * rcode == 3 (ERROR) = 内部错误
   * </pre>
   */
  public static final class ResponseStartGameRound extends
      com.google.protobuf.GeneratedMessage
      implements ResponseStartGameRoundOrBuilder {
    // Use ResponseStartGameRound.newBuilder() to construct.
    private ResponseStartGameRound(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
      super(builder);
      this.unknownFields = builder.getUnknownFields();
    }
    private ResponseStartGameRound(boolean noInit) { this.unknownFields = com.google.protobuf.UnknownFieldSet.getDefaultInstance(); }

    private static final ResponseStartGameRound defaultInstance;
    public static ResponseStartGameRound getDefaultInstance() {
      return defaultInstance;
    }

    public ResponseStartGameRound getDefaultInstanceForType() {
      return defaultInstance;
    }

    private final com.google.protobuf.UnknownFieldSet unknownFields;
    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
        getUnknownFields() {
      return this.unknownFields;
    }
    private ResponseStartGameRound(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      initFields();
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            default: {
              if (!parseUnknownField(input, unknownFields,
                                     extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
            case 10: {
              fm.lizhi.ocean.seal.protocol.GameRoundServiceProto.GameRound.Builder subBuilder = null;
              if (((bitField0_ & 0x00000001) == 0x00000001)) {
                subBuilder = gameRound_.toBuilder();
              }
              gameRound_ = input.readMessage(fm.lizhi.ocean.seal.protocol.GameRoundServiceProto.GameRound.PARSER, extensionRegistry);
              if (subBuilder != null) {
                subBuilder.mergeFrom(gameRound_);
                gameRound_ = subBuilder.buildPartial();
              }
              bitField0_ |= 0x00000001;
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e.getMessage()).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return fm.lizhi.ocean.seal.protocol.GameRoundServiceProto.internal_static_fm_lizhi_commons_template_datacenter_protocol_ResponseStartGameRound_descriptor;
    }

    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return fm.lizhi.ocean.seal.protocol.GameRoundServiceProto.internal_static_fm_lizhi_commons_template_datacenter_protocol_ResponseStartGameRound_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              fm.lizhi.ocean.seal.protocol.GameRoundServiceProto.ResponseStartGameRound.class, fm.lizhi.ocean.seal.protocol.GameRoundServiceProto.ResponseStartGameRound.Builder.class);
    }

    public static com.google.protobuf.Parser<ResponseStartGameRound> PARSER =
        new com.google.protobuf.AbstractParser<ResponseStartGameRound>() {
      public ResponseStartGameRound parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new ResponseStartGameRound(input, extensionRegistry);
      }
    };

    @java.lang.Override
    public com.google.protobuf.Parser<ResponseStartGameRound> getParserForType() {
      return PARSER;
    }

    private int bitField0_;
    // optional .fm.lizhi.commons.template.datacenter.protocol.GameRound gameRound = 1;
    public static final int GAMEROUND_FIELD_NUMBER = 1;
    private fm.lizhi.ocean.seal.protocol.GameRoundServiceProto.GameRound gameRound_;
    /**
     * <code>optional .fm.lizhi.commons.template.datacenter.protocol.GameRound gameRound = 1;</code>
     */
    public boolean hasGameRound() {
      return ((bitField0_ & 0x00000001) == 0x00000001);
    }
    /**
     * <code>optional .fm.lizhi.commons.template.datacenter.protocol.GameRound gameRound = 1;</code>
     */
    public fm.lizhi.ocean.seal.protocol.GameRoundServiceProto.GameRound getGameRound() {
      return gameRound_;
    }
    /**
     * <code>optional .fm.lizhi.commons.template.datacenter.protocol.GameRound gameRound = 1;</code>
     */
    public fm.lizhi.ocean.seal.protocol.GameRoundServiceProto.GameRoundOrBuilder getGameRoundOrBuilder() {
      return gameRound_;
    }

    private void initFields() {
      gameRound_ = fm.lizhi.ocean.seal.protocol.GameRoundServiceProto.GameRound.getDefaultInstance();
    }
    private byte memoizedIsInitialized = -1;
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized != -1) return isInitialized == 1;

      if (hasGameRound()) {
        if (!getGameRound().isInitialized()) {
          memoizedIsInitialized = 0;
          return false;
        }
      }
      memoizedIsInitialized = 1;
      return true;
    }

    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      getSerializedSize();
      if (((bitField0_ & 0x00000001) == 0x00000001)) {
        output.writeMessage(1, gameRound_);
      }
      getUnknownFields().writeTo(output);
    }

    private int memoizedSerializedSize = -1;
    public int getSerializedSize() {
      int size = memoizedSerializedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) == 0x00000001)) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(1, gameRound_);
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSerializedSize = size;
      return size;
    }

    private static final long serialVersionUID = 0L;
    @java.lang.Override
    protected java.lang.Object writeReplace()
        throws java.io.ObjectStreamException {
      return super.writeReplace();
    }

    public static fm.lizhi.ocean.seal.protocol.GameRoundServiceProto.ResponseStartGameRound parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static fm.lizhi.ocean.seal.protocol.GameRoundServiceProto.ResponseStartGameRound parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static fm.lizhi.ocean.seal.protocol.GameRoundServiceProto.ResponseStartGameRound parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static fm.lizhi.ocean.seal.protocol.GameRoundServiceProto.ResponseStartGameRound parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static fm.lizhi.ocean.seal.protocol.GameRoundServiceProto.ResponseStartGameRound parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return PARSER.parseFrom(input);
    }
    public static fm.lizhi.ocean.seal.protocol.GameRoundServiceProto.ResponseStartGameRound parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseFrom(input, extensionRegistry);
    }
    public static fm.lizhi.ocean.seal.protocol.GameRoundServiceProto.ResponseStartGameRound parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return PARSER.parseDelimitedFrom(input);
    }
    public static fm.lizhi.ocean.seal.protocol.GameRoundServiceProto.ResponseStartGameRound parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseDelimitedFrom(input, extensionRegistry);
    }
    public static fm.lizhi.ocean.seal.protocol.GameRoundServiceProto.ResponseStartGameRound parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return PARSER.parseFrom(input);
    }
    public static fm.lizhi.ocean.seal.protocol.GameRoundServiceProto.ResponseStartGameRound parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseFrom(input, extensionRegistry);
    }

    public static Builder newBuilder() { return Builder.create(); }
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder(fm.lizhi.ocean.seal.protocol.GameRoundServiceProto.ResponseStartGameRound prototype) {
      return newBuilder().mergeFrom(prototype);
    }
    public Builder toBuilder() { return newBuilder(this); }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code fm.lizhi.commons.template.datacenter.protocol.ResponseStartGameRound}
     *
     * <pre>
     * rcode == 1 (ILLEGAL_PARAMS) = 参数非法
     * rcode == 2 (NOT_EXISTS) = 游戏ID不存在
     * rcode == 3 (ERROR) = 内部错误
     * </pre>
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessage.Builder<Builder>
       implements fm.lizhi.ocean.seal.protocol.GameRoundServiceProto.ResponseStartGameRoundOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return fm.lizhi.ocean.seal.protocol.GameRoundServiceProto.internal_static_fm_lizhi_commons_template_datacenter_protocol_ResponseStartGameRound_descriptor;
      }

      protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return fm.lizhi.ocean.seal.protocol.GameRoundServiceProto.internal_static_fm_lizhi_commons_template_datacenter_protocol_ResponseStartGameRound_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                fm.lizhi.ocean.seal.protocol.GameRoundServiceProto.ResponseStartGameRound.class, fm.lizhi.ocean.seal.protocol.GameRoundServiceProto.ResponseStartGameRound.Builder.class);
      }

      // Construct using fm.lizhi.ocean.seal.protocol.GameRoundServiceProto.ResponseStartGameRound.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessage.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessage.alwaysUseFieldBuilders) {
          getGameRoundFieldBuilder();
        }
      }
      private static Builder create() {
        return new Builder();
      }

      public Builder clear() {
        super.clear();
        if (gameRoundBuilder_ == null) {
          gameRound_ = fm.lizhi.ocean.seal.protocol.GameRoundServiceProto.GameRound.getDefaultInstance();
        } else {
          gameRoundBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000001);
        return this;
      }

      public Builder clone() {
        return create().mergeFrom(buildPartial());
      }

      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return fm.lizhi.ocean.seal.protocol.GameRoundServiceProto.internal_static_fm_lizhi_commons_template_datacenter_protocol_ResponseStartGameRound_descriptor;
      }

      public fm.lizhi.ocean.seal.protocol.GameRoundServiceProto.ResponseStartGameRound getDefaultInstanceForType() {
        return fm.lizhi.ocean.seal.protocol.GameRoundServiceProto.ResponseStartGameRound.getDefaultInstance();
      }

      public fm.lizhi.ocean.seal.protocol.GameRoundServiceProto.ResponseStartGameRound build() {
        fm.lizhi.ocean.seal.protocol.GameRoundServiceProto.ResponseStartGameRound result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      public fm.lizhi.ocean.seal.protocol.GameRoundServiceProto.ResponseStartGameRound buildPartial() {
        fm.lizhi.ocean.seal.protocol.GameRoundServiceProto.ResponseStartGameRound result = new fm.lizhi.ocean.seal.protocol.GameRoundServiceProto.ResponseStartGameRound(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) == 0x00000001)) {
          to_bitField0_ |= 0x00000001;
        }
        if (gameRoundBuilder_ == null) {
          result.gameRound_ = gameRound_;
        } else {
          result.gameRound_ = gameRoundBuilder_.build();
        }
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof fm.lizhi.ocean.seal.protocol.GameRoundServiceProto.ResponseStartGameRound) {
          return mergeFrom((fm.lizhi.ocean.seal.protocol.GameRoundServiceProto.ResponseStartGameRound)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(fm.lizhi.ocean.seal.protocol.GameRoundServiceProto.ResponseStartGameRound other) {
        if (other == fm.lizhi.ocean.seal.protocol.GameRoundServiceProto.ResponseStartGameRound.getDefaultInstance()) return this;
        if (other.hasGameRound()) {
          mergeGameRound(other.getGameRound());
        }
        this.mergeUnknownFields(other.getUnknownFields());
        return this;
      }

      public final boolean isInitialized() {
        if (hasGameRound()) {
          if (!getGameRound().isInitialized()) {
            
            return false;
          }
        }
        return true;
      }

      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        fm.lizhi.ocean.seal.protocol.GameRoundServiceProto.ResponseStartGameRound parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (fm.lizhi.ocean.seal.protocol.GameRoundServiceProto.ResponseStartGameRound) e.getUnfinishedMessage();
          throw e;
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      // optional .fm.lizhi.commons.template.datacenter.protocol.GameRound gameRound = 1;
      private fm.lizhi.ocean.seal.protocol.GameRoundServiceProto.GameRound gameRound_ = fm.lizhi.ocean.seal.protocol.GameRoundServiceProto.GameRound.getDefaultInstance();
      private com.google.protobuf.SingleFieldBuilder<
          fm.lizhi.ocean.seal.protocol.GameRoundServiceProto.GameRound, fm.lizhi.ocean.seal.protocol.GameRoundServiceProto.GameRound.Builder, fm.lizhi.ocean.seal.protocol.GameRoundServiceProto.GameRoundOrBuilder> gameRoundBuilder_;
      /**
       * <code>optional .fm.lizhi.commons.template.datacenter.protocol.GameRound gameRound = 1;</code>
       */
      public boolean hasGameRound() {
        return ((bitField0_ & 0x00000001) == 0x00000001);
      }
      /**
       * <code>optional .fm.lizhi.commons.template.datacenter.protocol.GameRound gameRound = 1;</code>
       */
      public fm.lizhi.ocean.seal.protocol.GameRoundServiceProto.GameRound getGameRound() {
        if (gameRoundBuilder_ == null) {
          return gameRound_;
        } else {
          return gameRoundBuilder_.getMessage();
        }
      }
      /**
       * <code>optional .fm.lizhi.commons.template.datacenter.protocol.GameRound gameRound = 1;</code>
       */
      public Builder setGameRound(fm.lizhi.ocean.seal.protocol.GameRoundServiceProto.GameRound value) {
        if (gameRoundBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          gameRound_ = value;
          onChanged();
        } else {
          gameRoundBuilder_.setMessage(value);
        }
        bitField0_ |= 0x00000001;
        return this;
      }
      /**
       * <code>optional .fm.lizhi.commons.template.datacenter.protocol.GameRound gameRound = 1;</code>
       */
      public Builder setGameRound(
          fm.lizhi.ocean.seal.protocol.GameRoundServiceProto.GameRound.Builder builderForValue) {
        if (gameRoundBuilder_ == null) {
          gameRound_ = builderForValue.build();
          onChanged();
        } else {
          gameRoundBuilder_.setMessage(builderForValue.build());
        }
        bitField0_ |= 0x00000001;
        return this;
      }
      /**
       * <code>optional .fm.lizhi.commons.template.datacenter.protocol.GameRound gameRound = 1;</code>
       */
      public Builder mergeGameRound(fm.lizhi.ocean.seal.protocol.GameRoundServiceProto.GameRound value) {
        if (gameRoundBuilder_ == null) {
          if (((bitField0_ & 0x00000001) == 0x00000001) &&
              gameRound_ != fm.lizhi.ocean.seal.protocol.GameRoundServiceProto.GameRound.getDefaultInstance()) {
            gameRound_ =
              fm.lizhi.ocean.seal.protocol.GameRoundServiceProto.GameRound.newBuilder(gameRound_).mergeFrom(value).buildPartial();
          } else {
            gameRound_ = value;
          }
          onChanged();
        } else {
          gameRoundBuilder_.mergeFrom(value);
        }
        bitField0_ |= 0x00000001;
        return this;
      }
      /**
       * <code>optional .fm.lizhi.commons.template.datacenter.protocol.GameRound gameRound = 1;</code>
       */
      public Builder clearGameRound() {
        if (gameRoundBuilder_ == null) {
          gameRound_ = fm.lizhi.ocean.seal.protocol.GameRoundServiceProto.GameRound.getDefaultInstance();
          onChanged();
        } else {
          gameRoundBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000001);
        return this;
      }
      /**
       * <code>optional .fm.lizhi.commons.template.datacenter.protocol.GameRound gameRound = 1;</code>
       */
      public fm.lizhi.ocean.seal.protocol.GameRoundServiceProto.GameRound.Builder getGameRoundBuilder() {
        bitField0_ |= 0x00000001;
        onChanged();
        return getGameRoundFieldBuilder().getBuilder();
      }
      /**
       * <code>optional .fm.lizhi.commons.template.datacenter.protocol.GameRound gameRound = 1;</code>
       */
      public fm.lizhi.ocean.seal.protocol.GameRoundServiceProto.GameRoundOrBuilder getGameRoundOrBuilder() {
        if (gameRoundBuilder_ != null) {
          return gameRoundBuilder_.getMessageOrBuilder();
        } else {
          return gameRound_;
        }
      }
      /**
       * <code>optional .fm.lizhi.commons.template.datacenter.protocol.GameRound gameRound = 1;</code>
       */
      private com.google.protobuf.SingleFieldBuilder<
          fm.lizhi.ocean.seal.protocol.GameRoundServiceProto.GameRound, fm.lizhi.ocean.seal.protocol.GameRoundServiceProto.GameRound.Builder, fm.lizhi.ocean.seal.protocol.GameRoundServiceProto.GameRoundOrBuilder> 
          getGameRoundFieldBuilder() {
        if (gameRoundBuilder_ == null) {
          gameRoundBuilder_ = new com.google.protobuf.SingleFieldBuilder<
              fm.lizhi.ocean.seal.protocol.GameRoundServiceProto.GameRound, fm.lizhi.ocean.seal.protocol.GameRoundServiceProto.GameRound.Builder, fm.lizhi.ocean.seal.protocol.GameRoundServiceProto.GameRoundOrBuilder>(
                  gameRound_,
                  getParentForChildren(),
                  isClean());
          gameRound_ = null;
        }
        return gameRoundBuilder_;
      }

      // @@protoc_insertion_point(builder_scope:fm.lizhi.commons.template.datacenter.protocol.ResponseStartGameRound)
    }

    static {
      defaultInstance = new ResponseStartGameRound(true);
      defaultInstance.initFields();
    }

    // @@protoc_insertion_point(class_scope:fm.lizhi.commons.template.datacenter.protocol.ResponseStartGameRound)
  }

  public interface EndGameRoundParamOrBuilder
      extends com.google.protobuf.MessageOrBuilder {

    // required string appId = 1;
    /**
     * <code>required string appId = 1;</code>
     *
     * <pre>
     * 业务方appId，为了兼容已有业务用string类型
     * </pre>
     */
    boolean hasAppId();
    /**
     * <code>required string appId = 1;</code>
     *
     * <pre>
     * 业务方appId，为了兼容已有业务用string类型
     * </pre>
     */
    java.lang.String getAppId();
    /**
     * <code>required string appId = 1;</code>
     *
     * <pre>
     * 业务方appId，为了兼容已有业务用string类型
     * </pre>
     */
    com.google.protobuf.ByteString
        getAppIdBytes();

    // required string gameId = 2;
    /**
     * <code>required string gameId = 2;</code>
     *
     * <pre>
     * Seal平台游戏ID，也可以是渠道游戏ID，服务端内部通过gameId和appId组合识别
     * </pre>
     */
    boolean hasGameId();
    /**
     * <code>required string gameId = 2;</code>
     *
     * <pre>
     * Seal平台游戏ID，也可以是渠道游戏ID，服务端内部通过gameId和appId组合识别
     * </pre>
     */
    java.lang.String getGameId();
    /**
     * <code>required string gameId = 2;</code>
     *
     * <pre>
     * Seal平台游戏ID，也可以是渠道游戏ID，服务端内部通过gameId和appId组合识别
     * </pre>
     */
    com.google.protobuf.ByteString
        getGameIdBytes();

    // required string groupId = 3;
    /**
     * <code>required string groupId = 3;</code>
     *
     * <pre>
     * 组ID
     * </pre>
     */
    boolean hasGroupId();
    /**
     * <code>required string groupId = 3;</code>
     *
     * <pre>
     * 组ID
     * </pre>
     */
    java.lang.String getGroupId();
    /**
     * <code>required string groupId = 3;</code>
     *
     * <pre>
     * 组ID
     * </pre>
     */
    com.google.protobuf.ByteString
        getGroupIdBytes();

    // required string channelRoundId = 4;
    /**
     * <code>required string channelRoundId = 4;</code>
     *
     * <pre>
     * 渠道局ID
     * </pre>
     */
    boolean hasChannelRoundId();
    /**
     * <code>required string channelRoundId = 4;</code>
     *
     * <pre>
     * 渠道局ID
     * </pre>
     */
    java.lang.String getChannelRoundId();
    /**
     * <code>required string channelRoundId = 4;</code>
     *
     * <pre>
     * 渠道局ID
     * </pre>
     */
    com.google.protobuf.ByteString
        getChannelRoundIdBytes();
  }
  /**
   * Protobuf type {@code fm.lizhi.commons.template.datacenter.protocol.EndGameRoundParam}
   *
   * <pre>
   ** 结束游戏局参数 
   * </pre>
   */
  public static final class EndGameRoundParam extends
      com.google.protobuf.GeneratedMessage
      implements EndGameRoundParamOrBuilder {
    // Use EndGameRoundParam.newBuilder() to construct.
    private EndGameRoundParam(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
      super(builder);
      this.unknownFields = builder.getUnknownFields();
    }
    private EndGameRoundParam(boolean noInit) { this.unknownFields = com.google.protobuf.UnknownFieldSet.getDefaultInstance(); }

    private static final EndGameRoundParam defaultInstance;
    public static EndGameRoundParam getDefaultInstance() {
      return defaultInstance;
    }

    public EndGameRoundParam getDefaultInstanceForType() {
      return defaultInstance;
    }

    private final com.google.protobuf.UnknownFieldSet unknownFields;
    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
        getUnknownFields() {
      return this.unknownFields;
    }
    private EndGameRoundParam(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      initFields();
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            default: {
              if (!parseUnknownField(input, unknownFields,
                                     extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
            case 10: {
              bitField0_ |= 0x00000001;
              appId_ = input.readBytes();
              break;
            }
            case 18: {
              bitField0_ |= 0x00000002;
              gameId_ = input.readBytes();
              break;
            }
            case 26: {
              bitField0_ |= 0x00000004;
              groupId_ = input.readBytes();
              break;
            }
            case 34: {
              bitField0_ |= 0x00000008;
              channelRoundId_ = input.readBytes();
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e.getMessage()).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return fm.lizhi.ocean.seal.protocol.GameRoundServiceProto.internal_static_fm_lizhi_commons_template_datacenter_protocol_EndGameRoundParam_descriptor;
    }

    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return fm.lizhi.ocean.seal.protocol.GameRoundServiceProto.internal_static_fm_lizhi_commons_template_datacenter_protocol_EndGameRoundParam_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              fm.lizhi.ocean.seal.protocol.GameRoundServiceProto.EndGameRoundParam.class, fm.lizhi.ocean.seal.protocol.GameRoundServiceProto.EndGameRoundParam.Builder.class);
    }

    public static com.google.protobuf.Parser<EndGameRoundParam> PARSER =
        new com.google.protobuf.AbstractParser<EndGameRoundParam>() {
      public EndGameRoundParam parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new EndGameRoundParam(input, extensionRegistry);
      }
    };

    @java.lang.Override
    public com.google.protobuf.Parser<EndGameRoundParam> getParserForType() {
      return PARSER;
    }

    private int bitField0_;
    // required string appId = 1;
    public static final int APPID_FIELD_NUMBER = 1;
    private java.lang.Object appId_;
    /**
     * <code>required string appId = 1;</code>
     *
     * <pre>
     * 业务方appId，为了兼容已有业务用string类型
     * </pre>
     */
    public boolean hasAppId() {
      return ((bitField0_ & 0x00000001) == 0x00000001);
    }
    /**
     * <code>required string appId = 1;</code>
     *
     * <pre>
     * 业务方appId，为了兼容已有业务用string类型
     * </pre>
     */
    public java.lang.String getAppId() {
      java.lang.Object ref = appId_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          appId_ = s;
        }
        return s;
      }
    }
    /**
     * <code>required string appId = 1;</code>
     *
     * <pre>
     * 业务方appId，为了兼容已有业务用string类型
     * </pre>
     */
    public com.google.protobuf.ByteString
        getAppIdBytes() {
      java.lang.Object ref = appId_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        appId_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    // required string gameId = 2;
    public static final int GAMEID_FIELD_NUMBER = 2;
    private java.lang.Object gameId_;
    /**
     * <code>required string gameId = 2;</code>
     *
     * <pre>
     * Seal平台游戏ID，也可以是渠道游戏ID，服务端内部通过gameId和appId组合识别
     * </pre>
     */
    public boolean hasGameId() {
      return ((bitField0_ & 0x00000002) == 0x00000002);
    }
    /**
     * <code>required string gameId = 2;</code>
     *
     * <pre>
     * Seal平台游戏ID，也可以是渠道游戏ID，服务端内部通过gameId和appId组合识别
     * </pre>
     */
    public java.lang.String getGameId() {
      java.lang.Object ref = gameId_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          gameId_ = s;
        }
        return s;
      }
    }
    /**
     * <code>required string gameId = 2;</code>
     *
     * <pre>
     * Seal平台游戏ID，也可以是渠道游戏ID，服务端内部通过gameId和appId组合识别
     * </pre>
     */
    public com.google.protobuf.ByteString
        getGameIdBytes() {
      java.lang.Object ref = gameId_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        gameId_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    // required string groupId = 3;
    public static final int GROUPID_FIELD_NUMBER = 3;
    private java.lang.Object groupId_;
    /**
     * <code>required string groupId = 3;</code>
     *
     * <pre>
     * 组ID
     * </pre>
     */
    public boolean hasGroupId() {
      return ((bitField0_ & 0x00000004) == 0x00000004);
    }
    /**
     * <code>required string groupId = 3;</code>
     *
     * <pre>
     * 组ID
     * </pre>
     */
    public java.lang.String getGroupId() {
      java.lang.Object ref = groupId_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          groupId_ = s;
        }
        return s;
      }
    }
    /**
     * <code>required string groupId = 3;</code>
     *
     * <pre>
     * 组ID
     * </pre>
     */
    public com.google.protobuf.ByteString
        getGroupIdBytes() {
      java.lang.Object ref = groupId_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        groupId_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    // required string channelRoundId = 4;
    public static final int CHANNELROUNDID_FIELD_NUMBER = 4;
    private java.lang.Object channelRoundId_;
    /**
     * <code>required string channelRoundId = 4;</code>
     *
     * <pre>
     * 渠道局ID
     * </pre>
     */
    public boolean hasChannelRoundId() {
      return ((bitField0_ & 0x00000008) == 0x00000008);
    }
    /**
     * <code>required string channelRoundId = 4;</code>
     *
     * <pre>
     * 渠道局ID
     * </pre>
     */
    public java.lang.String getChannelRoundId() {
      java.lang.Object ref = channelRoundId_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          channelRoundId_ = s;
        }
        return s;
      }
    }
    /**
     * <code>required string channelRoundId = 4;</code>
     *
     * <pre>
     * 渠道局ID
     * </pre>
     */
    public com.google.protobuf.ByteString
        getChannelRoundIdBytes() {
      java.lang.Object ref = channelRoundId_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        channelRoundId_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    private void initFields() {
      appId_ = "";
      gameId_ = "";
      groupId_ = "";
      channelRoundId_ = "";
    }
    private byte memoizedIsInitialized = -1;
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized != -1) return isInitialized == 1;

      if (!hasAppId()) {
        memoizedIsInitialized = 0;
        return false;
      }
      if (!hasGameId()) {
        memoizedIsInitialized = 0;
        return false;
      }
      if (!hasGroupId()) {
        memoizedIsInitialized = 0;
        return false;
      }
      if (!hasChannelRoundId()) {
        memoizedIsInitialized = 0;
        return false;
      }
      memoizedIsInitialized = 1;
      return true;
    }

    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      getSerializedSize();
      if (((bitField0_ & 0x00000001) == 0x00000001)) {
        output.writeBytes(1, getAppIdBytes());
      }
      if (((bitField0_ & 0x00000002) == 0x00000002)) {
        output.writeBytes(2, getGameIdBytes());
      }
      if (((bitField0_ & 0x00000004) == 0x00000004)) {
        output.writeBytes(3, getGroupIdBytes());
      }
      if (((bitField0_ & 0x00000008) == 0x00000008)) {
        output.writeBytes(4, getChannelRoundIdBytes());
      }
      getUnknownFields().writeTo(output);
    }

    private int memoizedSerializedSize = -1;
    public int getSerializedSize() {
      int size = memoizedSerializedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) == 0x00000001)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(1, getAppIdBytes());
      }
      if (((bitField0_ & 0x00000002) == 0x00000002)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(2, getGameIdBytes());
      }
      if (((bitField0_ & 0x00000004) == 0x00000004)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(3, getGroupIdBytes());
      }
      if (((bitField0_ & 0x00000008) == 0x00000008)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(4, getChannelRoundIdBytes());
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSerializedSize = size;
      return size;
    }

    private static final long serialVersionUID = 0L;
    @java.lang.Override
    protected java.lang.Object writeReplace()
        throws java.io.ObjectStreamException {
      return super.writeReplace();
    }

    public static fm.lizhi.ocean.seal.protocol.GameRoundServiceProto.EndGameRoundParam parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static fm.lizhi.ocean.seal.protocol.GameRoundServiceProto.EndGameRoundParam parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static fm.lizhi.ocean.seal.protocol.GameRoundServiceProto.EndGameRoundParam parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static fm.lizhi.ocean.seal.protocol.GameRoundServiceProto.EndGameRoundParam parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static fm.lizhi.ocean.seal.protocol.GameRoundServiceProto.EndGameRoundParam parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return PARSER.parseFrom(input);
    }
    public static fm.lizhi.ocean.seal.protocol.GameRoundServiceProto.EndGameRoundParam parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseFrom(input, extensionRegistry);
    }
    public static fm.lizhi.ocean.seal.protocol.GameRoundServiceProto.EndGameRoundParam parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return PARSER.parseDelimitedFrom(input);
    }
    public static fm.lizhi.ocean.seal.protocol.GameRoundServiceProto.EndGameRoundParam parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseDelimitedFrom(input, extensionRegistry);
    }
    public static fm.lizhi.ocean.seal.protocol.GameRoundServiceProto.EndGameRoundParam parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return PARSER.parseFrom(input);
    }
    public static fm.lizhi.ocean.seal.protocol.GameRoundServiceProto.EndGameRoundParam parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseFrom(input, extensionRegistry);
    }

    public static Builder newBuilder() { return Builder.create(); }
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder(fm.lizhi.ocean.seal.protocol.GameRoundServiceProto.EndGameRoundParam prototype) {
      return newBuilder().mergeFrom(prototype);
    }
    public Builder toBuilder() { return newBuilder(this); }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code fm.lizhi.commons.template.datacenter.protocol.EndGameRoundParam}
     *
     * <pre>
     ** 结束游戏局参数 
     * </pre>
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessage.Builder<Builder>
       implements fm.lizhi.ocean.seal.protocol.GameRoundServiceProto.EndGameRoundParamOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return fm.lizhi.ocean.seal.protocol.GameRoundServiceProto.internal_static_fm_lizhi_commons_template_datacenter_protocol_EndGameRoundParam_descriptor;
      }

      protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return fm.lizhi.ocean.seal.protocol.GameRoundServiceProto.internal_static_fm_lizhi_commons_template_datacenter_protocol_EndGameRoundParam_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                fm.lizhi.ocean.seal.protocol.GameRoundServiceProto.EndGameRoundParam.class, fm.lizhi.ocean.seal.protocol.GameRoundServiceProto.EndGameRoundParam.Builder.class);
      }

      // Construct using fm.lizhi.ocean.seal.protocol.GameRoundServiceProto.EndGameRoundParam.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessage.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessage.alwaysUseFieldBuilders) {
        }
      }
      private static Builder create() {
        return new Builder();
      }

      public Builder clear() {
        super.clear();
        appId_ = "";
        bitField0_ = (bitField0_ & ~0x00000001);
        gameId_ = "";
        bitField0_ = (bitField0_ & ~0x00000002);
        groupId_ = "";
        bitField0_ = (bitField0_ & ~0x00000004);
        channelRoundId_ = "";
        bitField0_ = (bitField0_ & ~0x00000008);
        return this;
      }

      public Builder clone() {
        return create().mergeFrom(buildPartial());
      }

      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return fm.lizhi.ocean.seal.protocol.GameRoundServiceProto.internal_static_fm_lizhi_commons_template_datacenter_protocol_EndGameRoundParam_descriptor;
      }

      public fm.lizhi.ocean.seal.protocol.GameRoundServiceProto.EndGameRoundParam getDefaultInstanceForType() {
        return fm.lizhi.ocean.seal.protocol.GameRoundServiceProto.EndGameRoundParam.getDefaultInstance();
      }

      public fm.lizhi.ocean.seal.protocol.GameRoundServiceProto.EndGameRoundParam build() {
        fm.lizhi.ocean.seal.protocol.GameRoundServiceProto.EndGameRoundParam result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      public fm.lizhi.ocean.seal.protocol.GameRoundServiceProto.EndGameRoundParam buildPartial() {
        fm.lizhi.ocean.seal.protocol.GameRoundServiceProto.EndGameRoundParam result = new fm.lizhi.ocean.seal.protocol.GameRoundServiceProto.EndGameRoundParam(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) == 0x00000001)) {
          to_bitField0_ |= 0x00000001;
        }
        result.appId_ = appId_;
        if (((from_bitField0_ & 0x00000002) == 0x00000002)) {
          to_bitField0_ |= 0x00000002;
        }
        result.gameId_ = gameId_;
        if (((from_bitField0_ & 0x00000004) == 0x00000004)) {
          to_bitField0_ |= 0x00000004;
        }
        result.groupId_ = groupId_;
        if (((from_bitField0_ & 0x00000008) == 0x00000008)) {
          to_bitField0_ |= 0x00000008;
        }
        result.channelRoundId_ = channelRoundId_;
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof fm.lizhi.ocean.seal.protocol.GameRoundServiceProto.EndGameRoundParam) {
          return mergeFrom((fm.lizhi.ocean.seal.protocol.GameRoundServiceProto.EndGameRoundParam)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(fm.lizhi.ocean.seal.protocol.GameRoundServiceProto.EndGameRoundParam other) {
        if (other == fm.lizhi.ocean.seal.protocol.GameRoundServiceProto.EndGameRoundParam.getDefaultInstance()) return this;
        if (other.hasAppId()) {
          bitField0_ |= 0x00000001;
          appId_ = other.appId_;
          onChanged();
        }
        if (other.hasGameId()) {
          bitField0_ |= 0x00000002;
          gameId_ = other.gameId_;
          onChanged();
        }
        if (other.hasGroupId()) {
          bitField0_ |= 0x00000004;
          groupId_ = other.groupId_;
          onChanged();
        }
        if (other.hasChannelRoundId()) {
          bitField0_ |= 0x00000008;
          channelRoundId_ = other.channelRoundId_;
          onChanged();
        }
        this.mergeUnknownFields(other.getUnknownFields());
        return this;
      }

      public final boolean isInitialized() {
        if (!hasAppId()) {
          
          return false;
        }
        if (!hasGameId()) {
          
          return false;
        }
        if (!hasGroupId()) {
          
          return false;
        }
        if (!hasChannelRoundId()) {
          
          return false;
        }
        return true;
      }

      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        fm.lizhi.ocean.seal.protocol.GameRoundServiceProto.EndGameRoundParam parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (fm.lizhi.ocean.seal.protocol.GameRoundServiceProto.EndGameRoundParam) e.getUnfinishedMessage();
          throw e;
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      // required string appId = 1;
      private java.lang.Object appId_ = "";
      /**
       * <code>required string appId = 1;</code>
       *
       * <pre>
       * 业务方appId，为了兼容已有业务用string类型
       * </pre>
       */
      public boolean hasAppId() {
        return ((bitField0_ & 0x00000001) == 0x00000001);
      }
      /**
       * <code>required string appId = 1;</code>
       *
       * <pre>
       * 业务方appId，为了兼容已有业务用string类型
       * </pre>
       */
      public java.lang.String getAppId() {
        java.lang.Object ref = appId_;
        if (!(ref instanceof java.lang.String)) {
          java.lang.String s = ((com.google.protobuf.ByteString) ref)
              .toStringUtf8();
          appId_ = s;
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <code>required string appId = 1;</code>
       *
       * <pre>
       * 业务方appId，为了兼容已有业务用string类型
       * </pre>
       */
      public com.google.protobuf.ByteString
          getAppIdBytes() {
        java.lang.Object ref = appId_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          appId_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <code>required string appId = 1;</code>
       *
       * <pre>
       * 业务方appId，为了兼容已有业务用string类型
       * </pre>
       */
      public Builder setAppId(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000001;
        appId_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>required string appId = 1;</code>
       *
       * <pre>
       * 业务方appId，为了兼容已有业务用string类型
       * </pre>
       */
      public Builder clearAppId() {
        bitField0_ = (bitField0_ & ~0x00000001);
        appId_ = getDefaultInstance().getAppId();
        onChanged();
        return this;
      }
      /**
       * <code>required string appId = 1;</code>
       *
       * <pre>
       * 业务方appId，为了兼容已有业务用string类型
       * </pre>
       */
      public Builder setAppIdBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000001;
        appId_ = value;
        onChanged();
        return this;
      }

      // required string gameId = 2;
      private java.lang.Object gameId_ = "";
      /**
       * <code>required string gameId = 2;</code>
       *
       * <pre>
       * Seal平台游戏ID，也可以是渠道游戏ID，服务端内部通过gameId和appId组合识别
       * </pre>
       */
      public boolean hasGameId() {
        return ((bitField0_ & 0x00000002) == 0x00000002);
      }
      /**
       * <code>required string gameId = 2;</code>
       *
       * <pre>
       * Seal平台游戏ID，也可以是渠道游戏ID，服务端内部通过gameId和appId组合识别
       * </pre>
       */
      public java.lang.String getGameId() {
        java.lang.Object ref = gameId_;
        if (!(ref instanceof java.lang.String)) {
          java.lang.String s = ((com.google.protobuf.ByteString) ref)
              .toStringUtf8();
          gameId_ = s;
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <code>required string gameId = 2;</code>
       *
       * <pre>
       * Seal平台游戏ID，也可以是渠道游戏ID，服务端内部通过gameId和appId组合识别
       * </pre>
       */
      public com.google.protobuf.ByteString
          getGameIdBytes() {
        java.lang.Object ref = gameId_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          gameId_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <code>required string gameId = 2;</code>
       *
       * <pre>
       * Seal平台游戏ID，也可以是渠道游戏ID，服务端内部通过gameId和appId组合识别
       * </pre>
       */
      public Builder setGameId(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000002;
        gameId_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>required string gameId = 2;</code>
       *
       * <pre>
       * Seal平台游戏ID，也可以是渠道游戏ID，服务端内部通过gameId和appId组合识别
       * </pre>
       */
      public Builder clearGameId() {
        bitField0_ = (bitField0_ & ~0x00000002);
        gameId_ = getDefaultInstance().getGameId();
        onChanged();
        return this;
      }
      /**
       * <code>required string gameId = 2;</code>
       *
       * <pre>
       * Seal平台游戏ID，也可以是渠道游戏ID，服务端内部通过gameId和appId组合识别
       * </pre>
       */
      public Builder setGameIdBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000002;
        gameId_ = value;
        onChanged();
        return this;
      }

      // required string groupId = 3;
      private java.lang.Object groupId_ = "";
      /**
       * <code>required string groupId = 3;</code>
       *
       * <pre>
       * 组ID
       * </pre>
       */
      public boolean hasGroupId() {
        return ((bitField0_ & 0x00000004) == 0x00000004);
      }
      /**
       * <code>required string groupId = 3;</code>
       *
       * <pre>
       * 组ID
       * </pre>
       */
      public java.lang.String getGroupId() {
        java.lang.Object ref = groupId_;
        if (!(ref instanceof java.lang.String)) {
          java.lang.String s = ((com.google.protobuf.ByteString) ref)
              .toStringUtf8();
          groupId_ = s;
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <code>required string groupId = 3;</code>
       *
       * <pre>
       * 组ID
       * </pre>
       */
      public com.google.protobuf.ByteString
          getGroupIdBytes() {
        java.lang.Object ref = groupId_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          groupId_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <code>required string groupId = 3;</code>
       *
       * <pre>
       * 组ID
       * </pre>
       */
      public Builder setGroupId(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000004;
        groupId_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>required string groupId = 3;</code>
       *
       * <pre>
       * 组ID
       * </pre>
       */
      public Builder clearGroupId() {
        bitField0_ = (bitField0_ & ~0x00000004);
        groupId_ = getDefaultInstance().getGroupId();
        onChanged();
        return this;
      }
      /**
       * <code>required string groupId = 3;</code>
       *
       * <pre>
       * 组ID
       * </pre>
       */
      public Builder setGroupIdBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000004;
        groupId_ = value;
        onChanged();
        return this;
      }

      // required string channelRoundId = 4;
      private java.lang.Object channelRoundId_ = "";
      /**
       * <code>required string channelRoundId = 4;</code>
       *
       * <pre>
       * 渠道局ID
       * </pre>
       */
      public boolean hasChannelRoundId() {
        return ((bitField0_ & 0x00000008) == 0x00000008);
      }
      /**
       * <code>required string channelRoundId = 4;</code>
       *
       * <pre>
       * 渠道局ID
       * </pre>
       */
      public java.lang.String getChannelRoundId() {
        java.lang.Object ref = channelRoundId_;
        if (!(ref instanceof java.lang.String)) {
          java.lang.String s = ((com.google.protobuf.ByteString) ref)
              .toStringUtf8();
          channelRoundId_ = s;
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <code>required string channelRoundId = 4;</code>
       *
       * <pre>
       * 渠道局ID
       * </pre>
       */
      public com.google.protobuf.ByteString
          getChannelRoundIdBytes() {
        java.lang.Object ref = channelRoundId_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          channelRoundId_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <code>required string channelRoundId = 4;</code>
       *
       * <pre>
       * 渠道局ID
       * </pre>
       */
      public Builder setChannelRoundId(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000008;
        channelRoundId_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>required string channelRoundId = 4;</code>
       *
       * <pre>
       * 渠道局ID
       * </pre>
       */
      public Builder clearChannelRoundId() {
        bitField0_ = (bitField0_ & ~0x00000008);
        channelRoundId_ = getDefaultInstance().getChannelRoundId();
        onChanged();
        return this;
      }
      /**
       * <code>required string channelRoundId = 4;</code>
       *
       * <pre>
       * 渠道局ID
       * </pre>
       */
      public Builder setChannelRoundIdBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000008;
        channelRoundId_ = value;
        onChanged();
        return this;
      }

      // @@protoc_insertion_point(builder_scope:fm.lizhi.commons.template.datacenter.protocol.EndGameRoundParam)
    }

    static {
      defaultInstance = new EndGameRoundParam(true);
      defaultInstance.initFields();
    }

    // @@protoc_insertion_point(class_scope:fm.lizhi.commons.template.datacenter.protocol.EndGameRoundParam)
  }

  public interface RequestEndGameRoundOrBuilder
      extends com.google.protobuf.MessageOrBuilder {

    // optional .fm.lizhi.commons.template.datacenter.protocol.EndGameRoundParam param = 1;
    /**
     * <code>optional .fm.lizhi.commons.template.datacenter.protocol.EndGameRoundParam param = 1;</code>
     *
     * <pre>
     *参数
     * </pre>
     */
    boolean hasParam();
    /**
     * <code>optional .fm.lizhi.commons.template.datacenter.protocol.EndGameRoundParam param = 1;</code>
     *
     * <pre>
     *参数
     * </pre>
     */
    fm.lizhi.ocean.seal.protocol.GameRoundServiceProto.EndGameRoundParam getParam();
    /**
     * <code>optional .fm.lizhi.commons.template.datacenter.protocol.EndGameRoundParam param = 1;</code>
     *
     * <pre>
     *参数
     * </pre>
     */
    fm.lizhi.ocean.seal.protocol.GameRoundServiceProto.EndGameRoundParamOrBuilder getParamOrBuilder();
  }
  /**
   * Protobuf type {@code fm.lizhi.commons.template.datacenter.protocol.RequestEndGameRound}
   *
   * <pre>
   * GameRoundService.java
   * 开始平台游戏局，客户端在开始游戏的时候调用
   * domain = 4302, op = 83
   * </pre>
   */
  public static final class RequestEndGameRound extends
      com.google.protobuf.GeneratedMessage
      implements RequestEndGameRoundOrBuilder {
    // Use RequestEndGameRound.newBuilder() to construct.
    private RequestEndGameRound(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
      super(builder);
      this.unknownFields = builder.getUnknownFields();
    }
    private RequestEndGameRound(boolean noInit) { this.unknownFields = com.google.protobuf.UnknownFieldSet.getDefaultInstance(); }

    private static final RequestEndGameRound defaultInstance;
    public static RequestEndGameRound getDefaultInstance() {
      return defaultInstance;
    }

    public RequestEndGameRound getDefaultInstanceForType() {
      return defaultInstance;
    }

    private final com.google.protobuf.UnknownFieldSet unknownFields;
    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
        getUnknownFields() {
      return this.unknownFields;
    }
    private RequestEndGameRound(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      initFields();
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            default: {
              if (!parseUnknownField(input, unknownFields,
                                     extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
            case 10: {
              fm.lizhi.ocean.seal.protocol.GameRoundServiceProto.EndGameRoundParam.Builder subBuilder = null;
              if (((bitField0_ & 0x00000001) == 0x00000001)) {
                subBuilder = param_.toBuilder();
              }
              param_ = input.readMessage(fm.lizhi.ocean.seal.protocol.GameRoundServiceProto.EndGameRoundParam.PARSER, extensionRegistry);
              if (subBuilder != null) {
                subBuilder.mergeFrom(param_);
                param_ = subBuilder.buildPartial();
              }
              bitField0_ |= 0x00000001;
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e.getMessage()).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return fm.lizhi.ocean.seal.protocol.GameRoundServiceProto.internal_static_fm_lizhi_commons_template_datacenter_protocol_RequestEndGameRound_descriptor;
    }

    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return fm.lizhi.ocean.seal.protocol.GameRoundServiceProto.internal_static_fm_lizhi_commons_template_datacenter_protocol_RequestEndGameRound_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              fm.lizhi.ocean.seal.protocol.GameRoundServiceProto.RequestEndGameRound.class, fm.lizhi.ocean.seal.protocol.GameRoundServiceProto.RequestEndGameRound.Builder.class);
    }

    public static com.google.protobuf.Parser<RequestEndGameRound> PARSER =
        new com.google.protobuf.AbstractParser<RequestEndGameRound>() {
      public RequestEndGameRound parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new RequestEndGameRound(input, extensionRegistry);
      }
    };

    @java.lang.Override
    public com.google.protobuf.Parser<RequestEndGameRound> getParserForType() {
      return PARSER;
    }

    private int bitField0_;
    // optional .fm.lizhi.commons.template.datacenter.protocol.EndGameRoundParam param = 1;
    public static final int PARAM_FIELD_NUMBER = 1;
    private fm.lizhi.ocean.seal.protocol.GameRoundServiceProto.EndGameRoundParam param_;
    /**
     * <code>optional .fm.lizhi.commons.template.datacenter.protocol.EndGameRoundParam param = 1;</code>
     *
     * <pre>
     *参数
     * </pre>
     */
    public boolean hasParam() {
      return ((bitField0_ & 0x00000001) == 0x00000001);
    }
    /**
     * <code>optional .fm.lizhi.commons.template.datacenter.protocol.EndGameRoundParam param = 1;</code>
     *
     * <pre>
     *参数
     * </pre>
     */
    public fm.lizhi.ocean.seal.protocol.GameRoundServiceProto.EndGameRoundParam getParam() {
      return param_;
    }
    /**
     * <code>optional .fm.lizhi.commons.template.datacenter.protocol.EndGameRoundParam param = 1;</code>
     *
     * <pre>
     *参数
     * </pre>
     */
    public fm.lizhi.ocean.seal.protocol.GameRoundServiceProto.EndGameRoundParamOrBuilder getParamOrBuilder() {
      return param_;
    }

    private void initFields() {
      param_ = fm.lizhi.ocean.seal.protocol.GameRoundServiceProto.EndGameRoundParam.getDefaultInstance();
    }
    private byte memoizedIsInitialized = -1;
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized != -1) return isInitialized == 1;

      if (hasParam()) {
        if (!getParam().isInitialized()) {
          memoizedIsInitialized = 0;
          return false;
        }
      }
      memoizedIsInitialized = 1;
      return true;
    }

    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      getSerializedSize();
      if (((bitField0_ & 0x00000001) == 0x00000001)) {
        output.writeMessage(1, param_);
      }
      getUnknownFields().writeTo(output);
    }

    private int memoizedSerializedSize = -1;
    public int getSerializedSize() {
      int size = memoizedSerializedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) == 0x00000001)) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(1, param_);
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSerializedSize = size;
      return size;
    }

    private static final long serialVersionUID = 0L;
    @java.lang.Override
    protected java.lang.Object writeReplace()
        throws java.io.ObjectStreamException {
      return super.writeReplace();
    }

    public static fm.lizhi.ocean.seal.protocol.GameRoundServiceProto.RequestEndGameRound parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static fm.lizhi.ocean.seal.protocol.GameRoundServiceProto.RequestEndGameRound parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static fm.lizhi.ocean.seal.protocol.GameRoundServiceProto.RequestEndGameRound parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static fm.lizhi.ocean.seal.protocol.GameRoundServiceProto.RequestEndGameRound parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static fm.lizhi.ocean.seal.protocol.GameRoundServiceProto.RequestEndGameRound parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return PARSER.parseFrom(input);
    }
    public static fm.lizhi.ocean.seal.protocol.GameRoundServiceProto.RequestEndGameRound parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseFrom(input, extensionRegistry);
    }
    public static fm.lizhi.ocean.seal.protocol.GameRoundServiceProto.RequestEndGameRound parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return PARSER.parseDelimitedFrom(input);
    }
    public static fm.lizhi.ocean.seal.protocol.GameRoundServiceProto.RequestEndGameRound parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseDelimitedFrom(input, extensionRegistry);
    }
    public static fm.lizhi.ocean.seal.protocol.GameRoundServiceProto.RequestEndGameRound parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return PARSER.parseFrom(input);
    }
    public static fm.lizhi.ocean.seal.protocol.GameRoundServiceProto.RequestEndGameRound parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseFrom(input, extensionRegistry);
    }

    public static Builder newBuilder() { return Builder.create(); }
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder(fm.lizhi.ocean.seal.protocol.GameRoundServiceProto.RequestEndGameRound prototype) {
      return newBuilder().mergeFrom(prototype);
    }
    public Builder toBuilder() { return newBuilder(this); }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code fm.lizhi.commons.template.datacenter.protocol.RequestEndGameRound}
     *
     * <pre>
     * GameRoundService.java
     * 开始平台游戏局，客户端在开始游戏的时候调用
     * domain = 4302, op = 83
     * </pre>
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessage.Builder<Builder>
       implements fm.lizhi.ocean.seal.protocol.GameRoundServiceProto.RequestEndGameRoundOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return fm.lizhi.ocean.seal.protocol.GameRoundServiceProto.internal_static_fm_lizhi_commons_template_datacenter_protocol_RequestEndGameRound_descriptor;
      }

      protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return fm.lizhi.ocean.seal.protocol.GameRoundServiceProto.internal_static_fm_lizhi_commons_template_datacenter_protocol_RequestEndGameRound_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                fm.lizhi.ocean.seal.protocol.GameRoundServiceProto.RequestEndGameRound.class, fm.lizhi.ocean.seal.protocol.GameRoundServiceProto.RequestEndGameRound.Builder.class);
      }

      // Construct using fm.lizhi.ocean.seal.protocol.GameRoundServiceProto.RequestEndGameRound.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessage.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessage.alwaysUseFieldBuilders) {
          getParamFieldBuilder();
        }
      }
      private static Builder create() {
        return new Builder();
      }

      public Builder clear() {
        super.clear();
        if (paramBuilder_ == null) {
          param_ = fm.lizhi.ocean.seal.protocol.GameRoundServiceProto.EndGameRoundParam.getDefaultInstance();
        } else {
          paramBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000001);
        return this;
      }

      public Builder clone() {
        return create().mergeFrom(buildPartial());
      }

      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return fm.lizhi.ocean.seal.protocol.GameRoundServiceProto.internal_static_fm_lizhi_commons_template_datacenter_protocol_RequestEndGameRound_descriptor;
      }

      public fm.lizhi.ocean.seal.protocol.GameRoundServiceProto.RequestEndGameRound getDefaultInstanceForType() {
        return fm.lizhi.ocean.seal.protocol.GameRoundServiceProto.RequestEndGameRound.getDefaultInstance();
      }

      public fm.lizhi.ocean.seal.protocol.GameRoundServiceProto.RequestEndGameRound build() {
        fm.lizhi.ocean.seal.protocol.GameRoundServiceProto.RequestEndGameRound result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      public fm.lizhi.ocean.seal.protocol.GameRoundServiceProto.RequestEndGameRound buildPartial() {
        fm.lizhi.ocean.seal.protocol.GameRoundServiceProto.RequestEndGameRound result = new fm.lizhi.ocean.seal.protocol.GameRoundServiceProto.RequestEndGameRound(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) == 0x00000001)) {
          to_bitField0_ |= 0x00000001;
        }
        if (paramBuilder_ == null) {
          result.param_ = param_;
        } else {
          result.param_ = paramBuilder_.build();
        }
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof fm.lizhi.ocean.seal.protocol.GameRoundServiceProto.RequestEndGameRound) {
          return mergeFrom((fm.lizhi.ocean.seal.protocol.GameRoundServiceProto.RequestEndGameRound)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(fm.lizhi.ocean.seal.protocol.GameRoundServiceProto.RequestEndGameRound other) {
        if (other == fm.lizhi.ocean.seal.protocol.GameRoundServiceProto.RequestEndGameRound.getDefaultInstance()) return this;
        if (other.hasParam()) {
          mergeParam(other.getParam());
        }
        this.mergeUnknownFields(other.getUnknownFields());
        return this;
      }

      public final boolean isInitialized() {
        if (hasParam()) {
          if (!getParam().isInitialized()) {
            
            return false;
          }
        }
        return true;
      }

      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        fm.lizhi.ocean.seal.protocol.GameRoundServiceProto.RequestEndGameRound parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (fm.lizhi.ocean.seal.protocol.GameRoundServiceProto.RequestEndGameRound) e.getUnfinishedMessage();
          throw e;
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      // optional .fm.lizhi.commons.template.datacenter.protocol.EndGameRoundParam param = 1;
      private fm.lizhi.ocean.seal.protocol.GameRoundServiceProto.EndGameRoundParam param_ = fm.lizhi.ocean.seal.protocol.GameRoundServiceProto.EndGameRoundParam.getDefaultInstance();
      private com.google.protobuf.SingleFieldBuilder<
          fm.lizhi.ocean.seal.protocol.GameRoundServiceProto.EndGameRoundParam, fm.lizhi.ocean.seal.protocol.GameRoundServiceProto.EndGameRoundParam.Builder, fm.lizhi.ocean.seal.protocol.GameRoundServiceProto.EndGameRoundParamOrBuilder> paramBuilder_;
      /**
       * <code>optional .fm.lizhi.commons.template.datacenter.protocol.EndGameRoundParam param = 1;</code>
       *
       * <pre>
       *参数
       * </pre>
       */
      public boolean hasParam() {
        return ((bitField0_ & 0x00000001) == 0x00000001);
      }
      /**
       * <code>optional .fm.lizhi.commons.template.datacenter.protocol.EndGameRoundParam param = 1;</code>
       *
       * <pre>
       *参数
       * </pre>
       */
      public fm.lizhi.ocean.seal.protocol.GameRoundServiceProto.EndGameRoundParam getParam() {
        if (paramBuilder_ == null) {
          return param_;
        } else {
          return paramBuilder_.getMessage();
        }
      }
      /**
       * <code>optional .fm.lizhi.commons.template.datacenter.protocol.EndGameRoundParam param = 1;</code>
       *
       * <pre>
       *参数
       * </pre>
       */
      public Builder setParam(fm.lizhi.ocean.seal.protocol.GameRoundServiceProto.EndGameRoundParam value) {
        if (paramBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          param_ = value;
          onChanged();
        } else {
          paramBuilder_.setMessage(value);
        }
        bitField0_ |= 0x00000001;
        return this;
      }
      /**
       * <code>optional .fm.lizhi.commons.template.datacenter.protocol.EndGameRoundParam param = 1;</code>
       *
       * <pre>
       *参数
       * </pre>
       */
      public Builder setParam(
          fm.lizhi.ocean.seal.protocol.GameRoundServiceProto.EndGameRoundParam.Builder builderForValue) {
        if (paramBuilder_ == null) {
          param_ = builderForValue.build();
          onChanged();
        } else {
          paramBuilder_.setMessage(builderForValue.build());
        }
        bitField0_ |= 0x00000001;
        return this;
      }
      /**
       * <code>optional .fm.lizhi.commons.template.datacenter.protocol.EndGameRoundParam param = 1;</code>
       *
       * <pre>
       *参数
       * </pre>
       */
      public Builder mergeParam(fm.lizhi.ocean.seal.protocol.GameRoundServiceProto.EndGameRoundParam value) {
        if (paramBuilder_ == null) {
          if (((bitField0_ & 0x00000001) == 0x00000001) &&
              param_ != fm.lizhi.ocean.seal.protocol.GameRoundServiceProto.EndGameRoundParam.getDefaultInstance()) {
            param_ =
              fm.lizhi.ocean.seal.protocol.GameRoundServiceProto.EndGameRoundParam.newBuilder(param_).mergeFrom(value).buildPartial();
          } else {
            param_ = value;
          }
          onChanged();
        } else {
          paramBuilder_.mergeFrom(value);
        }
        bitField0_ |= 0x00000001;
        return this;
      }
      /**
       * <code>optional .fm.lizhi.commons.template.datacenter.protocol.EndGameRoundParam param = 1;</code>
       *
       * <pre>
       *参数
       * </pre>
       */
      public Builder clearParam() {
        if (paramBuilder_ == null) {
          param_ = fm.lizhi.ocean.seal.protocol.GameRoundServiceProto.EndGameRoundParam.getDefaultInstance();
          onChanged();
        } else {
          paramBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000001);
        return this;
      }
      /**
       * <code>optional .fm.lizhi.commons.template.datacenter.protocol.EndGameRoundParam param = 1;</code>
       *
       * <pre>
       *参数
       * </pre>
       */
      public fm.lizhi.ocean.seal.protocol.GameRoundServiceProto.EndGameRoundParam.Builder getParamBuilder() {
        bitField0_ |= 0x00000001;
        onChanged();
        return getParamFieldBuilder().getBuilder();
      }
      /**
       * <code>optional .fm.lizhi.commons.template.datacenter.protocol.EndGameRoundParam param = 1;</code>
       *
       * <pre>
       *参数
       * </pre>
       */
      public fm.lizhi.ocean.seal.protocol.GameRoundServiceProto.EndGameRoundParamOrBuilder getParamOrBuilder() {
        if (paramBuilder_ != null) {
          return paramBuilder_.getMessageOrBuilder();
        } else {
          return param_;
        }
      }
      /**
       * <code>optional .fm.lizhi.commons.template.datacenter.protocol.EndGameRoundParam param = 1;</code>
       *
       * <pre>
       *参数
       * </pre>
       */
      private com.google.protobuf.SingleFieldBuilder<
          fm.lizhi.ocean.seal.protocol.GameRoundServiceProto.EndGameRoundParam, fm.lizhi.ocean.seal.protocol.GameRoundServiceProto.EndGameRoundParam.Builder, fm.lizhi.ocean.seal.protocol.GameRoundServiceProto.EndGameRoundParamOrBuilder> 
          getParamFieldBuilder() {
        if (paramBuilder_ == null) {
          paramBuilder_ = new com.google.protobuf.SingleFieldBuilder<
              fm.lizhi.ocean.seal.protocol.GameRoundServiceProto.EndGameRoundParam, fm.lizhi.ocean.seal.protocol.GameRoundServiceProto.EndGameRoundParam.Builder, fm.lizhi.ocean.seal.protocol.GameRoundServiceProto.EndGameRoundParamOrBuilder>(
                  param_,
                  getParentForChildren(),
                  isClean());
          param_ = null;
        }
        return paramBuilder_;
      }

      // @@protoc_insertion_point(builder_scope:fm.lizhi.commons.template.datacenter.protocol.RequestEndGameRound)
    }

    static {
      defaultInstance = new RequestEndGameRound(true);
      defaultInstance.initFields();
    }

    // @@protoc_insertion_point(class_scope:fm.lizhi.commons.template.datacenter.protocol.RequestEndGameRound)
  }

  public interface ResponseEndGameRoundOrBuilder
      extends com.google.protobuf.MessageOrBuilder {
  }
  /**
   * Protobuf type {@code fm.lizhi.commons.template.datacenter.protocol.ResponseEndGameRound}
   *
   * <pre>
   * rcode == 1 (ILLEGAL_PARAMS) = 参数非法
   * rcode == 2 (NOT_EXISTS) = 游戏ID不存在
   * rcode == 3 (ERROR) = 内部错误
   * </pre>
   */
  public static final class ResponseEndGameRound extends
      com.google.protobuf.GeneratedMessage
      implements ResponseEndGameRoundOrBuilder {
    // Use ResponseEndGameRound.newBuilder() to construct.
    private ResponseEndGameRound(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
      super(builder);
      this.unknownFields = builder.getUnknownFields();
    }
    private ResponseEndGameRound(boolean noInit) { this.unknownFields = com.google.protobuf.UnknownFieldSet.getDefaultInstance(); }

    private static final ResponseEndGameRound defaultInstance;
    public static ResponseEndGameRound getDefaultInstance() {
      return defaultInstance;
    }

    public ResponseEndGameRound getDefaultInstanceForType() {
      return defaultInstance;
    }

    private final com.google.protobuf.UnknownFieldSet unknownFields;
    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
        getUnknownFields() {
      return this.unknownFields;
    }
    private ResponseEndGameRound(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      initFields();
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            default: {
              if (!parseUnknownField(input, unknownFields,
                                     extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e.getMessage()).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return fm.lizhi.ocean.seal.protocol.GameRoundServiceProto.internal_static_fm_lizhi_commons_template_datacenter_protocol_ResponseEndGameRound_descriptor;
    }

    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return fm.lizhi.ocean.seal.protocol.GameRoundServiceProto.internal_static_fm_lizhi_commons_template_datacenter_protocol_ResponseEndGameRound_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              fm.lizhi.ocean.seal.protocol.GameRoundServiceProto.ResponseEndGameRound.class, fm.lizhi.ocean.seal.protocol.GameRoundServiceProto.ResponseEndGameRound.Builder.class);
    }

    public static com.google.protobuf.Parser<ResponseEndGameRound> PARSER =
        new com.google.protobuf.AbstractParser<ResponseEndGameRound>() {
      public ResponseEndGameRound parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new ResponseEndGameRound(input, extensionRegistry);
      }
    };

    @java.lang.Override
    public com.google.protobuf.Parser<ResponseEndGameRound> getParserForType() {
      return PARSER;
    }

    private void initFields() {
    }
    private byte memoizedIsInitialized = -1;
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized != -1) return isInitialized == 1;

      memoizedIsInitialized = 1;
      return true;
    }

    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      getSerializedSize();
      getUnknownFields().writeTo(output);
    }

    private int memoizedSerializedSize = -1;
    public int getSerializedSize() {
      int size = memoizedSerializedSize;
      if (size != -1) return size;

      size = 0;
      size += getUnknownFields().getSerializedSize();
      memoizedSerializedSize = size;
      return size;
    }

    private static final long serialVersionUID = 0L;
    @java.lang.Override
    protected java.lang.Object writeReplace()
        throws java.io.ObjectStreamException {
      return super.writeReplace();
    }

    public static fm.lizhi.ocean.seal.protocol.GameRoundServiceProto.ResponseEndGameRound parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static fm.lizhi.ocean.seal.protocol.GameRoundServiceProto.ResponseEndGameRound parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static fm.lizhi.ocean.seal.protocol.GameRoundServiceProto.ResponseEndGameRound parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static fm.lizhi.ocean.seal.protocol.GameRoundServiceProto.ResponseEndGameRound parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static fm.lizhi.ocean.seal.protocol.GameRoundServiceProto.ResponseEndGameRound parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return PARSER.parseFrom(input);
    }
    public static fm.lizhi.ocean.seal.protocol.GameRoundServiceProto.ResponseEndGameRound parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseFrom(input, extensionRegistry);
    }
    public static fm.lizhi.ocean.seal.protocol.GameRoundServiceProto.ResponseEndGameRound parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return PARSER.parseDelimitedFrom(input);
    }
    public static fm.lizhi.ocean.seal.protocol.GameRoundServiceProto.ResponseEndGameRound parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseDelimitedFrom(input, extensionRegistry);
    }
    public static fm.lizhi.ocean.seal.protocol.GameRoundServiceProto.ResponseEndGameRound parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return PARSER.parseFrom(input);
    }
    public static fm.lizhi.ocean.seal.protocol.GameRoundServiceProto.ResponseEndGameRound parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseFrom(input, extensionRegistry);
    }

    public static Builder newBuilder() { return Builder.create(); }
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder(fm.lizhi.ocean.seal.protocol.GameRoundServiceProto.ResponseEndGameRound prototype) {
      return newBuilder().mergeFrom(prototype);
    }
    public Builder toBuilder() { return newBuilder(this); }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code fm.lizhi.commons.template.datacenter.protocol.ResponseEndGameRound}
     *
     * <pre>
     * rcode == 1 (ILLEGAL_PARAMS) = 参数非法
     * rcode == 2 (NOT_EXISTS) = 游戏ID不存在
     * rcode == 3 (ERROR) = 内部错误
     * </pre>
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessage.Builder<Builder>
       implements fm.lizhi.ocean.seal.protocol.GameRoundServiceProto.ResponseEndGameRoundOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return fm.lizhi.ocean.seal.protocol.GameRoundServiceProto.internal_static_fm_lizhi_commons_template_datacenter_protocol_ResponseEndGameRound_descriptor;
      }

      protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return fm.lizhi.ocean.seal.protocol.GameRoundServiceProto.internal_static_fm_lizhi_commons_template_datacenter_protocol_ResponseEndGameRound_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                fm.lizhi.ocean.seal.protocol.GameRoundServiceProto.ResponseEndGameRound.class, fm.lizhi.ocean.seal.protocol.GameRoundServiceProto.ResponseEndGameRound.Builder.class);
      }

      // Construct using fm.lizhi.ocean.seal.protocol.GameRoundServiceProto.ResponseEndGameRound.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessage.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessage.alwaysUseFieldBuilders) {
        }
      }
      private static Builder create() {
        return new Builder();
      }

      public Builder clear() {
        super.clear();
        return this;
      }

      public Builder clone() {
        return create().mergeFrom(buildPartial());
      }

      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return fm.lizhi.ocean.seal.protocol.GameRoundServiceProto.internal_static_fm_lizhi_commons_template_datacenter_protocol_ResponseEndGameRound_descriptor;
      }

      public fm.lizhi.ocean.seal.protocol.GameRoundServiceProto.ResponseEndGameRound getDefaultInstanceForType() {
        return fm.lizhi.ocean.seal.protocol.GameRoundServiceProto.ResponseEndGameRound.getDefaultInstance();
      }

      public fm.lizhi.ocean.seal.protocol.GameRoundServiceProto.ResponseEndGameRound build() {
        fm.lizhi.ocean.seal.protocol.GameRoundServiceProto.ResponseEndGameRound result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      public fm.lizhi.ocean.seal.protocol.GameRoundServiceProto.ResponseEndGameRound buildPartial() {
        fm.lizhi.ocean.seal.protocol.GameRoundServiceProto.ResponseEndGameRound result = new fm.lizhi.ocean.seal.protocol.GameRoundServiceProto.ResponseEndGameRound(this);
        onBuilt();
        return result;
      }

      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof fm.lizhi.ocean.seal.protocol.GameRoundServiceProto.ResponseEndGameRound) {
          return mergeFrom((fm.lizhi.ocean.seal.protocol.GameRoundServiceProto.ResponseEndGameRound)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(fm.lizhi.ocean.seal.protocol.GameRoundServiceProto.ResponseEndGameRound other) {
        if (other == fm.lizhi.ocean.seal.protocol.GameRoundServiceProto.ResponseEndGameRound.getDefaultInstance()) return this;
        this.mergeUnknownFields(other.getUnknownFields());
        return this;
      }

      public final boolean isInitialized() {
        return true;
      }

      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        fm.lizhi.ocean.seal.protocol.GameRoundServiceProto.ResponseEndGameRound parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (fm.lizhi.ocean.seal.protocol.GameRoundServiceProto.ResponseEndGameRound) e.getUnfinishedMessage();
          throw e;
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }

      // @@protoc_insertion_point(builder_scope:fm.lizhi.commons.template.datacenter.protocol.ResponseEndGameRound)
    }

    static {
      defaultInstance = new ResponseEndGameRound(true);
      defaultInstance.initFields();
    }

    // @@protoc_insertion_point(class_scope:fm.lizhi.commons.template.datacenter.protocol.ResponseEndGameRound)
  }

  private static com.google.protobuf.Descriptors.Descriptor
    internal_static_fm_lizhi_commons_template_datacenter_protocol_RelationGameRoundParam_descriptor;
  private static
    com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internal_static_fm_lizhi_commons_template_datacenter_protocol_RelationGameRoundParam_fieldAccessorTable;
  private static com.google.protobuf.Descriptors.Descriptor
    internal_static_fm_lizhi_commons_template_datacenter_protocol_RequestRelationGameRound_descriptor;
  private static
    com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internal_static_fm_lizhi_commons_template_datacenter_protocol_RequestRelationGameRound_fieldAccessorTable;
  private static com.google.protobuf.Descriptors.Descriptor
    internal_static_fm_lizhi_commons_template_datacenter_protocol_ResponseRelationGameRound_descriptor;
  private static
    com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internal_static_fm_lizhi_commons_template_datacenter_protocol_ResponseRelationGameRound_fieldAccessorTable;
  private static com.google.protobuf.Descriptors.Descriptor
    internal_static_fm_lizhi_commons_template_datacenter_protocol_StartGameRoundParam_descriptor;
  private static
    com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internal_static_fm_lizhi_commons_template_datacenter_protocol_StartGameRoundParam_fieldAccessorTable;
  private static com.google.protobuf.Descriptors.Descriptor
    internal_static_fm_lizhi_commons_template_datacenter_protocol_GameRound_descriptor;
  private static
    com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internal_static_fm_lizhi_commons_template_datacenter_protocol_GameRound_fieldAccessorTable;
  private static com.google.protobuf.Descriptors.Descriptor
    internal_static_fm_lizhi_commons_template_datacenter_protocol_RequestStartGameRound_descriptor;
  private static
    com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internal_static_fm_lizhi_commons_template_datacenter_protocol_RequestStartGameRound_fieldAccessorTable;
  private static com.google.protobuf.Descriptors.Descriptor
    internal_static_fm_lizhi_commons_template_datacenter_protocol_ResponseStartGameRound_descriptor;
  private static
    com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internal_static_fm_lizhi_commons_template_datacenter_protocol_ResponseStartGameRound_fieldAccessorTable;
  private static com.google.protobuf.Descriptors.Descriptor
    internal_static_fm_lizhi_commons_template_datacenter_protocol_EndGameRoundParam_descriptor;
  private static
    com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internal_static_fm_lizhi_commons_template_datacenter_protocol_EndGameRoundParam_fieldAccessorTable;
  private static com.google.protobuf.Descriptors.Descriptor
    internal_static_fm_lizhi_commons_template_datacenter_protocol_RequestEndGameRound_descriptor;
  private static
    com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internal_static_fm_lizhi_commons_template_datacenter_protocol_RequestEndGameRound_fieldAccessorTable;
  private static com.google.protobuf.Descriptors.Descriptor
    internal_static_fm_lizhi_commons_template_datacenter_protocol_ResponseEndGameRound_descriptor;
  private static
    com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internal_static_fm_lizhi_commons_template_datacenter_protocol_ResponseEndGameRound_fieldAccessorTable;

  public static com.google.protobuf.Descriptors.FileDescriptor
      getDescriptor() {
    return descriptor;
  }
  private static com.google.protobuf.Descriptors.FileDescriptor
      descriptor;
  static {
    java.lang.String[] descriptorData = {
      "\n\031protocol_game_round.proto\022-fm.lizhi.co" +
      "mmons.template.datacenter.protocol\"?\n\026Re" +
      "lationGameRoundParam\022\026\n\016channelRoundId\030\001" +
      " \002(\t\022\r\n\005extra\030\002 \002(\t\"p\n\030RequestRelationGa" +
      "meRound\022T\n\005param\030\001 \001(\0132E.fm.lizhi.common" +
      "s.template.datacenter.protocol.RelationG" +
      "ameRoundParam\"\033\n\031ResponseRelationGameRou" +
      "nd\"T\n\023StartGameRoundParam\022\r\n\005appId\030\001 \002(\t" +
      "\022\016\n\006gameId\030\002 \002(\t\022\017\n\007groupId\030\003 \002(\t\022\r\n\005ext" +
      "ra\030\004 \001(\t\"\034\n\tGameRound\022\017\n\007roundId\030\002 \002(\003\"j",
      "\n\025RequestStartGameRound\022Q\n\005param\030\001 \001(\0132B" +
      ".fm.lizhi.commons.template.datacenter.pr" +
      "otocol.StartGameRoundParam\"e\n\026ResponseSt" +
      "artGameRound\022K\n\tgameRound\030\001 \001(\01328.fm.liz" +
      "hi.commons.template.datacenter.protocol." +
      "GameRound\"[\n\021EndGameRoundParam\022\r\n\005appId\030" +
      "\001 \002(\t\022\016\n\006gameId\030\002 \002(\t\022\017\n\007groupId\030\003 \002(\t\022\026" +
      "\n\016channelRoundId\030\004 \002(\t\"f\n\023RequestEndGame" +
      "Round\022O\n\005param\030\001 \001(\<EMAIL>." +
      "template.datacenter.protocol.EndGameRoun",
      "dParam\"\026\n\024ResponseEndGameRoundB7\n\034fm.liz" +
      "hi.ocean.seal.protocolB\025GameRoundService" +
      "ProtoH\001"
    };
    com.google.protobuf.Descriptors.FileDescriptor.InternalDescriptorAssigner assigner =
      new com.google.protobuf.Descriptors.FileDescriptor.InternalDescriptorAssigner() {
        public com.google.protobuf.ExtensionRegistry assignDescriptors(
            com.google.protobuf.Descriptors.FileDescriptor root) {
          descriptor = root;
          internal_static_fm_lizhi_commons_template_datacenter_protocol_RelationGameRoundParam_descriptor =
            getDescriptor().getMessageTypes().get(0);
          internal_static_fm_lizhi_commons_template_datacenter_protocol_RelationGameRoundParam_fieldAccessorTable = new
            com.google.protobuf.GeneratedMessage.FieldAccessorTable(
              internal_static_fm_lizhi_commons_template_datacenter_protocol_RelationGameRoundParam_descriptor,
              new java.lang.String[] { "ChannelRoundId", "Extra", });
          internal_static_fm_lizhi_commons_template_datacenter_protocol_RequestRelationGameRound_descriptor =
            getDescriptor().getMessageTypes().get(1);
          internal_static_fm_lizhi_commons_template_datacenter_protocol_RequestRelationGameRound_fieldAccessorTable = new
            com.google.protobuf.GeneratedMessage.FieldAccessorTable(
              internal_static_fm_lizhi_commons_template_datacenter_protocol_RequestRelationGameRound_descriptor,
              new java.lang.String[] { "Param", });
          internal_static_fm_lizhi_commons_template_datacenter_protocol_ResponseRelationGameRound_descriptor =
            getDescriptor().getMessageTypes().get(2);
          internal_static_fm_lizhi_commons_template_datacenter_protocol_ResponseRelationGameRound_fieldAccessorTable = new
            com.google.protobuf.GeneratedMessage.FieldAccessorTable(
              internal_static_fm_lizhi_commons_template_datacenter_protocol_ResponseRelationGameRound_descriptor,
              new java.lang.String[] { });
          internal_static_fm_lizhi_commons_template_datacenter_protocol_StartGameRoundParam_descriptor =
            getDescriptor().getMessageTypes().get(3);
          internal_static_fm_lizhi_commons_template_datacenter_protocol_StartGameRoundParam_fieldAccessorTable = new
            com.google.protobuf.GeneratedMessage.FieldAccessorTable(
              internal_static_fm_lizhi_commons_template_datacenter_protocol_StartGameRoundParam_descriptor,
              new java.lang.String[] { "AppId", "GameId", "GroupId", "Extra", });
          internal_static_fm_lizhi_commons_template_datacenter_protocol_GameRound_descriptor =
            getDescriptor().getMessageTypes().get(4);
          internal_static_fm_lizhi_commons_template_datacenter_protocol_GameRound_fieldAccessorTable = new
            com.google.protobuf.GeneratedMessage.FieldAccessorTable(
              internal_static_fm_lizhi_commons_template_datacenter_protocol_GameRound_descriptor,
              new java.lang.String[] { "RoundId", });
          internal_static_fm_lizhi_commons_template_datacenter_protocol_RequestStartGameRound_descriptor =
            getDescriptor().getMessageTypes().get(5);
          internal_static_fm_lizhi_commons_template_datacenter_protocol_RequestStartGameRound_fieldAccessorTable = new
            com.google.protobuf.GeneratedMessage.FieldAccessorTable(
              internal_static_fm_lizhi_commons_template_datacenter_protocol_RequestStartGameRound_descriptor,
              new java.lang.String[] { "Param", });
          internal_static_fm_lizhi_commons_template_datacenter_protocol_ResponseStartGameRound_descriptor =
            getDescriptor().getMessageTypes().get(6);
          internal_static_fm_lizhi_commons_template_datacenter_protocol_ResponseStartGameRound_fieldAccessorTable = new
            com.google.protobuf.GeneratedMessage.FieldAccessorTable(
              internal_static_fm_lizhi_commons_template_datacenter_protocol_ResponseStartGameRound_descriptor,
              new java.lang.String[] { "GameRound", });
          internal_static_fm_lizhi_commons_template_datacenter_protocol_EndGameRoundParam_descriptor =
            getDescriptor().getMessageTypes().get(7);
          internal_static_fm_lizhi_commons_template_datacenter_protocol_EndGameRoundParam_fieldAccessorTable = new
            com.google.protobuf.GeneratedMessage.FieldAccessorTable(
              internal_static_fm_lizhi_commons_template_datacenter_protocol_EndGameRoundParam_descriptor,
              new java.lang.String[] { "AppId", "GameId", "GroupId", "ChannelRoundId", });
          internal_static_fm_lizhi_commons_template_datacenter_protocol_RequestEndGameRound_descriptor =
            getDescriptor().getMessageTypes().get(8);
          internal_static_fm_lizhi_commons_template_datacenter_protocol_RequestEndGameRound_fieldAccessorTable = new
            com.google.protobuf.GeneratedMessage.FieldAccessorTable(
              internal_static_fm_lizhi_commons_template_datacenter_protocol_RequestEndGameRound_descriptor,
              new java.lang.String[] { "Param", });
          internal_static_fm_lizhi_commons_template_datacenter_protocol_ResponseEndGameRound_descriptor =
            getDescriptor().getMessageTypes().get(9);
          internal_static_fm_lizhi_commons_template_datacenter_protocol_ResponseEndGameRound_fieldAccessorTable = new
            com.google.protobuf.GeneratedMessage.FieldAccessorTable(
              internal_static_fm_lizhi_commons_template_datacenter_protocol_ResponseEndGameRound_descriptor,
              new java.lang.String[] { });
          return null;
        }
      };
    com.google.protobuf.Descriptors.FileDescriptor
      .internalBuildGeneratedFileFrom(descriptorData,
        new com.google.protobuf.Descriptors.FileDescriptor[] {
        }, assigner);
  }

  // @@protoc_insertion_point(outer_class_scope)
}
