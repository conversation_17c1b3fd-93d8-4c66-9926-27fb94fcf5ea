package fm.lizhi.ocean.seal.api;

import java.util.List;
import fm.lizhi.commons.service.client.annotation.Attribute;
import fm.lizhi.commons.service.client.annotation.Return;
import fm.lizhi.commons.service.client.annotation.Service;
import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.ocean.seal.protocol.GameAppServiceProto.RequestGetAppCallback;
import fm.lizhi.ocean.seal.protocol.GameAppServiceProto.ResponseGetAppCallback;
import fm.lizhi.ocean.seal.protocol.GameAppServiceProto.RequestGetAppInfoByAppId;
import fm.lizhi.ocean.seal.protocol.GameAppServiceProto.ResponseGetAppInfoByAppId;
import fm.lizhi.ocean.seal.protocol.GameAppServiceProto.GetAppCallbackParam;
import fm.lizhi.ocean.seal.protocol.GameAppServiceProto.AppCallback;
import fm.lizhi.ocean.seal.protocol.GameAppServiceProto.ChannelInfo;
import fm.lizhi.ocean.seal.protocol.GameAppServiceProto.GameAppInfo;

/**
 * this file generated by autoapi-maven-plugin
 * 
 * do not edit this file manually, because this file is covered when you run autoapi-maven-plugin
 * 
 */

public interface GameAppService {
	
	
	/**
	 *  获取App回调配置信息
	 *
	 * @param param
	 *            参数
	 * @return 
	 *     //if rcode == 1 参数非法<br>
	 *     //if rcode == 2 app不存在<br>
	 *     //if rcode == 3 内部错误<br>
	 */
	@Service(domain = 4302, op = 30, request = RequestGetAppCallback.class, response = ResponseGetAppCallback.class)
	@Return(resultType = ResponseGetAppCallback.class)
	Result<ResponseGetAppCallback> getAppCallback(@Attribute(name = "param") GetAppCallbackParam param);
	
	
	/**
	 *  根据AppId获取游戏信息
	 *
	 * @param appId
	 *            
	 * @return 
	 *     //if rcode == 1 参数非法<br>
	 *     //if rcode == 2 app不存在<br>
	 *     //if rcode == 3 内部错误<br>
	 */
	@Service(domain = 4302, op = 31, request = RequestGetAppInfoByAppId.class, response = ResponseGetAppInfoByAppId.class)
	@Return(resultType = ResponseGetAppInfoByAppId.class)
	Result<ResponseGetAppInfoByAppId> getAppInfoByAppId(@Attribute(name = "appId") String appId);
	
	
	public static final int GET_APP_CALLBACK_ILLEGAL_PARAMS = 1; // 参数非法
	public static final int GET_APP_CALLBACK_NOT_EXISTS = 2; // app不存在
	public static final int GET_APP_CALLBACK_ERROR = 3; // 内部错误

	public static final int GET_APP_INFO_BY_APP_ID_ILLEGAL_PARAMS = 1; // 参数非法
	public static final int GET_APP_INFO_BY_APP_ID_NOT_EXISTS = 2; // app不存在
	public static final int GET_APP_INFO_BY_APP_ID_ERROR = 3; // 内部错误


}