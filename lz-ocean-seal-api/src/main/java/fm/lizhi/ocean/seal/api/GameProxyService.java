package fm.lizhi.ocean.seal.api;

import java.util.List;
import fm.lizhi.commons.service.client.annotation.Attribute;
import fm.lizhi.commons.service.client.annotation.Return;
import fm.lizhi.commons.service.client.annotation.Service;
import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.ocean.seal.protocol.GameProxyServiceProto.RequestInvokeTarget;
import fm.lizhi.ocean.seal.protocol.GameProxyServiceProto.ResponseInvokeTarget;
import fm.lizhi.ocean.seal.protocol.GameProxyServiceProto.RequestGetAppIdAppSecretByParam;
import fm.lizhi.ocean.seal.protocol.GameProxyServiceProto.ResponseGetAppIdAppSecretByParam;
import fm.lizhi.ocean.seal.protocol.GameProxyServiceProto.InvokeTargetParams;
import fm.lizhi.ocean.seal.protocol.GameProxyServiceProto.GetAppIdAppSecretParam;

/**
 * this file generated by autoapi-maven-plugin
 * 
 * do not edit this file manually, because this file is covered when you run autoapi-maven-plugin
 * 
 */

public interface GameProxyService {
	
	
	/**
	 *  获取App回调配置信息
	 *
	 * @param param
	 *            参数
	 * @return 
	 *     //if rcode == 1 参数非法<br>
	 *     //if rcode == 2 映射不存在<br>
	 *     //if rcode == 3 目标服务错误<br>
	 *     //if rcode == 4 内部错误<br>
	 */
	@Service(domain = 4302, op = 240, request = RequestInvokeTarget.class, response = ResponseInvokeTarget.class)
	@Return(resultType = ResponseInvokeTarget.class)
	Result<ResponseInvokeTarget> invokeTarget(@Attribute(name = "param") InvokeTargetParams param);
	
	
	/**
	 *  根据传入的appId、gameId、type 获取对应的appId、appSecret
	 *
	 * @param param
	 *            参数
	 * @return 
	 *     //if rcode == 1 参数非法<br>
	 *     //if rcode == 2 映射不存在<br>
	 *     //if rcode == 3 内部错误<br>
	 */
	@Service(domain = 4302, op = 241, request = RequestGetAppIdAppSecretByParam.class, response = ResponseGetAppIdAppSecretByParam.class)
	@Return(resultType = ResponseGetAppIdAppSecretByParam.class)
	Result<ResponseGetAppIdAppSecretByParam> getAppIdAppSecretByParam(@Attribute(name = "param") GetAppIdAppSecretParam param);
	
	
	public static final int INVOKE_TARGET_ILLEGAL_PARAMS = 1; // 参数非法
	public static final int INVOKE_TARGET_NOT_EXISTS = 2; // 映射不存在
	public static final int INVOKE_TARGET_ERROR_TARGET = 3; // 目标服务错误
	public static final int INVOKE_TARGET_ERROR = 4; // 内部错误

	public static final int GET_APP_ID_APP_SECRET_BY_PARAM_ILLEGAL_PARAMS = 1; // 参数非法
	public static final int GET_APP_ID_APP_SECRET_BY_PARAM_NOT_EXISTS = 2; // 映射不存在
	public static final int GET_APP_ID_APP_SECRET_BY_PARAM_ERROR = 3; // 内部错误


}