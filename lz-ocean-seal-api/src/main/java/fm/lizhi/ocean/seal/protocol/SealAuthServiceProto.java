// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: protocol_seal_auth.proto

package fm.lizhi.ocean.seal.protocol;

public final class SealAuthServiceProto {
  private SealAuthServiceProto() {}
  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistry registry) {
  }
  public interface SealTokenOrBuilder
      extends com.google.protobuf.MessageOrBuilder {

    // optional string token = 1;
    /**
     * <code>optional string token = 1;</code>
     *
     * <pre>
     *长期令牌
     * </pre>
     */
    boolean hasToken();
    /**
     * <code>optional string token = 1;</code>
     *
     * <pre>
     *长期令牌
     * </pre>
     */
    java.lang.String getToken();
    /**
     * <code>optional string token = 1;</code>
     *
     * <pre>
     *长期令牌
     * </pre>
     */
    com.google.protobuf.ByteString
        getTokenBytes();

    // optional int64 expireDate = 2;
    /**
     * <code>optional int64 expireDate = 2;</code>
     *
     * <pre>
     *长期令牌过期时间戳（毫秒）
     * </pre>
     */
    boolean hasExpireDate();
    /**
     * <code>optional int64 expireDate = 2;</code>
     *
     * <pre>
     *长期令牌过期时间戳（毫秒）
     * </pre>
     */
    long getExpireDate();
  }
  /**
   * Protobuf type {@code fm.lizhi.commons.template.datacenter.protocol.SealToken}
   */
  public static final class SealToken extends
      com.google.protobuf.GeneratedMessage
      implements SealTokenOrBuilder {
    // Use SealToken.newBuilder() to construct.
    private SealToken(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
      super(builder);
      this.unknownFields = builder.getUnknownFields();
    }
    private SealToken(boolean noInit) { this.unknownFields = com.google.protobuf.UnknownFieldSet.getDefaultInstance(); }

    private static final SealToken defaultInstance;
    public static SealToken getDefaultInstance() {
      return defaultInstance;
    }

    public SealToken getDefaultInstanceForType() {
      return defaultInstance;
    }

    private final com.google.protobuf.UnknownFieldSet unknownFields;
    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
        getUnknownFields() {
      return this.unknownFields;
    }
    private SealToken(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      initFields();
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            default: {
              if (!parseUnknownField(input, unknownFields,
                                     extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
            case 10: {
              bitField0_ |= 0x00000001;
              token_ = input.readBytes();
              break;
            }
            case 16: {
              bitField0_ |= 0x00000002;
              expireDate_ = input.readInt64();
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e.getMessage()).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return fm.lizhi.ocean.seal.protocol.SealAuthServiceProto.internal_static_fm_lizhi_commons_template_datacenter_protocol_SealToken_descriptor;
    }

    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return fm.lizhi.ocean.seal.protocol.SealAuthServiceProto.internal_static_fm_lizhi_commons_template_datacenter_protocol_SealToken_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              fm.lizhi.ocean.seal.protocol.SealAuthServiceProto.SealToken.class, fm.lizhi.ocean.seal.protocol.SealAuthServiceProto.SealToken.Builder.class);
    }

    public static com.google.protobuf.Parser<SealToken> PARSER =
        new com.google.protobuf.AbstractParser<SealToken>() {
      public SealToken parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new SealToken(input, extensionRegistry);
      }
    };

    @java.lang.Override
    public com.google.protobuf.Parser<SealToken> getParserForType() {
      return PARSER;
    }

    private int bitField0_;
    // optional string token = 1;
    public static final int TOKEN_FIELD_NUMBER = 1;
    private java.lang.Object token_;
    /**
     * <code>optional string token = 1;</code>
     *
     * <pre>
     *长期令牌
     * </pre>
     */
    public boolean hasToken() {
      return ((bitField0_ & 0x00000001) == 0x00000001);
    }
    /**
     * <code>optional string token = 1;</code>
     *
     * <pre>
     *长期令牌
     * </pre>
     */
    public java.lang.String getToken() {
      java.lang.Object ref = token_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          token_ = s;
        }
        return s;
      }
    }
    /**
     * <code>optional string token = 1;</code>
     *
     * <pre>
     *长期令牌
     * </pre>
     */
    public com.google.protobuf.ByteString
        getTokenBytes() {
      java.lang.Object ref = token_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        token_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    // optional int64 expireDate = 2;
    public static final int EXPIREDATE_FIELD_NUMBER = 2;
    private long expireDate_;
    /**
     * <code>optional int64 expireDate = 2;</code>
     *
     * <pre>
     *长期令牌过期时间戳（毫秒）
     * </pre>
     */
    public boolean hasExpireDate() {
      return ((bitField0_ & 0x00000002) == 0x00000002);
    }
    /**
     * <code>optional int64 expireDate = 2;</code>
     *
     * <pre>
     *长期令牌过期时间戳（毫秒）
     * </pre>
     */
    public long getExpireDate() {
      return expireDate_;
    }

    private void initFields() {
      token_ = "";
      expireDate_ = 0L;
    }
    private byte memoizedIsInitialized = -1;
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized != -1) return isInitialized == 1;

      memoizedIsInitialized = 1;
      return true;
    }

    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      getSerializedSize();
      if (((bitField0_ & 0x00000001) == 0x00000001)) {
        output.writeBytes(1, getTokenBytes());
      }
      if (((bitField0_ & 0x00000002) == 0x00000002)) {
        output.writeInt64(2, expireDate_);
      }
      getUnknownFields().writeTo(output);
    }

    private int memoizedSerializedSize = -1;
    public int getSerializedSize() {
      int size = memoizedSerializedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) == 0x00000001)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(1, getTokenBytes());
      }
      if (((bitField0_ & 0x00000002) == 0x00000002)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt64Size(2, expireDate_);
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSerializedSize = size;
      return size;
    }

    private static final long serialVersionUID = 0L;
    @java.lang.Override
    protected java.lang.Object writeReplace()
        throws java.io.ObjectStreamException {
      return super.writeReplace();
    }

    public static fm.lizhi.ocean.seal.protocol.SealAuthServiceProto.SealToken parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static fm.lizhi.ocean.seal.protocol.SealAuthServiceProto.SealToken parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static fm.lizhi.ocean.seal.protocol.SealAuthServiceProto.SealToken parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static fm.lizhi.ocean.seal.protocol.SealAuthServiceProto.SealToken parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static fm.lizhi.ocean.seal.protocol.SealAuthServiceProto.SealToken parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return PARSER.parseFrom(input);
    }
    public static fm.lizhi.ocean.seal.protocol.SealAuthServiceProto.SealToken parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseFrom(input, extensionRegistry);
    }
    public static fm.lizhi.ocean.seal.protocol.SealAuthServiceProto.SealToken parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return PARSER.parseDelimitedFrom(input);
    }
    public static fm.lizhi.ocean.seal.protocol.SealAuthServiceProto.SealToken parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseDelimitedFrom(input, extensionRegistry);
    }
    public static fm.lizhi.ocean.seal.protocol.SealAuthServiceProto.SealToken parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return PARSER.parseFrom(input);
    }
    public static fm.lizhi.ocean.seal.protocol.SealAuthServiceProto.SealToken parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseFrom(input, extensionRegistry);
    }

    public static Builder newBuilder() { return Builder.create(); }
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder(fm.lizhi.ocean.seal.protocol.SealAuthServiceProto.SealToken prototype) {
      return newBuilder().mergeFrom(prototype);
    }
    public Builder toBuilder() { return newBuilder(this); }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code fm.lizhi.commons.template.datacenter.protocol.SealToken}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessage.Builder<Builder>
       implements fm.lizhi.ocean.seal.protocol.SealAuthServiceProto.SealTokenOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return fm.lizhi.ocean.seal.protocol.SealAuthServiceProto.internal_static_fm_lizhi_commons_template_datacenter_protocol_SealToken_descriptor;
      }

      protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return fm.lizhi.ocean.seal.protocol.SealAuthServiceProto.internal_static_fm_lizhi_commons_template_datacenter_protocol_SealToken_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                fm.lizhi.ocean.seal.protocol.SealAuthServiceProto.SealToken.class, fm.lizhi.ocean.seal.protocol.SealAuthServiceProto.SealToken.Builder.class);
      }

      // Construct using fm.lizhi.ocean.seal.protocol.SealAuthServiceProto.SealToken.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessage.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessage.alwaysUseFieldBuilders) {
        }
      }
      private static Builder create() {
        return new Builder();
      }

      public Builder clear() {
        super.clear();
        token_ = "";
        bitField0_ = (bitField0_ & ~0x00000001);
        expireDate_ = 0L;
        bitField0_ = (bitField0_ & ~0x00000002);
        return this;
      }

      public Builder clone() {
        return create().mergeFrom(buildPartial());
      }

      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return fm.lizhi.ocean.seal.protocol.SealAuthServiceProto.internal_static_fm_lizhi_commons_template_datacenter_protocol_SealToken_descriptor;
      }

      public fm.lizhi.ocean.seal.protocol.SealAuthServiceProto.SealToken getDefaultInstanceForType() {
        return fm.lizhi.ocean.seal.protocol.SealAuthServiceProto.SealToken.getDefaultInstance();
      }

      public fm.lizhi.ocean.seal.protocol.SealAuthServiceProto.SealToken build() {
        fm.lizhi.ocean.seal.protocol.SealAuthServiceProto.SealToken result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      public fm.lizhi.ocean.seal.protocol.SealAuthServiceProto.SealToken buildPartial() {
        fm.lizhi.ocean.seal.protocol.SealAuthServiceProto.SealToken result = new fm.lizhi.ocean.seal.protocol.SealAuthServiceProto.SealToken(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) == 0x00000001)) {
          to_bitField0_ |= 0x00000001;
        }
        result.token_ = token_;
        if (((from_bitField0_ & 0x00000002) == 0x00000002)) {
          to_bitField0_ |= 0x00000002;
        }
        result.expireDate_ = expireDate_;
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof fm.lizhi.ocean.seal.protocol.SealAuthServiceProto.SealToken) {
          return mergeFrom((fm.lizhi.ocean.seal.protocol.SealAuthServiceProto.SealToken)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(fm.lizhi.ocean.seal.protocol.SealAuthServiceProto.SealToken other) {
        if (other == fm.lizhi.ocean.seal.protocol.SealAuthServiceProto.SealToken.getDefaultInstance()) return this;
        if (other.hasToken()) {
          bitField0_ |= 0x00000001;
          token_ = other.token_;
          onChanged();
        }
        if (other.hasExpireDate()) {
          setExpireDate(other.getExpireDate());
        }
        this.mergeUnknownFields(other.getUnknownFields());
        return this;
      }

      public final boolean isInitialized() {
        return true;
      }

      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        fm.lizhi.ocean.seal.protocol.SealAuthServiceProto.SealToken parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (fm.lizhi.ocean.seal.protocol.SealAuthServiceProto.SealToken) e.getUnfinishedMessage();
          throw e;
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      // optional string token = 1;
      private java.lang.Object token_ = "";
      /**
       * <code>optional string token = 1;</code>
       *
       * <pre>
       *长期令牌
       * </pre>
       */
      public boolean hasToken() {
        return ((bitField0_ & 0x00000001) == 0x00000001);
      }
      /**
       * <code>optional string token = 1;</code>
       *
       * <pre>
       *长期令牌
       * </pre>
       */
      public java.lang.String getToken() {
        java.lang.Object ref = token_;
        if (!(ref instanceof java.lang.String)) {
          java.lang.String s = ((com.google.protobuf.ByteString) ref)
              .toStringUtf8();
          token_ = s;
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <code>optional string token = 1;</code>
       *
       * <pre>
       *长期令牌
       * </pre>
       */
      public com.google.protobuf.ByteString
          getTokenBytes() {
        java.lang.Object ref = token_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          token_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <code>optional string token = 1;</code>
       *
       * <pre>
       *长期令牌
       * </pre>
       */
      public Builder setToken(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000001;
        token_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional string token = 1;</code>
       *
       * <pre>
       *长期令牌
       * </pre>
       */
      public Builder clearToken() {
        bitField0_ = (bitField0_ & ~0x00000001);
        token_ = getDefaultInstance().getToken();
        onChanged();
        return this;
      }
      /**
       * <code>optional string token = 1;</code>
       *
       * <pre>
       *长期令牌
       * </pre>
       */
      public Builder setTokenBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000001;
        token_ = value;
        onChanged();
        return this;
      }

      // optional int64 expireDate = 2;
      private long expireDate_ ;
      /**
       * <code>optional int64 expireDate = 2;</code>
       *
       * <pre>
       *长期令牌过期时间戳（毫秒）
       * </pre>
       */
      public boolean hasExpireDate() {
        return ((bitField0_ & 0x00000002) == 0x00000002);
      }
      /**
       * <code>optional int64 expireDate = 2;</code>
       *
       * <pre>
       *长期令牌过期时间戳（毫秒）
       * </pre>
       */
      public long getExpireDate() {
        return expireDate_;
      }
      /**
       * <code>optional int64 expireDate = 2;</code>
       *
       * <pre>
       *长期令牌过期时间戳（毫秒）
       * </pre>
       */
      public Builder setExpireDate(long value) {
        bitField0_ |= 0x00000002;
        expireDate_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional int64 expireDate = 2;</code>
       *
       * <pre>
       *长期令牌过期时间戳（毫秒）
       * </pre>
       */
      public Builder clearExpireDate() {
        bitField0_ = (bitField0_ & ~0x00000002);
        expireDate_ = 0L;
        onChanged();
        return this;
      }

      // @@protoc_insertion_point(builder_scope:fm.lizhi.commons.template.datacenter.protocol.SealToken)
    }

    static {
      defaultInstance = new SealToken(true);
      defaultInstance.initFields();
    }

    // @@protoc_insertion_point(class_scope:fm.lizhi.commons.template.datacenter.protocol.SealToken)
  }

  public interface JwtUserInfoOrBuilder
      extends com.google.protobuf.MessageOrBuilder {

    // optional int64 userId = 1;
    /**
     * <code>optional int64 userId = 1;</code>
     *
     * <pre>
     *用户id
     * </pre>
     */
    boolean hasUserId();
    /**
     * <code>optional int64 userId = 1;</code>
     *
     * <pre>
     *用户id
     * </pre>
     */
    long getUserId();

    // optional string userName = 2;
    /**
     * <code>optional string userName = 2;</code>
     *
     * <pre>
     *用户名称
     * </pre>
     */
    boolean hasUserName();
    /**
     * <code>optional string userName = 2;</code>
     *
     * <pre>
     *用户名称
     * </pre>
     */
    java.lang.String getUserName();
    /**
     * <code>optional string userName = 2;</code>
     *
     * <pre>
     *用户名称
     * </pre>
     */
    com.google.protobuf.ByteString
        getUserNameBytes();

    // optional string role = 3;
    /**
     * <code>optional string role = 3;</code>
     *
     * <pre>
     *用户角色
     * </pre>
     */
    boolean hasRole();
    /**
     * <code>optional string role = 3;</code>
     *
     * <pre>
     *用户角色
     * </pre>
     */
    java.lang.String getRole();
    /**
     * <code>optional string role = 3;</code>
     *
     * <pre>
     *用户角色
     * </pre>
     */
    com.google.protobuf.ByteString
        getRoleBytes();
  }
  /**
   * Protobuf type {@code fm.lizhi.commons.template.datacenter.protocol.JwtUserInfo}
   */
  public static final class JwtUserInfo extends
      com.google.protobuf.GeneratedMessage
      implements JwtUserInfoOrBuilder {
    // Use JwtUserInfo.newBuilder() to construct.
    private JwtUserInfo(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
      super(builder);
      this.unknownFields = builder.getUnknownFields();
    }
    private JwtUserInfo(boolean noInit) { this.unknownFields = com.google.protobuf.UnknownFieldSet.getDefaultInstance(); }

    private static final JwtUserInfo defaultInstance;
    public static JwtUserInfo getDefaultInstance() {
      return defaultInstance;
    }

    public JwtUserInfo getDefaultInstanceForType() {
      return defaultInstance;
    }

    private final com.google.protobuf.UnknownFieldSet unknownFields;
    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
        getUnknownFields() {
      return this.unknownFields;
    }
    private JwtUserInfo(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      initFields();
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            default: {
              if (!parseUnknownField(input, unknownFields,
                                     extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
            case 8: {
              bitField0_ |= 0x00000001;
              userId_ = input.readInt64();
              break;
            }
            case 18: {
              bitField0_ |= 0x00000002;
              userName_ = input.readBytes();
              break;
            }
            case 26: {
              bitField0_ |= 0x00000004;
              role_ = input.readBytes();
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e.getMessage()).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return fm.lizhi.ocean.seal.protocol.SealAuthServiceProto.internal_static_fm_lizhi_commons_template_datacenter_protocol_JwtUserInfo_descriptor;
    }

    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return fm.lizhi.ocean.seal.protocol.SealAuthServiceProto.internal_static_fm_lizhi_commons_template_datacenter_protocol_JwtUserInfo_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              fm.lizhi.ocean.seal.protocol.SealAuthServiceProto.JwtUserInfo.class, fm.lizhi.ocean.seal.protocol.SealAuthServiceProto.JwtUserInfo.Builder.class);
    }

    public static com.google.protobuf.Parser<JwtUserInfo> PARSER =
        new com.google.protobuf.AbstractParser<JwtUserInfo>() {
      public JwtUserInfo parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new JwtUserInfo(input, extensionRegistry);
      }
    };

    @java.lang.Override
    public com.google.protobuf.Parser<JwtUserInfo> getParserForType() {
      return PARSER;
    }

    private int bitField0_;
    // optional int64 userId = 1;
    public static final int USERID_FIELD_NUMBER = 1;
    private long userId_;
    /**
     * <code>optional int64 userId = 1;</code>
     *
     * <pre>
     *用户id
     * </pre>
     */
    public boolean hasUserId() {
      return ((bitField0_ & 0x00000001) == 0x00000001);
    }
    /**
     * <code>optional int64 userId = 1;</code>
     *
     * <pre>
     *用户id
     * </pre>
     */
    public long getUserId() {
      return userId_;
    }

    // optional string userName = 2;
    public static final int USERNAME_FIELD_NUMBER = 2;
    private java.lang.Object userName_;
    /**
     * <code>optional string userName = 2;</code>
     *
     * <pre>
     *用户名称
     * </pre>
     */
    public boolean hasUserName() {
      return ((bitField0_ & 0x00000002) == 0x00000002);
    }
    /**
     * <code>optional string userName = 2;</code>
     *
     * <pre>
     *用户名称
     * </pre>
     */
    public java.lang.String getUserName() {
      java.lang.Object ref = userName_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          userName_ = s;
        }
        return s;
      }
    }
    /**
     * <code>optional string userName = 2;</code>
     *
     * <pre>
     *用户名称
     * </pre>
     */
    public com.google.protobuf.ByteString
        getUserNameBytes() {
      java.lang.Object ref = userName_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        userName_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    // optional string role = 3;
    public static final int ROLE_FIELD_NUMBER = 3;
    private java.lang.Object role_;
    /**
     * <code>optional string role = 3;</code>
     *
     * <pre>
     *用户角色
     * </pre>
     */
    public boolean hasRole() {
      return ((bitField0_ & 0x00000004) == 0x00000004);
    }
    /**
     * <code>optional string role = 3;</code>
     *
     * <pre>
     *用户角色
     * </pre>
     */
    public java.lang.String getRole() {
      java.lang.Object ref = role_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          role_ = s;
        }
        return s;
      }
    }
    /**
     * <code>optional string role = 3;</code>
     *
     * <pre>
     *用户角色
     * </pre>
     */
    public com.google.protobuf.ByteString
        getRoleBytes() {
      java.lang.Object ref = role_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        role_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    private void initFields() {
      userId_ = 0L;
      userName_ = "";
      role_ = "";
    }
    private byte memoizedIsInitialized = -1;
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized != -1) return isInitialized == 1;

      memoizedIsInitialized = 1;
      return true;
    }

    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      getSerializedSize();
      if (((bitField0_ & 0x00000001) == 0x00000001)) {
        output.writeInt64(1, userId_);
      }
      if (((bitField0_ & 0x00000002) == 0x00000002)) {
        output.writeBytes(2, getUserNameBytes());
      }
      if (((bitField0_ & 0x00000004) == 0x00000004)) {
        output.writeBytes(3, getRoleBytes());
      }
      getUnknownFields().writeTo(output);
    }

    private int memoizedSerializedSize = -1;
    public int getSerializedSize() {
      int size = memoizedSerializedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) == 0x00000001)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt64Size(1, userId_);
      }
      if (((bitField0_ & 0x00000002) == 0x00000002)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(2, getUserNameBytes());
      }
      if (((bitField0_ & 0x00000004) == 0x00000004)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(3, getRoleBytes());
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSerializedSize = size;
      return size;
    }

    private static final long serialVersionUID = 0L;
    @java.lang.Override
    protected java.lang.Object writeReplace()
        throws java.io.ObjectStreamException {
      return super.writeReplace();
    }

    public static fm.lizhi.ocean.seal.protocol.SealAuthServiceProto.JwtUserInfo parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static fm.lizhi.ocean.seal.protocol.SealAuthServiceProto.JwtUserInfo parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static fm.lizhi.ocean.seal.protocol.SealAuthServiceProto.JwtUserInfo parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static fm.lizhi.ocean.seal.protocol.SealAuthServiceProto.JwtUserInfo parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static fm.lizhi.ocean.seal.protocol.SealAuthServiceProto.JwtUserInfo parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return PARSER.parseFrom(input);
    }
    public static fm.lizhi.ocean.seal.protocol.SealAuthServiceProto.JwtUserInfo parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseFrom(input, extensionRegistry);
    }
    public static fm.lizhi.ocean.seal.protocol.SealAuthServiceProto.JwtUserInfo parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return PARSER.parseDelimitedFrom(input);
    }
    public static fm.lizhi.ocean.seal.protocol.SealAuthServiceProto.JwtUserInfo parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseDelimitedFrom(input, extensionRegistry);
    }
    public static fm.lizhi.ocean.seal.protocol.SealAuthServiceProto.JwtUserInfo parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return PARSER.parseFrom(input);
    }
    public static fm.lizhi.ocean.seal.protocol.SealAuthServiceProto.JwtUserInfo parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseFrom(input, extensionRegistry);
    }

    public static Builder newBuilder() { return Builder.create(); }
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder(fm.lizhi.ocean.seal.protocol.SealAuthServiceProto.JwtUserInfo prototype) {
      return newBuilder().mergeFrom(prototype);
    }
    public Builder toBuilder() { return newBuilder(this); }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code fm.lizhi.commons.template.datacenter.protocol.JwtUserInfo}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessage.Builder<Builder>
       implements fm.lizhi.ocean.seal.protocol.SealAuthServiceProto.JwtUserInfoOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return fm.lizhi.ocean.seal.protocol.SealAuthServiceProto.internal_static_fm_lizhi_commons_template_datacenter_protocol_JwtUserInfo_descriptor;
      }

      protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return fm.lizhi.ocean.seal.protocol.SealAuthServiceProto.internal_static_fm_lizhi_commons_template_datacenter_protocol_JwtUserInfo_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                fm.lizhi.ocean.seal.protocol.SealAuthServiceProto.JwtUserInfo.class, fm.lizhi.ocean.seal.protocol.SealAuthServiceProto.JwtUserInfo.Builder.class);
      }

      // Construct using fm.lizhi.ocean.seal.protocol.SealAuthServiceProto.JwtUserInfo.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessage.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessage.alwaysUseFieldBuilders) {
        }
      }
      private static Builder create() {
        return new Builder();
      }

      public Builder clear() {
        super.clear();
        userId_ = 0L;
        bitField0_ = (bitField0_ & ~0x00000001);
        userName_ = "";
        bitField0_ = (bitField0_ & ~0x00000002);
        role_ = "";
        bitField0_ = (bitField0_ & ~0x00000004);
        return this;
      }

      public Builder clone() {
        return create().mergeFrom(buildPartial());
      }

      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return fm.lizhi.ocean.seal.protocol.SealAuthServiceProto.internal_static_fm_lizhi_commons_template_datacenter_protocol_JwtUserInfo_descriptor;
      }

      public fm.lizhi.ocean.seal.protocol.SealAuthServiceProto.JwtUserInfo getDefaultInstanceForType() {
        return fm.lizhi.ocean.seal.protocol.SealAuthServiceProto.JwtUserInfo.getDefaultInstance();
      }

      public fm.lizhi.ocean.seal.protocol.SealAuthServiceProto.JwtUserInfo build() {
        fm.lizhi.ocean.seal.protocol.SealAuthServiceProto.JwtUserInfo result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      public fm.lizhi.ocean.seal.protocol.SealAuthServiceProto.JwtUserInfo buildPartial() {
        fm.lizhi.ocean.seal.protocol.SealAuthServiceProto.JwtUserInfo result = new fm.lizhi.ocean.seal.protocol.SealAuthServiceProto.JwtUserInfo(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) == 0x00000001)) {
          to_bitField0_ |= 0x00000001;
        }
        result.userId_ = userId_;
        if (((from_bitField0_ & 0x00000002) == 0x00000002)) {
          to_bitField0_ |= 0x00000002;
        }
        result.userName_ = userName_;
        if (((from_bitField0_ & 0x00000004) == 0x00000004)) {
          to_bitField0_ |= 0x00000004;
        }
        result.role_ = role_;
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof fm.lizhi.ocean.seal.protocol.SealAuthServiceProto.JwtUserInfo) {
          return mergeFrom((fm.lizhi.ocean.seal.protocol.SealAuthServiceProto.JwtUserInfo)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(fm.lizhi.ocean.seal.protocol.SealAuthServiceProto.JwtUserInfo other) {
        if (other == fm.lizhi.ocean.seal.protocol.SealAuthServiceProto.JwtUserInfo.getDefaultInstance()) return this;
        if (other.hasUserId()) {
          setUserId(other.getUserId());
        }
        if (other.hasUserName()) {
          bitField0_ |= 0x00000002;
          userName_ = other.userName_;
          onChanged();
        }
        if (other.hasRole()) {
          bitField0_ |= 0x00000004;
          role_ = other.role_;
          onChanged();
        }
        this.mergeUnknownFields(other.getUnknownFields());
        return this;
      }

      public final boolean isInitialized() {
        return true;
      }

      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        fm.lizhi.ocean.seal.protocol.SealAuthServiceProto.JwtUserInfo parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (fm.lizhi.ocean.seal.protocol.SealAuthServiceProto.JwtUserInfo) e.getUnfinishedMessage();
          throw e;
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      // optional int64 userId = 1;
      private long userId_ ;
      /**
       * <code>optional int64 userId = 1;</code>
       *
       * <pre>
       *用户id
       * </pre>
       */
      public boolean hasUserId() {
        return ((bitField0_ & 0x00000001) == 0x00000001);
      }
      /**
       * <code>optional int64 userId = 1;</code>
       *
       * <pre>
       *用户id
       * </pre>
       */
      public long getUserId() {
        return userId_;
      }
      /**
       * <code>optional int64 userId = 1;</code>
       *
       * <pre>
       *用户id
       * </pre>
       */
      public Builder setUserId(long value) {
        bitField0_ |= 0x00000001;
        userId_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional int64 userId = 1;</code>
       *
       * <pre>
       *用户id
       * </pre>
       */
      public Builder clearUserId() {
        bitField0_ = (bitField0_ & ~0x00000001);
        userId_ = 0L;
        onChanged();
        return this;
      }

      // optional string userName = 2;
      private java.lang.Object userName_ = "";
      /**
       * <code>optional string userName = 2;</code>
       *
       * <pre>
       *用户名称
       * </pre>
       */
      public boolean hasUserName() {
        return ((bitField0_ & 0x00000002) == 0x00000002);
      }
      /**
       * <code>optional string userName = 2;</code>
       *
       * <pre>
       *用户名称
       * </pre>
       */
      public java.lang.String getUserName() {
        java.lang.Object ref = userName_;
        if (!(ref instanceof java.lang.String)) {
          java.lang.String s = ((com.google.protobuf.ByteString) ref)
              .toStringUtf8();
          userName_ = s;
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <code>optional string userName = 2;</code>
       *
       * <pre>
       *用户名称
       * </pre>
       */
      public com.google.protobuf.ByteString
          getUserNameBytes() {
        java.lang.Object ref = userName_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          userName_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <code>optional string userName = 2;</code>
       *
       * <pre>
       *用户名称
       * </pre>
       */
      public Builder setUserName(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000002;
        userName_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional string userName = 2;</code>
       *
       * <pre>
       *用户名称
       * </pre>
       */
      public Builder clearUserName() {
        bitField0_ = (bitField0_ & ~0x00000002);
        userName_ = getDefaultInstance().getUserName();
        onChanged();
        return this;
      }
      /**
       * <code>optional string userName = 2;</code>
       *
       * <pre>
       *用户名称
       * </pre>
       */
      public Builder setUserNameBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000002;
        userName_ = value;
        onChanged();
        return this;
      }

      // optional string role = 3;
      private java.lang.Object role_ = "";
      /**
       * <code>optional string role = 3;</code>
       *
       * <pre>
       *用户角色
       * </pre>
       */
      public boolean hasRole() {
        return ((bitField0_ & 0x00000004) == 0x00000004);
      }
      /**
       * <code>optional string role = 3;</code>
       *
       * <pre>
       *用户角色
       * </pre>
       */
      public java.lang.String getRole() {
        java.lang.Object ref = role_;
        if (!(ref instanceof java.lang.String)) {
          java.lang.String s = ((com.google.protobuf.ByteString) ref)
              .toStringUtf8();
          role_ = s;
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <code>optional string role = 3;</code>
       *
       * <pre>
       *用户角色
       * </pre>
       */
      public com.google.protobuf.ByteString
          getRoleBytes() {
        java.lang.Object ref = role_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          role_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <code>optional string role = 3;</code>
       *
       * <pre>
       *用户角色
       * </pre>
       */
      public Builder setRole(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000004;
        role_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional string role = 3;</code>
       *
       * <pre>
       *用户角色
       * </pre>
       */
      public Builder clearRole() {
        bitField0_ = (bitField0_ & ~0x00000004);
        role_ = getDefaultInstance().getRole();
        onChanged();
        return this;
      }
      /**
       * <code>optional string role = 3;</code>
       *
       * <pre>
       *用户角色
       * </pre>
       */
      public Builder setRoleBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000004;
        role_ = value;
        onChanged();
        return this;
      }

      // @@protoc_insertion_point(builder_scope:fm.lizhi.commons.template.datacenter.protocol.JwtUserInfo)
    }

    static {
      defaultInstance = new JwtUserInfo(true);
      defaultInstance.initFields();
    }

    // @@protoc_insertion_point(class_scope:fm.lizhi.commons.template.datacenter.protocol.JwtUserInfo)
  }

  public interface RequestGetSealTokenOrBuilder
      extends com.google.protobuf.MessageOrBuilder {

    // optional int64 userId = 1;
    /**
     * <code>optional int64 userId = 1;</code>
     *
     * <pre>
     *业务用户Id
     * </pre>
     */
    boolean hasUserId();
    /**
     * <code>optional int64 userId = 1;</code>
     *
     * <pre>
     *业务用户Id
     * </pre>
     */
    long getUserId();

    // optional int32 appId = 2;
    /**
     * <code>optional int32 appId = 2;</code>
     *
     * <pre>
     *appId
     * </pre>
     */
    boolean hasAppId();
    /**
     * <code>optional int32 appId = 2;</code>
     *
     * <pre>
     *appId
     * </pre>
     */
    int getAppId();

    // optional string channel = 3;
    /**
     * <code>optional string channel = 3;</code>
     *
     * <pre>
     *游戏渠道
     * </pre>
     */
    boolean hasChannel();
    /**
     * <code>optional string channel = 3;</code>
     *
     * <pre>
     *游戏渠道
     * </pre>
     */
    java.lang.String getChannel();
    /**
     * <code>optional string channel = 3;</code>
     *
     * <pre>
     *游戏渠道
     * </pre>
     */
    com.google.protobuf.ByteString
        getChannelBytes();

    // optional string checkCode = 4;
    /**
     * <code>optional string checkCode = 4;</code>
     *
     * <pre>
     *用户校验码
     * </pre>
     */
    boolean hasCheckCode();
    /**
     * <code>optional string checkCode = 4;</code>
     *
     * <pre>
     *用户校验码
     * </pre>
     */
    java.lang.String getCheckCode();
    /**
     * <code>optional string checkCode = 4;</code>
     *
     * <pre>
     *用户校验码
     * </pre>
     */
    com.google.protobuf.ByteString
        getCheckCodeBytes();
  }
  /**
   * Protobuf type {@code fm.lizhi.commons.template.datacenter.protocol.RequestGetSealToken}
   *
   * <pre>
   * SealAuthService.java
   * 获取短期令牌，默认时长2小时
   * domain = 4302, op = 999
   * </pre>
   */
  public static final class RequestGetSealToken extends
      com.google.protobuf.GeneratedMessage
      implements RequestGetSealTokenOrBuilder {
    // Use RequestGetSealToken.newBuilder() to construct.
    private RequestGetSealToken(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
      super(builder);
      this.unknownFields = builder.getUnknownFields();
    }
    private RequestGetSealToken(boolean noInit) { this.unknownFields = com.google.protobuf.UnknownFieldSet.getDefaultInstance(); }

    private static final RequestGetSealToken defaultInstance;
    public static RequestGetSealToken getDefaultInstance() {
      return defaultInstance;
    }

    public RequestGetSealToken getDefaultInstanceForType() {
      return defaultInstance;
    }

    private final com.google.protobuf.UnknownFieldSet unknownFields;
    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
        getUnknownFields() {
      return this.unknownFields;
    }
    private RequestGetSealToken(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      initFields();
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            default: {
              if (!parseUnknownField(input, unknownFields,
                                     extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
            case 8: {
              bitField0_ |= 0x00000001;
              userId_ = input.readInt64();
              break;
            }
            case 16: {
              bitField0_ |= 0x00000002;
              appId_ = input.readInt32();
              break;
            }
            case 26: {
              bitField0_ |= 0x00000004;
              channel_ = input.readBytes();
              break;
            }
            case 34: {
              bitField0_ |= 0x00000008;
              checkCode_ = input.readBytes();
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e.getMessage()).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return fm.lizhi.ocean.seal.protocol.SealAuthServiceProto.internal_static_fm_lizhi_commons_template_datacenter_protocol_RequestGetSealToken_descriptor;
    }

    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return fm.lizhi.ocean.seal.protocol.SealAuthServiceProto.internal_static_fm_lizhi_commons_template_datacenter_protocol_RequestGetSealToken_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              fm.lizhi.ocean.seal.protocol.SealAuthServiceProto.RequestGetSealToken.class, fm.lizhi.ocean.seal.protocol.SealAuthServiceProto.RequestGetSealToken.Builder.class);
    }

    public static com.google.protobuf.Parser<RequestGetSealToken> PARSER =
        new com.google.protobuf.AbstractParser<RequestGetSealToken>() {
      public RequestGetSealToken parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new RequestGetSealToken(input, extensionRegistry);
      }
    };

    @java.lang.Override
    public com.google.protobuf.Parser<RequestGetSealToken> getParserForType() {
      return PARSER;
    }

    private int bitField0_;
    // optional int64 userId = 1;
    public static final int USERID_FIELD_NUMBER = 1;
    private long userId_;
    /**
     * <code>optional int64 userId = 1;</code>
     *
     * <pre>
     *业务用户Id
     * </pre>
     */
    public boolean hasUserId() {
      return ((bitField0_ & 0x00000001) == 0x00000001);
    }
    /**
     * <code>optional int64 userId = 1;</code>
     *
     * <pre>
     *业务用户Id
     * </pre>
     */
    public long getUserId() {
      return userId_;
    }

    // optional int32 appId = 2;
    public static final int APPID_FIELD_NUMBER = 2;
    private int appId_;
    /**
     * <code>optional int32 appId = 2;</code>
     *
     * <pre>
     *appId
     * </pre>
     */
    public boolean hasAppId() {
      return ((bitField0_ & 0x00000002) == 0x00000002);
    }
    /**
     * <code>optional int32 appId = 2;</code>
     *
     * <pre>
     *appId
     * </pre>
     */
    public int getAppId() {
      return appId_;
    }

    // optional string channel = 3;
    public static final int CHANNEL_FIELD_NUMBER = 3;
    private java.lang.Object channel_;
    /**
     * <code>optional string channel = 3;</code>
     *
     * <pre>
     *游戏渠道
     * </pre>
     */
    public boolean hasChannel() {
      return ((bitField0_ & 0x00000004) == 0x00000004);
    }
    /**
     * <code>optional string channel = 3;</code>
     *
     * <pre>
     *游戏渠道
     * </pre>
     */
    public java.lang.String getChannel() {
      java.lang.Object ref = channel_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          channel_ = s;
        }
        return s;
      }
    }
    /**
     * <code>optional string channel = 3;</code>
     *
     * <pre>
     *游戏渠道
     * </pre>
     */
    public com.google.protobuf.ByteString
        getChannelBytes() {
      java.lang.Object ref = channel_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        channel_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    // optional string checkCode = 4;
    public static final int CHECKCODE_FIELD_NUMBER = 4;
    private java.lang.Object checkCode_;
    /**
     * <code>optional string checkCode = 4;</code>
     *
     * <pre>
     *用户校验码
     * </pre>
     */
    public boolean hasCheckCode() {
      return ((bitField0_ & 0x00000008) == 0x00000008);
    }
    /**
     * <code>optional string checkCode = 4;</code>
     *
     * <pre>
     *用户校验码
     * </pre>
     */
    public java.lang.String getCheckCode() {
      java.lang.Object ref = checkCode_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          checkCode_ = s;
        }
        return s;
      }
    }
    /**
     * <code>optional string checkCode = 4;</code>
     *
     * <pre>
     *用户校验码
     * </pre>
     */
    public com.google.protobuf.ByteString
        getCheckCodeBytes() {
      java.lang.Object ref = checkCode_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        checkCode_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    private void initFields() {
      userId_ = 0L;
      appId_ = 0;
      channel_ = "";
      checkCode_ = "";
    }
    private byte memoizedIsInitialized = -1;
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized != -1) return isInitialized == 1;

      memoizedIsInitialized = 1;
      return true;
    }

    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      getSerializedSize();
      if (((bitField0_ & 0x00000001) == 0x00000001)) {
        output.writeInt64(1, userId_);
      }
      if (((bitField0_ & 0x00000002) == 0x00000002)) {
        output.writeInt32(2, appId_);
      }
      if (((bitField0_ & 0x00000004) == 0x00000004)) {
        output.writeBytes(3, getChannelBytes());
      }
      if (((bitField0_ & 0x00000008) == 0x00000008)) {
        output.writeBytes(4, getCheckCodeBytes());
      }
      getUnknownFields().writeTo(output);
    }

    private int memoizedSerializedSize = -1;
    public int getSerializedSize() {
      int size = memoizedSerializedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) == 0x00000001)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt64Size(1, userId_);
      }
      if (((bitField0_ & 0x00000002) == 0x00000002)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(2, appId_);
      }
      if (((bitField0_ & 0x00000004) == 0x00000004)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(3, getChannelBytes());
      }
      if (((bitField0_ & 0x00000008) == 0x00000008)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(4, getCheckCodeBytes());
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSerializedSize = size;
      return size;
    }

    private static final long serialVersionUID = 0L;
    @java.lang.Override
    protected java.lang.Object writeReplace()
        throws java.io.ObjectStreamException {
      return super.writeReplace();
    }

    public static fm.lizhi.ocean.seal.protocol.SealAuthServiceProto.RequestGetSealToken parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static fm.lizhi.ocean.seal.protocol.SealAuthServiceProto.RequestGetSealToken parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static fm.lizhi.ocean.seal.protocol.SealAuthServiceProto.RequestGetSealToken parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static fm.lizhi.ocean.seal.protocol.SealAuthServiceProto.RequestGetSealToken parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static fm.lizhi.ocean.seal.protocol.SealAuthServiceProto.RequestGetSealToken parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return PARSER.parseFrom(input);
    }
    public static fm.lizhi.ocean.seal.protocol.SealAuthServiceProto.RequestGetSealToken parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseFrom(input, extensionRegistry);
    }
    public static fm.lizhi.ocean.seal.protocol.SealAuthServiceProto.RequestGetSealToken parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return PARSER.parseDelimitedFrom(input);
    }
    public static fm.lizhi.ocean.seal.protocol.SealAuthServiceProto.RequestGetSealToken parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseDelimitedFrom(input, extensionRegistry);
    }
    public static fm.lizhi.ocean.seal.protocol.SealAuthServiceProto.RequestGetSealToken parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return PARSER.parseFrom(input);
    }
    public static fm.lizhi.ocean.seal.protocol.SealAuthServiceProto.RequestGetSealToken parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseFrom(input, extensionRegistry);
    }

    public static Builder newBuilder() { return Builder.create(); }
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder(fm.lizhi.ocean.seal.protocol.SealAuthServiceProto.RequestGetSealToken prototype) {
      return newBuilder().mergeFrom(prototype);
    }
    public Builder toBuilder() { return newBuilder(this); }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code fm.lizhi.commons.template.datacenter.protocol.RequestGetSealToken}
     *
     * <pre>
     * SealAuthService.java
     * 获取短期令牌，默认时长2小时
     * domain = 4302, op = 999
     * </pre>
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessage.Builder<Builder>
       implements fm.lizhi.ocean.seal.protocol.SealAuthServiceProto.RequestGetSealTokenOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return fm.lizhi.ocean.seal.protocol.SealAuthServiceProto.internal_static_fm_lizhi_commons_template_datacenter_protocol_RequestGetSealToken_descriptor;
      }

      protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return fm.lizhi.ocean.seal.protocol.SealAuthServiceProto.internal_static_fm_lizhi_commons_template_datacenter_protocol_RequestGetSealToken_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                fm.lizhi.ocean.seal.protocol.SealAuthServiceProto.RequestGetSealToken.class, fm.lizhi.ocean.seal.protocol.SealAuthServiceProto.RequestGetSealToken.Builder.class);
      }

      // Construct using fm.lizhi.ocean.seal.protocol.SealAuthServiceProto.RequestGetSealToken.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessage.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessage.alwaysUseFieldBuilders) {
        }
      }
      private static Builder create() {
        return new Builder();
      }

      public Builder clear() {
        super.clear();
        userId_ = 0L;
        bitField0_ = (bitField0_ & ~0x00000001);
        appId_ = 0;
        bitField0_ = (bitField0_ & ~0x00000002);
        channel_ = "";
        bitField0_ = (bitField0_ & ~0x00000004);
        checkCode_ = "";
        bitField0_ = (bitField0_ & ~0x00000008);
        return this;
      }

      public Builder clone() {
        return create().mergeFrom(buildPartial());
      }

      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return fm.lizhi.ocean.seal.protocol.SealAuthServiceProto.internal_static_fm_lizhi_commons_template_datacenter_protocol_RequestGetSealToken_descriptor;
      }

      public fm.lizhi.ocean.seal.protocol.SealAuthServiceProto.RequestGetSealToken getDefaultInstanceForType() {
        return fm.lizhi.ocean.seal.protocol.SealAuthServiceProto.RequestGetSealToken.getDefaultInstance();
      }

      public fm.lizhi.ocean.seal.protocol.SealAuthServiceProto.RequestGetSealToken build() {
        fm.lizhi.ocean.seal.protocol.SealAuthServiceProto.RequestGetSealToken result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      public fm.lizhi.ocean.seal.protocol.SealAuthServiceProto.RequestGetSealToken buildPartial() {
        fm.lizhi.ocean.seal.protocol.SealAuthServiceProto.RequestGetSealToken result = new fm.lizhi.ocean.seal.protocol.SealAuthServiceProto.RequestGetSealToken(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) == 0x00000001)) {
          to_bitField0_ |= 0x00000001;
        }
        result.userId_ = userId_;
        if (((from_bitField0_ & 0x00000002) == 0x00000002)) {
          to_bitField0_ |= 0x00000002;
        }
        result.appId_ = appId_;
        if (((from_bitField0_ & 0x00000004) == 0x00000004)) {
          to_bitField0_ |= 0x00000004;
        }
        result.channel_ = channel_;
        if (((from_bitField0_ & 0x00000008) == 0x00000008)) {
          to_bitField0_ |= 0x00000008;
        }
        result.checkCode_ = checkCode_;
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof fm.lizhi.ocean.seal.protocol.SealAuthServiceProto.RequestGetSealToken) {
          return mergeFrom((fm.lizhi.ocean.seal.protocol.SealAuthServiceProto.RequestGetSealToken)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(fm.lizhi.ocean.seal.protocol.SealAuthServiceProto.RequestGetSealToken other) {
        if (other == fm.lizhi.ocean.seal.protocol.SealAuthServiceProto.RequestGetSealToken.getDefaultInstance()) return this;
        if (other.hasUserId()) {
          setUserId(other.getUserId());
        }
        if (other.hasAppId()) {
          setAppId(other.getAppId());
        }
        if (other.hasChannel()) {
          bitField0_ |= 0x00000004;
          channel_ = other.channel_;
          onChanged();
        }
        if (other.hasCheckCode()) {
          bitField0_ |= 0x00000008;
          checkCode_ = other.checkCode_;
          onChanged();
        }
        this.mergeUnknownFields(other.getUnknownFields());
        return this;
      }

      public final boolean isInitialized() {
        return true;
      }

      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        fm.lizhi.ocean.seal.protocol.SealAuthServiceProto.RequestGetSealToken parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (fm.lizhi.ocean.seal.protocol.SealAuthServiceProto.RequestGetSealToken) e.getUnfinishedMessage();
          throw e;
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      // optional int64 userId = 1;
      private long userId_ ;
      /**
       * <code>optional int64 userId = 1;</code>
       *
       * <pre>
       *业务用户Id
       * </pre>
       */
      public boolean hasUserId() {
        return ((bitField0_ & 0x00000001) == 0x00000001);
      }
      /**
       * <code>optional int64 userId = 1;</code>
       *
       * <pre>
       *业务用户Id
       * </pre>
       */
      public long getUserId() {
        return userId_;
      }
      /**
       * <code>optional int64 userId = 1;</code>
       *
       * <pre>
       *业务用户Id
       * </pre>
       */
      public Builder setUserId(long value) {
        bitField0_ |= 0x00000001;
        userId_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional int64 userId = 1;</code>
       *
       * <pre>
       *业务用户Id
       * </pre>
       */
      public Builder clearUserId() {
        bitField0_ = (bitField0_ & ~0x00000001);
        userId_ = 0L;
        onChanged();
        return this;
      }

      // optional int32 appId = 2;
      private int appId_ ;
      /**
       * <code>optional int32 appId = 2;</code>
       *
       * <pre>
       *appId
       * </pre>
       */
      public boolean hasAppId() {
        return ((bitField0_ & 0x00000002) == 0x00000002);
      }
      /**
       * <code>optional int32 appId = 2;</code>
       *
       * <pre>
       *appId
       * </pre>
       */
      public int getAppId() {
        return appId_;
      }
      /**
       * <code>optional int32 appId = 2;</code>
       *
       * <pre>
       *appId
       * </pre>
       */
      public Builder setAppId(int value) {
        bitField0_ |= 0x00000002;
        appId_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional int32 appId = 2;</code>
       *
       * <pre>
       *appId
       * </pre>
       */
      public Builder clearAppId() {
        bitField0_ = (bitField0_ & ~0x00000002);
        appId_ = 0;
        onChanged();
        return this;
      }

      // optional string channel = 3;
      private java.lang.Object channel_ = "";
      /**
       * <code>optional string channel = 3;</code>
       *
       * <pre>
       *游戏渠道
       * </pre>
       */
      public boolean hasChannel() {
        return ((bitField0_ & 0x00000004) == 0x00000004);
      }
      /**
       * <code>optional string channel = 3;</code>
       *
       * <pre>
       *游戏渠道
       * </pre>
       */
      public java.lang.String getChannel() {
        java.lang.Object ref = channel_;
        if (!(ref instanceof java.lang.String)) {
          java.lang.String s = ((com.google.protobuf.ByteString) ref)
              .toStringUtf8();
          channel_ = s;
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <code>optional string channel = 3;</code>
       *
       * <pre>
       *游戏渠道
       * </pre>
       */
      public com.google.protobuf.ByteString
          getChannelBytes() {
        java.lang.Object ref = channel_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          channel_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <code>optional string channel = 3;</code>
       *
       * <pre>
       *游戏渠道
       * </pre>
       */
      public Builder setChannel(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000004;
        channel_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional string channel = 3;</code>
       *
       * <pre>
       *游戏渠道
       * </pre>
       */
      public Builder clearChannel() {
        bitField0_ = (bitField0_ & ~0x00000004);
        channel_ = getDefaultInstance().getChannel();
        onChanged();
        return this;
      }
      /**
       * <code>optional string channel = 3;</code>
       *
       * <pre>
       *游戏渠道
       * </pre>
       */
      public Builder setChannelBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000004;
        channel_ = value;
        onChanged();
        return this;
      }

      // optional string checkCode = 4;
      private java.lang.Object checkCode_ = "";
      /**
       * <code>optional string checkCode = 4;</code>
       *
       * <pre>
       *用户校验码
       * </pre>
       */
      public boolean hasCheckCode() {
        return ((bitField0_ & 0x00000008) == 0x00000008);
      }
      /**
       * <code>optional string checkCode = 4;</code>
       *
       * <pre>
       *用户校验码
       * </pre>
       */
      public java.lang.String getCheckCode() {
        java.lang.Object ref = checkCode_;
        if (!(ref instanceof java.lang.String)) {
          java.lang.String s = ((com.google.protobuf.ByteString) ref)
              .toStringUtf8();
          checkCode_ = s;
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <code>optional string checkCode = 4;</code>
       *
       * <pre>
       *用户校验码
       * </pre>
       */
      public com.google.protobuf.ByteString
          getCheckCodeBytes() {
        java.lang.Object ref = checkCode_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          checkCode_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <code>optional string checkCode = 4;</code>
       *
       * <pre>
       *用户校验码
       * </pre>
       */
      public Builder setCheckCode(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000008;
        checkCode_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional string checkCode = 4;</code>
       *
       * <pre>
       *用户校验码
       * </pre>
       */
      public Builder clearCheckCode() {
        bitField0_ = (bitField0_ & ~0x00000008);
        checkCode_ = getDefaultInstance().getCheckCode();
        onChanged();
        return this;
      }
      /**
       * <code>optional string checkCode = 4;</code>
       *
       * <pre>
       *用户校验码
       * </pre>
       */
      public Builder setCheckCodeBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000008;
        checkCode_ = value;
        onChanged();
        return this;
      }

      // @@protoc_insertion_point(builder_scope:fm.lizhi.commons.template.datacenter.protocol.RequestGetSealToken)
    }

    static {
      defaultInstance = new RequestGetSealToken(true);
      defaultInstance.initFields();
    }

    // @@protoc_insertion_point(class_scope:fm.lizhi.commons.template.datacenter.protocol.RequestGetSealToken)
  }

  public interface ResponseGetSealTokenOrBuilder
      extends com.google.protobuf.MessageOrBuilder {

    // optional .fm.lizhi.commons.template.datacenter.protocol.SealToken token = 1;
    /**
     * <code>optional .fm.lizhi.commons.template.datacenter.protocol.SealToken token = 1;</code>
     */
    boolean hasToken();
    /**
     * <code>optional .fm.lizhi.commons.template.datacenter.protocol.SealToken token = 1;</code>
     */
    fm.lizhi.ocean.seal.protocol.SealAuthServiceProto.SealToken getToken();
    /**
     * <code>optional .fm.lizhi.commons.template.datacenter.protocol.SealToken token = 1;</code>
     */
    fm.lizhi.ocean.seal.protocol.SealAuthServiceProto.SealTokenOrBuilder getTokenOrBuilder();
  }
  /**
   * Protobuf type {@code fm.lizhi.commons.template.datacenter.protocol.ResponseGetSealToken}
   *
   * <pre>
   * rcode == 0(SUCCESS) = 执行成功
   * rcode == 1(FAIL) = 非法用户
   * rcode == 2(ILLEGAL_PARAMS) = 参数非法
   * </pre>
   */
  public static final class ResponseGetSealToken extends
      com.google.protobuf.GeneratedMessage
      implements ResponseGetSealTokenOrBuilder {
    // Use ResponseGetSealToken.newBuilder() to construct.
    private ResponseGetSealToken(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
      super(builder);
      this.unknownFields = builder.getUnknownFields();
    }
    private ResponseGetSealToken(boolean noInit) { this.unknownFields = com.google.protobuf.UnknownFieldSet.getDefaultInstance(); }

    private static final ResponseGetSealToken defaultInstance;
    public static ResponseGetSealToken getDefaultInstance() {
      return defaultInstance;
    }

    public ResponseGetSealToken getDefaultInstanceForType() {
      return defaultInstance;
    }

    private final com.google.protobuf.UnknownFieldSet unknownFields;
    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
        getUnknownFields() {
      return this.unknownFields;
    }
    private ResponseGetSealToken(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      initFields();
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            default: {
              if (!parseUnknownField(input, unknownFields,
                                     extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
            case 10: {
              fm.lizhi.ocean.seal.protocol.SealAuthServiceProto.SealToken.Builder subBuilder = null;
              if (((bitField0_ & 0x00000001) == 0x00000001)) {
                subBuilder = token_.toBuilder();
              }
              token_ = input.readMessage(fm.lizhi.ocean.seal.protocol.SealAuthServiceProto.SealToken.PARSER, extensionRegistry);
              if (subBuilder != null) {
                subBuilder.mergeFrom(token_);
                token_ = subBuilder.buildPartial();
              }
              bitField0_ |= 0x00000001;
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e.getMessage()).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return fm.lizhi.ocean.seal.protocol.SealAuthServiceProto.internal_static_fm_lizhi_commons_template_datacenter_protocol_ResponseGetSealToken_descriptor;
    }

    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return fm.lizhi.ocean.seal.protocol.SealAuthServiceProto.internal_static_fm_lizhi_commons_template_datacenter_protocol_ResponseGetSealToken_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              fm.lizhi.ocean.seal.protocol.SealAuthServiceProto.ResponseGetSealToken.class, fm.lizhi.ocean.seal.protocol.SealAuthServiceProto.ResponseGetSealToken.Builder.class);
    }

    public static com.google.protobuf.Parser<ResponseGetSealToken> PARSER =
        new com.google.protobuf.AbstractParser<ResponseGetSealToken>() {
      public ResponseGetSealToken parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new ResponseGetSealToken(input, extensionRegistry);
      }
    };

    @java.lang.Override
    public com.google.protobuf.Parser<ResponseGetSealToken> getParserForType() {
      return PARSER;
    }

    private int bitField0_;
    // optional .fm.lizhi.commons.template.datacenter.protocol.SealToken token = 1;
    public static final int TOKEN_FIELD_NUMBER = 1;
    private fm.lizhi.ocean.seal.protocol.SealAuthServiceProto.SealToken token_;
    /**
     * <code>optional .fm.lizhi.commons.template.datacenter.protocol.SealToken token = 1;</code>
     */
    public boolean hasToken() {
      return ((bitField0_ & 0x00000001) == 0x00000001);
    }
    /**
     * <code>optional .fm.lizhi.commons.template.datacenter.protocol.SealToken token = 1;</code>
     */
    public fm.lizhi.ocean.seal.protocol.SealAuthServiceProto.SealToken getToken() {
      return token_;
    }
    /**
     * <code>optional .fm.lizhi.commons.template.datacenter.protocol.SealToken token = 1;</code>
     */
    public fm.lizhi.ocean.seal.protocol.SealAuthServiceProto.SealTokenOrBuilder getTokenOrBuilder() {
      return token_;
    }

    private void initFields() {
      token_ = fm.lizhi.ocean.seal.protocol.SealAuthServiceProto.SealToken.getDefaultInstance();
    }
    private byte memoizedIsInitialized = -1;
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized != -1) return isInitialized == 1;

      memoizedIsInitialized = 1;
      return true;
    }

    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      getSerializedSize();
      if (((bitField0_ & 0x00000001) == 0x00000001)) {
        output.writeMessage(1, token_);
      }
      getUnknownFields().writeTo(output);
    }

    private int memoizedSerializedSize = -1;
    public int getSerializedSize() {
      int size = memoizedSerializedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) == 0x00000001)) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(1, token_);
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSerializedSize = size;
      return size;
    }

    private static final long serialVersionUID = 0L;
    @java.lang.Override
    protected java.lang.Object writeReplace()
        throws java.io.ObjectStreamException {
      return super.writeReplace();
    }

    public static fm.lizhi.ocean.seal.protocol.SealAuthServiceProto.ResponseGetSealToken parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static fm.lizhi.ocean.seal.protocol.SealAuthServiceProto.ResponseGetSealToken parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static fm.lizhi.ocean.seal.protocol.SealAuthServiceProto.ResponseGetSealToken parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static fm.lizhi.ocean.seal.protocol.SealAuthServiceProto.ResponseGetSealToken parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static fm.lizhi.ocean.seal.protocol.SealAuthServiceProto.ResponseGetSealToken parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return PARSER.parseFrom(input);
    }
    public static fm.lizhi.ocean.seal.protocol.SealAuthServiceProto.ResponseGetSealToken parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseFrom(input, extensionRegistry);
    }
    public static fm.lizhi.ocean.seal.protocol.SealAuthServiceProto.ResponseGetSealToken parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return PARSER.parseDelimitedFrom(input);
    }
    public static fm.lizhi.ocean.seal.protocol.SealAuthServiceProto.ResponseGetSealToken parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseDelimitedFrom(input, extensionRegistry);
    }
    public static fm.lizhi.ocean.seal.protocol.SealAuthServiceProto.ResponseGetSealToken parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return PARSER.parseFrom(input);
    }
    public static fm.lizhi.ocean.seal.protocol.SealAuthServiceProto.ResponseGetSealToken parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseFrom(input, extensionRegistry);
    }

    public static Builder newBuilder() { return Builder.create(); }
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder(fm.lizhi.ocean.seal.protocol.SealAuthServiceProto.ResponseGetSealToken prototype) {
      return newBuilder().mergeFrom(prototype);
    }
    public Builder toBuilder() { return newBuilder(this); }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code fm.lizhi.commons.template.datacenter.protocol.ResponseGetSealToken}
     *
     * <pre>
     * rcode == 0(SUCCESS) = 执行成功
     * rcode == 1(FAIL) = 非法用户
     * rcode == 2(ILLEGAL_PARAMS) = 参数非法
     * </pre>
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessage.Builder<Builder>
       implements fm.lizhi.ocean.seal.protocol.SealAuthServiceProto.ResponseGetSealTokenOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return fm.lizhi.ocean.seal.protocol.SealAuthServiceProto.internal_static_fm_lizhi_commons_template_datacenter_protocol_ResponseGetSealToken_descriptor;
      }

      protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return fm.lizhi.ocean.seal.protocol.SealAuthServiceProto.internal_static_fm_lizhi_commons_template_datacenter_protocol_ResponseGetSealToken_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                fm.lizhi.ocean.seal.protocol.SealAuthServiceProto.ResponseGetSealToken.class, fm.lizhi.ocean.seal.protocol.SealAuthServiceProto.ResponseGetSealToken.Builder.class);
      }

      // Construct using fm.lizhi.ocean.seal.protocol.SealAuthServiceProto.ResponseGetSealToken.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessage.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessage.alwaysUseFieldBuilders) {
          getTokenFieldBuilder();
        }
      }
      private static Builder create() {
        return new Builder();
      }

      public Builder clear() {
        super.clear();
        if (tokenBuilder_ == null) {
          token_ = fm.lizhi.ocean.seal.protocol.SealAuthServiceProto.SealToken.getDefaultInstance();
        } else {
          tokenBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000001);
        return this;
      }

      public Builder clone() {
        return create().mergeFrom(buildPartial());
      }

      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return fm.lizhi.ocean.seal.protocol.SealAuthServiceProto.internal_static_fm_lizhi_commons_template_datacenter_protocol_ResponseGetSealToken_descriptor;
      }

      public fm.lizhi.ocean.seal.protocol.SealAuthServiceProto.ResponseGetSealToken getDefaultInstanceForType() {
        return fm.lizhi.ocean.seal.protocol.SealAuthServiceProto.ResponseGetSealToken.getDefaultInstance();
      }

      public fm.lizhi.ocean.seal.protocol.SealAuthServiceProto.ResponseGetSealToken build() {
        fm.lizhi.ocean.seal.protocol.SealAuthServiceProto.ResponseGetSealToken result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      public fm.lizhi.ocean.seal.protocol.SealAuthServiceProto.ResponseGetSealToken buildPartial() {
        fm.lizhi.ocean.seal.protocol.SealAuthServiceProto.ResponseGetSealToken result = new fm.lizhi.ocean.seal.protocol.SealAuthServiceProto.ResponseGetSealToken(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) == 0x00000001)) {
          to_bitField0_ |= 0x00000001;
        }
        if (tokenBuilder_ == null) {
          result.token_ = token_;
        } else {
          result.token_ = tokenBuilder_.build();
        }
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof fm.lizhi.ocean.seal.protocol.SealAuthServiceProto.ResponseGetSealToken) {
          return mergeFrom((fm.lizhi.ocean.seal.protocol.SealAuthServiceProto.ResponseGetSealToken)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(fm.lizhi.ocean.seal.protocol.SealAuthServiceProto.ResponseGetSealToken other) {
        if (other == fm.lizhi.ocean.seal.protocol.SealAuthServiceProto.ResponseGetSealToken.getDefaultInstance()) return this;
        if (other.hasToken()) {
          mergeToken(other.getToken());
        }
        this.mergeUnknownFields(other.getUnknownFields());
        return this;
      }

      public final boolean isInitialized() {
        return true;
      }

      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        fm.lizhi.ocean.seal.protocol.SealAuthServiceProto.ResponseGetSealToken parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (fm.lizhi.ocean.seal.protocol.SealAuthServiceProto.ResponseGetSealToken) e.getUnfinishedMessage();
          throw e;
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      // optional .fm.lizhi.commons.template.datacenter.protocol.SealToken token = 1;
      private fm.lizhi.ocean.seal.protocol.SealAuthServiceProto.SealToken token_ = fm.lizhi.ocean.seal.protocol.SealAuthServiceProto.SealToken.getDefaultInstance();
      private com.google.protobuf.SingleFieldBuilder<
          fm.lizhi.ocean.seal.protocol.SealAuthServiceProto.SealToken, fm.lizhi.ocean.seal.protocol.SealAuthServiceProto.SealToken.Builder, fm.lizhi.ocean.seal.protocol.SealAuthServiceProto.SealTokenOrBuilder> tokenBuilder_;
      /**
       * <code>optional .fm.lizhi.commons.template.datacenter.protocol.SealToken token = 1;</code>
       */
      public boolean hasToken() {
        return ((bitField0_ & 0x00000001) == 0x00000001);
      }
      /**
       * <code>optional .fm.lizhi.commons.template.datacenter.protocol.SealToken token = 1;</code>
       */
      public fm.lizhi.ocean.seal.protocol.SealAuthServiceProto.SealToken getToken() {
        if (tokenBuilder_ == null) {
          return token_;
        } else {
          return tokenBuilder_.getMessage();
        }
      }
      /**
       * <code>optional .fm.lizhi.commons.template.datacenter.protocol.SealToken token = 1;</code>
       */
      public Builder setToken(fm.lizhi.ocean.seal.protocol.SealAuthServiceProto.SealToken value) {
        if (tokenBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          token_ = value;
          onChanged();
        } else {
          tokenBuilder_.setMessage(value);
        }
        bitField0_ |= 0x00000001;
        return this;
      }
      /**
       * <code>optional .fm.lizhi.commons.template.datacenter.protocol.SealToken token = 1;</code>
       */
      public Builder setToken(
          fm.lizhi.ocean.seal.protocol.SealAuthServiceProto.SealToken.Builder builderForValue) {
        if (tokenBuilder_ == null) {
          token_ = builderForValue.build();
          onChanged();
        } else {
          tokenBuilder_.setMessage(builderForValue.build());
        }
        bitField0_ |= 0x00000001;
        return this;
      }
      /**
       * <code>optional .fm.lizhi.commons.template.datacenter.protocol.SealToken token = 1;</code>
       */
      public Builder mergeToken(fm.lizhi.ocean.seal.protocol.SealAuthServiceProto.SealToken value) {
        if (tokenBuilder_ == null) {
          if (((bitField0_ & 0x00000001) == 0x00000001) &&
              token_ != fm.lizhi.ocean.seal.protocol.SealAuthServiceProto.SealToken.getDefaultInstance()) {
            token_ =
              fm.lizhi.ocean.seal.protocol.SealAuthServiceProto.SealToken.newBuilder(token_).mergeFrom(value).buildPartial();
          } else {
            token_ = value;
          }
          onChanged();
        } else {
          tokenBuilder_.mergeFrom(value);
        }
        bitField0_ |= 0x00000001;
        return this;
      }
      /**
       * <code>optional .fm.lizhi.commons.template.datacenter.protocol.SealToken token = 1;</code>
       */
      public Builder clearToken() {
        if (tokenBuilder_ == null) {
          token_ = fm.lizhi.ocean.seal.protocol.SealAuthServiceProto.SealToken.getDefaultInstance();
          onChanged();
        } else {
          tokenBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000001);
        return this;
      }
      /**
       * <code>optional .fm.lizhi.commons.template.datacenter.protocol.SealToken token = 1;</code>
       */
      public fm.lizhi.ocean.seal.protocol.SealAuthServiceProto.SealToken.Builder getTokenBuilder() {
        bitField0_ |= 0x00000001;
        onChanged();
        return getTokenFieldBuilder().getBuilder();
      }
      /**
       * <code>optional .fm.lizhi.commons.template.datacenter.protocol.SealToken token = 1;</code>
       */
      public fm.lizhi.ocean.seal.protocol.SealAuthServiceProto.SealTokenOrBuilder getTokenOrBuilder() {
        if (tokenBuilder_ != null) {
          return tokenBuilder_.getMessageOrBuilder();
        } else {
          return token_;
        }
      }
      /**
       * <code>optional .fm.lizhi.commons.template.datacenter.protocol.SealToken token = 1;</code>
       */
      private com.google.protobuf.SingleFieldBuilder<
          fm.lizhi.ocean.seal.protocol.SealAuthServiceProto.SealToken, fm.lizhi.ocean.seal.protocol.SealAuthServiceProto.SealToken.Builder, fm.lizhi.ocean.seal.protocol.SealAuthServiceProto.SealTokenOrBuilder> 
          getTokenFieldBuilder() {
        if (tokenBuilder_ == null) {
          tokenBuilder_ = new com.google.protobuf.SingleFieldBuilder<
              fm.lizhi.ocean.seal.protocol.SealAuthServiceProto.SealToken, fm.lizhi.ocean.seal.protocol.SealAuthServiceProto.SealToken.Builder, fm.lizhi.ocean.seal.protocol.SealAuthServiceProto.SealTokenOrBuilder>(
                  token_,
                  getParentForChildren(),
                  isClean());
          token_ = null;
        }
        return tokenBuilder_;
      }

      // @@protoc_insertion_point(builder_scope:fm.lizhi.commons.template.datacenter.protocol.ResponseGetSealToken)
    }

    static {
      defaultInstance = new ResponseGetSealToken(true);
      defaultInstance.initFields();
    }

    // @@protoc_insertion_point(class_scope:fm.lizhi.commons.template.datacenter.protocol.ResponseGetSealToken)
  }

  public interface RequestGetUserInfoBySealTokenOrBuilder
      extends com.google.protobuf.MessageOrBuilder {

    // optional string token = 1;
    /**
     * <code>optional string token = 1;</code>
     *
     * <pre>
     *业务用户Id
     * </pre>
     */
    boolean hasToken();
    /**
     * <code>optional string token = 1;</code>
     *
     * <pre>
     *业务用户Id
     * </pre>
     */
    java.lang.String getToken();
    /**
     * <code>optional string token = 1;</code>
     *
     * <pre>
     *业务用户Id
     * </pre>
     */
    com.google.protobuf.ByteString
        getTokenBytes();

    // optional string appId = 2;
    /**
     * <code>optional string appId = 2;</code>
     *
     * <pre>
     *appId
     * </pre>
     */
    boolean hasAppId();
    /**
     * <code>optional string appId = 2;</code>
     *
     * <pre>
     *appId
     * </pre>
     */
    java.lang.String getAppId();
    /**
     * <code>optional string appId = 2;</code>
     *
     * <pre>
     *appId
     * </pre>
     */
    com.google.protobuf.ByteString
        getAppIdBytes();

    // optional string channel = 3;
    /**
     * <code>optional string channel = 3;</code>
     *
     * <pre>
     *游戏渠道
     * </pre>
     */
    boolean hasChannel();
    /**
     * <code>optional string channel = 3;</code>
     *
     * <pre>
     *游戏渠道
     * </pre>
     */
    java.lang.String getChannel();
    /**
     * <code>optional string channel = 3;</code>
     *
     * <pre>
     *游戏渠道
     * </pre>
     */
    com.google.protobuf.ByteString
        getChannelBytes();
  }
  /**
   * Protobuf type {@code fm.lizhi.commons.template.datacenter.protocol.RequestGetUserInfoBySealToken}
   *
   * <pre>
   * SealAuthService.java
   * 获取令牌信息
   * domain = 4302, op = 1000
   * </pre>
   */
  public static final class RequestGetUserInfoBySealToken extends
      com.google.protobuf.GeneratedMessage
      implements RequestGetUserInfoBySealTokenOrBuilder {
    // Use RequestGetUserInfoBySealToken.newBuilder() to construct.
    private RequestGetUserInfoBySealToken(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
      super(builder);
      this.unknownFields = builder.getUnknownFields();
    }
    private RequestGetUserInfoBySealToken(boolean noInit) { this.unknownFields = com.google.protobuf.UnknownFieldSet.getDefaultInstance(); }

    private static final RequestGetUserInfoBySealToken defaultInstance;
    public static RequestGetUserInfoBySealToken getDefaultInstance() {
      return defaultInstance;
    }

    public RequestGetUserInfoBySealToken getDefaultInstanceForType() {
      return defaultInstance;
    }

    private final com.google.protobuf.UnknownFieldSet unknownFields;
    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
        getUnknownFields() {
      return this.unknownFields;
    }
    private RequestGetUserInfoBySealToken(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      initFields();
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            default: {
              if (!parseUnknownField(input, unknownFields,
                                     extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
            case 10: {
              bitField0_ |= 0x00000001;
              token_ = input.readBytes();
              break;
            }
            case 18: {
              bitField0_ |= 0x00000002;
              appId_ = input.readBytes();
              break;
            }
            case 26: {
              bitField0_ |= 0x00000004;
              channel_ = input.readBytes();
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e.getMessage()).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return fm.lizhi.ocean.seal.protocol.SealAuthServiceProto.internal_static_fm_lizhi_commons_template_datacenter_protocol_RequestGetUserInfoBySealToken_descriptor;
    }

    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return fm.lizhi.ocean.seal.protocol.SealAuthServiceProto.internal_static_fm_lizhi_commons_template_datacenter_protocol_RequestGetUserInfoBySealToken_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              fm.lizhi.ocean.seal.protocol.SealAuthServiceProto.RequestGetUserInfoBySealToken.class, fm.lizhi.ocean.seal.protocol.SealAuthServiceProto.RequestGetUserInfoBySealToken.Builder.class);
    }

    public static com.google.protobuf.Parser<RequestGetUserInfoBySealToken> PARSER =
        new com.google.protobuf.AbstractParser<RequestGetUserInfoBySealToken>() {
      public RequestGetUserInfoBySealToken parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new RequestGetUserInfoBySealToken(input, extensionRegistry);
      }
    };

    @java.lang.Override
    public com.google.protobuf.Parser<RequestGetUserInfoBySealToken> getParserForType() {
      return PARSER;
    }

    private int bitField0_;
    // optional string token = 1;
    public static final int TOKEN_FIELD_NUMBER = 1;
    private java.lang.Object token_;
    /**
     * <code>optional string token = 1;</code>
     *
     * <pre>
     *业务用户Id
     * </pre>
     */
    public boolean hasToken() {
      return ((bitField0_ & 0x00000001) == 0x00000001);
    }
    /**
     * <code>optional string token = 1;</code>
     *
     * <pre>
     *业务用户Id
     * </pre>
     */
    public java.lang.String getToken() {
      java.lang.Object ref = token_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          token_ = s;
        }
        return s;
      }
    }
    /**
     * <code>optional string token = 1;</code>
     *
     * <pre>
     *业务用户Id
     * </pre>
     */
    public com.google.protobuf.ByteString
        getTokenBytes() {
      java.lang.Object ref = token_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        token_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    // optional string appId = 2;
    public static final int APPID_FIELD_NUMBER = 2;
    private java.lang.Object appId_;
    /**
     * <code>optional string appId = 2;</code>
     *
     * <pre>
     *appId
     * </pre>
     */
    public boolean hasAppId() {
      return ((bitField0_ & 0x00000002) == 0x00000002);
    }
    /**
     * <code>optional string appId = 2;</code>
     *
     * <pre>
     *appId
     * </pre>
     */
    public java.lang.String getAppId() {
      java.lang.Object ref = appId_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          appId_ = s;
        }
        return s;
      }
    }
    /**
     * <code>optional string appId = 2;</code>
     *
     * <pre>
     *appId
     * </pre>
     */
    public com.google.protobuf.ByteString
        getAppIdBytes() {
      java.lang.Object ref = appId_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        appId_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    // optional string channel = 3;
    public static final int CHANNEL_FIELD_NUMBER = 3;
    private java.lang.Object channel_;
    /**
     * <code>optional string channel = 3;</code>
     *
     * <pre>
     *游戏渠道
     * </pre>
     */
    public boolean hasChannel() {
      return ((bitField0_ & 0x00000004) == 0x00000004);
    }
    /**
     * <code>optional string channel = 3;</code>
     *
     * <pre>
     *游戏渠道
     * </pre>
     */
    public java.lang.String getChannel() {
      java.lang.Object ref = channel_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          channel_ = s;
        }
        return s;
      }
    }
    /**
     * <code>optional string channel = 3;</code>
     *
     * <pre>
     *游戏渠道
     * </pre>
     */
    public com.google.protobuf.ByteString
        getChannelBytes() {
      java.lang.Object ref = channel_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        channel_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    private void initFields() {
      token_ = "";
      appId_ = "";
      channel_ = "";
    }
    private byte memoizedIsInitialized = -1;
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized != -1) return isInitialized == 1;

      memoizedIsInitialized = 1;
      return true;
    }

    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      getSerializedSize();
      if (((bitField0_ & 0x00000001) == 0x00000001)) {
        output.writeBytes(1, getTokenBytes());
      }
      if (((bitField0_ & 0x00000002) == 0x00000002)) {
        output.writeBytes(2, getAppIdBytes());
      }
      if (((bitField0_ & 0x00000004) == 0x00000004)) {
        output.writeBytes(3, getChannelBytes());
      }
      getUnknownFields().writeTo(output);
    }

    private int memoizedSerializedSize = -1;
    public int getSerializedSize() {
      int size = memoizedSerializedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) == 0x00000001)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(1, getTokenBytes());
      }
      if (((bitField0_ & 0x00000002) == 0x00000002)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(2, getAppIdBytes());
      }
      if (((bitField0_ & 0x00000004) == 0x00000004)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(3, getChannelBytes());
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSerializedSize = size;
      return size;
    }

    private static final long serialVersionUID = 0L;
    @java.lang.Override
    protected java.lang.Object writeReplace()
        throws java.io.ObjectStreamException {
      return super.writeReplace();
    }

    public static fm.lizhi.ocean.seal.protocol.SealAuthServiceProto.RequestGetUserInfoBySealToken parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static fm.lizhi.ocean.seal.protocol.SealAuthServiceProto.RequestGetUserInfoBySealToken parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static fm.lizhi.ocean.seal.protocol.SealAuthServiceProto.RequestGetUserInfoBySealToken parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static fm.lizhi.ocean.seal.protocol.SealAuthServiceProto.RequestGetUserInfoBySealToken parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static fm.lizhi.ocean.seal.protocol.SealAuthServiceProto.RequestGetUserInfoBySealToken parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return PARSER.parseFrom(input);
    }
    public static fm.lizhi.ocean.seal.protocol.SealAuthServiceProto.RequestGetUserInfoBySealToken parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseFrom(input, extensionRegistry);
    }
    public static fm.lizhi.ocean.seal.protocol.SealAuthServiceProto.RequestGetUserInfoBySealToken parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return PARSER.parseDelimitedFrom(input);
    }
    public static fm.lizhi.ocean.seal.protocol.SealAuthServiceProto.RequestGetUserInfoBySealToken parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseDelimitedFrom(input, extensionRegistry);
    }
    public static fm.lizhi.ocean.seal.protocol.SealAuthServiceProto.RequestGetUserInfoBySealToken parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return PARSER.parseFrom(input);
    }
    public static fm.lizhi.ocean.seal.protocol.SealAuthServiceProto.RequestGetUserInfoBySealToken parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseFrom(input, extensionRegistry);
    }

    public static Builder newBuilder() { return Builder.create(); }
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder(fm.lizhi.ocean.seal.protocol.SealAuthServiceProto.RequestGetUserInfoBySealToken prototype) {
      return newBuilder().mergeFrom(prototype);
    }
    public Builder toBuilder() { return newBuilder(this); }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code fm.lizhi.commons.template.datacenter.protocol.RequestGetUserInfoBySealToken}
     *
     * <pre>
     * SealAuthService.java
     * 获取令牌信息
     * domain = 4302, op = 1000
     * </pre>
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessage.Builder<Builder>
       implements fm.lizhi.ocean.seal.protocol.SealAuthServiceProto.RequestGetUserInfoBySealTokenOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return fm.lizhi.ocean.seal.protocol.SealAuthServiceProto.internal_static_fm_lizhi_commons_template_datacenter_protocol_RequestGetUserInfoBySealToken_descriptor;
      }

      protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return fm.lizhi.ocean.seal.protocol.SealAuthServiceProto.internal_static_fm_lizhi_commons_template_datacenter_protocol_RequestGetUserInfoBySealToken_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                fm.lizhi.ocean.seal.protocol.SealAuthServiceProto.RequestGetUserInfoBySealToken.class, fm.lizhi.ocean.seal.protocol.SealAuthServiceProto.RequestGetUserInfoBySealToken.Builder.class);
      }

      // Construct using fm.lizhi.ocean.seal.protocol.SealAuthServiceProto.RequestGetUserInfoBySealToken.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessage.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessage.alwaysUseFieldBuilders) {
        }
      }
      private static Builder create() {
        return new Builder();
      }

      public Builder clear() {
        super.clear();
        token_ = "";
        bitField0_ = (bitField0_ & ~0x00000001);
        appId_ = "";
        bitField0_ = (bitField0_ & ~0x00000002);
        channel_ = "";
        bitField0_ = (bitField0_ & ~0x00000004);
        return this;
      }

      public Builder clone() {
        return create().mergeFrom(buildPartial());
      }

      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return fm.lizhi.ocean.seal.protocol.SealAuthServiceProto.internal_static_fm_lizhi_commons_template_datacenter_protocol_RequestGetUserInfoBySealToken_descriptor;
      }

      public fm.lizhi.ocean.seal.protocol.SealAuthServiceProto.RequestGetUserInfoBySealToken getDefaultInstanceForType() {
        return fm.lizhi.ocean.seal.protocol.SealAuthServiceProto.RequestGetUserInfoBySealToken.getDefaultInstance();
      }

      public fm.lizhi.ocean.seal.protocol.SealAuthServiceProto.RequestGetUserInfoBySealToken build() {
        fm.lizhi.ocean.seal.protocol.SealAuthServiceProto.RequestGetUserInfoBySealToken result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      public fm.lizhi.ocean.seal.protocol.SealAuthServiceProto.RequestGetUserInfoBySealToken buildPartial() {
        fm.lizhi.ocean.seal.protocol.SealAuthServiceProto.RequestGetUserInfoBySealToken result = new fm.lizhi.ocean.seal.protocol.SealAuthServiceProto.RequestGetUserInfoBySealToken(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) == 0x00000001)) {
          to_bitField0_ |= 0x00000001;
        }
        result.token_ = token_;
        if (((from_bitField0_ & 0x00000002) == 0x00000002)) {
          to_bitField0_ |= 0x00000002;
        }
        result.appId_ = appId_;
        if (((from_bitField0_ & 0x00000004) == 0x00000004)) {
          to_bitField0_ |= 0x00000004;
        }
        result.channel_ = channel_;
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof fm.lizhi.ocean.seal.protocol.SealAuthServiceProto.RequestGetUserInfoBySealToken) {
          return mergeFrom((fm.lizhi.ocean.seal.protocol.SealAuthServiceProto.RequestGetUserInfoBySealToken)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(fm.lizhi.ocean.seal.protocol.SealAuthServiceProto.RequestGetUserInfoBySealToken other) {
        if (other == fm.lizhi.ocean.seal.protocol.SealAuthServiceProto.RequestGetUserInfoBySealToken.getDefaultInstance()) return this;
        if (other.hasToken()) {
          bitField0_ |= 0x00000001;
          token_ = other.token_;
          onChanged();
        }
        if (other.hasAppId()) {
          bitField0_ |= 0x00000002;
          appId_ = other.appId_;
          onChanged();
        }
        if (other.hasChannel()) {
          bitField0_ |= 0x00000004;
          channel_ = other.channel_;
          onChanged();
        }
        this.mergeUnknownFields(other.getUnknownFields());
        return this;
      }

      public final boolean isInitialized() {
        return true;
      }

      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        fm.lizhi.ocean.seal.protocol.SealAuthServiceProto.RequestGetUserInfoBySealToken parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (fm.lizhi.ocean.seal.protocol.SealAuthServiceProto.RequestGetUserInfoBySealToken) e.getUnfinishedMessage();
          throw e;
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      // optional string token = 1;
      private java.lang.Object token_ = "";
      /**
       * <code>optional string token = 1;</code>
       *
       * <pre>
       *业务用户Id
       * </pre>
       */
      public boolean hasToken() {
        return ((bitField0_ & 0x00000001) == 0x00000001);
      }
      /**
       * <code>optional string token = 1;</code>
       *
       * <pre>
       *业务用户Id
       * </pre>
       */
      public java.lang.String getToken() {
        java.lang.Object ref = token_;
        if (!(ref instanceof java.lang.String)) {
          java.lang.String s = ((com.google.protobuf.ByteString) ref)
              .toStringUtf8();
          token_ = s;
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <code>optional string token = 1;</code>
       *
       * <pre>
       *业务用户Id
       * </pre>
       */
      public com.google.protobuf.ByteString
          getTokenBytes() {
        java.lang.Object ref = token_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          token_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <code>optional string token = 1;</code>
       *
       * <pre>
       *业务用户Id
       * </pre>
       */
      public Builder setToken(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000001;
        token_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional string token = 1;</code>
       *
       * <pre>
       *业务用户Id
       * </pre>
       */
      public Builder clearToken() {
        bitField0_ = (bitField0_ & ~0x00000001);
        token_ = getDefaultInstance().getToken();
        onChanged();
        return this;
      }
      /**
       * <code>optional string token = 1;</code>
       *
       * <pre>
       *业务用户Id
       * </pre>
       */
      public Builder setTokenBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000001;
        token_ = value;
        onChanged();
        return this;
      }

      // optional string appId = 2;
      private java.lang.Object appId_ = "";
      /**
       * <code>optional string appId = 2;</code>
       *
       * <pre>
       *appId
       * </pre>
       */
      public boolean hasAppId() {
        return ((bitField0_ & 0x00000002) == 0x00000002);
      }
      /**
       * <code>optional string appId = 2;</code>
       *
       * <pre>
       *appId
       * </pre>
       */
      public java.lang.String getAppId() {
        java.lang.Object ref = appId_;
        if (!(ref instanceof java.lang.String)) {
          java.lang.String s = ((com.google.protobuf.ByteString) ref)
              .toStringUtf8();
          appId_ = s;
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <code>optional string appId = 2;</code>
       *
       * <pre>
       *appId
       * </pre>
       */
      public com.google.protobuf.ByteString
          getAppIdBytes() {
        java.lang.Object ref = appId_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          appId_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <code>optional string appId = 2;</code>
       *
       * <pre>
       *appId
       * </pre>
       */
      public Builder setAppId(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000002;
        appId_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional string appId = 2;</code>
       *
       * <pre>
       *appId
       * </pre>
       */
      public Builder clearAppId() {
        bitField0_ = (bitField0_ & ~0x00000002);
        appId_ = getDefaultInstance().getAppId();
        onChanged();
        return this;
      }
      /**
       * <code>optional string appId = 2;</code>
       *
       * <pre>
       *appId
       * </pre>
       */
      public Builder setAppIdBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000002;
        appId_ = value;
        onChanged();
        return this;
      }

      // optional string channel = 3;
      private java.lang.Object channel_ = "";
      /**
       * <code>optional string channel = 3;</code>
       *
       * <pre>
       *游戏渠道
       * </pre>
       */
      public boolean hasChannel() {
        return ((bitField0_ & 0x00000004) == 0x00000004);
      }
      /**
       * <code>optional string channel = 3;</code>
       *
       * <pre>
       *游戏渠道
       * </pre>
       */
      public java.lang.String getChannel() {
        java.lang.Object ref = channel_;
        if (!(ref instanceof java.lang.String)) {
          java.lang.String s = ((com.google.protobuf.ByteString) ref)
              .toStringUtf8();
          channel_ = s;
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <code>optional string channel = 3;</code>
       *
       * <pre>
       *游戏渠道
       * </pre>
       */
      public com.google.protobuf.ByteString
          getChannelBytes() {
        java.lang.Object ref = channel_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          channel_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <code>optional string channel = 3;</code>
       *
       * <pre>
       *游戏渠道
       * </pre>
       */
      public Builder setChannel(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000004;
        channel_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional string channel = 3;</code>
       *
       * <pre>
       *游戏渠道
       * </pre>
       */
      public Builder clearChannel() {
        bitField0_ = (bitField0_ & ~0x00000004);
        channel_ = getDefaultInstance().getChannel();
        onChanged();
        return this;
      }
      /**
       * <code>optional string channel = 3;</code>
       *
       * <pre>
       *游戏渠道
       * </pre>
       */
      public Builder setChannelBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000004;
        channel_ = value;
        onChanged();
        return this;
      }

      // @@protoc_insertion_point(builder_scope:fm.lizhi.commons.template.datacenter.protocol.RequestGetUserInfoBySealToken)
    }

    static {
      defaultInstance = new RequestGetUserInfoBySealToken(true);
      defaultInstance.initFields();
    }

    // @@protoc_insertion_point(class_scope:fm.lizhi.commons.template.datacenter.protocol.RequestGetUserInfoBySealToken)
  }

  public interface ResponseGetUserInfoBySealTokenOrBuilder
      extends com.google.protobuf.MessageOrBuilder {

    // optional .fm.lizhi.commons.template.datacenter.protocol.JwtUserInfo sealUser = 1;
    /**
     * <code>optional .fm.lizhi.commons.template.datacenter.protocol.JwtUserInfo sealUser = 1;</code>
     */
    boolean hasSealUser();
    /**
     * <code>optional .fm.lizhi.commons.template.datacenter.protocol.JwtUserInfo sealUser = 1;</code>
     */
    fm.lizhi.ocean.seal.protocol.SealAuthServiceProto.JwtUserInfo getSealUser();
    /**
     * <code>optional .fm.lizhi.commons.template.datacenter.protocol.JwtUserInfo sealUser = 1;</code>
     */
    fm.lizhi.ocean.seal.protocol.SealAuthServiceProto.JwtUserInfoOrBuilder getSealUserOrBuilder();
  }
  /**
   * Protobuf type {@code fm.lizhi.commons.template.datacenter.protocol.ResponseGetUserInfoBySealToken}
   *
   * <pre>
   * rcode == 0(SUCCESS) = 执行成功
   * rcode == 1(FAIL) = 无效Token
   * rcode == 2(ILLEGAL_PARAMS) = 参数非法
   * rcode == 1001(TOKEN_EXPIRED) = Token过期
   * </pre>
   */
  public static final class ResponseGetUserInfoBySealToken extends
      com.google.protobuf.GeneratedMessage
      implements ResponseGetUserInfoBySealTokenOrBuilder {
    // Use ResponseGetUserInfoBySealToken.newBuilder() to construct.
    private ResponseGetUserInfoBySealToken(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
      super(builder);
      this.unknownFields = builder.getUnknownFields();
    }
    private ResponseGetUserInfoBySealToken(boolean noInit) { this.unknownFields = com.google.protobuf.UnknownFieldSet.getDefaultInstance(); }

    private static final ResponseGetUserInfoBySealToken defaultInstance;
    public static ResponseGetUserInfoBySealToken getDefaultInstance() {
      return defaultInstance;
    }

    public ResponseGetUserInfoBySealToken getDefaultInstanceForType() {
      return defaultInstance;
    }

    private final com.google.protobuf.UnknownFieldSet unknownFields;
    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
        getUnknownFields() {
      return this.unknownFields;
    }
    private ResponseGetUserInfoBySealToken(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      initFields();
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            default: {
              if (!parseUnknownField(input, unknownFields,
                                     extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
            case 10: {
              fm.lizhi.ocean.seal.protocol.SealAuthServiceProto.JwtUserInfo.Builder subBuilder = null;
              if (((bitField0_ & 0x00000001) == 0x00000001)) {
                subBuilder = sealUser_.toBuilder();
              }
              sealUser_ = input.readMessage(fm.lizhi.ocean.seal.protocol.SealAuthServiceProto.JwtUserInfo.PARSER, extensionRegistry);
              if (subBuilder != null) {
                subBuilder.mergeFrom(sealUser_);
                sealUser_ = subBuilder.buildPartial();
              }
              bitField0_ |= 0x00000001;
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e.getMessage()).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return fm.lizhi.ocean.seal.protocol.SealAuthServiceProto.internal_static_fm_lizhi_commons_template_datacenter_protocol_ResponseGetUserInfoBySealToken_descriptor;
    }

    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return fm.lizhi.ocean.seal.protocol.SealAuthServiceProto.internal_static_fm_lizhi_commons_template_datacenter_protocol_ResponseGetUserInfoBySealToken_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              fm.lizhi.ocean.seal.protocol.SealAuthServiceProto.ResponseGetUserInfoBySealToken.class, fm.lizhi.ocean.seal.protocol.SealAuthServiceProto.ResponseGetUserInfoBySealToken.Builder.class);
    }

    public static com.google.protobuf.Parser<ResponseGetUserInfoBySealToken> PARSER =
        new com.google.protobuf.AbstractParser<ResponseGetUserInfoBySealToken>() {
      public ResponseGetUserInfoBySealToken parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new ResponseGetUserInfoBySealToken(input, extensionRegistry);
      }
    };

    @java.lang.Override
    public com.google.protobuf.Parser<ResponseGetUserInfoBySealToken> getParserForType() {
      return PARSER;
    }

    private int bitField0_;
    // optional .fm.lizhi.commons.template.datacenter.protocol.JwtUserInfo sealUser = 1;
    public static final int SEALUSER_FIELD_NUMBER = 1;
    private fm.lizhi.ocean.seal.protocol.SealAuthServiceProto.JwtUserInfo sealUser_;
    /**
     * <code>optional .fm.lizhi.commons.template.datacenter.protocol.JwtUserInfo sealUser = 1;</code>
     */
    public boolean hasSealUser() {
      return ((bitField0_ & 0x00000001) == 0x00000001);
    }
    /**
     * <code>optional .fm.lizhi.commons.template.datacenter.protocol.JwtUserInfo sealUser = 1;</code>
     */
    public fm.lizhi.ocean.seal.protocol.SealAuthServiceProto.JwtUserInfo getSealUser() {
      return sealUser_;
    }
    /**
     * <code>optional .fm.lizhi.commons.template.datacenter.protocol.JwtUserInfo sealUser = 1;</code>
     */
    public fm.lizhi.ocean.seal.protocol.SealAuthServiceProto.JwtUserInfoOrBuilder getSealUserOrBuilder() {
      return sealUser_;
    }

    private void initFields() {
      sealUser_ = fm.lizhi.ocean.seal.protocol.SealAuthServiceProto.JwtUserInfo.getDefaultInstance();
    }
    private byte memoizedIsInitialized = -1;
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized != -1) return isInitialized == 1;

      memoizedIsInitialized = 1;
      return true;
    }

    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      getSerializedSize();
      if (((bitField0_ & 0x00000001) == 0x00000001)) {
        output.writeMessage(1, sealUser_);
      }
      getUnknownFields().writeTo(output);
    }

    private int memoizedSerializedSize = -1;
    public int getSerializedSize() {
      int size = memoizedSerializedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) == 0x00000001)) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(1, sealUser_);
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSerializedSize = size;
      return size;
    }

    private static final long serialVersionUID = 0L;
    @java.lang.Override
    protected java.lang.Object writeReplace()
        throws java.io.ObjectStreamException {
      return super.writeReplace();
    }

    public static fm.lizhi.ocean.seal.protocol.SealAuthServiceProto.ResponseGetUserInfoBySealToken parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static fm.lizhi.ocean.seal.protocol.SealAuthServiceProto.ResponseGetUserInfoBySealToken parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static fm.lizhi.ocean.seal.protocol.SealAuthServiceProto.ResponseGetUserInfoBySealToken parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static fm.lizhi.ocean.seal.protocol.SealAuthServiceProto.ResponseGetUserInfoBySealToken parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static fm.lizhi.ocean.seal.protocol.SealAuthServiceProto.ResponseGetUserInfoBySealToken parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return PARSER.parseFrom(input);
    }
    public static fm.lizhi.ocean.seal.protocol.SealAuthServiceProto.ResponseGetUserInfoBySealToken parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseFrom(input, extensionRegistry);
    }
    public static fm.lizhi.ocean.seal.protocol.SealAuthServiceProto.ResponseGetUserInfoBySealToken parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return PARSER.parseDelimitedFrom(input);
    }
    public static fm.lizhi.ocean.seal.protocol.SealAuthServiceProto.ResponseGetUserInfoBySealToken parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseDelimitedFrom(input, extensionRegistry);
    }
    public static fm.lizhi.ocean.seal.protocol.SealAuthServiceProto.ResponseGetUserInfoBySealToken parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return PARSER.parseFrom(input);
    }
    public static fm.lizhi.ocean.seal.protocol.SealAuthServiceProto.ResponseGetUserInfoBySealToken parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseFrom(input, extensionRegistry);
    }

    public static Builder newBuilder() { return Builder.create(); }
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder(fm.lizhi.ocean.seal.protocol.SealAuthServiceProto.ResponseGetUserInfoBySealToken prototype) {
      return newBuilder().mergeFrom(prototype);
    }
    public Builder toBuilder() { return newBuilder(this); }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code fm.lizhi.commons.template.datacenter.protocol.ResponseGetUserInfoBySealToken}
     *
     * <pre>
     * rcode == 0(SUCCESS) = 执行成功
     * rcode == 1(FAIL) = 无效Token
     * rcode == 2(ILLEGAL_PARAMS) = 参数非法
     * rcode == 1001(TOKEN_EXPIRED) = Token过期
     * </pre>
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessage.Builder<Builder>
       implements fm.lizhi.ocean.seal.protocol.SealAuthServiceProto.ResponseGetUserInfoBySealTokenOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return fm.lizhi.ocean.seal.protocol.SealAuthServiceProto.internal_static_fm_lizhi_commons_template_datacenter_protocol_ResponseGetUserInfoBySealToken_descriptor;
      }

      protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return fm.lizhi.ocean.seal.protocol.SealAuthServiceProto.internal_static_fm_lizhi_commons_template_datacenter_protocol_ResponseGetUserInfoBySealToken_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                fm.lizhi.ocean.seal.protocol.SealAuthServiceProto.ResponseGetUserInfoBySealToken.class, fm.lizhi.ocean.seal.protocol.SealAuthServiceProto.ResponseGetUserInfoBySealToken.Builder.class);
      }

      // Construct using fm.lizhi.ocean.seal.protocol.SealAuthServiceProto.ResponseGetUserInfoBySealToken.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessage.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessage.alwaysUseFieldBuilders) {
          getSealUserFieldBuilder();
        }
      }
      private static Builder create() {
        return new Builder();
      }

      public Builder clear() {
        super.clear();
        if (sealUserBuilder_ == null) {
          sealUser_ = fm.lizhi.ocean.seal.protocol.SealAuthServiceProto.JwtUserInfo.getDefaultInstance();
        } else {
          sealUserBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000001);
        return this;
      }

      public Builder clone() {
        return create().mergeFrom(buildPartial());
      }

      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return fm.lizhi.ocean.seal.protocol.SealAuthServiceProto.internal_static_fm_lizhi_commons_template_datacenter_protocol_ResponseGetUserInfoBySealToken_descriptor;
      }

      public fm.lizhi.ocean.seal.protocol.SealAuthServiceProto.ResponseGetUserInfoBySealToken getDefaultInstanceForType() {
        return fm.lizhi.ocean.seal.protocol.SealAuthServiceProto.ResponseGetUserInfoBySealToken.getDefaultInstance();
      }

      public fm.lizhi.ocean.seal.protocol.SealAuthServiceProto.ResponseGetUserInfoBySealToken build() {
        fm.lizhi.ocean.seal.protocol.SealAuthServiceProto.ResponseGetUserInfoBySealToken result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      public fm.lizhi.ocean.seal.protocol.SealAuthServiceProto.ResponseGetUserInfoBySealToken buildPartial() {
        fm.lizhi.ocean.seal.protocol.SealAuthServiceProto.ResponseGetUserInfoBySealToken result = new fm.lizhi.ocean.seal.protocol.SealAuthServiceProto.ResponseGetUserInfoBySealToken(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) == 0x00000001)) {
          to_bitField0_ |= 0x00000001;
        }
        if (sealUserBuilder_ == null) {
          result.sealUser_ = sealUser_;
        } else {
          result.sealUser_ = sealUserBuilder_.build();
        }
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof fm.lizhi.ocean.seal.protocol.SealAuthServiceProto.ResponseGetUserInfoBySealToken) {
          return mergeFrom((fm.lizhi.ocean.seal.protocol.SealAuthServiceProto.ResponseGetUserInfoBySealToken)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(fm.lizhi.ocean.seal.protocol.SealAuthServiceProto.ResponseGetUserInfoBySealToken other) {
        if (other == fm.lizhi.ocean.seal.protocol.SealAuthServiceProto.ResponseGetUserInfoBySealToken.getDefaultInstance()) return this;
        if (other.hasSealUser()) {
          mergeSealUser(other.getSealUser());
        }
        this.mergeUnknownFields(other.getUnknownFields());
        return this;
      }

      public final boolean isInitialized() {
        return true;
      }

      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        fm.lizhi.ocean.seal.protocol.SealAuthServiceProto.ResponseGetUserInfoBySealToken parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (fm.lizhi.ocean.seal.protocol.SealAuthServiceProto.ResponseGetUserInfoBySealToken) e.getUnfinishedMessage();
          throw e;
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      // optional .fm.lizhi.commons.template.datacenter.protocol.JwtUserInfo sealUser = 1;
      private fm.lizhi.ocean.seal.protocol.SealAuthServiceProto.JwtUserInfo sealUser_ = fm.lizhi.ocean.seal.protocol.SealAuthServiceProto.JwtUserInfo.getDefaultInstance();
      private com.google.protobuf.SingleFieldBuilder<
          fm.lizhi.ocean.seal.protocol.SealAuthServiceProto.JwtUserInfo, fm.lizhi.ocean.seal.protocol.SealAuthServiceProto.JwtUserInfo.Builder, fm.lizhi.ocean.seal.protocol.SealAuthServiceProto.JwtUserInfoOrBuilder> sealUserBuilder_;
      /**
       * <code>optional .fm.lizhi.commons.template.datacenter.protocol.JwtUserInfo sealUser = 1;</code>
       */
      public boolean hasSealUser() {
        return ((bitField0_ & 0x00000001) == 0x00000001);
      }
      /**
       * <code>optional .fm.lizhi.commons.template.datacenter.protocol.JwtUserInfo sealUser = 1;</code>
       */
      public fm.lizhi.ocean.seal.protocol.SealAuthServiceProto.JwtUserInfo getSealUser() {
        if (sealUserBuilder_ == null) {
          return sealUser_;
        } else {
          return sealUserBuilder_.getMessage();
        }
      }
      /**
       * <code>optional .fm.lizhi.commons.template.datacenter.protocol.JwtUserInfo sealUser = 1;</code>
       */
      public Builder setSealUser(fm.lizhi.ocean.seal.protocol.SealAuthServiceProto.JwtUserInfo value) {
        if (sealUserBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          sealUser_ = value;
          onChanged();
        } else {
          sealUserBuilder_.setMessage(value);
        }
        bitField0_ |= 0x00000001;
        return this;
      }
      /**
       * <code>optional .fm.lizhi.commons.template.datacenter.protocol.JwtUserInfo sealUser = 1;</code>
       */
      public Builder setSealUser(
          fm.lizhi.ocean.seal.protocol.SealAuthServiceProto.JwtUserInfo.Builder builderForValue) {
        if (sealUserBuilder_ == null) {
          sealUser_ = builderForValue.build();
          onChanged();
        } else {
          sealUserBuilder_.setMessage(builderForValue.build());
        }
        bitField0_ |= 0x00000001;
        return this;
      }
      /**
       * <code>optional .fm.lizhi.commons.template.datacenter.protocol.JwtUserInfo sealUser = 1;</code>
       */
      public Builder mergeSealUser(fm.lizhi.ocean.seal.protocol.SealAuthServiceProto.JwtUserInfo value) {
        if (sealUserBuilder_ == null) {
          if (((bitField0_ & 0x00000001) == 0x00000001) &&
              sealUser_ != fm.lizhi.ocean.seal.protocol.SealAuthServiceProto.JwtUserInfo.getDefaultInstance()) {
            sealUser_ =
              fm.lizhi.ocean.seal.protocol.SealAuthServiceProto.JwtUserInfo.newBuilder(sealUser_).mergeFrom(value).buildPartial();
          } else {
            sealUser_ = value;
          }
          onChanged();
        } else {
          sealUserBuilder_.mergeFrom(value);
        }
        bitField0_ |= 0x00000001;
        return this;
      }
      /**
       * <code>optional .fm.lizhi.commons.template.datacenter.protocol.JwtUserInfo sealUser = 1;</code>
       */
      public Builder clearSealUser() {
        if (sealUserBuilder_ == null) {
          sealUser_ = fm.lizhi.ocean.seal.protocol.SealAuthServiceProto.JwtUserInfo.getDefaultInstance();
          onChanged();
        } else {
          sealUserBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000001);
        return this;
      }
      /**
       * <code>optional .fm.lizhi.commons.template.datacenter.protocol.JwtUserInfo sealUser = 1;</code>
       */
      public fm.lizhi.ocean.seal.protocol.SealAuthServiceProto.JwtUserInfo.Builder getSealUserBuilder() {
        bitField0_ |= 0x00000001;
        onChanged();
        return getSealUserFieldBuilder().getBuilder();
      }
      /**
       * <code>optional .fm.lizhi.commons.template.datacenter.protocol.JwtUserInfo sealUser = 1;</code>
       */
      public fm.lizhi.ocean.seal.protocol.SealAuthServiceProto.JwtUserInfoOrBuilder getSealUserOrBuilder() {
        if (sealUserBuilder_ != null) {
          return sealUserBuilder_.getMessageOrBuilder();
        } else {
          return sealUser_;
        }
      }
      /**
       * <code>optional .fm.lizhi.commons.template.datacenter.protocol.JwtUserInfo sealUser = 1;</code>
       */
      private com.google.protobuf.SingleFieldBuilder<
          fm.lizhi.ocean.seal.protocol.SealAuthServiceProto.JwtUserInfo, fm.lizhi.ocean.seal.protocol.SealAuthServiceProto.JwtUserInfo.Builder, fm.lizhi.ocean.seal.protocol.SealAuthServiceProto.JwtUserInfoOrBuilder> 
          getSealUserFieldBuilder() {
        if (sealUserBuilder_ == null) {
          sealUserBuilder_ = new com.google.protobuf.SingleFieldBuilder<
              fm.lizhi.ocean.seal.protocol.SealAuthServiceProto.JwtUserInfo, fm.lizhi.ocean.seal.protocol.SealAuthServiceProto.JwtUserInfo.Builder, fm.lizhi.ocean.seal.protocol.SealAuthServiceProto.JwtUserInfoOrBuilder>(
                  sealUser_,
                  getParentForChildren(),
                  isClean());
          sealUser_ = null;
        }
        return sealUserBuilder_;
      }

      // @@protoc_insertion_point(builder_scope:fm.lizhi.commons.template.datacenter.protocol.ResponseGetUserInfoBySealToken)
    }

    static {
      defaultInstance = new ResponseGetUserInfoBySealToken(true);
      defaultInstance.initFields();
    }

    // @@protoc_insertion_point(class_scope:fm.lizhi.commons.template.datacenter.protocol.ResponseGetUserInfoBySealToken)
  }

  public interface RequestGetRefreshSealTokenOrBuilder
      extends com.google.protobuf.MessageOrBuilder {

    // optional string token = 1;
    /**
     * <code>optional string token = 1;</code>
     *
     * <pre>
     *业务用户Id
     * </pre>
     */
    boolean hasToken();
    /**
     * <code>optional string token = 1;</code>
     *
     * <pre>
     *业务用户Id
     * </pre>
     */
    java.lang.String getToken();
    /**
     * <code>optional string token = 1;</code>
     *
     * <pre>
     *业务用户Id
     * </pre>
     */
    com.google.protobuf.ByteString
        getTokenBytes();

    // optional string channel = 2;
    /**
     * <code>optional string channel = 2;</code>
     *
     * <pre>
     *游戏渠道
     * </pre>
     */
    boolean hasChannel();
    /**
     * <code>optional string channel = 2;</code>
     *
     * <pre>
     *游戏渠道
     * </pre>
     */
    java.lang.String getChannel();
    /**
     * <code>optional string channel = 2;</code>
     *
     * <pre>
     *游戏渠道
     * </pre>
     */
    com.google.protobuf.ByteString
        getChannelBytes();
  }
  /**
   * Protobuf type {@code fm.lizhi.commons.template.datacenter.protocol.RequestGetRefreshSealToken}
   *
   * <pre>
   * SealAuthService.java
   * 获取长期令牌，默认时长2天
   * domain = 4302, op = 1001
   * </pre>
   */
  public static final class RequestGetRefreshSealToken extends
      com.google.protobuf.GeneratedMessage
      implements RequestGetRefreshSealTokenOrBuilder {
    // Use RequestGetRefreshSealToken.newBuilder() to construct.
    private RequestGetRefreshSealToken(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
      super(builder);
      this.unknownFields = builder.getUnknownFields();
    }
    private RequestGetRefreshSealToken(boolean noInit) { this.unknownFields = com.google.protobuf.UnknownFieldSet.getDefaultInstance(); }

    private static final RequestGetRefreshSealToken defaultInstance;
    public static RequestGetRefreshSealToken getDefaultInstance() {
      return defaultInstance;
    }

    public RequestGetRefreshSealToken getDefaultInstanceForType() {
      return defaultInstance;
    }

    private final com.google.protobuf.UnknownFieldSet unknownFields;
    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
        getUnknownFields() {
      return this.unknownFields;
    }
    private RequestGetRefreshSealToken(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      initFields();
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            default: {
              if (!parseUnknownField(input, unknownFields,
                                     extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
            case 10: {
              bitField0_ |= 0x00000001;
              token_ = input.readBytes();
              break;
            }
            case 18: {
              bitField0_ |= 0x00000002;
              channel_ = input.readBytes();
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e.getMessage()).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return fm.lizhi.ocean.seal.protocol.SealAuthServiceProto.internal_static_fm_lizhi_commons_template_datacenter_protocol_RequestGetRefreshSealToken_descriptor;
    }

    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return fm.lizhi.ocean.seal.protocol.SealAuthServiceProto.internal_static_fm_lizhi_commons_template_datacenter_protocol_RequestGetRefreshSealToken_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              fm.lizhi.ocean.seal.protocol.SealAuthServiceProto.RequestGetRefreshSealToken.class, fm.lizhi.ocean.seal.protocol.SealAuthServiceProto.RequestGetRefreshSealToken.Builder.class);
    }

    public static com.google.protobuf.Parser<RequestGetRefreshSealToken> PARSER =
        new com.google.protobuf.AbstractParser<RequestGetRefreshSealToken>() {
      public RequestGetRefreshSealToken parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new RequestGetRefreshSealToken(input, extensionRegistry);
      }
    };

    @java.lang.Override
    public com.google.protobuf.Parser<RequestGetRefreshSealToken> getParserForType() {
      return PARSER;
    }

    private int bitField0_;
    // optional string token = 1;
    public static final int TOKEN_FIELD_NUMBER = 1;
    private java.lang.Object token_;
    /**
     * <code>optional string token = 1;</code>
     *
     * <pre>
     *业务用户Id
     * </pre>
     */
    public boolean hasToken() {
      return ((bitField0_ & 0x00000001) == 0x00000001);
    }
    /**
     * <code>optional string token = 1;</code>
     *
     * <pre>
     *业务用户Id
     * </pre>
     */
    public java.lang.String getToken() {
      java.lang.Object ref = token_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          token_ = s;
        }
        return s;
      }
    }
    /**
     * <code>optional string token = 1;</code>
     *
     * <pre>
     *业务用户Id
     * </pre>
     */
    public com.google.protobuf.ByteString
        getTokenBytes() {
      java.lang.Object ref = token_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        token_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    // optional string channel = 2;
    public static final int CHANNEL_FIELD_NUMBER = 2;
    private java.lang.Object channel_;
    /**
     * <code>optional string channel = 2;</code>
     *
     * <pre>
     *游戏渠道
     * </pre>
     */
    public boolean hasChannel() {
      return ((bitField0_ & 0x00000002) == 0x00000002);
    }
    /**
     * <code>optional string channel = 2;</code>
     *
     * <pre>
     *游戏渠道
     * </pre>
     */
    public java.lang.String getChannel() {
      java.lang.Object ref = channel_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          channel_ = s;
        }
        return s;
      }
    }
    /**
     * <code>optional string channel = 2;</code>
     *
     * <pre>
     *游戏渠道
     * </pre>
     */
    public com.google.protobuf.ByteString
        getChannelBytes() {
      java.lang.Object ref = channel_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        channel_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    private void initFields() {
      token_ = "";
      channel_ = "";
    }
    private byte memoizedIsInitialized = -1;
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized != -1) return isInitialized == 1;

      memoizedIsInitialized = 1;
      return true;
    }

    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      getSerializedSize();
      if (((bitField0_ & 0x00000001) == 0x00000001)) {
        output.writeBytes(1, getTokenBytes());
      }
      if (((bitField0_ & 0x00000002) == 0x00000002)) {
        output.writeBytes(2, getChannelBytes());
      }
      getUnknownFields().writeTo(output);
    }

    private int memoizedSerializedSize = -1;
    public int getSerializedSize() {
      int size = memoizedSerializedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) == 0x00000001)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(1, getTokenBytes());
      }
      if (((bitField0_ & 0x00000002) == 0x00000002)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(2, getChannelBytes());
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSerializedSize = size;
      return size;
    }

    private static final long serialVersionUID = 0L;
    @java.lang.Override
    protected java.lang.Object writeReplace()
        throws java.io.ObjectStreamException {
      return super.writeReplace();
    }

    public static fm.lizhi.ocean.seal.protocol.SealAuthServiceProto.RequestGetRefreshSealToken parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static fm.lizhi.ocean.seal.protocol.SealAuthServiceProto.RequestGetRefreshSealToken parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static fm.lizhi.ocean.seal.protocol.SealAuthServiceProto.RequestGetRefreshSealToken parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static fm.lizhi.ocean.seal.protocol.SealAuthServiceProto.RequestGetRefreshSealToken parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static fm.lizhi.ocean.seal.protocol.SealAuthServiceProto.RequestGetRefreshSealToken parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return PARSER.parseFrom(input);
    }
    public static fm.lizhi.ocean.seal.protocol.SealAuthServiceProto.RequestGetRefreshSealToken parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseFrom(input, extensionRegistry);
    }
    public static fm.lizhi.ocean.seal.protocol.SealAuthServiceProto.RequestGetRefreshSealToken parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return PARSER.parseDelimitedFrom(input);
    }
    public static fm.lizhi.ocean.seal.protocol.SealAuthServiceProto.RequestGetRefreshSealToken parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseDelimitedFrom(input, extensionRegistry);
    }
    public static fm.lizhi.ocean.seal.protocol.SealAuthServiceProto.RequestGetRefreshSealToken parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return PARSER.parseFrom(input);
    }
    public static fm.lizhi.ocean.seal.protocol.SealAuthServiceProto.RequestGetRefreshSealToken parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseFrom(input, extensionRegistry);
    }

    public static Builder newBuilder() { return Builder.create(); }
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder(fm.lizhi.ocean.seal.protocol.SealAuthServiceProto.RequestGetRefreshSealToken prototype) {
      return newBuilder().mergeFrom(prototype);
    }
    public Builder toBuilder() { return newBuilder(this); }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code fm.lizhi.commons.template.datacenter.protocol.RequestGetRefreshSealToken}
     *
     * <pre>
     * SealAuthService.java
     * 获取长期令牌，默认时长2天
     * domain = 4302, op = 1001
     * </pre>
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessage.Builder<Builder>
       implements fm.lizhi.ocean.seal.protocol.SealAuthServiceProto.RequestGetRefreshSealTokenOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return fm.lizhi.ocean.seal.protocol.SealAuthServiceProto.internal_static_fm_lizhi_commons_template_datacenter_protocol_RequestGetRefreshSealToken_descriptor;
      }

      protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return fm.lizhi.ocean.seal.protocol.SealAuthServiceProto.internal_static_fm_lizhi_commons_template_datacenter_protocol_RequestGetRefreshSealToken_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                fm.lizhi.ocean.seal.protocol.SealAuthServiceProto.RequestGetRefreshSealToken.class, fm.lizhi.ocean.seal.protocol.SealAuthServiceProto.RequestGetRefreshSealToken.Builder.class);
      }

      // Construct using fm.lizhi.ocean.seal.protocol.SealAuthServiceProto.RequestGetRefreshSealToken.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessage.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessage.alwaysUseFieldBuilders) {
        }
      }
      private static Builder create() {
        return new Builder();
      }

      public Builder clear() {
        super.clear();
        token_ = "";
        bitField0_ = (bitField0_ & ~0x00000001);
        channel_ = "";
        bitField0_ = (bitField0_ & ~0x00000002);
        return this;
      }

      public Builder clone() {
        return create().mergeFrom(buildPartial());
      }

      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return fm.lizhi.ocean.seal.protocol.SealAuthServiceProto.internal_static_fm_lizhi_commons_template_datacenter_protocol_RequestGetRefreshSealToken_descriptor;
      }

      public fm.lizhi.ocean.seal.protocol.SealAuthServiceProto.RequestGetRefreshSealToken getDefaultInstanceForType() {
        return fm.lizhi.ocean.seal.protocol.SealAuthServiceProto.RequestGetRefreshSealToken.getDefaultInstance();
      }

      public fm.lizhi.ocean.seal.protocol.SealAuthServiceProto.RequestGetRefreshSealToken build() {
        fm.lizhi.ocean.seal.protocol.SealAuthServiceProto.RequestGetRefreshSealToken result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      public fm.lizhi.ocean.seal.protocol.SealAuthServiceProto.RequestGetRefreshSealToken buildPartial() {
        fm.lizhi.ocean.seal.protocol.SealAuthServiceProto.RequestGetRefreshSealToken result = new fm.lizhi.ocean.seal.protocol.SealAuthServiceProto.RequestGetRefreshSealToken(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) == 0x00000001)) {
          to_bitField0_ |= 0x00000001;
        }
        result.token_ = token_;
        if (((from_bitField0_ & 0x00000002) == 0x00000002)) {
          to_bitField0_ |= 0x00000002;
        }
        result.channel_ = channel_;
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof fm.lizhi.ocean.seal.protocol.SealAuthServiceProto.RequestGetRefreshSealToken) {
          return mergeFrom((fm.lizhi.ocean.seal.protocol.SealAuthServiceProto.RequestGetRefreshSealToken)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(fm.lizhi.ocean.seal.protocol.SealAuthServiceProto.RequestGetRefreshSealToken other) {
        if (other == fm.lizhi.ocean.seal.protocol.SealAuthServiceProto.RequestGetRefreshSealToken.getDefaultInstance()) return this;
        if (other.hasToken()) {
          bitField0_ |= 0x00000001;
          token_ = other.token_;
          onChanged();
        }
        if (other.hasChannel()) {
          bitField0_ |= 0x00000002;
          channel_ = other.channel_;
          onChanged();
        }
        this.mergeUnknownFields(other.getUnknownFields());
        return this;
      }

      public final boolean isInitialized() {
        return true;
      }

      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        fm.lizhi.ocean.seal.protocol.SealAuthServiceProto.RequestGetRefreshSealToken parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (fm.lizhi.ocean.seal.protocol.SealAuthServiceProto.RequestGetRefreshSealToken) e.getUnfinishedMessage();
          throw e;
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      // optional string token = 1;
      private java.lang.Object token_ = "";
      /**
       * <code>optional string token = 1;</code>
       *
       * <pre>
       *业务用户Id
       * </pre>
       */
      public boolean hasToken() {
        return ((bitField0_ & 0x00000001) == 0x00000001);
      }
      /**
       * <code>optional string token = 1;</code>
       *
       * <pre>
       *业务用户Id
       * </pre>
       */
      public java.lang.String getToken() {
        java.lang.Object ref = token_;
        if (!(ref instanceof java.lang.String)) {
          java.lang.String s = ((com.google.protobuf.ByteString) ref)
              .toStringUtf8();
          token_ = s;
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <code>optional string token = 1;</code>
       *
       * <pre>
       *业务用户Id
       * </pre>
       */
      public com.google.protobuf.ByteString
          getTokenBytes() {
        java.lang.Object ref = token_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          token_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <code>optional string token = 1;</code>
       *
       * <pre>
       *业务用户Id
       * </pre>
       */
      public Builder setToken(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000001;
        token_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional string token = 1;</code>
       *
       * <pre>
       *业务用户Id
       * </pre>
       */
      public Builder clearToken() {
        bitField0_ = (bitField0_ & ~0x00000001);
        token_ = getDefaultInstance().getToken();
        onChanged();
        return this;
      }
      /**
       * <code>optional string token = 1;</code>
       *
       * <pre>
       *业务用户Id
       * </pre>
       */
      public Builder setTokenBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000001;
        token_ = value;
        onChanged();
        return this;
      }

      // optional string channel = 2;
      private java.lang.Object channel_ = "";
      /**
       * <code>optional string channel = 2;</code>
       *
       * <pre>
       *游戏渠道
       * </pre>
       */
      public boolean hasChannel() {
        return ((bitField0_ & 0x00000002) == 0x00000002);
      }
      /**
       * <code>optional string channel = 2;</code>
       *
       * <pre>
       *游戏渠道
       * </pre>
       */
      public java.lang.String getChannel() {
        java.lang.Object ref = channel_;
        if (!(ref instanceof java.lang.String)) {
          java.lang.String s = ((com.google.protobuf.ByteString) ref)
              .toStringUtf8();
          channel_ = s;
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <code>optional string channel = 2;</code>
       *
       * <pre>
       *游戏渠道
       * </pre>
       */
      public com.google.protobuf.ByteString
          getChannelBytes() {
        java.lang.Object ref = channel_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          channel_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <code>optional string channel = 2;</code>
       *
       * <pre>
       *游戏渠道
       * </pre>
       */
      public Builder setChannel(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000002;
        channel_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional string channel = 2;</code>
       *
       * <pre>
       *游戏渠道
       * </pre>
       */
      public Builder clearChannel() {
        bitField0_ = (bitField0_ & ~0x00000002);
        channel_ = getDefaultInstance().getChannel();
        onChanged();
        return this;
      }
      /**
       * <code>optional string channel = 2;</code>
       *
       * <pre>
       *游戏渠道
       * </pre>
       */
      public Builder setChannelBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000002;
        channel_ = value;
        onChanged();
        return this;
      }

      // @@protoc_insertion_point(builder_scope:fm.lizhi.commons.template.datacenter.protocol.RequestGetRefreshSealToken)
    }

    static {
      defaultInstance = new RequestGetRefreshSealToken(true);
      defaultInstance.initFields();
    }

    // @@protoc_insertion_point(class_scope:fm.lizhi.commons.template.datacenter.protocol.RequestGetRefreshSealToken)
  }

  public interface ResponseGetRefreshSealTokenOrBuilder
      extends com.google.protobuf.MessageOrBuilder {

    // optional .fm.lizhi.commons.template.datacenter.protocol.SealToken token = 1;
    /**
     * <code>optional .fm.lizhi.commons.template.datacenter.protocol.SealToken token = 1;</code>
     */
    boolean hasToken();
    /**
     * <code>optional .fm.lizhi.commons.template.datacenter.protocol.SealToken token = 1;</code>
     */
    fm.lizhi.ocean.seal.protocol.SealAuthServiceProto.SealToken getToken();
    /**
     * <code>optional .fm.lizhi.commons.template.datacenter.protocol.SealToken token = 1;</code>
     */
    fm.lizhi.ocean.seal.protocol.SealAuthServiceProto.SealTokenOrBuilder getTokenOrBuilder();
  }
  /**
   * Protobuf type {@code fm.lizhi.commons.template.datacenter.protocol.ResponseGetRefreshSealToken}
   *
   * <pre>
   * rcode == 0(SUCCESS) = 执行成功
   * rcode == 1(FAIL) = 非法用户
   * rcode == 2(ILLEGAL_PARAMS) = 参数非法
   * </pre>
   */
  public static final class ResponseGetRefreshSealToken extends
      com.google.protobuf.GeneratedMessage
      implements ResponseGetRefreshSealTokenOrBuilder {
    // Use ResponseGetRefreshSealToken.newBuilder() to construct.
    private ResponseGetRefreshSealToken(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
      super(builder);
      this.unknownFields = builder.getUnknownFields();
    }
    private ResponseGetRefreshSealToken(boolean noInit) { this.unknownFields = com.google.protobuf.UnknownFieldSet.getDefaultInstance(); }

    private static final ResponseGetRefreshSealToken defaultInstance;
    public static ResponseGetRefreshSealToken getDefaultInstance() {
      return defaultInstance;
    }

    public ResponseGetRefreshSealToken getDefaultInstanceForType() {
      return defaultInstance;
    }

    private final com.google.protobuf.UnknownFieldSet unknownFields;
    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
        getUnknownFields() {
      return this.unknownFields;
    }
    private ResponseGetRefreshSealToken(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      initFields();
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            default: {
              if (!parseUnknownField(input, unknownFields,
                                     extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
            case 10: {
              fm.lizhi.ocean.seal.protocol.SealAuthServiceProto.SealToken.Builder subBuilder = null;
              if (((bitField0_ & 0x00000001) == 0x00000001)) {
                subBuilder = token_.toBuilder();
              }
              token_ = input.readMessage(fm.lizhi.ocean.seal.protocol.SealAuthServiceProto.SealToken.PARSER, extensionRegistry);
              if (subBuilder != null) {
                subBuilder.mergeFrom(token_);
                token_ = subBuilder.buildPartial();
              }
              bitField0_ |= 0x00000001;
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e.getMessage()).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return fm.lizhi.ocean.seal.protocol.SealAuthServiceProto.internal_static_fm_lizhi_commons_template_datacenter_protocol_ResponseGetRefreshSealToken_descriptor;
    }

    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return fm.lizhi.ocean.seal.protocol.SealAuthServiceProto.internal_static_fm_lizhi_commons_template_datacenter_protocol_ResponseGetRefreshSealToken_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              fm.lizhi.ocean.seal.protocol.SealAuthServiceProto.ResponseGetRefreshSealToken.class, fm.lizhi.ocean.seal.protocol.SealAuthServiceProto.ResponseGetRefreshSealToken.Builder.class);
    }

    public static com.google.protobuf.Parser<ResponseGetRefreshSealToken> PARSER =
        new com.google.protobuf.AbstractParser<ResponseGetRefreshSealToken>() {
      public ResponseGetRefreshSealToken parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new ResponseGetRefreshSealToken(input, extensionRegistry);
      }
    };

    @java.lang.Override
    public com.google.protobuf.Parser<ResponseGetRefreshSealToken> getParserForType() {
      return PARSER;
    }

    private int bitField0_;
    // optional .fm.lizhi.commons.template.datacenter.protocol.SealToken token = 1;
    public static final int TOKEN_FIELD_NUMBER = 1;
    private fm.lizhi.ocean.seal.protocol.SealAuthServiceProto.SealToken token_;
    /**
     * <code>optional .fm.lizhi.commons.template.datacenter.protocol.SealToken token = 1;</code>
     */
    public boolean hasToken() {
      return ((bitField0_ & 0x00000001) == 0x00000001);
    }
    /**
     * <code>optional .fm.lizhi.commons.template.datacenter.protocol.SealToken token = 1;</code>
     */
    public fm.lizhi.ocean.seal.protocol.SealAuthServiceProto.SealToken getToken() {
      return token_;
    }
    /**
     * <code>optional .fm.lizhi.commons.template.datacenter.protocol.SealToken token = 1;</code>
     */
    public fm.lizhi.ocean.seal.protocol.SealAuthServiceProto.SealTokenOrBuilder getTokenOrBuilder() {
      return token_;
    }

    private void initFields() {
      token_ = fm.lizhi.ocean.seal.protocol.SealAuthServiceProto.SealToken.getDefaultInstance();
    }
    private byte memoizedIsInitialized = -1;
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized != -1) return isInitialized == 1;

      memoizedIsInitialized = 1;
      return true;
    }

    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      getSerializedSize();
      if (((bitField0_ & 0x00000001) == 0x00000001)) {
        output.writeMessage(1, token_);
      }
      getUnknownFields().writeTo(output);
    }

    private int memoizedSerializedSize = -1;
    public int getSerializedSize() {
      int size = memoizedSerializedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) == 0x00000001)) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(1, token_);
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSerializedSize = size;
      return size;
    }

    private static final long serialVersionUID = 0L;
    @java.lang.Override
    protected java.lang.Object writeReplace()
        throws java.io.ObjectStreamException {
      return super.writeReplace();
    }

    public static fm.lizhi.ocean.seal.protocol.SealAuthServiceProto.ResponseGetRefreshSealToken parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static fm.lizhi.ocean.seal.protocol.SealAuthServiceProto.ResponseGetRefreshSealToken parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static fm.lizhi.ocean.seal.protocol.SealAuthServiceProto.ResponseGetRefreshSealToken parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static fm.lizhi.ocean.seal.protocol.SealAuthServiceProto.ResponseGetRefreshSealToken parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static fm.lizhi.ocean.seal.protocol.SealAuthServiceProto.ResponseGetRefreshSealToken parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return PARSER.parseFrom(input);
    }
    public static fm.lizhi.ocean.seal.protocol.SealAuthServiceProto.ResponseGetRefreshSealToken parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseFrom(input, extensionRegistry);
    }
    public static fm.lizhi.ocean.seal.protocol.SealAuthServiceProto.ResponseGetRefreshSealToken parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return PARSER.parseDelimitedFrom(input);
    }
    public static fm.lizhi.ocean.seal.protocol.SealAuthServiceProto.ResponseGetRefreshSealToken parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseDelimitedFrom(input, extensionRegistry);
    }
    public static fm.lizhi.ocean.seal.protocol.SealAuthServiceProto.ResponseGetRefreshSealToken parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return PARSER.parseFrom(input);
    }
    public static fm.lizhi.ocean.seal.protocol.SealAuthServiceProto.ResponseGetRefreshSealToken parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseFrom(input, extensionRegistry);
    }

    public static Builder newBuilder() { return Builder.create(); }
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder(fm.lizhi.ocean.seal.protocol.SealAuthServiceProto.ResponseGetRefreshSealToken prototype) {
      return newBuilder().mergeFrom(prototype);
    }
    public Builder toBuilder() { return newBuilder(this); }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code fm.lizhi.commons.template.datacenter.protocol.ResponseGetRefreshSealToken}
     *
     * <pre>
     * rcode == 0(SUCCESS) = 执行成功
     * rcode == 1(FAIL) = 非法用户
     * rcode == 2(ILLEGAL_PARAMS) = 参数非法
     * </pre>
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessage.Builder<Builder>
       implements fm.lizhi.ocean.seal.protocol.SealAuthServiceProto.ResponseGetRefreshSealTokenOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return fm.lizhi.ocean.seal.protocol.SealAuthServiceProto.internal_static_fm_lizhi_commons_template_datacenter_protocol_ResponseGetRefreshSealToken_descriptor;
      }

      protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return fm.lizhi.ocean.seal.protocol.SealAuthServiceProto.internal_static_fm_lizhi_commons_template_datacenter_protocol_ResponseGetRefreshSealToken_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                fm.lizhi.ocean.seal.protocol.SealAuthServiceProto.ResponseGetRefreshSealToken.class, fm.lizhi.ocean.seal.protocol.SealAuthServiceProto.ResponseGetRefreshSealToken.Builder.class);
      }

      // Construct using fm.lizhi.ocean.seal.protocol.SealAuthServiceProto.ResponseGetRefreshSealToken.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessage.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessage.alwaysUseFieldBuilders) {
          getTokenFieldBuilder();
        }
      }
      private static Builder create() {
        return new Builder();
      }

      public Builder clear() {
        super.clear();
        if (tokenBuilder_ == null) {
          token_ = fm.lizhi.ocean.seal.protocol.SealAuthServiceProto.SealToken.getDefaultInstance();
        } else {
          tokenBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000001);
        return this;
      }

      public Builder clone() {
        return create().mergeFrom(buildPartial());
      }

      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return fm.lizhi.ocean.seal.protocol.SealAuthServiceProto.internal_static_fm_lizhi_commons_template_datacenter_protocol_ResponseGetRefreshSealToken_descriptor;
      }

      public fm.lizhi.ocean.seal.protocol.SealAuthServiceProto.ResponseGetRefreshSealToken getDefaultInstanceForType() {
        return fm.lizhi.ocean.seal.protocol.SealAuthServiceProto.ResponseGetRefreshSealToken.getDefaultInstance();
      }

      public fm.lizhi.ocean.seal.protocol.SealAuthServiceProto.ResponseGetRefreshSealToken build() {
        fm.lizhi.ocean.seal.protocol.SealAuthServiceProto.ResponseGetRefreshSealToken result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      public fm.lizhi.ocean.seal.protocol.SealAuthServiceProto.ResponseGetRefreshSealToken buildPartial() {
        fm.lizhi.ocean.seal.protocol.SealAuthServiceProto.ResponseGetRefreshSealToken result = new fm.lizhi.ocean.seal.protocol.SealAuthServiceProto.ResponseGetRefreshSealToken(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) == 0x00000001)) {
          to_bitField0_ |= 0x00000001;
        }
        if (tokenBuilder_ == null) {
          result.token_ = token_;
        } else {
          result.token_ = tokenBuilder_.build();
        }
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof fm.lizhi.ocean.seal.protocol.SealAuthServiceProto.ResponseGetRefreshSealToken) {
          return mergeFrom((fm.lizhi.ocean.seal.protocol.SealAuthServiceProto.ResponseGetRefreshSealToken)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(fm.lizhi.ocean.seal.protocol.SealAuthServiceProto.ResponseGetRefreshSealToken other) {
        if (other == fm.lizhi.ocean.seal.protocol.SealAuthServiceProto.ResponseGetRefreshSealToken.getDefaultInstance()) return this;
        if (other.hasToken()) {
          mergeToken(other.getToken());
        }
        this.mergeUnknownFields(other.getUnknownFields());
        return this;
      }

      public final boolean isInitialized() {
        return true;
      }

      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        fm.lizhi.ocean.seal.protocol.SealAuthServiceProto.ResponseGetRefreshSealToken parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (fm.lizhi.ocean.seal.protocol.SealAuthServiceProto.ResponseGetRefreshSealToken) e.getUnfinishedMessage();
          throw e;
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      // optional .fm.lizhi.commons.template.datacenter.protocol.SealToken token = 1;
      private fm.lizhi.ocean.seal.protocol.SealAuthServiceProto.SealToken token_ = fm.lizhi.ocean.seal.protocol.SealAuthServiceProto.SealToken.getDefaultInstance();
      private com.google.protobuf.SingleFieldBuilder<
          fm.lizhi.ocean.seal.protocol.SealAuthServiceProto.SealToken, fm.lizhi.ocean.seal.protocol.SealAuthServiceProto.SealToken.Builder, fm.lizhi.ocean.seal.protocol.SealAuthServiceProto.SealTokenOrBuilder> tokenBuilder_;
      /**
       * <code>optional .fm.lizhi.commons.template.datacenter.protocol.SealToken token = 1;</code>
       */
      public boolean hasToken() {
        return ((bitField0_ & 0x00000001) == 0x00000001);
      }
      /**
       * <code>optional .fm.lizhi.commons.template.datacenter.protocol.SealToken token = 1;</code>
       */
      public fm.lizhi.ocean.seal.protocol.SealAuthServiceProto.SealToken getToken() {
        if (tokenBuilder_ == null) {
          return token_;
        } else {
          return tokenBuilder_.getMessage();
        }
      }
      /**
       * <code>optional .fm.lizhi.commons.template.datacenter.protocol.SealToken token = 1;</code>
       */
      public Builder setToken(fm.lizhi.ocean.seal.protocol.SealAuthServiceProto.SealToken value) {
        if (tokenBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          token_ = value;
          onChanged();
        } else {
          tokenBuilder_.setMessage(value);
        }
        bitField0_ |= 0x00000001;
        return this;
      }
      /**
       * <code>optional .fm.lizhi.commons.template.datacenter.protocol.SealToken token = 1;</code>
       */
      public Builder setToken(
          fm.lizhi.ocean.seal.protocol.SealAuthServiceProto.SealToken.Builder builderForValue) {
        if (tokenBuilder_ == null) {
          token_ = builderForValue.build();
          onChanged();
        } else {
          tokenBuilder_.setMessage(builderForValue.build());
        }
        bitField0_ |= 0x00000001;
        return this;
      }
      /**
       * <code>optional .fm.lizhi.commons.template.datacenter.protocol.SealToken token = 1;</code>
       */
      public Builder mergeToken(fm.lizhi.ocean.seal.protocol.SealAuthServiceProto.SealToken value) {
        if (tokenBuilder_ == null) {
          if (((bitField0_ & 0x00000001) == 0x00000001) &&
              token_ != fm.lizhi.ocean.seal.protocol.SealAuthServiceProto.SealToken.getDefaultInstance()) {
            token_ =
              fm.lizhi.ocean.seal.protocol.SealAuthServiceProto.SealToken.newBuilder(token_).mergeFrom(value).buildPartial();
          } else {
            token_ = value;
          }
          onChanged();
        } else {
          tokenBuilder_.mergeFrom(value);
        }
        bitField0_ |= 0x00000001;
        return this;
      }
      /**
       * <code>optional .fm.lizhi.commons.template.datacenter.protocol.SealToken token = 1;</code>
       */
      public Builder clearToken() {
        if (tokenBuilder_ == null) {
          token_ = fm.lizhi.ocean.seal.protocol.SealAuthServiceProto.SealToken.getDefaultInstance();
          onChanged();
        } else {
          tokenBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000001);
        return this;
      }
      /**
       * <code>optional .fm.lizhi.commons.template.datacenter.protocol.SealToken token = 1;</code>
       */
      public fm.lizhi.ocean.seal.protocol.SealAuthServiceProto.SealToken.Builder getTokenBuilder() {
        bitField0_ |= 0x00000001;
        onChanged();
        return getTokenFieldBuilder().getBuilder();
      }
      /**
       * <code>optional .fm.lizhi.commons.template.datacenter.protocol.SealToken token = 1;</code>
       */
      public fm.lizhi.ocean.seal.protocol.SealAuthServiceProto.SealTokenOrBuilder getTokenOrBuilder() {
        if (tokenBuilder_ != null) {
          return tokenBuilder_.getMessageOrBuilder();
        } else {
          return token_;
        }
      }
      /**
       * <code>optional .fm.lizhi.commons.template.datacenter.protocol.SealToken token = 1;</code>
       */
      private com.google.protobuf.SingleFieldBuilder<
          fm.lizhi.ocean.seal.protocol.SealAuthServiceProto.SealToken, fm.lizhi.ocean.seal.protocol.SealAuthServiceProto.SealToken.Builder, fm.lizhi.ocean.seal.protocol.SealAuthServiceProto.SealTokenOrBuilder> 
          getTokenFieldBuilder() {
        if (tokenBuilder_ == null) {
          tokenBuilder_ = new com.google.protobuf.SingleFieldBuilder<
              fm.lizhi.ocean.seal.protocol.SealAuthServiceProto.SealToken, fm.lizhi.ocean.seal.protocol.SealAuthServiceProto.SealToken.Builder, fm.lizhi.ocean.seal.protocol.SealAuthServiceProto.SealTokenOrBuilder>(
                  token_,
                  getParentForChildren(),
                  isClean());
          token_ = null;
        }
        return tokenBuilder_;
      }

      // @@protoc_insertion_point(builder_scope:fm.lizhi.commons.template.datacenter.protocol.ResponseGetRefreshSealToken)
    }

    static {
      defaultInstance = new ResponseGetRefreshSealToken(true);
      defaultInstance.initFields();
    }

    // @@protoc_insertion_point(class_scope:fm.lizhi.commons.template.datacenter.protocol.ResponseGetRefreshSealToken)
  }

  private static com.google.protobuf.Descriptors.Descriptor
    internal_static_fm_lizhi_commons_template_datacenter_protocol_SealToken_descriptor;
  private static
    com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internal_static_fm_lizhi_commons_template_datacenter_protocol_SealToken_fieldAccessorTable;
  private static com.google.protobuf.Descriptors.Descriptor
    internal_static_fm_lizhi_commons_template_datacenter_protocol_JwtUserInfo_descriptor;
  private static
    com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internal_static_fm_lizhi_commons_template_datacenter_protocol_JwtUserInfo_fieldAccessorTable;
  private static com.google.protobuf.Descriptors.Descriptor
    internal_static_fm_lizhi_commons_template_datacenter_protocol_RequestGetSealToken_descriptor;
  private static
    com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internal_static_fm_lizhi_commons_template_datacenter_protocol_RequestGetSealToken_fieldAccessorTable;
  private static com.google.protobuf.Descriptors.Descriptor
    internal_static_fm_lizhi_commons_template_datacenter_protocol_ResponseGetSealToken_descriptor;
  private static
    com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internal_static_fm_lizhi_commons_template_datacenter_protocol_ResponseGetSealToken_fieldAccessorTable;
  private static com.google.protobuf.Descriptors.Descriptor
    internal_static_fm_lizhi_commons_template_datacenter_protocol_RequestGetUserInfoBySealToken_descriptor;
  private static
    com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internal_static_fm_lizhi_commons_template_datacenter_protocol_RequestGetUserInfoBySealToken_fieldAccessorTable;
  private static com.google.protobuf.Descriptors.Descriptor
    internal_static_fm_lizhi_commons_template_datacenter_protocol_ResponseGetUserInfoBySealToken_descriptor;
  private static
    com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internal_static_fm_lizhi_commons_template_datacenter_protocol_ResponseGetUserInfoBySealToken_fieldAccessorTable;
  private static com.google.protobuf.Descriptors.Descriptor
    internal_static_fm_lizhi_commons_template_datacenter_protocol_RequestGetRefreshSealToken_descriptor;
  private static
    com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internal_static_fm_lizhi_commons_template_datacenter_protocol_RequestGetRefreshSealToken_fieldAccessorTable;
  private static com.google.protobuf.Descriptors.Descriptor
    internal_static_fm_lizhi_commons_template_datacenter_protocol_ResponseGetRefreshSealToken_descriptor;
  private static
    com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internal_static_fm_lizhi_commons_template_datacenter_protocol_ResponseGetRefreshSealToken_fieldAccessorTable;

  public static com.google.protobuf.Descriptors.FileDescriptor
      getDescriptor() {
    return descriptor;
  }
  private static com.google.protobuf.Descriptors.FileDescriptor
      descriptor;
  static {
    java.lang.String[] descriptorData = {
      "\n\030protocol_seal_auth.proto\022-fm.lizhi.com" +
      "mons.template.datacenter.protocol\".\n\tSea" +
      "lToken\022\r\n\005token\030\001 \001(\t\022\022\n\nexpireDate\030\002 \001(" +
      "\003\"=\n\013JwtUserInfo\022\016\n\006userId\030\001 \001(\003\022\020\n\010user" +
      "Name\030\002 \001(\t\022\014\n\004role\030\003 \001(\t\"X\n\023RequestGetSe" +
      "alToken\022\016\n\006userId\030\001 \001(\003\022\r\n\005appId\030\002 \001(\005\022\017" +
      "\n\007channel\030\003 \001(\t\022\021\n\tcheckCode\030\004 \001(\t\"_\n\024Re" +
      "sponseGetSealToken\022G\n\005token\030\001 \001(\01328.fm.l" +
      "izhi.commons.template.datacenter.protoco" +
      "l.SealToken\"N\n\035RequestGetUserInfoBySealT",
      "oken\022\r\n\005token\030\001 \001(\t\022\r\n\005appId\030\002 \001(\t\022\017\n\007ch" +
      "annel\030\003 \001(\t\"n\n\036ResponseGetUserInfoBySeal" +
      "Token\022L\n\010sealUser\030\001 \001(\0132:.fm.lizhi.commo" +
      "ns.template.datacenter.protocol.JwtUserI" +
      "nfo\"<\n\032RequestGetRefreshSealToken\022\r\n\005tok" +
      "en\030\001 \001(\t\022\017\n\007channel\030\002 \001(\t\"f\n\033ResponseGet" +
      "RefreshSealToken\022G\n\005token\030\001 \001(\01328.fm.liz" +
      "hi.commons.template.datacenter.protocol." +
      "SealTokenB6\n\034fm.lizhi.ocean.seal.protoco" +
      "lB\024SealAuthServiceProtoH\001"
    };
    com.google.protobuf.Descriptors.FileDescriptor.InternalDescriptorAssigner assigner =
      new com.google.protobuf.Descriptors.FileDescriptor.InternalDescriptorAssigner() {
        public com.google.protobuf.ExtensionRegistry assignDescriptors(
            com.google.protobuf.Descriptors.FileDescriptor root) {
          descriptor = root;
          internal_static_fm_lizhi_commons_template_datacenter_protocol_SealToken_descriptor =
            getDescriptor().getMessageTypes().get(0);
          internal_static_fm_lizhi_commons_template_datacenter_protocol_SealToken_fieldAccessorTable = new
            com.google.protobuf.GeneratedMessage.FieldAccessorTable(
              internal_static_fm_lizhi_commons_template_datacenter_protocol_SealToken_descriptor,
              new java.lang.String[] { "Token", "ExpireDate", });
          internal_static_fm_lizhi_commons_template_datacenter_protocol_JwtUserInfo_descriptor =
            getDescriptor().getMessageTypes().get(1);
          internal_static_fm_lizhi_commons_template_datacenter_protocol_JwtUserInfo_fieldAccessorTable = new
            com.google.protobuf.GeneratedMessage.FieldAccessorTable(
              internal_static_fm_lizhi_commons_template_datacenter_protocol_JwtUserInfo_descriptor,
              new java.lang.String[] { "UserId", "UserName", "Role", });
          internal_static_fm_lizhi_commons_template_datacenter_protocol_RequestGetSealToken_descriptor =
            getDescriptor().getMessageTypes().get(2);
          internal_static_fm_lizhi_commons_template_datacenter_protocol_RequestGetSealToken_fieldAccessorTable = new
            com.google.protobuf.GeneratedMessage.FieldAccessorTable(
              internal_static_fm_lizhi_commons_template_datacenter_protocol_RequestGetSealToken_descriptor,
              new java.lang.String[] { "UserId", "AppId", "Channel", "CheckCode", });
          internal_static_fm_lizhi_commons_template_datacenter_protocol_ResponseGetSealToken_descriptor =
            getDescriptor().getMessageTypes().get(3);
          internal_static_fm_lizhi_commons_template_datacenter_protocol_ResponseGetSealToken_fieldAccessorTable = new
            com.google.protobuf.GeneratedMessage.FieldAccessorTable(
              internal_static_fm_lizhi_commons_template_datacenter_protocol_ResponseGetSealToken_descriptor,
              new java.lang.String[] { "Token", });
          internal_static_fm_lizhi_commons_template_datacenter_protocol_RequestGetUserInfoBySealToken_descriptor =
            getDescriptor().getMessageTypes().get(4);
          internal_static_fm_lizhi_commons_template_datacenter_protocol_RequestGetUserInfoBySealToken_fieldAccessorTable = new
            com.google.protobuf.GeneratedMessage.FieldAccessorTable(
              internal_static_fm_lizhi_commons_template_datacenter_protocol_RequestGetUserInfoBySealToken_descriptor,
              new java.lang.String[] { "Token", "AppId", "Channel", });
          internal_static_fm_lizhi_commons_template_datacenter_protocol_ResponseGetUserInfoBySealToken_descriptor =
            getDescriptor().getMessageTypes().get(5);
          internal_static_fm_lizhi_commons_template_datacenter_protocol_ResponseGetUserInfoBySealToken_fieldAccessorTable = new
            com.google.protobuf.GeneratedMessage.FieldAccessorTable(
              internal_static_fm_lizhi_commons_template_datacenter_protocol_ResponseGetUserInfoBySealToken_descriptor,
              new java.lang.String[] { "SealUser", });
          internal_static_fm_lizhi_commons_template_datacenter_protocol_RequestGetRefreshSealToken_descriptor =
            getDescriptor().getMessageTypes().get(6);
          internal_static_fm_lizhi_commons_template_datacenter_protocol_RequestGetRefreshSealToken_fieldAccessorTable = new
            com.google.protobuf.GeneratedMessage.FieldAccessorTable(
              internal_static_fm_lizhi_commons_template_datacenter_protocol_RequestGetRefreshSealToken_descriptor,
              new java.lang.String[] { "Token", "Channel", });
          internal_static_fm_lizhi_commons_template_datacenter_protocol_ResponseGetRefreshSealToken_descriptor =
            getDescriptor().getMessageTypes().get(7);
          internal_static_fm_lizhi_commons_template_datacenter_protocol_ResponseGetRefreshSealToken_fieldAccessorTable = new
            com.google.protobuf.GeneratedMessage.FieldAccessorTable(
              internal_static_fm_lizhi_commons_template_datacenter_protocol_ResponseGetRefreshSealToken_descriptor,
              new java.lang.String[] { "Token", });
          return null;
        }
      };
    com.google.protobuf.Descriptors.FileDescriptor
      .internalBuildGeneratedFileFrom(descriptorData,
        new com.google.protobuf.Descriptors.FileDescriptor[] {
        }, assigner);
  }

  // @@protoc_insertion_point(outer_class_scope)
}
