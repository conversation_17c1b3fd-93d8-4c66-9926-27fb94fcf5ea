package fm.lizhi.ocean.seal.api;

import java.util.List;
import fm.lizhi.commons.service.client.annotation.Attribute;
import fm.lizhi.commons.service.client.annotation.Return;
import fm.lizhi.commons.service.client.annotation.Service;
import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.ocean.seal.protocol.GameLanguageServiceProto.RequestGetAllLanguages;
import fm.lizhi.ocean.seal.protocol.GameLanguageServiceProto.ResponseGetAllLanguages;
import fm.lizhi.ocean.seal.protocol.GameLanguageServiceProto.GameLanguage;

/**
 * this file generated by autoapi-maven-plugin
 * 
 * do not edit this file manually, because this file is covered when you run autoapi-maven-plugin
 * 
 */

public interface GameLanguageService {
	
	
	/**
	 *  获取游戏语言列表
	 *
	 * @return 
	 *     //if rcode == 0 执行成功<br>
	 *     //if rcode == 1 错误<br>
	 */
	@Service(domain = 4302, op = 200, request = RequestGetAllLanguages.class, response = ResponseGetAllLanguages.class)
	@Return(resultType = ResponseGetAllLanguages.class)
	Result<ResponseGetAllLanguages> getAllLanguages();
	
	
	public static final int GET_ALL_LANGUAGES_SUCCESS = 0; // 执行成功
	public static final int GET_ALL_LANGUAGES_FAIL = 1; // 错误


}