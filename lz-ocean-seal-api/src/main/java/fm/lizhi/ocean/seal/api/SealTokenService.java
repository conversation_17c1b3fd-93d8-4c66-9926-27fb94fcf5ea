package fm.lizhi.ocean.seal.api;

import java.util.List;
import fm.lizhi.commons.service.client.annotation.Attribute;
import fm.lizhi.commons.service.client.annotation.Return;
import fm.lizhi.commons.service.client.annotation.Service;
import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.ocean.seal.protocol.SealTokenServiceProto.RequestGetSealHashingToken;
import fm.lizhi.ocean.seal.protocol.SealTokenServiceProto.ResponseGetSealHashingToken;
import fm.lizhi.ocean.seal.protocol.SealTokenServiceProto.RequestVerifySealToken;
import fm.lizhi.ocean.seal.protocol.SealTokenServiceProto.ResponseVerifySealToken;
import fm.lizhi.ocean.seal.protocol.SealTokenServiceProto.RequestGetUserInfoBySealToken;
import fm.lizhi.ocean.seal.protocol.SealTokenServiceProto.ResponseGetUserInfoBySealToken;
import fm.lizhi.ocean.seal.protocol.SealTokenServiceProto.SealHashingTokenParam;
import fm.lizhi.ocean.seal.protocol.SealTokenServiceProto.JwtUserInfo;

/**
 * this file generated by autoapi-maven-plugin
 * 
 * do not edit this file manually, because this file is covered when you run autoapi-maven-plugin
 * 
 */

public interface SealTokenService {
	
	
	/**
	 *  根据传入的参数，获取Seal sdk与服务端通信的短期token。这个方法与SealAuthService的区别是，这个是新版的方案jwt+缓存。userId 业务对应的标识码。建议使用userId作为标识码 hashingToken 验证token，验证的时候把它放进去
	 *
	 * @param sealHashingTokenParam
	 *            {@link SealHashingTokenParam}
	 * @return 
	 *     //if rcode == 0 执行成功<br>
	 *     //if rcode == 1 非法用户<br>
	 *     //if rcode == 2 参数非法<br>
	 *     //if rcode == 3 服务错误<br>
	 */
	@Service(domain = 4302, op = 100, request = RequestGetSealHashingToken.class, response = ResponseGetSealHashingToken.class)
	@Return(resultType = ResponseGetSealHashingToken.class)
	Result<ResponseGetSealHashingToken> getSealHashingToken(@Attribute(name = "sealHashingTokenParam") SealHashingTokenParam sealHashingTokenParam);
	
	
	/**
	 *  验证SealToken是否正确.如果正确token,且token未过期，将会进行续命
	 *
	 * @param sealToken
	 *            sealToken获取用户信息
	 * @param appId
	 *            
	 * @return 
	 *     //if rcode == 0 执行成功<br>
	 *     //if rcode == 1 无效Token<br>
	 *     //if rcode == 2 服务错误<br>
	 */
	@Service(domain = 4302, op = 101, request = RequestVerifySealToken.class, response = ResponseVerifySealToken.class)
	@Return(resultType = ResponseVerifySealToken.class)
	Result<ResponseVerifySealToken> verifySealToken(@Attribute(name = "sealToken") String sealToken, @Attribute(name = "appId") String appId);
	
	
	/**
	 *  根据用户SealToken获取用户信息
	 *
	 * @param sealToken
	 *            sealToken获取用户信息
	 * @param appId
	 *            
	 * @return 
	 *     //if rcode == 0 执行成功<br>
	 *     //if rcode == 1 无效Token<br>
	 *     //if rcode == 2 参数非法<br>
	 *     //if rcode == 3 服务错误<br>
	 */
	@Service(domain = 4302, op = 102, request = RequestGetUserInfoBySealToken.class, response = ResponseGetUserInfoBySealToken.class)
	@Return(resultType = ResponseGetUserInfoBySealToken.class)
	Result<ResponseGetUserInfoBySealToken> getUserInfoBySealToken(@Attribute(name = "sealToken") String sealToken, @Attribute(name = "appId") String appId);
	
	
	public static final int GET_SEAL_HASHING_TOKEN_SUCCESS = 0; // 执行成功
	public static final int GET_SEAL_HASHING_TOKEN_FAIL = 1; // 非法用户
	public static final int GET_SEAL_HASHING_TOKEN_ILLEGAL_PARAMS = 2; // 参数非法
	public static final int GET_SEAL_HASHING_TOKEN_ERROR = 3; // 服务错误

	public static final int VERIFY_SEAL_TOKEN_SUCCESS = 0; // 执行成功
	public static final int VERIFY_SEAL_TOKEN_INVALID = 1; // 无效Token
	public static final int VERIFY_SEAL_TOKEN_ERROR = 2; // 服务错误

	public static final int GET_USER_INFO_BY_SEAL_TOKEN_SUCCESS = 0; // 执行成功
	public static final int GET_USER_INFO_BY_SEAL_TOKEN_FAIL = 1; // 无效Token
	public static final int GET_USER_INFO_BY_SEAL_TOKEN_ILLEGAL_PARAMS = 2; // 参数非法
	public static final int GET_USER_INFO_BY_SEAL_TOKEN_ERROR = 3; // 服务错误


}