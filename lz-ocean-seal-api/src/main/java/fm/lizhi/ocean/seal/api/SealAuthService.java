package fm.lizhi.ocean.seal.api;

import java.util.List;
import fm.lizhi.commons.service.client.annotation.Attribute;
import fm.lizhi.commons.service.client.annotation.Return;
import fm.lizhi.commons.service.client.annotation.Service;
import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.ocean.seal.protocol.SealAuthServiceProto.RequestGetSealToken;
import fm.lizhi.ocean.seal.protocol.SealAuthServiceProto.ResponseGetSealToken;
import fm.lizhi.ocean.seal.protocol.SealAuthServiceProto.RequestGetUserInfoBySealToken;
import fm.lizhi.ocean.seal.protocol.SealAuthServiceProto.ResponseGetUserInfoBySealToken;
import fm.lizhi.ocean.seal.protocol.SealAuthServiceProto.RequestGetRefreshSealToken;
import fm.lizhi.ocean.seal.protocol.SealAuthServiceProto.ResponseGetRefreshSealToken;
import fm.lizhi.ocean.seal.protocol.SealAuthServiceProto.SealToken;
import fm.lizhi.ocean.seal.protocol.SealAuthServiceProto.JwtUserInfo;

/**
 * this file generated by autoapi-maven-plugin
 * 
 * do not edit this file manually, because this file is covered when you run autoapi-maven-plugin
 * 
 */
@Deprecated
public interface SealAuthService {
	
	
	/**
	 *  获取短期令牌，默认时长2小时
	 *
	 * @param userId
	 *            业务用户Id
	 * @param appId
	 *            appId
	 * @param channel
	 *            游戏渠道
	 * @param checkCode
	 *            用户校验码
	 * @return 
	 *     //if rcode == 0 执行成功<br>
	 *     //if rcode == 1 非法用户<br>
	 *     //if rcode == 2 参数非法<br>
	 */
	@Service(domain = 4302, op = 999, request = RequestGetSealToken.class, response = ResponseGetSealToken.class)
	@Return(resultType = ResponseGetSealToken.class)
	Result<ResponseGetSealToken> getSealToken(@Attribute(name = "userId") long userId, @Attribute(name = "appId") int appId, @Attribute(name = "channel") String channel, @Attribute(name = "checkCode") String checkCode);
	
	
	/**
	 *  获取令牌信息
	 *
	 * @param token
	 *            业务用户Id
	 * @param appId
	 *            appId
	 * @param channel
	 *            游戏渠道
	 * @return 
	 *     //if rcode == 0 执行成功<br>
	 *     //if rcode == 1 无效Token<br>
	 *     //if rcode == 2 参数非法<br>
	 *     //if rcode == 1001 Token过期<br>
	 */
	@Service(domain = 4302, op = 1000, request = RequestGetUserInfoBySealToken.class, response = ResponseGetUserInfoBySealToken.class)
	@Return(resultType = ResponseGetUserInfoBySealToken.class)
	Result<ResponseGetUserInfoBySealToken> getUserInfoBySealToken(@Attribute(name = "token") String token, @Attribute(name = "appId") String appId, @Attribute(name = "channel") String channel);
	
	
	/**
	 *  获取长期令牌，默认时长2天
	 *
	 * @param token
	 *            业务用户Id
	 * @param channel
	 *            游戏渠道
	 * @return 
	 *     //if rcode == 0 执行成功<br>
	 *     //if rcode == 1 非法用户<br>
	 *     //if rcode == 2 参数非法<br>
	 */
	@Service(domain = 4302, op = 1001, request = RequestGetRefreshSealToken.class, response = ResponseGetRefreshSealToken.class)
	@Return(resultType = ResponseGetRefreshSealToken.class)
	Result<ResponseGetRefreshSealToken> getRefreshSealToken(@Attribute(name = "token") String token, @Attribute(name = "channel") String channel);
	
	
	public static final int GET_SEAL_TOKEN_SUCCESS = 0; // 执行成功
	public static final int GET_SEAL_TOKEN_FAIL = 1; // 非法用户
	public static final int GET_SEAL_TOKEN_ILLEGAL_PARAMS = 2; // 参数非法

	public static final int GET_USER_INFO_BY_SEAL_TOKEN_SUCCESS = 0; // 执行成功
	public static final int GET_USER_INFO_BY_SEAL_TOKEN_FAIL = 1; // 无效Token
	public static final int GET_USER_INFO_BY_SEAL_TOKEN_ILLEGAL_PARAMS = 2; // 参数非法
	public static final int GET_USER_INFO_BY_SEAL_TOKEN_TOKEN_EXPIRED = 1001; // Token过期

	public static final int GET_REFRESH_SEAL_TOKEN_SUCCESS = 0; // 执行成功
	public static final int GET_REFRESH_SEAL_TOKEN_FAIL = 1; // 非法用户
	public static final int GET_REFRESH_SEAL_TOKEN_ILLEGAL_PARAMS = 2; // 参数非法


}