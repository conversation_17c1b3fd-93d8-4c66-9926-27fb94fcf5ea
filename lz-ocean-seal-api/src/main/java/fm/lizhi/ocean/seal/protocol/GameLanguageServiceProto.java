// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: protocol_game_language.proto

package fm.lizhi.ocean.seal.protocol;

public final class GameLanguageServiceProto {
  private GameLanguageServiceProto() {}
  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistry registry) {
  }
  public interface GameLanguageOrBuilder
      extends com.google.protobuf.MessageOrBuilder {

    // optional int64 id = 1;
    /**
     * <code>optional int64 id = 1;</code>
     */
    boolean hasId();
    /**
     * <code>optional int64 id = 1;</code>
     */
    long getId();

    // optional string abbreviation = 2;
    /**
     * <code>optional string abbreviation = 2;</code>
     *
     * <pre>
     *简称 en ar zh
     * </pre>
     */
    boolean hasAbbreviation();
    /**
     * <code>optional string abbreviation = 2;</code>
     *
     * <pre>
     *简称 en ar zh
     * </pre>
     */
    java.lang.String getAbbreviation();
    /**
     * <code>optional string abbreviation = 2;</code>
     *
     * <pre>
     *简称 en ar zh
     * </pre>
     */
    com.google.protobuf.ByteString
        getAbbreviationBytes();

    // optional string languageName = 3;
    /**
     * <code>optional string languageName = 3;</code>
     *
     * <pre>
     *中文名称
     * </pre>
     */
    boolean hasLanguageName();
    /**
     * <code>optional string languageName = 3;</code>
     *
     * <pre>
     *中文名称
     * </pre>
     */
    java.lang.String getLanguageName();
    /**
     * <code>optional string languageName = 3;</code>
     *
     * <pre>
     *中文名称
     * </pre>
     */
    com.google.protobuf.ByteString
        getLanguageNameBytes();
  }
  /**
   * Protobuf type {@code fm.lizhi.commons.template.datacenter.protocol.GameLanguage}
   */
  public static final class GameLanguage extends
      com.google.protobuf.GeneratedMessage
      implements GameLanguageOrBuilder {
    // Use GameLanguage.newBuilder() to construct.
    private GameLanguage(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
      super(builder);
      this.unknownFields = builder.getUnknownFields();
    }
    private GameLanguage(boolean noInit) { this.unknownFields = com.google.protobuf.UnknownFieldSet.getDefaultInstance(); }

    private static final GameLanguage defaultInstance;
    public static GameLanguage getDefaultInstance() {
      return defaultInstance;
    }

    public GameLanguage getDefaultInstanceForType() {
      return defaultInstance;
    }

    private final com.google.protobuf.UnknownFieldSet unknownFields;
    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
        getUnknownFields() {
      return this.unknownFields;
    }
    private GameLanguage(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      initFields();
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            default: {
              if (!parseUnknownField(input, unknownFields,
                                     extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
            case 8: {
              bitField0_ |= 0x00000001;
              id_ = input.readInt64();
              break;
            }
            case 18: {
              bitField0_ |= 0x00000002;
              abbreviation_ = input.readBytes();
              break;
            }
            case 26: {
              bitField0_ |= 0x00000004;
              languageName_ = input.readBytes();
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e.getMessage()).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return fm.lizhi.ocean.seal.protocol.GameLanguageServiceProto.internal_static_fm_lizhi_commons_template_datacenter_protocol_GameLanguage_descriptor;
    }

    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return fm.lizhi.ocean.seal.protocol.GameLanguageServiceProto.internal_static_fm_lizhi_commons_template_datacenter_protocol_GameLanguage_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              fm.lizhi.ocean.seal.protocol.GameLanguageServiceProto.GameLanguage.class, fm.lizhi.ocean.seal.protocol.GameLanguageServiceProto.GameLanguage.Builder.class);
    }

    public static com.google.protobuf.Parser<GameLanguage> PARSER =
        new com.google.protobuf.AbstractParser<GameLanguage>() {
      public GameLanguage parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new GameLanguage(input, extensionRegistry);
      }
    };

    @java.lang.Override
    public com.google.protobuf.Parser<GameLanguage> getParserForType() {
      return PARSER;
    }

    private int bitField0_;
    // optional int64 id = 1;
    public static final int ID_FIELD_NUMBER = 1;
    private long id_;
    /**
     * <code>optional int64 id = 1;</code>
     */
    public boolean hasId() {
      return ((bitField0_ & 0x00000001) == 0x00000001);
    }
    /**
     * <code>optional int64 id = 1;</code>
     */
    public long getId() {
      return id_;
    }

    // optional string abbreviation = 2;
    public static final int ABBREVIATION_FIELD_NUMBER = 2;
    private java.lang.Object abbreviation_;
    /**
     * <code>optional string abbreviation = 2;</code>
     *
     * <pre>
     *简称 en ar zh
     * </pre>
     */
    public boolean hasAbbreviation() {
      return ((bitField0_ & 0x00000002) == 0x00000002);
    }
    /**
     * <code>optional string abbreviation = 2;</code>
     *
     * <pre>
     *简称 en ar zh
     * </pre>
     */
    public java.lang.String getAbbreviation() {
      java.lang.Object ref = abbreviation_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          abbreviation_ = s;
        }
        return s;
      }
    }
    /**
     * <code>optional string abbreviation = 2;</code>
     *
     * <pre>
     *简称 en ar zh
     * </pre>
     */
    public com.google.protobuf.ByteString
        getAbbreviationBytes() {
      java.lang.Object ref = abbreviation_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        abbreviation_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    // optional string languageName = 3;
    public static final int LANGUAGENAME_FIELD_NUMBER = 3;
    private java.lang.Object languageName_;
    /**
     * <code>optional string languageName = 3;</code>
     *
     * <pre>
     *中文名称
     * </pre>
     */
    public boolean hasLanguageName() {
      return ((bitField0_ & 0x00000004) == 0x00000004);
    }
    /**
     * <code>optional string languageName = 3;</code>
     *
     * <pre>
     *中文名称
     * </pre>
     */
    public java.lang.String getLanguageName() {
      java.lang.Object ref = languageName_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          languageName_ = s;
        }
        return s;
      }
    }
    /**
     * <code>optional string languageName = 3;</code>
     *
     * <pre>
     *中文名称
     * </pre>
     */
    public com.google.protobuf.ByteString
        getLanguageNameBytes() {
      java.lang.Object ref = languageName_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        languageName_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    private void initFields() {
      id_ = 0L;
      abbreviation_ = "";
      languageName_ = "";
    }
    private byte memoizedIsInitialized = -1;
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized != -1) return isInitialized == 1;

      memoizedIsInitialized = 1;
      return true;
    }

    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      getSerializedSize();
      if (((bitField0_ & 0x00000001) == 0x00000001)) {
        output.writeInt64(1, id_);
      }
      if (((bitField0_ & 0x00000002) == 0x00000002)) {
        output.writeBytes(2, getAbbreviationBytes());
      }
      if (((bitField0_ & 0x00000004) == 0x00000004)) {
        output.writeBytes(3, getLanguageNameBytes());
      }
      getUnknownFields().writeTo(output);
    }

    private int memoizedSerializedSize = -1;
    public int getSerializedSize() {
      int size = memoizedSerializedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) == 0x00000001)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt64Size(1, id_);
      }
      if (((bitField0_ & 0x00000002) == 0x00000002)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(2, getAbbreviationBytes());
      }
      if (((bitField0_ & 0x00000004) == 0x00000004)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(3, getLanguageNameBytes());
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSerializedSize = size;
      return size;
    }

    private static final long serialVersionUID = 0L;
    @java.lang.Override
    protected java.lang.Object writeReplace()
        throws java.io.ObjectStreamException {
      return super.writeReplace();
    }

    public static fm.lizhi.ocean.seal.protocol.GameLanguageServiceProto.GameLanguage parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static fm.lizhi.ocean.seal.protocol.GameLanguageServiceProto.GameLanguage parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static fm.lizhi.ocean.seal.protocol.GameLanguageServiceProto.GameLanguage parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static fm.lizhi.ocean.seal.protocol.GameLanguageServiceProto.GameLanguage parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static fm.lizhi.ocean.seal.protocol.GameLanguageServiceProto.GameLanguage parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return PARSER.parseFrom(input);
    }
    public static fm.lizhi.ocean.seal.protocol.GameLanguageServiceProto.GameLanguage parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseFrom(input, extensionRegistry);
    }
    public static fm.lizhi.ocean.seal.protocol.GameLanguageServiceProto.GameLanguage parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return PARSER.parseDelimitedFrom(input);
    }
    public static fm.lizhi.ocean.seal.protocol.GameLanguageServiceProto.GameLanguage parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseDelimitedFrom(input, extensionRegistry);
    }
    public static fm.lizhi.ocean.seal.protocol.GameLanguageServiceProto.GameLanguage parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return PARSER.parseFrom(input);
    }
    public static fm.lizhi.ocean.seal.protocol.GameLanguageServiceProto.GameLanguage parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseFrom(input, extensionRegistry);
    }

    public static Builder newBuilder() { return Builder.create(); }
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder(fm.lizhi.ocean.seal.protocol.GameLanguageServiceProto.GameLanguage prototype) {
      return newBuilder().mergeFrom(prototype);
    }
    public Builder toBuilder() { return newBuilder(this); }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code fm.lizhi.commons.template.datacenter.protocol.GameLanguage}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessage.Builder<Builder>
       implements fm.lizhi.ocean.seal.protocol.GameLanguageServiceProto.GameLanguageOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return fm.lizhi.ocean.seal.protocol.GameLanguageServiceProto.internal_static_fm_lizhi_commons_template_datacenter_protocol_GameLanguage_descriptor;
      }

      protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return fm.lizhi.ocean.seal.protocol.GameLanguageServiceProto.internal_static_fm_lizhi_commons_template_datacenter_protocol_GameLanguage_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                fm.lizhi.ocean.seal.protocol.GameLanguageServiceProto.GameLanguage.class, fm.lizhi.ocean.seal.protocol.GameLanguageServiceProto.GameLanguage.Builder.class);
      }

      // Construct using fm.lizhi.ocean.seal.protocol.GameLanguageServiceProto.GameLanguage.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessage.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessage.alwaysUseFieldBuilders) {
        }
      }
      private static Builder create() {
        return new Builder();
      }

      public Builder clear() {
        super.clear();
        id_ = 0L;
        bitField0_ = (bitField0_ & ~0x00000001);
        abbreviation_ = "";
        bitField0_ = (bitField0_ & ~0x00000002);
        languageName_ = "";
        bitField0_ = (bitField0_ & ~0x00000004);
        return this;
      }

      public Builder clone() {
        return create().mergeFrom(buildPartial());
      }

      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return fm.lizhi.ocean.seal.protocol.GameLanguageServiceProto.internal_static_fm_lizhi_commons_template_datacenter_protocol_GameLanguage_descriptor;
      }

      public fm.lizhi.ocean.seal.protocol.GameLanguageServiceProto.GameLanguage getDefaultInstanceForType() {
        return fm.lizhi.ocean.seal.protocol.GameLanguageServiceProto.GameLanguage.getDefaultInstance();
      }

      public fm.lizhi.ocean.seal.protocol.GameLanguageServiceProto.GameLanguage build() {
        fm.lizhi.ocean.seal.protocol.GameLanguageServiceProto.GameLanguage result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      public fm.lizhi.ocean.seal.protocol.GameLanguageServiceProto.GameLanguage buildPartial() {
        fm.lizhi.ocean.seal.protocol.GameLanguageServiceProto.GameLanguage result = new fm.lizhi.ocean.seal.protocol.GameLanguageServiceProto.GameLanguage(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) == 0x00000001)) {
          to_bitField0_ |= 0x00000001;
        }
        result.id_ = id_;
        if (((from_bitField0_ & 0x00000002) == 0x00000002)) {
          to_bitField0_ |= 0x00000002;
        }
        result.abbreviation_ = abbreviation_;
        if (((from_bitField0_ & 0x00000004) == 0x00000004)) {
          to_bitField0_ |= 0x00000004;
        }
        result.languageName_ = languageName_;
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof fm.lizhi.ocean.seal.protocol.GameLanguageServiceProto.GameLanguage) {
          return mergeFrom((fm.lizhi.ocean.seal.protocol.GameLanguageServiceProto.GameLanguage)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(fm.lizhi.ocean.seal.protocol.GameLanguageServiceProto.GameLanguage other) {
        if (other == fm.lizhi.ocean.seal.protocol.GameLanguageServiceProto.GameLanguage.getDefaultInstance()) return this;
        if (other.hasId()) {
          setId(other.getId());
        }
        if (other.hasAbbreviation()) {
          bitField0_ |= 0x00000002;
          abbreviation_ = other.abbreviation_;
          onChanged();
        }
        if (other.hasLanguageName()) {
          bitField0_ |= 0x00000004;
          languageName_ = other.languageName_;
          onChanged();
        }
        this.mergeUnknownFields(other.getUnknownFields());
        return this;
      }

      public final boolean isInitialized() {
        return true;
      }

      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        fm.lizhi.ocean.seal.protocol.GameLanguageServiceProto.GameLanguage parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (fm.lizhi.ocean.seal.protocol.GameLanguageServiceProto.GameLanguage) e.getUnfinishedMessage();
          throw e;
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      // optional int64 id = 1;
      private long id_ ;
      /**
       * <code>optional int64 id = 1;</code>
       */
      public boolean hasId() {
        return ((bitField0_ & 0x00000001) == 0x00000001);
      }
      /**
       * <code>optional int64 id = 1;</code>
       */
      public long getId() {
        return id_;
      }
      /**
       * <code>optional int64 id = 1;</code>
       */
      public Builder setId(long value) {
        bitField0_ |= 0x00000001;
        id_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional int64 id = 1;</code>
       */
      public Builder clearId() {
        bitField0_ = (bitField0_ & ~0x00000001);
        id_ = 0L;
        onChanged();
        return this;
      }

      // optional string abbreviation = 2;
      private java.lang.Object abbreviation_ = "";
      /**
       * <code>optional string abbreviation = 2;</code>
       *
       * <pre>
       *简称 en ar zh
       * </pre>
       */
      public boolean hasAbbreviation() {
        return ((bitField0_ & 0x00000002) == 0x00000002);
      }
      /**
       * <code>optional string abbreviation = 2;</code>
       *
       * <pre>
       *简称 en ar zh
       * </pre>
       */
      public java.lang.String getAbbreviation() {
        java.lang.Object ref = abbreviation_;
        if (!(ref instanceof java.lang.String)) {
          java.lang.String s = ((com.google.protobuf.ByteString) ref)
              .toStringUtf8();
          abbreviation_ = s;
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <code>optional string abbreviation = 2;</code>
       *
       * <pre>
       *简称 en ar zh
       * </pre>
       */
      public com.google.protobuf.ByteString
          getAbbreviationBytes() {
        java.lang.Object ref = abbreviation_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          abbreviation_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <code>optional string abbreviation = 2;</code>
       *
       * <pre>
       *简称 en ar zh
       * </pre>
       */
      public Builder setAbbreviation(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000002;
        abbreviation_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional string abbreviation = 2;</code>
       *
       * <pre>
       *简称 en ar zh
       * </pre>
       */
      public Builder clearAbbreviation() {
        bitField0_ = (bitField0_ & ~0x00000002);
        abbreviation_ = getDefaultInstance().getAbbreviation();
        onChanged();
        return this;
      }
      /**
       * <code>optional string abbreviation = 2;</code>
       *
       * <pre>
       *简称 en ar zh
       * </pre>
       */
      public Builder setAbbreviationBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000002;
        abbreviation_ = value;
        onChanged();
        return this;
      }

      // optional string languageName = 3;
      private java.lang.Object languageName_ = "";
      /**
       * <code>optional string languageName = 3;</code>
       *
       * <pre>
       *中文名称
       * </pre>
       */
      public boolean hasLanguageName() {
        return ((bitField0_ & 0x00000004) == 0x00000004);
      }
      /**
       * <code>optional string languageName = 3;</code>
       *
       * <pre>
       *中文名称
       * </pre>
       */
      public java.lang.String getLanguageName() {
        java.lang.Object ref = languageName_;
        if (!(ref instanceof java.lang.String)) {
          java.lang.String s = ((com.google.protobuf.ByteString) ref)
              .toStringUtf8();
          languageName_ = s;
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <code>optional string languageName = 3;</code>
       *
       * <pre>
       *中文名称
       * </pre>
       */
      public com.google.protobuf.ByteString
          getLanguageNameBytes() {
        java.lang.Object ref = languageName_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          languageName_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <code>optional string languageName = 3;</code>
       *
       * <pre>
       *中文名称
       * </pre>
       */
      public Builder setLanguageName(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000004;
        languageName_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional string languageName = 3;</code>
       *
       * <pre>
       *中文名称
       * </pre>
       */
      public Builder clearLanguageName() {
        bitField0_ = (bitField0_ & ~0x00000004);
        languageName_ = getDefaultInstance().getLanguageName();
        onChanged();
        return this;
      }
      /**
       * <code>optional string languageName = 3;</code>
       *
       * <pre>
       *中文名称
       * </pre>
       */
      public Builder setLanguageNameBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000004;
        languageName_ = value;
        onChanged();
        return this;
      }

      // @@protoc_insertion_point(builder_scope:fm.lizhi.commons.template.datacenter.protocol.GameLanguage)
    }

    static {
      defaultInstance = new GameLanguage(true);
      defaultInstance.initFields();
    }

    // @@protoc_insertion_point(class_scope:fm.lizhi.commons.template.datacenter.protocol.GameLanguage)
  }

  public interface RequestGetAllLanguagesOrBuilder
      extends com.google.protobuf.MessageOrBuilder {
  }
  /**
   * Protobuf type {@code fm.lizhi.commons.template.datacenter.protocol.RequestGetAllLanguages}
   *
   * <pre>
   * GameLanguageService.java
   * 获取游戏语言列表
   * domain = 4302, op = 200
   * </pre>
   */
  public static final class RequestGetAllLanguages extends
      com.google.protobuf.GeneratedMessage
      implements RequestGetAllLanguagesOrBuilder {
    // Use RequestGetAllLanguages.newBuilder() to construct.
    private RequestGetAllLanguages(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
      super(builder);
      this.unknownFields = builder.getUnknownFields();
    }
    private RequestGetAllLanguages(boolean noInit) { this.unknownFields = com.google.protobuf.UnknownFieldSet.getDefaultInstance(); }

    private static final RequestGetAllLanguages defaultInstance;
    public static RequestGetAllLanguages getDefaultInstance() {
      return defaultInstance;
    }

    public RequestGetAllLanguages getDefaultInstanceForType() {
      return defaultInstance;
    }

    private final com.google.protobuf.UnknownFieldSet unknownFields;
    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
        getUnknownFields() {
      return this.unknownFields;
    }
    private RequestGetAllLanguages(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      initFields();
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            default: {
              if (!parseUnknownField(input, unknownFields,
                                     extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e.getMessage()).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return fm.lizhi.ocean.seal.protocol.GameLanguageServiceProto.internal_static_fm_lizhi_commons_template_datacenter_protocol_RequestGetAllLanguages_descriptor;
    }

    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return fm.lizhi.ocean.seal.protocol.GameLanguageServiceProto.internal_static_fm_lizhi_commons_template_datacenter_protocol_RequestGetAllLanguages_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              fm.lizhi.ocean.seal.protocol.GameLanguageServiceProto.RequestGetAllLanguages.class, fm.lizhi.ocean.seal.protocol.GameLanguageServiceProto.RequestGetAllLanguages.Builder.class);
    }

    public static com.google.protobuf.Parser<RequestGetAllLanguages> PARSER =
        new com.google.protobuf.AbstractParser<RequestGetAllLanguages>() {
      public RequestGetAllLanguages parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new RequestGetAllLanguages(input, extensionRegistry);
      }
    };

    @java.lang.Override
    public com.google.protobuf.Parser<RequestGetAllLanguages> getParserForType() {
      return PARSER;
    }

    private void initFields() {
    }
    private byte memoizedIsInitialized = -1;
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized != -1) return isInitialized == 1;

      memoizedIsInitialized = 1;
      return true;
    }

    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      getSerializedSize();
      getUnknownFields().writeTo(output);
    }

    private int memoizedSerializedSize = -1;
    public int getSerializedSize() {
      int size = memoizedSerializedSize;
      if (size != -1) return size;

      size = 0;
      size += getUnknownFields().getSerializedSize();
      memoizedSerializedSize = size;
      return size;
    }

    private static final long serialVersionUID = 0L;
    @java.lang.Override
    protected java.lang.Object writeReplace()
        throws java.io.ObjectStreamException {
      return super.writeReplace();
    }

    public static fm.lizhi.ocean.seal.protocol.GameLanguageServiceProto.RequestGetAllLanguages parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static fm.lizhi.ocean.seal.protocol.GameLanguageServiceProto.RequestGetAllLanguages parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static fm.lizhi.ocean.seal.protocol.GameLanguageServiceProto.RequestGetAllLanguages parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static fm.lizhi.ocean.seal.protocol.GameLanguageServiceProto.RequestGetAllLanguages parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static fm.lizhi.ocean.seal.protocol.GameLanguageServiceProto.RequestGetAllLanguages parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return PARSER.parseFrom(input);
    }
    public static fm.lizhi.ocean.seal.protocol.GameLanguageServiceProto.RequestGetAllLanguages parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseFrom(input, extensionRegistry);
    }
    public static fm.lizhi.ocean.seal.protocol.GameLanguageServiceProto.RequestGetAllLanguages parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return PARSER.parseDelimitedFrom(input);
    }
    public static fm.lizhi.ocean.seal.protocol.GameLanguageServiceProto.RequestGetAllLanguages parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseDelimitedFrom(input, extensionRegistry);
    }
    public static fm.lizhi.ocean.seal.protocol.GameLanguageServiceProto.RequestGetAllLanguages parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return PARSER.parseFrom(input);
    }
    public static fm.lizhi.ocean.seal.protocol.GameLanguageServiceProto.RequestGetAllLanguages parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseFrom(input, extensionRegistry);
    }

    public static Builder newBuilder() { return Builder.create(); }
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder(fm.lizhi.ocean.seal.protocol.GameLanguageServiceProto.RequestGetAllLanguages prototype) {
      return newBuilder().mergeFrom(prototype);
    }
    public Builder toBuilder() { return newBuilder(this); }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code fm.lizhi.commons.template.datacenter.protocol.RequestGetAllLanguages}
     *
     * <pre>
     * GameLanguageService.java
     * 获取游戏语言列表
     * domain = 4302, op = 200
     * </pre>
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessage.Builder<Builder>
       implements fm.lizhi.ocean.seal.protocol.GameLanguageServiceProto.RequestGetAllLanguagesOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return fm.lizhi.ocean.seal.protocol.GameLanguageServiceProto.internal_static_fm_lizhi_commons_template_datacenter_protocol_RequestGetAllLanguages_descriptor;
      }

      protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return fm.lizhi.ocean.seal.protocol.GameLanguageServiceProto.internal_static_fm_lizhi_commons_template_datacenter_protocol_RequestGetAllLanguages_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                fm.lizhi.ocean.seal.protocol.GameLanguageServiceProto.RequestGetAllLanguages.class, fm.lizhi.ocean.seal.protocol.GameLanguageServiceProto.RequestGetAllLanguages.Builder.class);
      }

      // Construct using fm.lizhi.ocean.seal.protocol.GameLanguageServiceProto.RequestGetAllLanguages.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessage.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessage.alwaysUseFieldBuilders) {
        }
      }
      private static Builder create() {
        return new Builder();
      }

      public Builder clear() {
        super.clear();
        return this;
      }

      public Builder clone() {
        return create().mergeFrom(buildPartial());
      }

      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return fm.lizhi.ocean.seal.protocol.GameLanguageServiceProto.internal_static_fm_lizhi_commons_template_datacenter_protocol_RequestGetAllLanguages_descriptor;
      }

      public fm.lizhi.ocean.seal.protocol.GameLanguageServiceProto.RequestGetAllLanguages getDefaultInstanceForType() {
        return fm.lizhi.ocean.seal.protocol.GameLanguageServiceProto.RequestGetAllLanguages.getDefaultInstance();
      }

      public fm.lizhi.ocean.seal.protocol.GameLanguageServiceProto.RequestGetAllLanguages build() {
        fm.lizhi.ocean.seal.protocol.GameLanguageServiceProto.RequestGetAllLanguages result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      public fm.lizhi.ocean.seal.protocol.GameLanguageServiceProto.RequestGetAllLanguages buildPartial() {
        fm.lizhi.ocean.seal.protocol.GameLanguageServiceProto.RequestGetAllLanguages result = new fm.lizhi.ocean.seal.protocol.GameLanguageServiceProto.RequestGetAllLanguages(this);
        onBuilt();
        return result;
      }

      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof fm.lizhi.ocean.seal.protocol.GameLanguageServiceProto.RequestGetAllLanguages) {
          return mergeFrom((fm.lizhi.ocean.seal.protocol.GameLanguageServiceProto.RequestGetAllLanguages)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(fm.lizhi.ocean.seal.protocol.GameLanguageServiceProto.RequestGetAllLanguages other) {
        if (other == fm.lizhi.ocean.seal.protocol.GameLanguageServiceProto.RequestGetAllLanguages.getDefaultInstance()) return this;
        this.mergeUnknownFields(other.getUnknownFields());
        return this;
      }

      public final boolean isInitialized() {
        return true;
      }

      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        fm.lizhi.ocean.seal.protocol.GameLanguageServiceProto.RequestGetAllLanguages parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (fm.lizhi.ocean.seal.protocol.GameLanguageServiceProto.RequestGetAllLanguages) e.getUnfinishedMessage();
          throw e;
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }

      // @@protoc_insertion_point(builder_scope:fm.lizhi.commons.template.datacenter.protocol.RequestGetAllLanguages)
    }

    static {
      defaultInstance = new RequestGetAllLanguages(true);
      defaultInstance.initFields();
    }

    // @@protoc_insertion_point(class_scope:fm.lizhi.commons.template.datacenter.protocol.RequestGetAllLanguages)
  }

  public interface ResponseGetAllLanguagesOrBuilder
      extends com.google.protobuf.MessageOrBuilder {

    // repeated .fm.lizhi.commons.template.datacenter.protocol.GameLanguage gameLanguages = 1;
    /**
     * <code>repeated .fm.lizhi.commons.template.datacenter.protocol.GameLanguage gameLanguages = 1;</code>
     */
    java.util.List<fm.lizhi.ocean.seal.protocol.GameLanguageServiceProto.GameLanguage> 
        getGameLanguagesList();
    /**
     * <code>repeated .fm.lizhi.commons.template.datacenter.protocol.GameLanguage gameLanguages = 1;</code>
     */
    fm.lizhi.ocean.seal.protocol.GameLanguageServiceProto.GameLanguage getGameLanguages(int index);
    /**
     * <code>repeated .fm.lizhi.commons.template.datacenter.protocol.GameLanguage gameLanguages = 1;</code>
     */
    int getGameLanguagesCount();
    /**
     * <code>repeated .fm.lizhi.commons.template.datacenter.protocol.GameLanguage gameLanguages = 1;</code>
     */
    java.util.List<? extends fm.lizhi.ocean.seal.protocol.GameLanguageServiceProto.GameLanguageOrBuilder> 
        getGameLanguagesOrBuilderList();
    /**
     * <code>repeated .fm.lizhi.commons.template.datacenter.protocol.GameLanguage gameLanguages = 1;</code>
     */
    fm.lizhi.ocean.seal.protocol.GameLanguageServiceProto.GameLanguageOrBuilder getGameLanguagesOrBuilder(
        int index);
  }
  /**
   * Protobuf type {@code fm.lizhi.commons.template.datacenter.protocol.ResponseGetAllLanguages}
   *
   * <pre>
   * rcode == 0(SUCCESS) = 执行成功
   * rcode == 1(FAIL) = 错误
   * </pre>
   */
  public static final class ResponseGetAllLanguages extends
      com.google.protobuf.GeneratedMessage
      implements ResponseGetAllLanguagesOrBuilder {
    // Use ResponseGetAllLanguages.newBuilder() to construct.
    private ResponseGetAllLanguages(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
      super(builder);
      this.unknownFields = builder.getUnknownFields();
    }
    private ResponseGetAllLanguages(boolean noInit) { this.unknownFields = com.google.protobuf.UnknownFieldSet.getDefaultInstance(); }

    private static final ResponseGetAllLanguages defaultInstance;
    public static ResponseGetAllLanguages getDefaultInstance() {
      return defaultInstance;
    }

    public ResponseGetAllLanguages getDefaultInstanceForType() {
      return defaultInstance;
    }

    private final com.google.protobuf.UnknownFieldSet unknownFields;
    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
        getUnknownFields() {
      return this.unknownFields;
    }
    private ResponseGetAllLanguages(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      initFields();
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            default: {
              if (!parseUnknownField(input, unknownFields,
                                     extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
            case 10: {
              if (!((mutable_bitField0_ & 0x00000001) == 0x00000001)) {
                gameLanguages_ = new java.util.ArrayList<fm.lizhi.ocean.seal.protocol.GameLanguageServiceProto.GameLanguage>();
                mutable_bitField0_ |= 0x00000001;
              }
              gameLanguages_.add(input.readMessage(fm.lizhi.ocean.seal.protocol.GameLanguageServiceProto.GameLanguage.PARSER, extensionRegistry));
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e.getMessage()).setUnfinishedMessage(this);
      } finally {
        if (((mutable_bitField0_ & 0x00000001) == 0x00000001)) {
          gameLanguages_ = java.util.Collections.unmodifiableList(gameLanguages_);
        }
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return fm.lizhi.ocean.seal.protocol.GameLanguageServiceProto.internal_static_fm_lizhi_commons_template_datacenter_protocol_ResponseGetAllLanguages_descriptor;
    }

    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return fm.lizhi.ocean.seal.protocol.GameLanguageServiceProto.internal_static_fm_lizhi_commons_template_datacenter_protocol_ResponseGetAllLanguages_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              fm.lizhi.ocean.seal.protocol.GameLanguageServiceProto.ResponseGetAllLanguages.class, fm.lizhi.ocean.seal.protocol.GameLanguageServiceProto.ResponseGetAllLanguages.Builder.class);
    }

    public static com.google.protobuf.Parser<ResponseGetAllLanguages> PARSER =
        new com.google.protobuf.AbstractParser<ResponseGetAllLanguages>() {
      public ResponseGetAllLanguages parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new ResponseGetAllLanguages(input, extensionRegistry);
      }
    };

    @java.lang.Override
    public com.google.protobuf.Parser<ResponseGetAllLanguages> getParserForType() {
      return PARSER;
    }

    // repeated .fm.lizhi.commons.template.datacenter.protocol.GameLanguage gameLanguages = 1;
    public static final int GAMELANGUAGES_FIELD_NUMBER = 1;
    private java.util.List<fm.lizhi.ocean.seal.protocol.GameLanguageServiceProto.GameLanguage> gameLanguages_;
    /**
     * <code>repeated .fm.lizhi.commons.template.datacenter.protocol.GameLanguage gameLanguages = 1;</code>
     */
    public java.util.List<fm.lizhi.ocean.seal.protocol.GameLanguageServiceProto.GameLanguage> getGameLanguagesList() {
      return gameLanguages_;
    }
    /**
     * <code>repeated .fm.lizhi.commons.template.datacenter.protocol.GameLanguage gameLanguages = 1;</code>
     */
    public java.util.List<? extends fm.lizhi.ocean.seal.protocol.GameLanguageServiceProto.GameLanguageOrBuilder> 
        getGameLanguagesOrBuilderList() {
      return gameLanguages_;
    }
    /**
     * <code>repeated .fm.lizhi.commons.template.datacenter.protocol.GameLanguage gameLanguages = 1;</code>
     */
    public int getGameLanguagesCount() {
      return gameLanguages_.size();
    }
    /**
     * <code>repeated .fm.lizhi.commons.template.datacenter.protocol.GameLanguage gameLanguages = 1;</code>
     */
    public fm.lizhi.ocean.seal.protocol.GameLanguageServiceProto.GameLanguage getGameLanguages(int index) {
      return gameLanguages_.get(index);
    }
    /**
     * <code>repeated .fm.lizhi.commons.template.datacenter.protocol.GameLanguage gameLanguages = 1;</code>
     */
    public fm.lizhi.ocean.seal.protocol.GameLanguageServiceProto.GameLanguageOrBuilder getGameLanguagesOrBuilder(
        int index) {
      return gameLanguages_.get(index);
    }

    private void initFields() {
      gameLanguages_ = java.util.Collections.emptyList();
    }
    private byte memoizedIsInitialized = -1;
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized != -1) return isInitialized == 1;

      memoizedIsInitialized = 1;
      return true;
    }

    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      getSerializedSize();
      for (int i = 0; i < gameLanguages_.size(); i++) {
        output.writeMessage(1, gameLanguages_.get(i));
      }
      getUnknownFields().writeTo(output);
    }

    private int memoizedSerializedSize = -1;
    public int getSerializedSize() {
      int size = memoizedSerializedSize;
      if (size != -1) return size;

      size = 0;
      for (int i = 0; i < gameLanguages_.size(); i++) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(1, gameLanguages_.get(i));
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSerializedSize = size;
      return size;
    }

    private static final long serialVersionUID = 0L;
    @java.lang.Override
    protected java.lang.Object writeReplace()
        throws java.io.ObjectStreamException {
      return super.writeReplace();
    }

    public static fm.lizhi.ocean.seal.protocol.GameLanguageServiceProto.ResponseGetAllLanguages parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static fm.lizhi.ocean.seal.protocol.GameLanguageServiceProto.ResponseGetAllLanguages parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static fm.lizhi.ocean.seal.protocol.GameLanguageServiceProto.ResponseGetAllLanguages parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static fm.lizhi.ocean.seal.protocol.GameLanguageServiceProto.ResponseGetAllLanguages parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static fm.lizhi.ocean.seal.protocol.GameLanguageServiceProto.ResponseGetAllLanguages parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return PARSER.parseFrom(input);
    }
    public static fm.lizhi.ocean.seal.protocol.GameLanguageServiceProto.ResponseGetAllLanguages parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseFrom(input, extensionRegistry);
    }
    public static fm.lizhi.ocean.seal.protocol.GameLanguageServiceProto.ResponseGetAllLanguages parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return PARSER.parseDelimitedFrom(input);
    }
    public static fm.lizhi.ocean.seal.protocol.GameLanguageServiceProto.ResponseGetAllLanguages parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseDelimitedFrom(input, extensionRegistry);
    }
    public static fm.lizhi.ocean.seal.protocol.GameLanguageServiceProto.ResponseGetAllLanguages parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return PARSER.parseFrom(input);
    }
    public static fm.lizhi.ocean.seal.protocol.GameLanguageServiceProto.ResponseGetAllLanguages parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseFrom(input, extensionRegistry);
    }

    public static Builder newBuilder() { return Builder.create(); }
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder(fm.lizhi.ocean.seal.protocol.GameLanguageServiceProto.ResponseGetAllLanguages prototype) {
      return newBuilder().mergeFrom(prototype);
    }
    public Builder toBuilder() { return newBuilder(this); }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code fm.lizhi.commons.template.datacenter.protocol.ResponseGetAllLanguages}
     *
     * <pre>
     * rcode == 0(SUCCESS) = 执行成功
     * rcode == 1(FAIL) = 错误
     * </pre>
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessage.Builder<Builder>
       implements fm.lizhi.ocean.seal.protocol.GameLanguageServiceProto.ResponseGetAllLanguagesOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return fm.lizhi.ocean.seal.protocol.GameLanguageServiceProto.internal_static_fm_lizhi_commons_template_datacenter_protocol_ResponseGetAllLanguages_descriptor;
      }

      protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return fm.lizhi.ocean.seal.protocol.GameLanguageServiceProto.internal_static_fm_lizhi_commons_template_datacenter_protocol_ResponseGetAllLanguages_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                fm.lizhi.ocean.seal.protocol.GameLanguageServiceProto.ResponseGetAllLanguages.class, fm.lizhi.ocean.seal.protocol.GameLanguageServiceProto.ResponseGetAllLanguages.Builder.class);
      }

      // Construct using fm.lizhi.ocean.seal.protocol.GameLanguageServiceProto.ResponseGetAllLanguages.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessage.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessage.alwaysUseFieldBuilders) {
          getGameLanguagesFieldBuilder();
        }
      }
      private static Builder create() {
        return new Builder();
      }

      public Builder clear() {
        super.clear();
        if (gameLanguagesBuilder_ == null) {
          gameLanguages_ = java.util.Collections.emptyList();
          bitField0_ = (bitField0_ & ~0x00000001);
        } else {
          gameLanguagesBuilder_.clear();
        }
        return this;
      }

      public Builder clone() {
        return create().mergeFrom(buildPartial());
      }

      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return fm.lizhi.ocean.seal.protocol.GameLanguageServiceProto.internal_static_fm_lizhi_commons_template_datacenter_protocol_ResponseGetAllLanguages_descriptor;
      }

      public fm.lizhi.ocean.seal.protocol.GameLanguageServiceProto.ResponseGetAllLanguages getDefaultInstanceForType() {
        return fm.lizhi.ocean.seal.protocol.GameLanguageServiceProto.ResponseGetAllLanguages.getDefaultInstance();
      }

      public fm.lizhi.ocean.seal.protocol.GameLanguageServiceProto.ResponseGetAllLanguages build() {
        fm.lizhi.ocean.seal.protocol.GameLanguageServiceProto.ResponseGetAllLanguages result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      public fm.lizhi.ocean.seal.protocol.GameLanguageServiceProto.ResponseGetAllLanguages buildPartial() {
        fm.lizhi.ocean.seal.protocol.GameLanguageServiceProto.ResponseGetAllLanguages result = new fm.lizhi.ocean.seal.protocol.GameLanguageServiceProto.ResponseGetAllLanguages(this);
        int from_bitField0_ = bitField0_;
        if (gameLanguagesBuilder_ == null) {
          if (((bitField0_ & 0x00000001) == 0x00000001)) {
            gameLanguages_ = java.util.Collections.unmodifiableList(gameLanguages_);
            bitField0_ = (bitField0_ & ~0x00000001);
          }
          result.gameLanguages_ = gameLanguages_;
        } else {
          result.gameLanguages_ = gameLanguagesBuilder_.build();
        }
        onBuilt();
        return result;
      }

      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof fm.lizhi.ocean.seal.protocol.GameLanguageServiceProto.ResponseGetAllLanguages) {
          return mergeFrom((fm.lizhi.ocean.seal.protocol.GameLanguageServiceProto.ResponseGetAllLanguages)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(fm.lizhi.ocean.seal.protocol.GameLanguageServiceProto.ResponseGetAllLanguages other) {
        if (other == fm.lizhi.ocean.seal.protocol.GameLanguageServiceProto.ResponseGetAllLanguages.getDefaultInstance()) return this;
        if (gameLanguagesBuilder_ == null) {
          if (!other.gameLanguages_.isEmpty()) {
            if (gameLanguages_.isEmpty()) {
              gameLanguages_ = other.gameLanguages_;
              bitField0_ = (bitField0_ & ~0x00000001);
            } else {
              ensureGameLanguagesIsMutable();
              gameLanguages_.addAll(other.gameLanguages_);
            }
            onChanged();
          }
        } else {
          if (!other.gameLanguages_.isEmpty()) {
            if (gameLanguagesBuilder_.isEmpty()) {
              gameLanguagesBuilder_.dispose();
              gameLanguagesBuilder_ = null;
              gameLanguages_ = other.gameLanguages_;
              bitField0_ = (bitField0_ & ~0x00000001);
              gameLanguagesBuilder_ = 
                com.google.protobuf.GeneratedMessage.alwaysUseFieldBuilders ?
                   getGameLanguagesFieldBuilder() : null;
            } else {
              gameLanguagesBuilder_.addAllMessages(other.gameLanguages_);
            }
          }
        }
        this.mergeUnknownFields(other.getUnknownFields());
        return this;
      }

      public final boolean isInitialized() {
        return true;
      }

      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        fm.lizhi.ocean.seal.protocol.GameLanguageServiceProto.ResponseGetAllLanguages parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (fm.lizhi.ocean.seal.protocol.GameLanguageServiceProto.ResponseGetAllLanguages) e.getUnfinishedMessage();
          throw e;
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      // repeated .fm.lizhi.commons.template.datacenter.protocol.GameLanguage gameLanguages = 1;
      private java.util.List<fm.lizhi.ocean.seal.protocol.GameLanguageServiceProto.GameLanguage> gameLanguages_ =
        java.util.Collections.emptyList();
      private void ensureGameLanguagesIsMutable() {
        if (!((bitField0_ & 0x00000001) == 0x00000001)) {
          gameLanguages_ = new java.util.ArrayList<fm.lizhi.ocean.seal.protocol.GameLanguageServiceProto.GameLanguage>(gameLanguages_);
          bitField0_ |= 0x00000001;
         }
      }

      private com.google.protobuf.RepeatedFieldBuilder<
          fm.lizhi.ocean.seal.protocol.GameLanguageServiceProto.GameLanguage, fm.lizhi.ocean.seal.protocol.GameLanguageServiceProto.GameLanguage.Builder, fm.lizhi.ocean.seal.protocol.GameLanguageServiceProto.GameLanguageOrBuilder> gameLanguagesBuilder_;

      /**
       * <code>repeated .fm.lizhi.commons.template.datacenter.protocol.GameLanguage gameLanguages = 1;</code>
       */
      public java.util.List<fm.lizhi.ocean.seal.protocol.GameLanguageServiceProto.GameLanguage> getGameLanguagesList() {
        if (gameLanguagesBuilder_ == null) {
          return java.util.Collections.unmodifiableList(gameLanguages_);
        } else {
          return gameLanguagesBuilder_.getMessageList();
        }
      }
      /**
       * <code>repeated .fm.lizhi.commons.template.datacenter.protocol.GameLanguage gameLanguages = 1;</code>
       */
      public int getGameLanguagesCount() {
        if (gameLanguagesBuilder_ == null) {
          return gameLanguages_.size();
        } else {
          return gameLanguagesBuilder_.getCount();
        }
      }
      /**
       * <code>repeated .fm.lizhi.commons.template.datacenter.protocol.GameLanguage gameLanguages = 1;</code>
       */
      public fm.lizhi.ocean.seal.protocol.GameLanguageServiceProto.GameLanguage getGameLanguages(int index) {
        if (gameLanguagesBuilder_ == null) {
          return gameLanguages_.get(index);
        } else {
          return gameLanguagesBuilder_.getMessage(index);
        }
      }
      /**
       * <code>repeated .fm.lizhi.commons.template.datacenter.protocol.GameLanguage gameLanguages = 1;</code>
       */
      public Builder setGameLanguages(
          int index, fm.lizhi.ocean.seal.protocol.GameLanguageServiceProto.GameLanguage value) {
        if (gameLanguagesBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureGameLanguagesIsMutable();
          gameLanguages_.set(index, value);
          onChanged();
        } else {
          gameLanguagesBuilder_.setMessage(index, value);
        }
        return this;
      }
      /**
       * <code>repeated .fm.lizhi.commons.template.datacenter.protocol.GameLanguage gameLanguages = 1;</code>
       */
      public Builder setGameLanguages(
          int index, fm.lizhi.ocean.seal.protocol.GameLanguageServiceProto.GameLanguage.Builder builderForValue) {
        if (gameLanguagesBuilder_ == null) {
          ensureGameLanguagesIsMutable();
          gameLanguages_.set(index, builderForValue.build());
          onChanged();
        } else {
          gameLanguagesBuilder_.setMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .fm.lizhi.commons.template.datacenter.protocol.GameLanguage gameLanguages = 1;</code>
       */
      public Builder addGameLanguages(fm.lizhi.ocean.seal.protocol.GameLanguageServiceProto.GameLanguage value) {
        if (gameLanguagesBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureGameLanguagesIsMutable();
          gameLanguages_.add(value);
          onChanged();
        } else {
          gameLanguagesBuilder_.addMessage(value);
        }
        return this;
      }
      /**
       * <code>repeated .fm.lizhi.commons.template.datacenter.protocol.GameLanguage gameLanguages = 1;</code>
       */
      public Builder addGameLanguages(
          int index, fm.lizhi.ocean.seal.protocol.GameLanguageServiceProto.GameLanguage value) {
        if (gameLanguagesBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureGameLanguagesIsMutable();
          gameLanguages_.add(index, value);
          onChanged();
        } else {
          gameLanguagesBuilder_.addMessage(index, value);
        }
        return this;
      }
      /**
       * <code>repeated .fm.lizhi.commons.template.datacenter.protocol.GameLanguage gameLanguages = 1;</code>
       */
      public Builder addGameLanguages(
          fm.lizhi.ocean.seal.protocol.GameLanguageServiceProto.GameLanguage.Builder builderForValue) {
        if (gameLanguagesBuilder_ == null) {
          ensureGameLanguagesIsMutable();
          gameLanguages_.add(builderForValue.build());
          onChanged();
        } else {
          gameLanguagesBuilder_.addMessage(builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .fm.lizhi.commons.template.datacenter.protocol.GameLanguage gameLanguages = 1;</code>
       */
      public Builder addGameLanguages(
          int index, fm.lizhi.ocean.seal.protocol.GameLanguageServiceProto.GameLanguage.Builder builderForValue) {
        if (gameLanguagesBuilder_ == null) {
          ensureGameLanguagesIsMutable();
          gameLanguages_.add(index, builderForValue.build());
          onChanged();
        } else {
          gameLanguagesBuilder_.addMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .fm.lizhi.commons.template.datacenter.protocol.GameLanguage gameLanguages = 1;</code>
       */
      public Builder addAllGameLanguages(
          java.lang.Iterable<? extends fm.lizhi.ocean.seal.protocol.GameLanguageServiceProto.GameLanguage> values) {
        if (gameLanguagesBuilder_ == null) {
          ensureGameLanguagesIsMutable();
          super.addAll(values, gameLanguages_);
          onChanged();
        } else {
          gameLanguagesBuilder_.addAllMessages(values);
        }
        return this;
      }
      /**
       * <code>repeated .fm.lizhi.commons.template.datacenter.protocol.GameLanguage gameLanguages = 1;</code>
       */
      public Builder clearGameLanguages() {
        if (gameLanguagesBuilder_ == null) {
          gameLanguages_ = java.util.Collections.emptyList();
          bitField0_ = (bitField0_ & ~0x00000001);
          onChanged();
        } else {
          gameLanguagesBuilder_.clear();
        }
        return this;
      }
      /**
       * <code>repeated .fm.lizhi.commons.template.datacenter.protocol.GameLanguage gameLanguages = 1;</code>
       */
      public Builder removeGameLanguages(int index) {
        if (gameLanguagesBuilder_ == null) {
          ensureGameLanguagesIsMutable();
          gameLanguages_.remove(index);
          onChanged();
        } else {
          gameLanguagesBuilder_.remove(index);
        }
        return this;
      }
      /**
       * <code>repeated .fm.lizhi.commons.template.datacenter.protocol.GameLanguage gameLanguages = 1;</code>
       */
      public fm.lizhi.ocean.seal.protocol.GameLanguageServiceProto.GameLanguage.Builder getGameLanguagesBuilder(
          int index) {
        return getGameLanguagesFieldBuilder().getBuilder(index);
      }
      /**
       * <code>repeated .fm.lizhi.commons.template.datacenter.protocol.GameLanguage gameLanguages = 1;</code>
       */
      public fm.lizhi.ocean.seal.protocol.GameLanguageServiceProto.GameLanguageOrBuilder getGameLanguagesOrBuilder(
          int index) {
        if (gameLanguagesBuilder_ == null) {
          return gameLanguages_.get(index);  } else {
          return gameLanguagesBuilder_.getMessageOrBuilder(index);
        }
      }
      /**
       * <code>repeated .fm.lizhi.commons.template.datacenter.protocol.GameLanguage gameLanguages = 1;</code>
       */
      public java.util.List<? extends fm.lizhi.ocean.seal.protocol.GameLanguageServiceProto.GameLanguageOrBuilder> 
           getGameLanguagesOrBuilderList() {
        if (gameLanguagesBuilder_ != null) {
          return gameLanguagesBuilder_.getMessageOrBuilderList();
        } else {
          return java.util.Collections.unmodifiableList(gameLanguages_);
        }
      }
      /**
       * <code>repeated .fm.lizhi.commons.template.datacenter.protocol.GameLanguage gameLanguages = 1;</code>
       */
      public fm.lizhi.ocean.seal.protocol.GameLanguageServiceProto.GameLanguage.Builder addGameLanguagesBuilder() {
        return getGameLanguagesFieldBuilder().addBuilder(
            fm.lizhi.ocean.seal.protocol.GameLanguageServiceProto.GameLanguage.getDefaultInstance());
      }
      /**
       * <code>repeated .fm.lizhi.commons.template.datacenter.protocol.GameLanguage gameLanguages = 1;</code>
       */
      public fm.lizhi.ocean.seal.protocol.GameLanguageServiceProto.GameLanguage.Builder addGameLanguagesBuilder(
          int index) {
        return getGameLanguagesFieldBuilder().addBuilder(
            index, fm.lizhi.ocean.seal.protocol.GameLanguageServiceProto.GameLanguage.getDefaultInstance());
      }
      /**
       * <code>repeated .fm.lizhi.commons.template.datacenter.protocol.GameLanguage gameLanguages = 1;</code>
       */
      public java.util.List<fm.lizhi.ocean.seal.protocol.GameLanguageServiceProto.GameLanguage.Builder> 
           getGameLanguagesBuilderList() {
        return getGameLanguagesFieldBuilder().getBuilderList();
      }
      private com.google.protobuf.RepeatedFieldBuilder<
          fm.lizhi.ocean.seal.protocol.GameLanguageServiceProto.GameLanguage, fm.lizhi.ocean.seal.protocol.GameLanguageServiceProto.GameLanguage.Builder, fm.lizhi.ocean.seal.protocol.GameLanguageServiceProto.GameLanguageOrBuilder> 
          getGameLanguagesFieldBuilder() {
        if (gameLanguagesBuilder_ == null) {
          gameLanguagesBuilder_ = new com.google.protobuf.RepeatedFieldBuilder<
              fm.lizhi.ocean.seal.protocol.GameLanguageServiceProto.GameLanguage, fm.lizhi.ocean.seal.protocol.GameLanguageServiceProto.GameLanguage.Builder, fm.lizhi.ocean.seal.protocol.GameLanguageServiceProto.GameLanguageOrBuilder>(
                  gameLanguages_,
                  ((bitField0_ & 0x00000001) == 0x00000001),
                  getParentForChildren(),
                  isClean());
          gameLanguages_ = null;
        }
        return gameLanguagesBuilder_;
      }

      // @@protoc_insertion_point(builder_scope:fm.lizhi.commons.template.datacenter.protocol.ResponseGetAllLanguages)
    }

    static {
      defaultInstance = new ResponseGetAllLanguages(true);
      defaultInstance.initFields();
    }

    // @@protoc_insertion_point(class_scope:fm.lizhi.commons.template.datacenter.protocol.ResponseGetAllLanguages)
  }

  private static com.google.protobuf.Descriptors.Descriptor
    internal_static_fm_lizhi_commons_template_datacenter_protocol_GameLanguage_descriptor;
  private static
    com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internal_static_fm_lizhi_commons_template_datacenter_protocol_GameLanguage_fieldAccessorTable;
  private static com.google.protobuf.Descriptors.Descriptor
    internal_static_fm_lizhi_commons_template_datacenter_protocol_RequestGetAllLanguages_descriptor;
  private static
    com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internal_static_fm_lizhi_commons_template_datacenter_protocol_RequestGetAllLanguages_fieldAccessorTable;
  private static com.google.protobuf.Descriptors.Descriptor
    internal_static_fm_lizhi_commons_template_datacenter_protocol_ResponseGetAllLanguages_descriptor;
  private static
    com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internal_static_fm_lizhi_commons_template_datacenter_protocol_ResponseGetAllLanguages_fieldAccessorTable;

  public static com.google.protobuf.Descriptors.FileDescriptor
      getDescriptor() {
    return descriptor;
  }
  private static com.google.protobuf.Descriptors.FileDescriptor
      descriptor;
  static {
    java.lang.String[] descriptorData = {
      "\n\034protocol_game_language.proto\022-fm.lizhi" +
      ".commons.template.datacenter.protocol\"F\n" +
      "\014GameLanguage\022\n\n\002id\030\001 \001(\003\022\024\n\014abbreviatio" +
      "n\030\002 \001(\t\022\024\n\014languageName\030\003 \001(\t\"\030\n\026Request" +
      "GetAllLanguages\"m\n\027ResponseGetAllLanguag" +
      "es\022R\n\rgameLanguages\030\001 \003(\0132;.fm.lizhi.com" +
      "mons.template.datacenter.protocol.GameLa" +
      "nguageB8\n\034fm.lizhi.ocean.seal.protocolB\030" +
      "GameLanguageServiceProto"
    };
    com.google.protobuf.Descriptors.FileDescriptor.InternalDescriptorAssigner assigner =
      new com.google.protobuf.Descriptors.FileDescriptor.InternalDescriptorAssigner() {
        public com.google.protobuf.ExtensionRegistry assignDescriptors(
            com.google.protobuf.Descriptors.FileDescriptor root) {
          descriptor = root;
          internal_static_fm_lizhi_commons_template_datacenter_protocol_GameLanguage_descriptor =
            getDescriptor().getMessageTypes().get(0);
          internal_static_fm_lizhi_commons_template_datacenter_protocol_GameLanguage_fieldAccessorTable = new
            com.google.protobuf.GeneratedMessage.FieldAccessorTable(
              internal_static_fm_lizhi_commons_template_datacenter_protocol_GameLanguage_descriptor,
              new java.lang.String[] { "Id", "Abbreviation", "LanguageName", });
          internal_static_fm_lizhi_commons_template_datacenter_protocol_RequestGetAllLanguages_descriptor =
            getDescriptor().getMessageTypes().get(1);
          internal_static_fm_lizhi_commons_template_datacenter_protocol_RequestGetAllLanguages_fieldAccessorTable = new
            com.google.protobuf.GeneratedMessage.FieldAccessorTable(
              internal_static_fm_lizhi_commons_template_datacenter_protocol_RequestGetAllLanguages_descriptor,
              new java.lang.String[] { });
          internal_static_fm_lizhi_commons_template_datacenter_protocol_ResponseGetAllLanguages_descriptor =
            getDescriptor().getMessageTypes().get(2);
          internal_static_fm_lizhi_commons_template_datacenter_protocol_ResponseGetAllLanguages_fieldAccessorTable = new
            com.google.protobuf.GeneratedMessage.FieldAccessorTable(
              internal_static_fm_lizhi_commons_template_datacenter_protocol_ResponseGetAllLanguages_descriptor,
              new java.lang.String[] { "GameLanguages", });
          return null;
        }
      };
    com.google.protobuf.Descriptors.FileDescriptor
      .internalBuildGeneratedFileFrom(descriptorData,
        new com.google.protobuf.Descriptors.FileDescriptor[] {
        }, assigner);
  }

  // @@protoc_insertion_point(outer_class_scope)
}
