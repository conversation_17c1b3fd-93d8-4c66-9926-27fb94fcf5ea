// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: protocol_seal_token.proto

package fm.lizhi.ocean.seal.protocol;

public final class SealTokenServiceProto {
  private SealTokenServiceProto() {}
  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistry registry) {
  }
  public interface SealHashingTokenParamOrBuilder
      extends com.google.protobuf.MessageOrBuilder {

    // optional int64 userId = 1;
    /**
     * <code>optional int64 userId = 1;</code>
     *
     * <pre>
     *业务对应的标识码。使用userId作为标识码
     * </pre>
     */
    boolean hasUserId();
    /**
     * <code>optional int64 userId = 1;</code>
     *
     * <pre>
     *业务对应的标识码。使用userId作为标识码
     * </pre>
     */
    long getUserId();

    // optional string appId = 2;
    /**
     * <code>optional string appId = 2;</code>
     *
     * <pre>
     *appId
     * </pre>
     */
    boolean hasAppId();
    /**
     * <code>optional string appId = 2;</code>
     *
     * <pre>
     *appId
     * </pre>
     */
    java.lang.String getAppId();
    /**
     * <code>optional string appId = 2;</code>
     *
     * <pre>
     *appId
     * </pre>
     */
    com.google.protobuf.ByteString
        getAppIdBytes();

    // optional int64 currentTimeMillis = 3;
    /**
     * <code>optional int64 currentTimeMillis = 3;</code>
     *
     * <pre>
     *时间戳
     * </pre>
     */
    boolean hasCurrentTimeMillis();
    /**
     * <code>optional int64 currentTimeMillis = 3;</code>
     *
     * <pre>
     *时间戳
     * </pre>
     */
    long getCurrentTimeMillis();

    // optional string hashingToken = 4;
    /**
     * <code>optional string hashingToken = 4;</code>
     *
     * <pre>
     *验证token，验证的时候把它放进去
     * </pre>
     */
    boolean hasHashingToken();
    /**
     * <code>optional string hashingToken = 4;</code>
     *
     * <pre>
     *验证token，验证的时候把它放进去
     * </pre>
     */
    java.lang.String getHashingToken();
    /**
     * <code>optional string hashingToken = 4;</code>
     *
     * <pre>
     *验证token，验证的时候把它放进去
     * </pre>
     */
    com.google.protobuf.ByteString
        getHashingTokenBytes();

    // optional bool notVerifySign = 5;
    /**
     * <code>optional bool notVerifySign = 5;</code>
     *
     * <pre>
     * 不进行验签名.
     * </pre>
     */
    boolean hasNotVerifySign();
    /**
     * <code>optional bool notVerifySign = 5;</code>
     *
     * <pre>
     * 不进行验签名.
     * </pre>
     */
    boolean getNotVerifySign();
  }
  /**
   * Protobuf type {@code fm.lizhi.commons.template.datacenter.protocol.SealHashingTokenParam}
   */
  public static final class SealHashingTokenParam extends
      com.google.protobuf.GeneratedMessage
      implements SealHashingTokenParamOrBuilder {
    // Use SealHashingTokenParam.newBuilder() to construct.
    private SealHashingTokenParam(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
      super(builder);
      this.unknownFields = builder.getUnknownFields();
    }
    private SealHashingTokenParam(boolean noInit) { this.unknownFields = com.google.protobuf.UnknownFieldSet.getDefaultInstance(); }

    private static final SealHashingTokenParam defaultInstance;
    public static SealHashingTokenParam getDefaultInstance() {
      return defaultInstance;
    }

    public SealHashingTokenParam getDefaultInstanceForType() {
      return defaultInstance;
    }

    private final com.google.protobuf.UnknownFieldSet unknownFields;
    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
        getUnknownFields() {
      return this.unknownFields;
    }
    private SealHashingTokenParam(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      initFields();
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            default: {
              if (!parseUnknownField(input, unknownFields,
                                     extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
            case 8: {
              bitField0_ |= 0x00000001;
              userId_ = input.readInt64();
              break;
            }
            case 18: {
              bitField0_ |= 0x00000002;
              appId_ = input.readBytes();
              break;
            }
            case 24: {
              bitField0_ |= 0x00000004;
              currentTimeMillis_ = input.readInt64();
              break;
            }
            case 34: {
              bitField0_ |= 0x00000008;
              hashingToken_ = input.readBytes();
              break;
            }
            case 40: {
              bitField0_ |= 0x00000010;
              notVerifySign_ = input.readBool();
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e.getMessage()).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return fm.lizhi.ocean.seal.protocol.SealTokenServiceProto.internal_static_fm_lizhi_commons_template_datacenter_protocol_SealHashingTokenParam_descriptor;
    }

    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return fm.lizhi.ocean.seal.protocol.SealTokenServiceProto.internal_static_fm_lizhi_commons_template_datacenter_protocol_SealHashingTokenParam_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              fm.lizhi.ocean.seal.protocol.SealTokenServiceProto.SealHashingTokenParam.class, fm.lizhi.ocean.seal.protocol.SealTokenServiceProto.SealHashingTokenParam.Builder.class);
    }

    public static com.google.protobuf.Parser<SealHashingTokenParam> PARSER =
        new com.google.protobuf.AbstractParser<SealHashingTokenParam>() {
      public SealHashingTokenParam parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new SealHashingTokenParam(input, extensionRegistry);
      }
    };

    @java.lang.Override
    public com.google.protobuf.Parser<SealHashingTokenParam> getParserForType() {
      return PARSER;
    }

    private int bitField0_;
    // optional int64 userId = 1;
    public static final int USERID_FIELD_NUMBER = 1;
    private long userId_;
    /**
     * <code>optional int64 userId = 1;</code>
     *
     * <pre>
     *业务对应的标识码。使用userId作为标识码
     * </pre>
     */
    public boolean hasUserId() {
      return ((bitField0_ & 0x00000001) == 0x00000001);
    }
    /**
     * <code>optional int64 userId = 1;</code>
     *
     * <pre>
     *业务对应的标识码。使用userId作为标识码
     * </pre>
     */
    public long getUserId() {
      return userId_;
    }

    // optional string appId = 2;
    public static final int APPID_FIELD_NUMBER = 2;
    private java.lang.Object appId_;
    /**
     * <code>optional string appId = 2;</code>
     *
     * <pre>
     *appId
     * </pre>
     */
    public boolean hasAppId() {
      return ((bitField0_ & 0x00000002) == 0x00000002);
    }
    /**
     * <code>optional string appId = 2;</code>
     *
     * <pre>
     *appId
     * </pre>
     */
    public java.lang.String getAppId() {
      java.lang.Object ref = appId_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          appId_ = s;
        }
        return s;
      }
    }
    /**
     * <code>optional string appId = 2;</code>
     *
     * <pre>
     *appId
     * </pre>
     */
    public com.google.protobuf.ByteString
        getAppIdBytes() {
      java.lang.Object ref = appId_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        appId_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    // optional int64 currentTimeMillis = 3;
    public static final int CURRENTTIMEMILLIS_FIELD_NUMBER = 3;
    private long currentTimeMillis_;
    /**
     * <code>optional int64 currentTimeMillis = 3;</code>
     *
     * <pre>
     *时间戳
     * </pre>
     */
    public boolean hasCurrentTimeMillis() {
      return ((bitField0_ & 0x00000004) == 0x00000004);
    }
    /**
     * <code>optional int64 currentTimeMillis = 3;</code>
     *
     * <pre>
     *时间戳
     * </pre>
     */
    public long getCurrentTimeMillis() {
      return currentTimeMillis_;
    }

    // optional string hashingToken = 4;
    public static final int HASHINGTOKEN_FIELD_NUMBER = 4;
    private java.lang.Object hashingToken_;
    /**
     * <code>optional string hashingToken = 4;</code>
     *
     * <pre>
     *验证token，验证的时候把它放进去
     * </pre>
     */
    public boolean hasHashingToken() {
      return ((bitField0_ & 0x00000008) == 0x00000008);
    }
    /**
     * <code>optional string hashingToken = 4;</code>
     *
     * <pre>
     *验证token，验证的时候把它放进去
     * </pre>
     */
    public java.lang.String getHashingToken() {
      java.lang.Object ref = hashingToken_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          hashingToken_ = s;
        }
        return s;
      }
    }
    /**
     * <code>optional string hashingToken = 4;</code>
     *
     * <pre>
     *验证token，验证的时候把它放进去
     * </pre>
     */
    public com.google.protobuf.ByteString
        getHashingTokenBytes() {
      java.lang.Object ref = hashingToken_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        hashingToken_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    // optional bool notVerifySign = 5;
    public static final int NOTVERIFYSIGN_FIELD_NUMBER = 5;
    private boolean notVerifySign_;
    /**
     * <code>optional bool notVerifySign = 5;</code>
     *
     * <pre>
     * 不进行验签名.
     * </pre>
     */
    public boolean hasNotVerifySign() {
      return ((bitField0_ & 0x00000010) == 0x00000010);
    }
    /**
     * <code>optional bool notVerifySign = 5;</code>
     *
     * <pre>
     * 不进行验签名.
     * </pre>
     */
    public boolean getNotVerifySign() {
      return notVerifySign_;
    }

    private void initFields() {
      userId_ = 0L;
      appId_ = "";
      currentTimeMillis_ = 0L;
      hashingToken_ = "";
      notVerifySign_ = false;
    }
    private byte memoizedIsInitialized = -1;
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized != -1) return isInitialized == 1;

      memoizedIsInitialized = 1;
      return true;
    }

    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      getSerializedSize();
      if (((bitField0_ & 0x00000001) == 0x00000001)) {
        output.writeInt64(1, userId_);
      }
      if (((bitField0_ & 0x00000002) == 0x00000002)) {
        output.writeBytes(2, getAppIdBytes());
      }
      if (((bitField0_ & 0x00000004) == 0x00000004)) {
        output.writeInt64(3, currentTimeMillis_);
      }
      if (((bitField0_ & 0x00000008) == 0x00000008)) {
        output.writeBytes(4, getHashingTokenBytes());
      }
      if (((bitField0_ & 0x00000010) == 0x00000010)) {
        output.writeBool(5, notVerifySign_);
      }
      getUnknownFields().writeTo(output);
    }

    private int memoizedSerializedSize = -1;
    public int getSerializedSize() {
      int size = memoizedSerializedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) == 0x00000001)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt64Size(1, userId_);
      }
      if (((bitField0_ & 0x00000002) == 0x00000002)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(2, getAppIdBytes());
      }
      if (((bitField0_ & 0x00000004) == 0x00000004)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt64Size(3, currentTimeMillis_);
      }
      if (((bitField0_ & 0x00000008) == 0x00000008)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(4, getHashingTokenBytes());
      }
      if (((bitField0_ & 0x00000010) == 0x00000010)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBoolSize(5, notVerifySign_);
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSerializedSize = size;
      return size;
    }

    private static final long serialVersionUID = 0L;
    @java.lang.Override
    protected java.lang.Object writeReplace()
        throws java.io.ObjectStreamException {
      return super.writeReplace();
    }

    public static fm.lizhi.ocean.seal.protocol.SealTokenServiceProto.SealHashingTokenParam parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static fm.lizhi.ocean.seal.protocol.SealTokenServiceProto.SealHashingTokenParam parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static fm.lizhi.ocean.seal.protocol.SealTokenServiceProto.SealHashingTokenParam parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static fm.lizhi.ocean.seal.protocol.SealTokenServiceProto.SealHashingTokenParam parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static fm.lizhi.ocean.seal.protocol.SealTokenServiceProto.SealHashingTokenParam parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return PARSER.parseFrom(input);
    }
    public static fm.lizhi.ocean.seal.protocol.SealTokenServiceProto.SealHashingTokenParam parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseFrom(input, extensionRegistry);
    }
    public static fm.lizhi.ocean.seal.protocol.SealTokenServiceProto.SealHashingTokenParam parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return PARSER.parseDelimitedFrom(input);
    }
    public static fm.lizhi.ocean.seal.protocol.SealTokenServiceProto.SealHashingTokenParam parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseDelimitedFrom(input, extensionRegistry);
    }
    public static fm.lizhi.ocean.seal.protocol.SealTokenServiceProto.SealHashingTokenParam parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return PARSER.parseFrom(input);
    }
    public static fm.lizhi.ocean.seal.protocol.SealTokenServiceProto.SealHashingTokenParam parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseFrom(input, extensionRegistry);
    }

    public static Builder newBuilder() { return Builder.create(); }
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder(fm.lizhi.ocean.seal.protocol.SealTokenServiceProto.SealHashingTokenParam prototype) {
      return newBuilder().mergeFrom(prototype);
    }
    public Builder toBuilder() { return newBuilder(this); }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code fm.lizhi.commons.template.datacenter.protocol.SealHashingTokenParam}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessage.Builder<Builder>
       implements fm.lizhi.ocean.seal.protocol.SealTokenServiceProto.SealHashingTokenParamOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return fm.lizhi.ocean.seal.protocol.SealTokenServiceProto.internal_static_fm_lizhi_commons_template_datacenter_protocol_SealHashingTokenParam_descriptor;
      }

      protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return fm.lizhi.ocean.seal.protocol.SealTokenServiceProto.internal_static_fm_lizhi_commons_template_datacenter_protocol_SealHashingTokenParam_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                fm.lizhi.ocean.seal.protocol.SealTokenServiceProto.SealHashingTokenParam.class, fm.lizhi.ocean.seal.protocol.SealTokenServiceProto.SealHashingTokenParam.Builder.class);
      }

      // Construct using fm.lizhi.ocean.seal.protocol.SealTokenServiceProto.SealHashingTokenParam.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessage.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessage.alwaysUseFieldBuilders) {
        }
      }
      private static Builder create() {
        return new Builder();
      }

      public Builder clear() {
        super.clear();
        userId_ = 0L;
        bitField0_ = (bitField0_ & ~0x00000001);
        appId_ = "";
        bitField0_ = (bitField0_ & ~0x00000002);
        currentTimeMillis_ = 0L;
        bitField0_ = (bitField0_ & ~0x00000004);
        hashingToken_ = "";
        bitField0_ = (bitField0_ & ~0x00000008);
        notVerifySign_ = false;
        bitField0_ = (bitField0_ & ~0x00000010);
        return this;
      }

      public Builder clone() {
        return create().mergeFrom(buildPartial());
      }

      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return fm.lizhi.ocean.seal.protocol.SealTokenServiceProto.internal_static_fm_lizhi_commons_template_datacenter_protocol_SealHashingTokenParam_descriptor;
      }

      public fm.lizhi.ocean.seal.protocol.SealTokenServiceProto.SealHashingTokenParam getDefaultInstanceForType() {
        return fm.lizhi.ocean.seal.protocol.SealTokenServiceProto.SealHashingTokenParam.getDefaultInstance();
      }

      public fm.lizhi.ocean.seal.protocol.SealTokenServiceProto.SealHashingTokenParam build() {
        fm.lizhi.ocean.seal.protocol.SealTokenServiceProto.SealHashingTokenParam result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      public fm.lizhi.ocean.seal.protocol.SealTokenServiceProto.SealHashingTokenParam buildPartial() {
        fm.lizhi.ocean.seal.protocol.SealTokenServiceProto.SealHashingTokenParam result = new fm.lizhi.ocean.seal.protocol.SealTokenServiceProto.SealHashingTokenParam(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) == 0x00000001)) {
          to_bitField0_ |= 0x00000001;
        }
        result.userId_ = userId_;
        if (((from_bitField0_ & 0x00000002) == 0x00000002)) {
          to_bitField0_ |= 0x00000002;
        }
        result.appId_ = appId_;
        if (((from_bitField0_ & 0x00000004) == 0x00000004)) {
          to_bitField0_ |= 0x00000004;
        }
        result.currentTimeMillis_ = currentTimeMillis_;
        if (((from_bitField0_ & 0x00000008) == 0x00000008)) {
          to_bitField0_ |= 0x00000008;
        }
        result.hashingToken_ = hashingToken_;
        if (((from_bitField0_ & 0x00000010) == 0x00000010)) {
          to_bitField0_ |= 0x00000010;
        }
        result.notVerifySign_ = notVerifySign_;
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof fm.lizhi.ocean.seal.protocol.SealTokenServiceProto.SealHashingTokenParam) {
          return mergeFrom((fm.lizhi.ocean.seal.protocol.SealTokenServiceProto.SealHashingTokenParam)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(fm.lizhi.ocean.seal.protocol.SealTokenServiceProto.SealHashingTokenParam other) {
        if (other == fm.lizhi.ocean.seal.protocol.SealTokenServiceProto.SealHashingTokenParam.getDefaultInstance()) return this;
        if (other.hasUserId()) {
          setUserId(other.getUserId());
        }
        if (other.hasAppId()) {
          bitField0_ |= 0x00000002;
          appId_ = other.appId_;
          onChanged();
        }
        if (other.hasCurrentTimeMillis()) {
          setCurrentTimeMillis(other.getCurrentTimeMillis());
        }
        if (other.hasHashingToken()) {
          bitField0_ |= 0x00000008;
          hashingToken_ = other.hashingToken_;
          onChanged();
        }
        if (other.hasNotVerifySign()) {
          setNotVerifySign(other.getNotVerifySign());
        }
        this.mergeUnknownFields(other.getUnknownFields());
        return this;
      }

      public final boolean isInitialized() {
        return true;
      }

      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        fm.lizhi.ocean.seal.protocol.SealTokenServiceProto.SealHashingTokenParam parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (fm.lizhi.ocean.seal.protocol.SealTokenServiceProto.SealHashingTokenParam) e.getUnfinishedMessage();
          throw e;
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      // optional int64 userId = 1;
      private long userId_ ;
      /**
       * <code>optional int64 userId = 1;</code>
       *
       * <pre>
       *业务对应的标识码。使用userId作为标识码
       * </pre>
       */
      public boolean hasUserId() {
        return ((bitField0_ & 0x00000001) == 0x00000001);
      }
      /**
       * <code>optional int64 userId = 1;</code>
       *
       * <pre>
       *业务对应的标识码。使用userId作为标识码
       * </pre>
       */
      public long getUserId() {
        return userId_;
      }
      /**
       * <code>optional int64 userId = 1;</code>
       *
       * <pre>
       *业务对应的标识码。使用userId作为标识码
       * </pre>
       */
      public Builder setUserId(long value) {
        bitField0_ |= 0x00000001;
        userId_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional int64 userId = 1;</code>
       *
       * <pre>
       *业务对应的标识码。使用userId作为标识码
       * </pre>
       */
      public Builder clearUserId() {
        bitField0_ = (bitField0_ & ~0x00000001);
        userId_ = 0L;
        onChanged();
        return this;
      }

      // optional string appId = 2;
      private java.lang.Object appId_ = "";
      /**
       * <code>optional string appId = 2;</code>
       *
       * <pre>
       *appId
       * </pre>
       */
      public boolean hasAppId() {
        return ((bitField0_ & 0x00000002) == 0x00000002);
      }
      /**
       * <code>optional string appId = 2;</code>
       *
       * <pre>
       *appId
       * </pre>
       */
      public java.lang.String getAppId() {
        java.lang.Object ref = appId_;
        if (!(ref instanceof java.lang.String)) {
          java.lang.String s = ((com.google.protobuf.ByteString) ref)
              .toStringUtf8();
          appId_ = s;
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <code>optional string appId = 2;</code>
       *
       * <pre>
       *appId
       * </pre>
       */
      public com.google.protobuf.ByteString
          getAppIdBytes() {
        java.lang.Object ref = appId_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          appId_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <code>optional string appId = 2;</code>
       *
       * <pre>
       *appId
       * </pre>
       */
      public Builder setAppId(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000002;
        appId_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional string appId = 2;</code>
       *
       * <pre>
       *appId
       * </pre>
       */
      public Builder clearAppId() {
        bitField0_ = (bitField0_ & ~0x00000002);
        appId_ = getDefaultInstance().getAppId();
        onChanged();
        return this;
      }
      /**
       * <code>optional string appId = 2;</code>
       *
       * <pre>
       *appId
       * </pre>
       */
      public Builder setAppIdBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000002;
        appId_ = value;
        onChanged();
        return this;
      }

      // optional int64 currentTimeMillis = 3;
      private long currentTimeMillis_ ;
      /**
       * <code>optional int64 currentTimeMillis = 3;</code>
       *
       * <pre>
       *时间戳
       * </pre>
       */
      public boolean hasCurrentTimeMillis() {
        return ((bitField0_ & 0x00000004) == 0x00000004);
      }
      /**
       * <code>optional int64 currentTimeMillis = 3;</code>
       *
       * <pre>
       *时间戳
       * </pre>
       */
      public long getCurrentTimeMillis() {
        return currentTimeMillis_;
      }
      /**
       * <code>optional int64 currentTimeMillis = 3;</code>
       *
       * <pre>
       *时间戳
       * </pre>
       */
      public Builder setCurrentTimeMillis(long value) {
        bitField0_ |= 0x00000004;
        currentTimeMillis_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional int64 currentTimeMillis = 3;</code>
       *
       * <pre>
       *时间戳
       * </pre>
       */
      public Builder clearCurrentTimeMillis() {
        bitField0_ = (bitField0_ & ~0x00000004);
        currentTimeMillis_ = 0L;
        onChanged();
        return this;
      }

      // optional string hashingToken = 4;
      private java.lang.Object hashingToken_ = "";
      /**
       * <code>optional string hashingToken = 4;</code>
       *
       * <pre>
       *验证token，验证的时候把它放进去
       * </pre>
       */
      public boolean hasHashingToken() {
        return ((bitField0_ & 0x00000008) == 0x00000008);
      }
      /**
       * <code>optional string hashingToken = 4;</code>
       *
       * <pre>
       *验证token，验证的时候把它放进去
       * </pre>
       */
      public java.lang.String getHashingToken() {
        java.lang.Object ref = hashingToken_;
        if (!(ref instanceof java.lang.String)) {
          java.lang.String s = ((com.google.protobuf.ByteString) ref)
              .toStringUtf8();
          hashingToken_ = s;
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <code>optional string hashingToken = 4;</code>
       *
       * <pre>
       *验证token，验证的时候把它放进去
       * </pre>
       */
      public com.google.protobuf.ByteString
          getHashingTokenBytes() {
        java.lang.Object ref = hashingToken_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          hashingToken_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <code>optional string hashingToken = 4;</code>
       *
       * <pre>
       *验证token，验证的时候把它放进去
       * </pre>
       */
      public Builder setHashingToken(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000008;
        hashingToken_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional string hashingToken = 4;</code>
       *
       * <pre>
       *验证token，验证的时候把它放进去
       * </pre>
       */
      public Builder clearHashingToken() {
        bitField0_ = (bitField0_ & ~0x00000008);
        hashingToken_ = getDefaultInstance().getHashingToken();
        onChanged();
        return this;
      }
      /**
       * <code>optional string hashingToken = 4;</code>
       *
       * <pre>
       *验证token，验证的时候把它放进去
       * </pre>
       */
      public Builder setHashingTokenBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000008;
        hashingToken_ = value;
        onChanged();
        return this;
      }

      // optional bool notVerifySign = 5;
      private boolean notVerifySign_ ;
      /**
       * <code>optional bool notVerifySign = 5;</code>
       *
       * <pre>
       * 不进行验签名.
       * </pre>
       */
      public boolean hasNotVerifySign() {
        return ((bitField0_ & 0x00000010) == 0x00000010);
      }
      /**
       * <code>optional bool notVerifySign = 5;</code>
       *
       * <pre>
       * 不进行验签名.
       * </pre>
       */
      public boolean getNotVerifySign() {
        return notVerifySign_;
      }
      /**
       * <code>optional bool notVerifySign = 5;</code>
       *
       * <pre>
       * 不进行验签名.
       * </pre>
       */
      public Builder setNotVerifySign(boolean value) {
        bitField0_ |= 0x00000010;
        notVerifySign_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional bool notVerifySign = 5;</code>
       *
       * <pre>
       * 不进行验签名.
       * </pre>
       */
      public Builder clearNotVerifySign() {
        bitField0_ = (bitField0_ & ~0x00000010);
        notVerifySign_ = false;
        onChanged();
        return this;
      }

      // @@protoc_insertion_point(builder_scope:fm.lizhi.commons.template.datacenter.protocol.SealHashingTokenParam)
    }

    static {
      defaultInstance = new SealHashingTokenParam(true);
      defaultInstance.initFields();
    }

    // @@protoc_insertion_point(class_scope:fm.lizhi.commons.template.datacenter.protocol.SealHashingTokenParam)
  }

  public interface JwtUserInfoOrBuilder
      extends com.google.protobuf.MessageOrBuilder {

    // optional int64 userId = 1;
    /**
     * <code>optional int64 userId = 1;</code>
     *
     * <pre>
     *用户id
     * </pre>
     */
    boolean hasUserId();
    /**
     * <code>optional int64 userId = 1;</code>
     *
     * <pre>
     *用户id
     * </pre>
     */
    long getUserId();

    // optional string userName = 2;
    /**
     * <code>optional string userName = 2;</code>
     *
     * <pre>
     *用户名称
     * </pre>
     */
    boolean hasUserName();
    /**
     * <code>optional string userName = 2;</code>
     *
     * <pre>
     *用户名称
     * </pre>
     */
    java.lang.String getUserName();
    /**
     * <code>optional string userName = 2;</code>
     *
     * <pre>
     *用户名称
     * </pre>
     */
    com.google.protobuf.ByteString
        getUserNameBytes();

    // optional string role = 3;
    /**
     * <code>optional string role = 3;</code>
     *
     * <pre>
     *用户角色
     * </pre>
     */
    boolean hasRole();
    /**
     * <code>optional string role = 3;</code>
     *
     * <pre>
     *用户角色
     * </pre>
     */
    java.lang.String getRole();
    /**
     * <code>optional string role = 3;</code>
     *
     * <pre>
     *用户角色
     * </pre>
     */
    com.google.protobuf.ByteString
        getRoleBytes();
  }
  /**
   * Protobuf type {@code fm.lizhi.commons.template.datacenter.protocol.JwtUserInfo}
   */
  public static final class JwtUserInfo extends
      com.google.protobuf.GeneratedMessage
      implements JwtUserInfoOrBuilder {
    // Use JwtUserInfo.newBuilder() to construct.
    private JwtUserInfo(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
      super(builder);
      this.unknownFields = builder.getUnknownFields();
    }
    private JwtUserInfo(boolean noInit) { this.unknownFields = com.google.protobuf.UnknownFieldSet.getDefaultInstance(); }

    private static final JwtUserInfo defaultInstance;
    public static JwtUserInfo getDefaultInstance() {
      return defaultInstance;
    }

    public JwtUserInfo getDefaultInstanceForType() {
      return defaultInstance;
    }

    private final com.google.protobuf.UnknownFieldSet unknownFields;
    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
        getUnknownFields() {
      return this.unknownFields;
    }
    private JwtUserInfo(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      initFields();
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            default: {
              if (!parseUnknownField(input, unknownFields,
                                     extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
            case 8: {
              bitField0_ |= 0x00000001;
              userId_ = input.readInt64();
              break;
            }
            case 18: {
              bitField0_ |= 0x00000002;
              userName_ = input.readBytes();
              break;
            }
            case 26: {
              bitField0_ |= 0x00000004;
              role_ = input.readBytes();
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e.getMessage()).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return fm.lizhi.ocean.seal.protocol.SealTokenServiceProto.internal_static_fm_lizhi_commons_template_datacenter_protocol_JwtUserInfo_descriptor;
    }

    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return fm.lizhi.ocean.seal.protocol.SealTokenServiceProto.internal_static_fm_lizhi_commons_template_datacenter_protocol_JwtUserInfo_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              fm.lizhi.ocean.seal.protocol.SealTokenServiceProto.JwtUserInfo.class, fm.lizhi.ocean.seal.protocol.SealTokenServiceProto.JwtUserInfo.Builder.class);
    }

    public static com.google.protobuf.Parser<JwtUserInfo> PARSER =
        new com.google.protobuf.AbstractParser<JwtUserInfo>() {
      public JwtUserInfo parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new JwtUserInfo(input, extensionRegistry);
      }
    };

    @java.lang.Override
    public com.google.protobuf.Parser<JwtUserInfo> getParserForType() {
      return PARSER;
    }

    private int bitField0_;
    // optional int64 userId = 1;
    public static final int USERID_FIELD_NUMBER = 1;
    private long userId_;
    /**
     * <code>optional int64 userId = 1;</code>
     *
     * <pre>
     *用户id
     * </pre>
     */
    public boolean hasUserId() {
      return ((bitField0_ & 0x00000001) == 0x00000001);
    }
    /**
     * <code>optional int64 userId = 1;</code>
     *
     * <pre>
     *用户id
     * </pre>
     */
    public long getUserId() {
      return userId_;
    }

    // optional string userName = 2;
    public static final int USERNAME_FIELD_NUMBER = 2;
    private java.lang.Object userName_;
    /**
     * <code>optional string userName = 2;</code>
     *
     * <pre>
     *用户名称
     * </pre>
     */
    public boolean hasUserName() {
      return ((bitField0_ & 0x00000002) == 0x00000002);
    }
    /**
     * <code>optional string userName = 2;</code>
     *
     * <pre>
     *用户名称
     * </pre>
     */
    public java.lang.String getUserName() {
      java.lang.Object ref = userName_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          userName_ = s;
        }
        return s;
      }
    }
    /**
     * <code>optional string userName = 2;</code>
     *
     * <pre>
     *用户名称
     * </pre>
     */
    public com.google.protobuf.ByteString
        getUserNameBytes() {
      java.lang.Object ref = userName_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        userName_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    // optional string role = 3;
    public static final int ROLE_FIELD_NUMBER = 3;
    private java.lang.Object role_;
    /**
     * <code>optional string role = 3;</code>
     *
     * <pre>
     *用户角色
     * </pre>
     */
    public boolean hasRole() {
      return ((bitField0_ & 0x00000004) == 0x00000004);
    }
    /**
     * <code>optional string role = 3;</code>
     *
     * <pre>
     *用户角色
     * </pre>
     */
    public java.lang.String getRole() {
      java.lang.Object ref = role_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          role_ = s;
        }
        return s;
      }
    }
    /**
     * <code>optional string role = 3;</code>
     *
     * <pre>
     *用户角色
     * </pre>
     */
    public com.google.protobuf.ByteString
        getRoleBytes() {
      java.lang.Object ref = role_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        role_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    private void initFields() {
      userId_ = 0L;
      userName_ = "";
      role_ = "";
    }
    private byte memoizedIsInitialized = -1;
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized != -1) return isInitialized == 1;

      memoizedIsInitialized = 1;
      return true;
    }

    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      getSerializedSize();
      if (((bitField0_ & 0x00000001) == 0x00000001)) {
        output.writeInt64(1, userId_);
      }
      if (((bitField0_ & 0x00000002) == 0x00000002)) {
        output.writeBytes(2, getUserNameBytes());
      }
      if (((bitField0_ & 0x00000004) == 0x00000004)) {
        output.writeBytes(3, getRoleBytes());
      }
      getUnknownFields().writeTo(output);
    }

    private int memoizedSerializedSize = -1;
    public int getSerializedSize() {
      int size = memoizedSerializedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) == 0x00000001)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt64Size(1, userId_);
      }
      if (((bitField0_ & 0x00000002) == 0x00000002)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(2, getUserNameBytes());
      }
      if (((bitField0_ & 0x00000004) == 0x00000004)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(3, getRoleBytes());
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSerializedSize = size;
      return size;
    }

    private static final long serialVersionUID = 0L;
    @java.lang.Override
    protected java.lang.Object writeReplace()
        throws java.io.ObjectStreamException {
      return super.writeReplace();
    }

    public static fm.lizhi.ocean.seal.protocol.SealTokenServiceProto.JwtUserInfo parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static fm.lizhi.ocean.seal.protocol.SealTokenServiceProto.JwtUserInfo parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static fm.lizhi.ocean.seal.protocol.SealTokenServiceProto.JwtUserInfo parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static fm.lizhi.ocean.seal.protocol.SealTokenServiceProto.JwtUserInfo parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static fm.lizhi.ocean.seal.protocol.SealTokenServiceProto.JwtUserInfo parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return PARSER.parseFrom(input);
    }
    public static fm.lizhi.ocean.seal.protocol.SealTokenServiceProto.JwtUserInfo parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseFrom(input, extensionRegistry);
    }
    public static fm.lizhi.ocean.seal.protocol.SealTokenServiceProto.JwtUserInfo parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return PARSER.parseDelimitedFrom(input);
    }
    public static fm.lizhi.ocean.seal.protocol.SealTokenServiceProto.JwtUserInfo parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseDelimitedFrom(input, extensionRegistry);
    }
    public static fm.lizhi.ocean.seal.protocol.SealTokenServiceProto.JwtUserInfo parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return PARSER.parseFrom(input);
    }
    public static fm.lizhi.ocean.seal.protocol.SealTokenServiceProto.JwtUserInfo parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseFrom(input, extensionRegistry);
    }

    public static Builder newBuilder() { return Builder.create(); }
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder(fm.lizhi.ocean.seal.protocol.SealTokenServiceProto.JwtUserInfo prototype) {
      return newBuilder().mergeFrom(prototype);
    }
    public Builder toBuilder() { return newBuilder(this); }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code fm.lizhi.commons.template.datacenter.protocol.JwtUserInfo}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessage.Builder<Builder>
       implements fm.lizhi.ocean.seal.protocol.SealTokenServiceProto.JwtUserInfoOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return fm.lizhi.ocean.seal.protocol.SealTokenServiceProto.internal_static_fm_lizhi_commons_template_datacenter_protocol_JwtUserInfo_descriptor;
      }

      protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return fm.lizhi.ocean.seal.protocol.SealTokenServiceProto.internal_static_fm_lizhi_commons_template_datacenter_protocol_JwtUserInfo_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                fm.lizhi.ocean.seal.protocol.SealTokenServiceProto.JwtUserInfo.class, fm.lizhi.ocean.seal.protocol.SealTokenServiceProto.JwtUserInfo.Builder.class);
      }

      // Construct using fm.lizhi.ocean.seal.protocol.SealTokenServiceProto.JwtUserInfo.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessage.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessage.alwaysUseFieldBuilders) {
        }
      }
      private static Builder create() {
        return new Builder();
      }

      public Builder clear() {
        super.clear();
        userId_ = 0L;
        bitField0_ = (bitField0_ & ~0x00000001);
        userName_ = "";
        bitField0_ = (bitField0_ & ~0x00000002);
        role_ = "";
        bitField0_ = (bitField0_ & ~0x00000004);
        return this;
      }

      public Builder clone() {
        return create().mergeFrom(buildPartial());
      }

      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return fm.lizhi.ocean.seal.protocol.SealTokenServiceProto.internal_static_fm_lizhi_commons_template_datacenter_protocol_JwtUserInfo_descriptor;
      }

      public fm.lizhi.ocean.seal.protocol.SealTokenServiceProto.JwtUserInfo getDefaultInstanceForType() {
        return fm.lizhi.ocean.seal.protocol.SealTokenServiceProto.JwtUserInfo.getDefaultInstance();
      }

      public fm.lizhi.ocean.seal.protocol.SealTokenServiceProto.JwtUserInfo build() {
        fm.lizhi.ocean.seal.protocol.SealTokenServiceProto.JwtUserInfo result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      public fm.lizhi.ocean.seal.protocol.SealTokenServiceProto.JwtUserInfo buildPartial() {
        fm.lizhi.ocean.seal.protocol.SealTokenServiceProto.JwtUserInfo result = new fm.lizhi.ocean.seal.protocol.SealTokenServiceProto.JwtUserInfo(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) == 0x00000001)) {
          to_bitField0_ |= 0x00000001;
        }
        result.userId_ = userId_;
        if (((from_bitField0_ & 0x00000002) == 0x00000002)) {
          to_bitField0_ |= 0x00000002;
        }
        result.userName_ = userName_;
        if (((from_bitField0_ & 0x00000004) == 0x00000004)) {
          to_bitField0_ |= 0x00000004;
        }
        result.role_ = role_;
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof fm.lizhi.ocean.seal.protocol.SealTokenServiceProto.JwtUserInfo) {
          return mergeFrom((fm.lizhi.ocean.seal.protocol.SealTokenServiceProto.JwtUserInfo)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(fm.lizhi.ocean.seal.protocol.SealTokenServiceProto.JwtUserInfo other) {
        if (other == fm.lizhi.ocean.seal.protocol.SealTokenServiceProto.JwtUserInfo.getDefaultInstance()) return this;
        if (other.hasUserId()) {
          setUserId(other.getUserId());
        }
        if (other.hasUserName()) {
          bitField0_ |= 0x00000002;
          userName_ = other.userName_;
          onChanged();
        }
        if (other.hasRole()) {
          bitField0_ |= 0x00000004;
          role_ = other.role_;
          onChanged();
        }
        this.mergeUnknownFields(other.getUnknownFields());
        return this;
      }

      public final boolean isInitialized() {
        return true;
      }

      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        fm.lizhi.ocean.seal.protocol.SealTokenServiceProto.JwtUserInfo parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (fm.lizhi.ocean.seal.protocol.SealTokenServiceProto.JwtUserInfo) e.getUnfinishedMessage();
          throw e;
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      // optional int64 userId = 1;
      private long userId_ ;
      /**
       * <code>optional int64 userId = 1;</code>
       *
       * <pre>
       *用户id
       * </pre>
       */
      public boolean hasUserId() {
        return ((bitField0_ & 0x00000001) == 0x00000001);
      }
      /**
       * <code>optional int64 userId = 1;</code>
       *
       * <pre>
       *用户id
       * </pre>
       */
      public long getUserId() {
        return userId_;
      }
      /**
       * <code>optional int64 userId = 1;</code>
       *
       * <pre>
       *用户id
       * </pre>
       */
      public Builder setUserId(long value) {
        bitField0_ |= 0x00000001;
        userId_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional int64 userId = 1;</code>
       *
       * <pre>
       *用户id
       * </pre>
       */
      public Builder clearUserId() {
        bitField0_ = (bitField0_ & ~0x00000001);
        userId_ = 0L;
        onChanged();
        return this;
      }

      // optional string userName = 2;
      private java.lang.Object userName_ = "";
      /**
       * <code>optional string userName = 2;</code>
       *
       * <pre>
       *用户名称
       * </pre>
       */
      public boolean hasUserName() {
        return ((bitField0_ & 0x00000002) == 0x00000002);
      }
      /**
       * <code>optional string userName = 2;</code>
       *
       * <pre>
       *用户名称
       * </pre>
       */
      public java.lang.String getUserName() {
        java.lang.Object ref = userName_;
        if (!(ref instanceof java.lang.String)) {
          java.lang.String s = ((com.google.protobuf.ByteString) ref)
              .toStringUtf8();
          userName_ = s;
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <code>optional string userName = 2;</code>
       *
       * <pre>
       *用户名称
       * </pre>
       */
      public com.google.protobuf.ByteString
          getUserNameBytes() {
        java.lang.Object ref = userName_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          userName_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <code>optional string userName = 2;</code>
       *
       * <pre>
       *用户名称
       * </pre>
       */
      public Builder setUserName(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000002;
        userName_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional string userName = 2;</code>
       *
       * <pre>
       *用户名称
       * </pre>
       */
      public Builder clearUserName() {
        bitField0_ = (bitField0_ & ~0x00000002);
        userName_ = getDefaultInstance().getUserName();
        onChanged();
        return this;
      }
      /**
       * <code>optional string userName = 2;</code>
       *
       * <pre>
       *用户名称
       * </pre>
       */
      public Builder setUserNameBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000002;
        userName_ = value;
        onChanged();
        return this;
      }

      // optional string role = 3;
      private java.lang.Object role_ = "";
      /**
       * <code>optional string role = 3;</code>
       *
       * <pre>
       *用户角色
       * </pre>
       */
      public boolean hasRole() {
        return ((bitField0_ & 0x00000004) == 0x00000004);
      }
      /**
       * <code>optional string role = 3;</code>
       *
       * <pre>
       *用户角色
       * </pre>
       */
      public java.lang.String getRole() {
        java.lang.Object ref = role_;
        if (!(ref instanceof java.lang.String)) {
          java.lang.String s = ((com.google.protobuf.ByteString) ref)
              .toStringUtf8();
          role_ = s;
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <code>optional string role = 3;</code>
       *
       * <pre>
       *用户角色
       * </pre>
       */
      public com.google.protobuf.ByteString
          getRoleBytes() {
        java.lang.Object ref = role_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          role_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <code>optional string role = 3;</code>
       *
       * <pre>
       *用户角色
       * </pre>
       */
      public Builder setRole(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000004;
        role_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional string role = 3;</code>
       *
       * <pre>
       *用户角色
       * </pre>
       */
      public Builder clearRole() {
        bitField0_ = (bitField0_ & ~0x00000004);
        role_ = getDefaultInstance().getRole();
        onChanged();
        return this;
      }
      /**
       * <code>optional string role = 3;</code>
       *
       * <pre>
       *用户角色
       * </pre>
       */
      public Builder setRoleBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000004;
        role_ = value;
        onChanged();
        return this;
      }

      // @@protoc_insertion_point(builder_scope:fm.lizhi.commons.template.datacenter.protocol.JwtUserInfo)
    }

    static {
      defaultInstance = new JwtUserInfo(true);
      defaultInstance.initFields();
    }

    // @@protoc_insertion_point(class_scope:fm.lizhi.commons.template.datacenter.protocol.JwtUserInfo)
  }

  public interface RequestGetSealHashingTokenOrBuilder
      extends com.google.protobuf.MessageOrBuilder {

    // optional .fm.lizhi.commons.template.datacenter.protocol.SealHashingTokenParam sealHashingTokenParam = 1;
    /**
     * <code>optional .fm.lizhi.commons.template.datacenter.protocol.SealHashingTokenParam sealHashingTokenParam = 1;</code>
     *
     * <pre>
     * {&#64;link SealHashingTokenParam}
     * </pre>
     */
    boolean hasSealHashingTokenParam();
    /**
     * <code>optional .fm.lizhi.commons.template.datacenter.protocol.SealHashingTokenParam sealHashingTokenParam = 1;</code>
     *
     * <pre>
     * {&#64;link SealHashingTokenParam}
     * </pre>
     */
    fm.lizhi.ocean.seal.protocol.SealTokenServiceProto.SealHashingTokenParam getSealHashingTokenParam();
    /**
     * <code>optional .fm.lizhi.commons.template.datacenter.protocol.SealHashingTokenParam sealHashingTokenParam = 1;</code>
     *
     * <pre>
     * {&#64;link SealHashingTokenParam}
     * </pre>
     */
    fm.lizhi.ocean.seal.protocol.SealTokenServiceProto.SealHashingTokenParamOrBuilder getSealHashingTokenParamOrBuilder();
  }
  /**
   * Protobuf type {@code fm.lizhi.commons.template.datacenter.protocol.RequestGetSealHashingToken}
   *
   * <pre>
   * SealTokenService.java
   * 根据传入的参数，获取Seal sdk与服务端通信的短期token。这个方法与SealAuthService的区别是，这个是新版的方案jwt+缓存。userId 业务对应的标识码。建议使用userId作为标识码 hashingToken 验证token，验证的时候把它放进去
   * domain = 4302, op = 100
   * </pre>
   */
  public static final class RequestGetSealHashingToken extends
      com.google.protobuf.GeneratedMessage
      implements RequestGetSealHashingTokenOrBuilder {
    // Use RequestGetSealHashingToken.newBuilder() to construct.
    private RequestGetSealHashingToken(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
      super(builder);
      this.unknownFields = builder.getUnknownFields();
    }
    private RequestGetSealHashingToken(boolean noInit) { this.unknownFields = com.google.protobuf.UnknownFieldSet.getDefaultInstance(); }

    private static final RequestGetSealHashingToken defaultInstance;
    public static RequestGetSealHashingToken getDefaultInstance() {
      return defaultInstance;
    }

    public RequestGetSealHashingToken getDefaultInstanceForType() {
      return defaultInstance;
    }

    private final com.google.protobuf.UnknownFieldSet unknownFields;
    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
        getUnknownFields() {
      return this.unknownFields;
    }
    private RequestGetSealHashingToken(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      initFields();
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            default: {
              if (!parseUnknownField(input, unknownFields,
                                     extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
            case 10: {
              fm.lizhi.ocean.seal.protocol.SealTokenServiceProto.SealHashingTokenParam.Builder subBuilder = null;
              if (((bitField0_ & 0x00000001) == 0x00000001)) {
                subBuilder = sealHashingTokenParam_.toBuilder();
              }
              sealHashingTokenParam_ = input.readMessage(fm.lizhi.ocean.seal.protocol.SealTokenServiceProto.SealHashingTokenParam.PARSER, extensionRegistry);
              if (subBuilder != null) {
                subBuilder.mergeFrom(sealHashingTokenParam_);
                sealHashingTokenParam_ = subBuilder.buildPartial();
              }
              bitField0_ |= 0x00000001;
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e.getMessage()).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return fm.lizhi.ocean.seal.protocol.SealTokenServiceProto.internal_static_fm_lizhi_commons_template_datacenter_protocol_RequestGetSealHashingToken_descriptor;
    }

    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return fm.lizhi.ocean.seal.protocol.SealTokenServiceProto.internal_static_fm_lizhi_commons_template_datacenter_protocol_RequestGetSealHashingToken_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              fm.lizhi.ocean.seal.protocol.SealTokenServiceProto.RequestGetSealHashingToken.class, fm.lizhi.ocean.seal.protocol.SealTokenServiceProto.RequestGetSealHashingToken.Builder.class);
    }

    public static com.google.protobuf.Parser<RequestGetSealHashingToken> PARSER =
        new com.google.protobuf.AbstractParser<RequestGetSealHashingToken>() {
      public RequestGetSealHashingToken parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new RequestGetSealHashingToken(input, extensionRegistry);
      }
    };

    @java.lang.Override
    public com.google.protobuf.Parser<RequestGetSealHashingToken> getParserForType() {
      return PARSER;
    }

    private int bitField0_;
    // optional .fm.lizhi.commons.template.datacenter.protocol.SealHashingTokenParam sealHashingTokenParam = 1;
    public static final int SEALHASHINGTOKENPARAM_FIELD_NUMBER = 1;
    private fm.lizhi.ocean.seal.protocol.SealTokenServiceProto.SealHashingTokenParam sealHashingTokenParam_;
    /**
     * <code>optional .fm.lizhi.commons.template.datacenter.protocol.SealHashingTokenParam sealHashingTokenParam = 1;</code>
     *
     * <pre>
     * {&#64;link SealHashingTokenParam}
     * </pre>
     */
    public boolean hasSealHashingTokenParam() {
      return ((bitField0_ & 0x00000001) == 0x00000001);
    }
    /**
     * <code>optional .fm.lizhi.commons.template.datacenter.protocol.SealHashingTokenParam sealHashingTokenParam = 1;</code>
     *
     * <pre>
     * {&#64;link SealHashingTokenParam}
     * </pre>
     */
    public fm.lizhi.ocean.seal.protocol.SealTokenServiceProto.SealHashingTokenParam getSealHashingTokenParam() {
      return sealHashingTokenParam_;
    }
    /**
     * <code>optional .fm.lizhi.commons.template.datacenter.protocol.SealHashingTokenParam sealHashingTokenParam = 1;</code>
     *
     * <pre>
     * {&#64;link SealHashingTokenParam}
     * </pre>
     */
    public fm.lizhi.ocean.seal.protocol.SealTokenServiceProto.SealHashingTokenParamOrBuilder getSealHashingTokenParamOrBuilder() {
      return sealHashingTokenParam_;
    }

    private void initFields() {
      sealHashingTokenParam_ = fm.lizhi.ocean.seal.protocol.SealTokenServiceProto.SealHashingTokenParam.getDefaultInstance();
    }
    private byte memoizedIsInitialized = -1;
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized != -1) return isInitialized == 1;

      memoizedIsInitialized = 1;
      return true;
    }

    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      getSerializedSize();
      if (((bitField0_ & 0x00000001) == 0x00000001)) {
        output.writeMessage(1, sealHashingTokenParam_);
      }
      getUnknownFields().writeTo(output);
    }

    private int memoizedSerializedSize = -1;
    public int getSerializedSize() {
      int size = memoizedSerializedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) == 0x00000001)) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(1, sealHashingTokenParam_);
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSerializedSize = size;
      return size;
    }

    private static final long serialVersionUID = 0L;
    @java.lang.Override
    protected java.lang.Object writeReplace()
        throws java.io.ObjectStreamException {
      return super.writeReplace();
    }

    public static fm.lizhi.ocean.seal.protocol.SealTokenServiceProto.RequestGetSealHashingToken parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static fm.lizhi.ocean.seal.protocol.SealTokenServiceProto.RequestGetSealHashingToken parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static fm.lizhi.ocean.seal.protocol.SealTokenServiceProto.RequestGetSealHashingToken parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static fm.lizhi.ocean.seal.protocol.SealTokenServiceProto.RequestGetSealHashingToken parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static fm.lizhi.ocean.seal.protocol.SealTokenServiceProto.RequestGetSealHashingToken parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return PARSER.parseFrom(input);
    }
    public static fm.lizhi.ocean.seal.protocol.SealTokenServiceProto.RequestGetSealHashingToken parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseFrom(input, extensionRegistry);
    }
    public static fm.lizhi.ocean.seal.protocol.SealTokenServiceProto.RequestGetSealHashingToken parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return PARSER.parseDelimitedFrom(input);
    }
    public static fm.lizhi.ocean.seal.protocol.SealTokenServiceProto.RequestGetSealHashingToken parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseDelimitedFrom(input, extensionRegistry);
    }
    public static fm.lizhi.ocean.seal.protocol.SealTokenServiceProto.RequestGetSealHashingToken parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return PARSER.parseFrom(input);
    }
    public static fm.lizhi.ocean.seal.protocol.SealTokenServiceProto.RequestGetSealHashingToken parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseFrom(input, extensionRegistry);
    }

    public static Builder newBuilder() { return Builder.create(); }
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder(fm.lizhi.ocean.seal.protocol.SealTokenServiceProto.RequestGetSealHashingToken prototype) {
      return newBuilder().mergeFrom(prototype);
    }
    public Builder toBuilder() { return newBuilder(this); }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code fm.lizhi.commons.template.datacenter.protocol.RequestGetSealHashingToken}
     *
     * <pre>
     * SealTokenService.java
     * 根据传入的参数，获取Seal sdk与服务端通信的短期token。这个方法与SealAuthService的区别是，这个是新版的方案jwt+缓存。userId 业务对应的标识码。建议使用userId作为标识码 hashingToken 验证token，验证的时候把它放进去
     * domain = 4302, op = 100
     * </pre>
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessage.Builder<Builder>
       implements fm.lizhi.ocean.seal.protocol.SealTokenServiceProto.RequestGetSealHashingTokenOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return fm.lizhi.ocean.seal.protocol.SealTokenServiceProto.internal_static_fm_lizhi_commons_template_datacenter_protocol_RequestGetSealHashingToken_descriptor;
      }

      protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return fm.lizhi.ocean.seal.protocol.SealTokenServiceProto.internal_static_fm_lizhi_commons_template_datacenter_protocol_RequestGetSealHashingToken_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                fm.lizhi.ocean.seal.protocol.SealTokenServiceProto.RequestGetSealHashingToken.class, fm.lizhi.ocean.seal.protocol.SealTokenServiceProto.RequestGetSealHashingToken.Builder.class);
      }

      // Construct using fm.lizhi.ocean.seal.protocol.SealTokenServiceProto.RequestGetSealHashingToken.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessage.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessage.alwaysUseFieldBuilders) {
          getSealHashingTokenParamFieldBuilder();
        }
      }
      private static Builder create() {
        return new Builder();
      }

      public Builder clear() {
        super.clear();
        if (sealHashingTokenParamBuilder_ == null) {
          sealHashingTokenParam_ = fm.lizhi.ocean.seal.protocol.SealTokenServiceProto.SealHashingTokenParam.getDefaultInstance();
        } else {
          sealHashingTokenParamBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000001);
        return this;
      }

      public Builder clone() {
        return create().mergeFrom(buildPartial());
      }

      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return fm.lizhi.ocean.seal.protocol.SealTokenServiceProto.internal_static_fm_lizhi_commons_template_datacenter_protocol_RequestGetSealHashingToken_descriptor;
      }

      public fm.lizhi.ocean.seal.protocol.SealTokenServiceProto.RequestGetSealHashingToken getDefaultInstanceForType() {
        return fm.lizhi.ocean.seal.protocol.SealTokenServiceProto.RequestGetSealHashingToken.getDefaultInstance();
      }

      public fm.lizhi.ocean.seal.protocol.SealTokenServiceProto.RequestGetSealHashingToken build() {
        fm.lizhi.ocean.seal.protocol.SealTokenServiceProto.RequestGetSealHashingToken result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      public fm.lizhi.ocean.seal.protocol.SealTokenServiceProto.RequestGetSealHashingToken buildPartial() {
        fm.lizhi.ocean.seal.protocol.SealTokenServiceProto.RequestGetSealHashingToken result = new fm.lizhi.ocean.seal.protocol.SealTokenServiceProto.RequestGetSealHashingToken(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) == 0x00000001)) {
          to_bitField0_ |= 0x00000001;
        }
        if (sealHashingTokenParamBuilder_ == null) {
          result.sealHashingTokenParam_ = sealHashingTokenParam_;
        } else {
          result.sealHashingTokenParam_ = sealHashingTokenParamBuilder_.build();
        }
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof fm.lizhi.ocean.seal.protocol.SealTokenServiceProto.RequestGetSealHashingToken) {
          return mergeFrom((fm.lizhi.ocean.seal.protocol.SealTokenServiceProto.RequestGetSealHashingToken)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(fm.lizhi.ocean.seal.protocol.SealTokenServiceProto.RequestGetSealHashingToken other) {
        if (other == fm.lizhi.ocean.seal.protocol.SealTokenServiceProto.RequestGetSealHashingToken.getDefaultInstance()) return this;
        if (other.hasSealHashingTokenParam()) {
          mergeSealHashingTokenParam(other.getSealHashingTokenParam());
        }
        this.mergeUnknownFields(other.getUnknownFields());
        return this;
      }

      public final boolean isInitialized() {
        return true;
      }

      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        fm.lizhi.ocean.seal.protocol.SealTokenServiceProto.RequestGetSealHashingToken parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (fm.lizhi.ocean.seal.protocol.SealTokenServiceProto.RequestGetSealHashingToken) e.getUnfinishedMessage();
          throw e;
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      // optional .fm.lizhi.commons.template.datacenter.protocol.SealHashingTokenParam sealHashingTokenParam = 1;
      private fm.lizhi.ocean.seal.protocol.SealTokenServiceProto.SealHashingTokenParam sealHashingTokenParam_ = fm.lizhi.ocean.seal.protocol.SealTokenServiceProto.SealHashingTokenParam.getDefaultInstance();
      private com.google.protobuf.SingleFieldBuilder<
          fm.lizhi.ocean.seal.protocol.SealTokenServiceProto.SealHashingTokenParam, fm.lizhi.ocean.seal.protocol.SealTokenServiceProto.SealHashingTokenParam.Builder, fm.lizhi.ocean.seal.protocol.SealTokenServiceProto.SealHashingTokenParamOrBuilder> sealHashingTokenParamBuilder_;
      /**
       * <code>optional .fm.lizhi.commons.template.datacenter.protocol.SealHashingTokenParam sealHashingTokenParam = 1;</code>
       *
       * <pre>
       * {&#64;link SealHashingTokenParam}
       * </pre>
       */
      public boolean hasSealHashingTokenParam() {
        return ((bitField0_ & 0x00000001) == 0x00000001);
      }
      /**
       * <code>optional .fm.lizhi.commons.template.datacenter.protocol.SealHashingTokenParam sealHashingTokenParam = 1;</code>
       *
       * <pre>
       * {&#64;link SealHashingTokenParam}
       * </pre>
       */
      public fm.lizhi.ocean.seal.protocol.SealTokenServiceProto.SealHashingTokenParam getSealHashingTokenParam() {
        if (sealHashingTokenParamBuilder_ == null) {
          return sealHashingTokenParam_;
        } else {
          return sealHashingTokenParamBuilder_.getMessage();
        }
      }
      /**
       * <code>optional .fm.lizhi.commons.template.datacenter.protocol.SealHashingTokenParam sealHashingTokenParam = 1;</code>
       *
       * <pre>
       * {&#64;link SealHashingTokenParam}
       * </pre>
       */
      public Builder setSealHashingTokenParam(fm.lizhi.ocean.seal.protocol.SealTokenServiceProto.SealHashingTokenParam value) {
        if (sealHashingTokenParamBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          sealHashingTokenParam_ = value;
          onChanged();
        } else {
          sealHashingTokenParamBuilder_.setMessage(value);
        }
        bitField0_ |= 0x00000001;
        return this;
      }
      /**
       * <code>optional .fm.lizhi.commons.template.datacenter.protocol.SealHashingTokenParam sealHashingTokenParam = 1;</code>
       *
       * <pre>
       * {&#64;link SealHashingTokenParam}
       * </pre>
       */
      public Builder setSealHashingTokenParam(
          fm.lizhi.ocean.seal.protocol.SealTokenServiceProto.SealHashingTokenParam.Builder builderForValue) {
        if (sealHashingTokenParamBuilder_ == null) {
          sealHashingTokenParam_ = builderForValue.build();
          onChanged();
        } else {
          sealHashingTokenParamBuilder_.setMessage(builderForValue.build());
        }
        bitField0_ |= 0x00000001;
        return this;
      }
      /**
       * <code>optional .fm.lizhi.commons.template.datacenter.protocol.SealHashingTokenParam sealHashingTokenParam = 1;</code>
       *
       * <pre>
       * {&#64;link SealHashingTokenParam}
       * </pre>
       */
      public Builder mergeSealHashingTokenParam(fm.lizhi.ocean.seal.protocol.SealTokenServiceProto.SealHashingTokenParam value) {
        if (sealHashingTokenParamBuilder_ == null) {
          if (((bitField0_ & 0x00000001) == 0x00000001) &&
              sealHashingTokenParam_ != fm.lizhi.ocean.seal.protocol.SealTokenServiceProto.SealHashingTokenParam.getDefaultInstance()) {
            sealHashingTokenParam_ =
              fm.lizhi.ocean.seal.protocol.SealTokenServiceProto.SealHashingTokenParam.newBuilder(sealHashingTokenParam_).mergeFrom(value).buildPartial();
          } else {
            sealHashingTokenParam_ = value;
          }
          onChanged();
        } else {
          sealHashingTokenParamBuilder_.mergeFrom(value);
        }
        bitField0_ |= 0x00000001;
        return this;
      }
      /**
       * <code>optional .fm.lizhi.commons.template.datacenter.protocol.SealHashingTokenParam sealHashingTokenParam = 1;</code>
       *
       * <pre>
       * {&#64;link SealHashingTokenParam}
       * </pre>
       */
      public Builder clearSealHashingTokenParam() {
        if (sealHashingTokenParamBuilder_ == null) {
          sealHashingTokenParam_ = fm.lizhi.ocean.seal.protocol.SealTokenServiceProto.SealHashingTokenParam.getDefaultInstance();
          onChanged();
        } else {
          sealHashingTokenParamBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000001);
        return this;
      }
      /**
       * <code>optional .fm.lizhi.commons.template.datacenter.protocol.SealHashingTokenParam sealHashingTokenParam = 1;</code>
       *
       * <pre>
       * {&#64;link SealHashingTokenParam}
       * </pre>
       */
      public fm.lizhi.ocean.seal.protocol.SealTokenServiceProto.SealHashingTokenParam.Builder getSealHashingTokenParamBuilder() {
        bitField0_ |= 0x00000001;
        onChanged();
        return getSealHashingTokenParamFieldBuilder().getBuilder();
      }
      /**
       * <code>optional .fm.lizhi.commons.template.datacenter.protocol.SealHashingTokenParam sealHashingTokenParam = 1;</code>
       *
       * <pre>
       * {&#64;link SealHashingTokenParam}
       * </pre>
       */
      public fm.lizhi.ocean.seal.protocol.SealTokenServiceProto.SealHashingTokenParamOrBuilder getSealHashingTokenParamOrBuilder() {
        if (sealHashingTokenParamBuilder_ != null) {
          return sealHashingTokenParamBuilder_.getMessageOrBuilder();
        } else {
          return sealHashingTokenParam_;
        }
      }
      /**
       * <code>optional .fm.lizhi.commons.template.datacenter.protocol.SealHashingTokenParam sealHashingTokenParam = 1;</code>
       *
       * <pre>
       * {&#64;link SealHashingTokenParam}
       * </pre>
       */
      private com.google.protobuf.SingleFieldBuilder<
          fm.lizhi.ocean.seal.protocol.SealTokenServiceProto.SealHashingTokenParam, fm.lizhi.ocean.seal.protocol.SealTokenServiceProto.SealHashingTokenParam.Builder, fm.lizhi.ocean.seal.protocol.SealTokenServiceProto.SealHashingTokenParamOrBuilder> 
          getSealHashingTokenParamFieldBuilder() {
        if (sealHashingTokenParamBuilder_ == null) {
          sealHashingTokenParamBuilder_ = new com.google.protobuf.SingleFieldBuilder<
              fm.lizhi.ocean.seal.protocol.SealTokenServiceProto.SealHashingTokenParam, fm.lizhi.ocean.seal.protocol.SealTokenServiceProto.SealHashingTokenParam.Builder, fm.lizhi.ocean.seal.protocol.SealTokenServiceProto.SealHashingTokenParamOrBuilder>(
                  sealHashingTokenParam_,
                  getParentForChildren(),
                  isClean());
          sealHashingTokenParam_ = null;
        }
        return sealHashingTokenParamBuilder_;
      }

      // @@protoc_insertion_point(builder_scope:fm.lizhi.commons.template.datacenter.protocol.RequestGetSealHashingToken)
    }

    static {
      defaultInstance = new RequestGetSealHashingToken(true);
      defaultInstance.initFields();
    }

    // @@protoc_insertion_point(class_scope:fm.lizhi.commons.template.datacenter.protocol.RequestGetSealHashingToken)
  }

  public interface ResponseGetSealHashingTokenOrBuilder
      extends com.google.protobuf.MessageOrBuilder {

    // optional string sealToken = 1;
    /**
     * <code>optional string sealToken = 1;</code>
     *
     * <pre>
     *获取sealToken
     * </pre>
     */
    boolean hasSealToken();
    /**
     * <code>optional string sealToken = 1;</code>
     *
     * <pre>
     *获取sealToken
     * </pre>
     */
    java.lang.String getSealToken();
    /**
     * <code>optional string sealToken = 1;</code>
     *
     * <pre>
     *获取sealToken
     * </pre>
     */
    com.google.protobuf.ByteString
        getSealTokenBytes();

    // optional int64 expireDate = 2;
    /**
     * <code>optional int64 expireDate = 2;</code>
     *
     * <pre>
     *长期令牌过期时间戳（毫秒）
     * </pre>
     */
    boolean hasExpireDate();
    /**
     * <code>optional int64 expireDate = 2;</code>
     *
     * <pre>
     *长期令牌过期时间戳（毫秒）
     * </pre>
     */
    long getExpireDate();
  }
  /**
   * Protobuf type {@code fm.lizhi.commons.template.datacenter.protocol.ResponseGetSealHashingToken}
   *
   * <pre>
   * rcode == 0(SUCCESS) = 执行成功
   * rcode == 1(FAIL) = 非法用户
   * rcode == 2(ILLEGAL_PARAMS) = 参数非法
   * rcode == 3(ERROR) = 服务错误
   * </pre>
   */
  public static final class ResponseGetSealHashingToken extends
      com.google.protobuf.GeneratedMessage
      implements ResponseGetSealHashingTokenOrBuilder {
    // Use ResponseGetSealHashingToken.newBuilder() to construct.
    private ResponseGetSealHashingToken(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
      super(builder);
      this.unknownFields = builder.getUnknownFields();
    }
    private ResponseGetSealHashingToken(boolean noInit) { this.unknownFields = com.google.protobuf.UnknownFieldSet.getDefaultInstance(); }

    private static final ResponseGetSealHashingToken defaultInstance;
    public static ResponseGetSealHashingToken getDefaultInstance() {
      return defaultInstance;
    }

    public ResponseGetSealHashingToken getDefaultInstanceForType() {
      return defaultInstance;
    }

    private final com.google.protobuf.UnknownFieldSet unknownFields;
    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
        getUnknownFields() {
      return this.unknownFields;
    }
    private ResponseGetSealHashingToken(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      initFields();
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            default: {
              if (!parseUnknownField(input, unknownFields,
                                     extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
            case 10: {
              bitField0_ |= 0x00000001;
              sealToken_ = input.readBytes();
              break;
            }
            case 16: {
              bitField0_ |= 0x00000002;
              expireDate_ = input.readInt64();
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e.getMessage()).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return fm.lizhi.ocean.seal.protocol.SealTokenServiceProto.internal_static_fm_lizhi_commons_template_datacenter_protocol_ResponseGetSealHashingToken_descriptor;
    }

    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return fm.lizhi.ocean.seal.protocol.SealTokenServiceProto.internal_static_fm_lizhi_commons_template_datacenter_protocol_ResponseGetSealHashingToken_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              fm.lizhi.ocean.seal.protocol.SealTokenServiceProto.ResponseGetSealHashingToken.class, fm.lizhi.ocean.seal.protocol.SealTokenServiceProto.ResponseGetSealHashingToken.Builder.class);
    }

    public static com.google.protobuf.Parser<ResponseGetSealHashingToken> PARSER =
        new com.google.protobuf.AbstractParser<ResponseGetSealHashingToken>() {
      public ResponseGetSealHashingToken parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new ResponseGetSealHashingToken(input, extensionRegistry);
      }
    };

    @java.lang.Override
    public com.google.protobuf.Parser<ResponseGetSealHashingToken> getParserForType() {
      return PARSER;
    }

    private int bitField0_;
    // optional string sealToken = 1;
    public static final int SEALTOKEN_FIELD_NUMBER = 1;
    private java.lang.Object sealToken_;
    /**
     * <code>optional string sealToken = 1;</code>
     *
     * <pre>
     *获取sealToken
     * </pre>
     */
    public boolean hasSealToken() {
      return ((bitField0_ & 0x00000001) == 0x00000001);
    }
    /**
     * <code>optional string sealToken = 1;</code>
     *
     * <pre>
     *获取sealToken
     * </pre>
     */
    public java.lang.String getSealToken() {
      java.lang.Object ref = sealToken_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          sealToken_ = s;
        }
        return s;
      }
    }
    /**
     * <code>optional string sealToken = 1;</code>
     *
     * <pre>
     *获取sealToken
     * </pre>
     */
    public com.google.protobuf.ByteString
        getSealTokenBytes() {
      java.lang.Object ref = sealToken_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        sealToken_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    // optional int64 expireDate = 2;
    public static final int EXPIREDATE_FIELD_NUMBER = 2;
    private long expireDate_;
    /**
     * <code>optional int64 expireDate = 2;</code>
     *
     * <pre>
     *长期令牌过期时间戳（毫秒）
     * </pre>
     */
    public boolean hasExpireDate() {
      return ((bitField0_ & 0x00000002) == 0x00000002);
    }
    /**
     * <code>optional int64 expireDate = 2;</code>
     *
     * <pre>
     *长期令牌过期时间戳（毫秒）
     * </pre>
     */
    public long getExpireDate() {
      return expireDate_;
    }

    private void initFields() {
      sealToken_ = "";
      expireDate_ = 0L;
    }
    private byte memoizedIsInitialized = -1;
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized != -1) return isInitialized == 1;

      memoizedIsInitialized = 1;
      return true;
    }

    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      getSerializedSize();
      if (((bitField0_ & 0x00000001) == 0x00000001)) {
        output.writeBytes(1, getSealTokenBytes());
      }
      if (((bitField0_ & 0x00000002) == 0x00000002)) {
        output.writeInt64(2, expireDate_);
      }
      getUnknownFields().writeTo(output);
    }

    private int memoizedSerializedSize = -1;
    public int getSerializedSize() {
      int size = memoizedSerializedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) == 0x00000001)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(1, getSealTokenBytes());
      }
      if (((bitField0_ & 0x00000002) == 0x00000002)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt64Size(2, expireDate_);
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSerializedSize = size;
      return size;
    }

    private static final long serialVersionUID = 0L;
    @java.lang.Override
    protected java.lang.Object writeReplace()
        throws java.io.ObjectStreamException {
      return super.writeReplace();
    }

    public static fm.lizhi.ocean.seal.protocol.SealTokenServiceProto.ResponseGetSealHashingToken parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static fm.lizhi.ocean.seal.protocol.SealTokenServiceProto.ResponseGetSealHashingToken parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static fm.lizhi.ocean.seal.protocol.SealTokenServiceProto.ResponseGetSealHashingToken parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static fm.lizhi.ocean.seal.protocol.SealTokenServiceProto.ResponseGetSealHashingToken parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static fm.lizhi.ocean.seal.protocol.SealTokenServiceProto.ResponseGetSealHashingToken parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return PARSER.parseFrom(input);
    }
    public static fm.lizhi.ocean.seal.protocol.SealTokenServiceProto.ResponseGetSealHashingToken parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseFrom(input, extensionRegistry);
    }
    public static fm.lizhi.ocean.seal.protocol.SealTokenServiceProto.ResponseGetSealHashingToken parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return PARSER.parseDelimitedFrom(input);
    }
    public static fm.lizhi.ocean.seal.protocol.SealTokenServiceProto.ResponseGetSealHashingToken parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseDelimitedFrom(input, extensionRegistry);
    }
    public static fm.lizhi.ocean.seal.protocol.SealTokenServiceProto.ResponseGetSealHashingToken parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return PARSER.parseFrom(input);
    }
    public static fm.lizhi.ocean.seal.protocol.SealTokenServiceProto.ResponseGetSealHashingToken parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseFrom(input, extensionRegistry);
    }

    public static Builder newBuilder() { return Builder.create(); }
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder(fm.lizhi.ocean.seal.protocol.SealTokenServiceProto.ResponseGetSealHashingToken prototype) {
      return newBuilder().mergeFrom(prototype);
    }
    public Builder toBuilder() { return newBuilder(this); }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code fm.lizhi.commons.template.datacenter.protocol.ResponseGetSealHashingToken}
     *
     * <pre>
     * rcode == 0(SUCCESS) = 执行成功
     * rcode == 1(FAIL) = 非法用户
     * rcode == 2(ILLEGAL_PARAMS) = 参数非法
     * rcode == 3(ERROR) = 服务错误
     * </pre>
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessage.Builder<Builder>
       implements fm.lizhi.ocean.seal.protocol.SealTokenServiceProto.ResponseGetSealHashingTokenOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return fm.lizhi.ocean.seal.protocol.SealTokenServiceProto.internal_static_fm_lizhi_commons_template_datacenter_protocol_ResponseGetSealHashingToken_descriptor;
      }

      protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return fm.lizhi.ocean.seal.protocol.SealTokenServiceProto.internal_static_fm_lizhi_commons_template_datacenter_protocol_ResponseGetSealHashingToken_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                fm.lizhi.ocean.seal.protocol.SealTokenServiceProto.ResponseGetSealHashingToken.class, fm.lizhi.ocean.seal.protocol.SealTokenServiceProto.ResponseGetSealHashingToken.Builder.class);
      }

      // Construct using fm.lizhi.ocean.seal.protocol.SealTokenServiceProto.ResponseGetSealHashingToken.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessage.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessage.alwaysUseFieldBuilders) {
        }
      }
      private static Builder create() {
        return new Builder();
      }

      public Builder clear() {
        super.clear();
        sealToken_ = "";
        bitField0_ = (bitField0_ & ~0x00000001);
        expireDate_ = 0L;
        bitField0_ = (bitField0_ & ~0x00000002);
        return this;
      }

      public Builder clone() {
        return create().mergeFrom(buildPartial());
      }

      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return fm.lizhi.ocean.seal.protocol.SealTokenServiceProto.internal_static_fm_lizhi_commons_template_datacenter_protocol_ResponseGetSealHashingToken_descriptor;
      }

      public fm.lizhi.ocean.seal.protocol.SealTokenServiceProto.ResponseGetSealHashingToken getDefaultInstanceForType() {
        return fm.lizhi.ocean.seal.protocol.SealTokenServiceProto.ResponseGetSealHashingToken.getDefaultInstance();
      }

      public fm.lizhi.ocean.seal.protocol.SealTokenServiceProto.ResponseGetSealHashingToken build() {
        fm.lizhi.ocean.seal.protocol.SealTokenServiceProto.ResponseGetSealHashingToken result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      public fm.lizhi.ocean.seal.protocol.SealTokenServiceProto.ResponseGetSealHashingToken buildPartial() {
        fm.lizhi.ocean.seal.protocol.SealTokenServiceProto.ResponseGetSealHashingToken result = new fm.lizhi.ocean.seal.protocol.SealTokenServiceProto.ResponseGetSealHashingToken(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) == 0x00000001)) {
          to_bitField0_ |= 0x00000001;
        }
        result.sealToken_ = sealToken_;
        if (((from_bitField0_ & 0x00000002) == 0x00000002)) {
          to_bitField0_ |= 0x00000002;
        }
        result.expireDate_ = expireDate_;
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof fm.lizhi.ocean.seal.protocol.SealTokenServiceProto.ResponseGetSealHashingToken) {
          return mergeFrom((fm.lizhi.ocean.seal.protocol.SealTokenServiceProto.ResponseGetSealHashingToken)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(fm.lizhi.ocean.seal.protocol.SealTokenServiceProto.ResponseGetSealHashingToken other) {
        if (other == fm.lizhi.ocean.seal.protocol.SealTokenServiceProto.ResponseGetSealHashingToken.getDefaultInstance()) return this;
        if (other.hasSealToken()) {
          bitField0_ |= 0x00000001;
          sealToken_ = other.sealToken_;
          onChanged();
        }
        if (other.hasExpireDate()) {
          setExpireDate(other.getExpireDate());
        }
        this.mergeUnknownFields(other.getUnknownFields());
        return this;
      }

      public final boolean isInitialized() {
        return true;
      }

      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        fm.lizhi.ocean.seal.protocol.SealTokenServiceProto.ResponseGetSealHashingToken parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (fm.lizhi.ocean.seal.protocol.SealTokenServiceProto.ResponseGetSealHashingToken) e.getUnfinishedMessage();
          throw e;
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      // optional string sealToken = 1;
      private java.lang.Object sealToken_ = "";
      /**
       * <code>optional string sealToken = 1;</code>
       *
       * <pre>
       *获取sealToken
       * </pre>
       */
      public boolean hasSealToken() {
        return ((bitField0_ & 0x00000001) == 0x00000001);
      }
      /**
       * <code>optional string sealToken = 1;</code>
       *
       * <pre>
       *获取sealToken
       * </pre>
       */
      public java.lang.String getSealToken() {
        java.lang.Object ref = sealToken_;
        if (!(ref instanceof java.lang.String)) {
          java.lang.String s = ((com.google.protobuf.ByteString) ref)
              .toStringUtf8();
          sealToken_ = s;
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <code>optional string sealToken = 1;</code>
       *
       * <pre>
       *获取sealToken
       * </pre>
       */
      public com.google.protobuf.ByteString
          getSealTokenBytes() {
        java.lang.Object ref = sealToken_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          sealToken_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <code>optional string sealToken = 1;</code>
       *
       * <pre>
       *获取sealToken
       * </pre>
       */
      public Builder setSealToken(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000001;
        sealToken_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional string sealToken = 1;</code>
       *
       * <pre>
       *获取sealToken
       * </pre>
       */
      public Builder clearSealToken() {
        bitField0_ = (bitField0_ & ~0x00000001);
        sealToken_ = getDefaultInstance().getSealToken();
        onChanged();
        return this;
      }
      /**
       * <code>optional string sealToken = 1;</code>
       *
       * <pre>
       *获取sealToken
       * </pre>
       */
      public Builder setSealTokenBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000001;
        sealToken_ = value;
        onChanged();
        return this;
      }

      // optional int64 expireDate = 2;
      private long expireDate_ ;
      /**
       * <code>optional int64 expireDate = 2;</code>
       *
       * <pre>
       *长期令牌过期时间戳（毫秒）
       * </pre>
       */
      public boolean hasExpireDate() {
        return ((bitField0_ & 0x00000002) == 0x00000002);
      }
      /**
       * <code>optional int64 expireDate = 2;</code>
       *
       * <pre>
       *长期令牌过期时间戳（毫秒）
       * </pre>
       */
      public long getExpireDate() {
        return expireDate_;
      }
      /**
       * <code>optional int64 expireDate = 2;</code>
       *
       * <pre>
       *长期令牌过期时间戳（毫秒）
       * </pre>
       */
      public Builder setExpireDate(long value) {
        bitField0_ |= 0x00000002;
        expireDate_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional int64 expireDate = 2;</code>
       *
       * <pre>
       *长期令牌过期时间戳（毫秒）
       * </pre>
       */
      public Builder clearExpireDate() {
        bitField0_ = (bitField0_ & ~0x00000002);
        expireDate_ = 0L;
        onChanged();
        return this;
      }

      // @@protoc_insertion_point(builder_scope:fm.lizhi.commons.template.datacenter.protocol.ResponseGetSealHashingToken)
    }

    static {
      defaultInstance = new ResponseGetSealHashingToken(true);
      defaultInstance.initFields();
    }

    // @@protoc_insertion_point(class_scope:fm.lizhi.commons.template.datacenter.protocol.ResponseGetSealHashingToken)
  }

  public interface RequestVerifySealTokenOrBuilder
      extends com.google.protobuf.MessageOrBuilder {

    // optional string sealToken = 1;
    /**
     * <code>optional string sealToken = 1;</code>
     *
     * <pre>
     *sealToken获取用户信息
     * </pre>
     */
    boolean hasSealToken();
    /**
     * <code>optional string sealToken = 1;</code>
     *
     * <pre>
     *sealToken获取用户信息
     * </pre>
     */
    java.lang.String getSealToken();
    /**
     * <code>optional string sealToken = 1;</code>
     *
     * <pre>
     *sealToken获取用户信息
     * </pre>
     */
    com.google.protobuf.ByteString
        getSealTokenBytes();

    // optional string appId = 2;
    /**
     * <code>optional string appId = 2;</code>
     */
    boolean hasAppId();
    /**
     * <code>optional string appId = 2;</code>
     */
    java.lang.String getAppId();
    /**
     * <code>optional string appId = 2;</code>
     */
    com.google.protobuf.ByteString
        getAppIdBytes();
  }
  /**
   * Protobuf type {@code fm.lizhi.commons.template.datacenter.protocol.RequestVerifySealToken}
   *
   * <pre>
   * SealTokenService.java
   * 验证SealToken是否正确.如果正确token,且token未过期，将会进行续命
   * domain = 4302, op = 101
   * </pre>
   */
  public static final class RequestVerifySealToken extends
      com.google.protobuf.GeneratedMessage
      implements RequestVerifySealTokenOrBuilder {
    // Use RequestVerifySealToken.newBuilder() to construct.
    private RequestVerifySealToken(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
      super(builder);
      this.unknownFields = builder.getUnknownFields();
    }
    private RequestVerifySealToken(boolean noInit) { this.unknownFields = com.google.protobuf.UnknownFieldSet.getDefaultInstance(); }

    private static final RequestVerifySealToken defaultInstance;
    public static RequestVerifySealToken getDefaultInstance() {
      return defaultInstance;
    }

    public RequestVerifySealToken getDefaultInstanceForType() {
      return defaultInstance;
    }

    private final com.google.protobuf.UnknownFieldSet unknownFields;
    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
        getUnknownFields() {
      return this.unknownFields;
    }
    private RequestVerifySealToken(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      initFields();
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            default: {
              if (!parseUnknownField(input, unknownFields,
                                     extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
            case 10: {
              bitField0_ |= 0x00000001;
              sealToken_ = input.readBytes();
              break;
            }
            case 18: {
              bitField0_ |= 0x00000002;
              appId_ = input.readBytes();
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e.getMessage()).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return fm.lizhi.ocean.seal.protocol.SealTokenServiceProto.internal_static_fm_lizhi_commons_template_datacenter_protocol_RequestVerifySealToken_descriptor;
    }

    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return fm.lizhi.ocean.seal.protocol.SealTokenServiceProto.internal_static_fm_lizhi_commons_template_datacenter_protocol_RequestVerifySealToken_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              fm.lizhi.ocean.seal.protocol.SealTokenServiceProto.RequestVerifySealToken.class, fm.lizhi.ocean.seal.protocol.SealTokenServiceProto.RequestVerifySealToken.Builder.class);
    }

    public static com.google.protobuf.Parser<RequestVerifySealToken> PARSER =
        new com.google.protobuf.AbstractParser<RequestVerifySealToken>() {
      public RequestVerifySealToken parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new RequestVerifySealToken(input, extensionRegistry);
      }
    };

    @java.lang.Override
    public com.google.protobuf.Parser<RequestVerifySealToken> getParserForType() {
      return PARSER;
    }

    private int bitField0_;
    // optional string sealToken = 1;
    public static final int SEALTOKEN_FIELD_NUMBER = 1;
    private java.lang.Object sealToken_;
    /**
     * <code>optional string sealToken = 1;</code>
     *
     * <pre>
     *sealToken获取用户信息
     * </pre>
     */
    public boolean hasSealToken() {
      return ((bitField0_ & 0x00000001) == 0x00000001);
    }
    /**
     * <code>optional string sealToken = 1;</code>
     *
     * <pre>
     *sealToken获取用户信息
     * </pre>
     */
    public java.lang.String getSealToken() {
      java.lang.Object ref = sealToken_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          sealToken_ = s;
        }
        return s;
      }
    }
    /**
     * <code>optional string sealToken = 1;</code>
     *
     * <pre>
     *sealToken获取用户信息
     * </pre>
     */
    public com.google.protobuf.ByteString
        getSealTokenBytes() {
      java.lang.Object ref = sealToken_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        sealToken_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    // optional string appId = 2;
    public static final int APPID_FIELD_NUMBER = 2;
    private java.lang.Object appId_;
    /**
     * <code>optional string appId = 2;</code>
     */
    public boolean hasAppId() {
      return ((bitField0_ & 0x00000002) == 0x00000002);
    }
    /**
     * <code>optional string appId = 2;</code>
     */
    public java.lang.String getAppId() {
      java.lang.Object ref = appId_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          appId_ = s;
        }
        return s;
      }
    }
    /**
     * <code>optional string appId = 2;</code>
     */
    public com.google.protobuf.ByteString
        getAppIdBytes() {
      java.lang.Object ref = appId_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        appId_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    private void initFields() {
      sealToken_ = "";
      appId_ = "";
    }
    private byte memoizedIsInitialized = -1;
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized != -1) return isInitialized == 1;

      memoizedIsInitialized = 1;
      return true;
    }

    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      getSerializedSize();
      if (((bitField0_ & 0x00000001) == 0x00000001)) {
        output.writeBytes(1, getSealTokenBytes());
      }
      if (((bitField0_ & 0x00000002) == 0x00000002)) {
        output.writeBytes(2, getAppIdBytes());
      }
      getUnknownFields().writeTo(output);
    }

    private int memoizedSerializedSize = -1;
    public int getSerializedSize() {
      int size = memoizedSerializedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) == 0x00000001)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(1, getSealTokenBytes());
      }
      if (((bitField0_ & 0x00000002) == 0x00000002)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(2, getAppIdBytes());
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSerializedSize = size;
      return size;
    }

    private static final long serialVersionUID = 0L;
    @java.lang.Override
    protected java.lang.Object writeReplace()
        throws java.io.ObjectStreamException {
      return super.writeReplace();
    }

    public static fm.lizhi.ocean.seal.protocol.SealTokenServiceProto.RequestVerifySealToken parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static fm.lizhi.ocean.seal.protocol.SealTokenServiceProto.RequestVerifySealToken parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static fm.lizhi.ocean.seal.protocol.SealTokenServiceProto.RequestVerifySealToken parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static fm.lizhi.ocean.seal.protocol.SealTokenServiceProto.RequestVerifySealToken parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static fm.lizhi.ocean.seal.protocol.SealTokenServiceProto.RequestVerifySealToken parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return PARSER.parseFrom(input);
    }
    public static fm.lizhi.ocean.seal.protocol.SealTokenServiceProto.RequestVerifySealToken parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseFrom(input, extensionRegistry);
    }
    public static fm.lizhi.ocean.seal.protocol.SealTokenServiceProto.RequestVerifySealToken parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return PARSER.parseDelimitedFrom(input);
    }
    public static fm.lizhi.ocean.seal.protocol.SealTokenServiceProto.RequestVerifySealToken parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseDelimitedFrom(input, extensionRegistry);
    }
    public static fm.lizhi.ocean.seal.protocol.SealTokenServiceProto.RequestVerifySealToken parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return PARSER.parseFrom(input);
    }
    public static fm.lizhi.ocean.seal.protocol.SealTokenServiceProto.RequestVerifySealToken parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseFrom(input, extensionRegistry);
    }

    public static Builder newBuilder() { return Builder.create(); }
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder(fm.lizhi.ocean.seal.protocol.SealTokenServiceProto.RequestVerifySealToken prototype) {
      return newBuilder().mergeFrom(prototype);
    }
    public Builder toBuilder() { return newBuilder(this); }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code fm.lizhi.commons.template.datacenter.protocol.RequestVerifySealToken}
     *
     * <pre>
     * SealTokenService.java
     * 验证SealToken是否正确.如果正确token,且token未过期，将会进行续命
     * domain = 4302, op = 101
     * </pre>
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessage.Builder<Builder>
       implements fm.lizhi.ocean.seal.protocol.SealTokenServiceProto.RequestVerifySealTokenOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return fm.lizhi.ocean.seal.protocol.SealTokenServiceProto.internal_static_fm_lizhi_commons_template_datacenter_protocol_RequestVerifySealToken_descriptor;
      }

      protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return fm.lizhi.ocean.seal.protocol.SealTokenServiceProto.internal_static_fm_lizhi_commons_template_datacenter_protocol_RequestVerifySealToken_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                fm.lizhi.ocean.seal.protocol.SealTokenServiceProto.RequestVerifySealToken.class, fm.lizhi.ocean.seal.protocol.SealTokenServiceProto.RequestVerifySealToken.Builder.class);
      }

      // Construct using fm.lizhi.ocean.seal.protocol.SealTokenServiceProto.RequestVerifySealToken.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessage.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessage.alwaysUseFieldBuilders) {
        }
      }
      private static Builder create() {
        return new Builder();
      }

      public Builder clear() {
        super.clear();
        sealToken_ = "";
        bitField0_ = (bitField0_ & ~0x00000001);
        appId_ = "";
        bitField0_ = (bitField0_ & ~0x00000002);
        return this;
      }

      public Builder clone() {
        return create().mergeFrom(buildPartial());
      }

      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return fm.lizhi.ocean.seal.protocol.SealTokenServiceProto.internal_static_fm_lizhi_commons_template_datacenter_protocol_RequestVerifySealToken_descriptor;
      }

      public fm.lizhi.ocean.seal.protocol.SealTokenServiceProto.RequestVerifySealToken getDefaultInstanceForType() {
        return fm.lizhi.ocean.seal.protocol.SealTokenServiceProto.RequestVerifySealToken.getDefaultInstance();
      }

      public fm.lizhi.ocean.seal.protocol.SealTokenServiceProto.RequestVerifySealToken build() {
        fm.lizhi.ocean.seal.protocol.SealTokenServiceProto.RequestVerifySealToken result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      public fm.lizhi.ocean.seal.protocol.SealTokenServiceProto.RequestVerifySealToken buildPartial() {
        fm.lizhi.ocean.seal.protocol.SealTokenServiceProto.RequestVerifySealToken result = new fm.lizhi.ocean.seal.protocol.SealTokenServiceProto.RequestVerifySealToken(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) == 0x00000001)) {
          to_bitField0_ |= 0x00000001;
        }
        result.sealToken_ = sealToken_;
        if (((from_bitField0_ & 0x00000002) == 0x00000002)) {
          to_bitField0_ |= 0x00000002;
        }
        result.appId_ = appId_;
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof fm.lizhi.ocean.seal.protocol.SealTokenServiceProto.RequestVerifySealToken) {
          return mergeFrom((fm.lizhi.ocean.seal.protocol.SealTokenServiceProto.RequestVerifySealToken)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(fm.lizhi.ocean.seal.protocol.SealTokenServiceProto.RequestVerifySealToken other) {
        if (other == fm.lizhi.ocean.seal.protocol.SealTokenServiceProto.RequestVerifySealToken.getDefaultInstance()) return this;
        if (other.hasSealToken()) {
          bitField0_ |= 0x00000001;
          sealToken_ = other.sealToken_;
          onChanged();
        }
        if (other.hasAppId()) {
          bitField0_ |= 0x00000002;
          appId_ = other.appId_;
          onChanged();
        }
        this.mergeUnknownFields(other.getUnknownFields());
        return this;
      }

      public final boolean isInitialized() {
        return true;
      }

      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        fm.lizhi.ocean.seal.protocol.SealTokenServiceProto.RequestVerifySealToken parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (fm.lizhi.ocean.seal.protocol.SealTokenServiceProto.RequestVerifySealToken) e.getUnfinishedMessage();
          throw e;
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      // optional string sealToken = 1;
      private java.lang.Object sealToken_ = "";
      /**
       * <code>optional string sealToken = 1;</code>
       *
       * <pre>
       *sealToken获取用户信息
       * </pre>
       */
      public boolean hasSealToken() {
        return ((bitField0_ & 0x00000001) == 0x00000001);
      }
      /**
       * <code>optional string sealToken = 1;</code>
       *
       * <pre>
       *sealToken获取用户信息
       * </pre>
       */
      public java.lang.String getSealToken() {
        java.lang.Object ref = sealToken_;
        if (!(ref instanceof java.lang.String)) {
          java.lang.String s = ((com.google.protobuf.ByteString) ref)
              .toStringUtf8();
          sealToken_ = s;
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <code>optional string sealToken = 1;</code>
       *
       * <pre>
       *sealToken获取用户信息
       * </pre>
       */
      public com.google.protobuf.ByteString
          getSealTokenBytes() {
        java.lang.Object ref = sealToken_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          sealToken_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <code>optional string sealToken = 1;</code>
       *
       * <pre>
       *sealToken获取用户信息
       * </pre>
       */
      public Builder setSealToken(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000001;
        sealToken_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional string sealToken = 1;</code>
       *
       * <pre>
       *sealToken获取用户信息
       * </pre>
       */
      public Builder clearSealToken() {
        bitField0_ = (bitField0_ & ~0x00000001);
        sealToken_ = getDefaultInstance().getSealToken();
        onChanged();
        return this;
      }
      /**
       * <code>optional string sealToken = 1;</code>
       *
       * <pre>
       *sealToken获取用户信息
       * </pre>
       */
      public Builder setSealTokenBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000001;
        sealToken_ = value;
        onChanged();
        return this;
      }

      // optional string appId = 2;
      private java.lang.Object appId_ = "";
      /**
       * <code>optional string appId = 2;</code>
       */
      public boolean hasAppId() {
        return ((bitField0_ & 0x00000002) == 0x00000002);
      }
      /**
       * <code>optional string appId = 2;</code>
       */
      public java.lang.String getAppId() {
        java.lang.Object ref = appId_;
        if (!(ref instanceof java.lang.String)) {
          java.lang.String s = ((com.google.protobuf.ByteString) ref)
              .toStringUtf8();
          appId_ = s;
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <code>optional string appId = 2;</code>
       */
      public com.google.protobuf.ByteString
          getAppIdBytes() {
        java.lang.Object ref = appId_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          appId_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <code>optional string appId = 2;</code>
       */
      public Builder setAppId(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000002;
        appId_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional string appId = 2;</code>
       */
      public Builder clearAppId() {
        bitField0_ = (bitField0_ & ~0x00000002);
        appId_ = getDefaultInstance().getAppId();
        onChanged();
        return this;
      }
      /**
       * <code>optional string appId = 2;</code>
       */
      public Builder setAppIdBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000002;
        appId_ = value;
        onChanged();
        return this;
      }

      // @@protoc_insertion_point(builder_scope:fm.lizhi.commons.template.datacenter.protocol.RequestVerifySealToken)
    }

    static {
      defaultInstance = new RequestVerifySealToken(true);
      defaultInstance.initFields();
    }

    // @@protoc_insertion_point(class_scope:fm.lizhi.commons.template.datacenter.protocol.RequestVerifySealToken)
  }

  public interface ResponseVerifySealTokenOrBuilder
      extends com.google.protobuf.MessageOrBuilder {
  }
  /**
   * Protobuf type {@code fm.lizhi.commons.template.datacenter.protocol.ResponseVerifySealToken}
   *
   * <pre>
   * rcode == 0(SUCCESS) = 执行成功
   * rcode == 1(INVALID) = 无效Token
   * rcode == 2(ERROR) = 服务错误
   * </pre>
   */
  public static final class ResponseVerifySealToken extends
      com.google.protobuf.GeneratedMessage
      implements ResponseVerifySealTokenOrBuilder {
    // Use ResponseVerifySealToken.newBuilder() to construct.
    private ResponseVerifySealToken(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
      super(builder);
      this.unknownFields = builder.getUnknownFields();
    }
    private ResponseVerifySealToken(boolean noInit) { this.unknownFields = com.google.protobuf.UnknownFieldSet.getDefaultInstance(); }

    private static final ResponseVerifySealToken defaultInstance;
    public static ResponseVerifySealToken getDefaultInstance() {
      return defaultInstance;
    }

    public ResponseVerifySealToken getDefaultInstanceForType() {
      return defaultInstance;
    }

    private final com.google.protobuf.UnknownFieldSet unknownFields;
    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
        getUnknownFields() {
      return this.unknownFields;
    }
    private ResponseVerifySealToken(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      initFields();
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            default: {
              if (!parseUnknownField(input, unknownFields,
                                     extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e.getMessage()).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return fm.lizhi.ocean.seal.protocol.SealTokenServiceProto.internal_static_fm_lizhi_commons_template_datacenter_protocol_ResponseVerifySealToken_descriptor;
    }

    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return fm.lizhi.ocean.seal.protocol.SealTokenServiceProto.internal_static_fm_lizhi_commons_template_datacenter_protocol_ResponseVerifySealToken_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              fm.lizhi.ocean.seal.protocol.SealTokenServiceProto.ResponseVerifySealToken.class, fm.lizhi.ocean.seal.protocol.SealTokenServiceProto.ResponseVerifySealToken.Builder.class);
    }

    public static com.google.protobuf.Parser<ResponseVerifySealToken> PARSER =
        new com.google.protobuf.AbstractParser<ResponseVerifySealToken>() {
      public ResponseVerifySealToken parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new ResponseVerifySealToken(input, extensionRegistry);
      }
    };

    @java.lang.Override
    public com.google.protobuf.Parser<ResponseVerifySealToken> getParserForType() {
      return PARSER;
    }

    private void initFields() {
    }
    private byte memoizedIsInitialized = -1;
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized != -1) return isInitialized == 1;

      memoizedIsInitialized = 1;
      return true;
    }

    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      getSerializedSize();
      getUnknownFields().writeTo(output);
    }

    private int memoizedSerializedSize = -1;
    public int getSerializedSize() {
      int size = memoizedSerializedSize;
      if (size != -1) return size;

      size = 0;
      size += getUnknownFields().getSerializedSize();
      memoizedSerializedSize = size;
      return size;
    }

    private static final long serialVersionUID = 0L;
    @java.lang.Override
    protected java.lang.Object writeReplace()
        throws java.io.ObjectStreamException {
      return super.writeReplace();
    }

    public static fm.lizhi.ocean.seal.protocol.SealTokenServiceProto.ResponseVerifySealToken parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static fm.lizhi.ocean.seal.protocol.SealTokenServiceProto.ResponseVerifySealToken parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static fm.lizhi.ocean.seal.protocol.SealTokenServiceProto.ResponseVerifySealToken parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static fm.lizhi.ocean.seal.protocol.SealTokenServiceProto.ResponseVerifySealToken parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static fm.lizhi.ocean.seal.protocol.SealTokenServiceProto.ResponseVerifySealToken parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return PARSER.parseFrom(input);
    }
    public static fm.lizhi.ocean.seal.protocol.SealTokenServiceProto.ResponseVerifySealToken parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseFrom(input, extensionRegistry);
    }
    public static fm.lizhi.ocean.seal.protocol.SealTokenServiceProto.ResponseVerifySealToken parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return PARSER.parseDelimitedFrom(input);
    }
    public static fm.lizhi.ocean.seal.protocol.SealTokenServiceProto.ResponseVerifySealToken parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseDelimitedFrom(input, extensionRegistry);
    }
    public static fm.lizhi.ocean.seal.protocol.SealTokenServiceProto.ResponseVerifySealToken parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return PARSER.parseFrom(input);
    }
    public static fm.lizhi.ocean.seal.protocol.SealTokenServiceProto.ResponseVerifySealToken parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseFrom(input, extensionRegistry);
    }

    public static Builder newBuilder() { return Builder.create(); }
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder(fm.lizhi.ocean.seal.protocol.SealTokenServiceProto.ResponseVerifySealToken prototype) {
      return newBuilder().mergeFrom(prototype);
    }
    public Builder toBuilder() { return newBuilder(this); }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code fm.lizhi.commons.template.datacenter.protocol.ResponseVerifySealToken}
     *
     * <pre>
     * rcode == 0(SUCCESS) = 执行成功
     * rcode == 1(INVALID) = 无效Token
     * rcode == 2(ERROR) = 服务错误
     * </pre>
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessage.Builder<Builder>
       implements fm.lizhi.ocean.seal.protocol.SealTokenServiceProto.ResponseVerifySealTokenOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return fm.lizhi.ocean.seal.protocol.SealTokenServiceProto.internal_static_fm_lizhi_commons_template_datacenter_protocol_ResponseVerifySealToken_descriptor;
      }

      protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return fm.lizhi.ocean.seal.protocol.SealTokenServiceProto.internal_static_fm_lizhi_commons_template_datacenter_protocol_ResponseVerifySealToken_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                fm.lizhi.ocean.seal.protocol.SealTokenServiceProto.ResponseVerifySealToken.class, fm.lizhi.ocean.seal.protocol.SealTokenServiceProto.ResponseVerifySealToken.Builder.class);
      }

      // Construct using fm.lizhi.ocean.seal.protocol.SealTokenServiceProto.ResponseVerifySealToken.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessage.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessage.alwaysUseFieldBuilders) {
        }
      }
      private static Builder create() {
        return new Builder();
      }

      public Builder clear() {
        super.clear();
        return this;
      }

      public Builder clone() {
        return create().mergeFrom(buildPartial());
      }

      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return fm.lizhi.ocean.seal.protocol.SealTokenServiceProto.internal_static_fm_lizhi_commons_template_datacenter_protocol_ResponseVerifySealToken_descriptor;
      }

      public fm.lizhi.ocean.seal.protocol.SealTokenServiceProto.ResponseVerifySealToken getDefaultInstanceForType() {
        return fm.lizhi.ocean.seal.protocol.SealTokenServiceProto.ResponseVerifySealToken.getDefaultInstance();
      }

      public fm.lizhi.ocean.seal.protocol.SealTokenServiceProto.ResponseVerifySealToken build() {
        fm.lizhi.ocean.seal.protocol.SealTokenServiceProto.ResponseVerifySealToken result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      public fm.lizhi.ocean.seal.protocol.SealTokenServiceProto.ResponseVerifySealToken buildPartial() {
        fm.lizhi.ocean.seal.protocol.SealTokenServiceProto.ResponseVerifySealToken result = new fm.lizhi.ocean.seal.protocol.SealTokenServiceProto.ResponseVerifySealToken(this);
        onBuilt();
        return result;
      }

      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof fm.lizhi.ocean.seal.protocol.SealTokenServiceProto.ResponseVerifySealToken) {
          return mergeFrom((fm.lizhi.ocean.seal.protocol.SealTokenServiceProto.ResponseVerifySealToken)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(fm.lizhi.ocean.seal.protocol.SealTokenServiceProto.ResponseVerifySealToken other) {
        if (other == fm.lizhi.ocean.seal.protocol.SealTokenServiceProto.ResponseVerifySealToken.getDefaultInstance()) return this;
        this.mergeUnknownFields(other.getUnknownFields());
        return this;
      }

      public final boolean isInitialized() {
        return true;
      }

      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        fm.lizhi.ocean.seal.protocol.SealTokenServiceProto.ResponseVerifySealToken parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (fm.lizhi.ocean.seal.protocol.SealTokenServiceProto.ResponseVerifySealToken) e.getUnfinishedMessage();
          throw e;
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }

      // @@protoc_insertion_point(builder_scope:fm.lizhi.commons.template.datacenter.protocol.ResponseVerifySealToken)
    }

    static {
      defaultInstance = new ResponseVerifySealToken(true);
      defaultInstance.initFields();
    }

    // @@protoc_insertion_point(class_scope:fm.lizhi.commons.template.datacenter.protocol.ResponseVerifySealToken)
  }

  public interface RequestGetUserInfoBySealTokenOrBuilder
      extends com.google.protobuf.MessageOrBuilder {

    // optional string sealToken = 1;
    /**
     * <code>optional string sealToken = 1;</code>
     *
     * <pre>
     *sealToken获取用户信息
     * </pre>
     */
    boolean hasSealToken();
    /**
     * <code>optional string sealToken = 1;</code>
     *
     * <pre>
     *sealToken获取用户信息
     * </pre>
     */
    java.lang.String getSealToken();
    /**
     * <code>optional string sealToken = 1;</code>
     *
     * <pre>
     *sealToken获取用户信息
     * </pre>
     */
    com.google.protobuf.ByteString
        getSealTokenBytes();

    // optional string appId = 2;
    /**
     * <code>optional string appId = 2;</code>
     */
    boolean hasAppId();
    /**
     * <code>optional string appId = 2;</code>
     */
    java.lang.String getAppId();
    /**
     * <code>optional string appId = 2;</code>
     */
    com.google.protobuf.ByteString
        getAppIdBytes();
  }
  /**
   * Protobuf type {@code fm.lizhi.commons.template.datacenter.protocol.RequestGetUserInfoBySealToken}
   *
   * <pre>
   * SealTokenService.java
   * 根据用户SealToken获取用户信息
   * domain = 4302, op = 102
   * </pre>
   */
  public static final class RequestGetUserInfoBySealToken extends
      com.google.protobuf.GeneratedMessage
      implements RequestGetUserInfoBySealTokenOrBuilder {
    // Use RequestGetUserInfoBySealToken.newBuilder() to construct.
    private RequestGetUserInfoBySealToken(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
      super(builder);
      this.unknownFields = builder.getUnknownFields();
    }
    private RequestGetUserInfoBySealToken(boolean noInit) { this.unknownFields = com.google.protobuf.UnknownFieldSet.getDefaultInstance(); }

    private static final RequestGetUserInfoBySealToken defaultInstance;
    public static RequestGetUserInfoBySealToken getDefaultInstance() {
      return defaultInstance;
    }

    public RequestGetUserInfoBySealToken getDefaultInstanceForType() {
      return defaultInstance;
    }

    private final com.google.protobuf.UnknownFieldSet unknownFields;
    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
        getUnknownFields() {
      return this.unknownFields;
    }
    private RequestGetUserInfoBySealToken(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      initFields();
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            default: {
              if (!parseUnknownField(input, unknownFields,
                                     extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
            case 10: {
              bitField0_ |= 0x00000001;
              sealToken_ = input.readBytes();
              break;
            }
            case 18: {
              bitField0_ |= 0x00000002;
              appId_ = input.readBytes();
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e.getMessage()).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return fm.lizhi.ocean.seal.protocol.SealTokenServiceProto.internal_static_fm_lizhi_commons_template_datacenter_protocol_RequestGetUserInfoBySealToken_descriptor;
    }

    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return fm.lizhi.ocean.seal.protocol.SealTokenServiceProto.internal_static_fm_lizhi_commons_template_datacenter_protocol_RequestGetUserInfoBySealToken_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              fm.lizhi.ocean.seal.protocol.SealTokenServiceProto.RequestGetUserInfoBySealToken.class, fm.lizhi.ocean.seal.protocol.SealTokenServiceProto.RequestGetUserInfoBySealToken.Builder.class);
    }

    public static com.google.protobuf.Parser<RequestGetUserInfoBySealToken> PARSER =
        new com.google.protobuf.AbstractParser<RequestGetUserInfoBySealToken>() {
      public RequestGetUserInfoBySealToken parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new RequestGetUserInfoBySealToken(input, extensionRegistry);
      }
    };

    @java.lang.Override
    public com.google.protobuf.Parser<RequestGetUserInfoBySealToken> getParserForType() {
      return PARSER;
    }

    private int bitField0_;
    // optional string sealToken = 1;
    public static final int SEALTOKEN_FIELD_NUMBER = 1;
    private java.lang.Object sealToken_;
    /**
     * <code>optional string sealToken = 1;</code>
     *
     * <pre>
     *sealToken获取用户信息
     * </pre>
     */
    public boolean hasSealToken() {
      return ((bitField0_ & 0x00000001) == 0x00000001);
    }
    /**
     * <code>optional string sealToken = 1;</code>
     *
     * <pre>
     *sealToken获取用户信息
     * </pre>
     */
    public java.lang.String getSealToken() {
      java.lang.Object ref = sealToken_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          sealToken_ = s;
        }
        return s;
      }
    }
    /**
     * <code>optional string sealToken = 1;</code>
     *
     * <pre>
     *sealToken获取用户信息
     * </pre>
     */
    public com.google.protobuf.ByteString
        getSealTokenBytes() {
      java.lang.Object ref = sealToken_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        sealToken_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    // optional string appId = 2;
    public static final int APPID_FIELD_NUMBER = 2;
    private java.lang.Object appId_;
    /**
     * <code>optional string appId = 2;</code>
     */
    public boolean hasAppId() {
      return ((bitField0_ & 0x00000002) == 0x00000002);
    }
    /**
     * <code>optional string appId = 2;</code>
     */
    public java.lang.String getAppId() {
      java.lang.Object ref = appId_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          appId_ = s;
        }
        return s;
      }
    }
    /**
     * <code>optional string appId = 2;</code>
     */
    public com.google.protobuf.ByteString
        getAppIdBytes() {
      java.lang.Object ref = appId_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        appId_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    private void initFields() {
      sealToken_ = "";
      appId_ = "";
    }
    private byte memoizedIsInitialized = -1;
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized != -1) return isInitialized == 1;

      memoizedIsInitialized = 1;
      return true;
    }

    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      getSerializedSize();
      if (((bitField0_ & 0x00000001) == 0x00000001)) {
        output.writeBytes(1, getSealTokenBytes());
      }
      if (((bitField0_ & 0x00000002) == 0x00000002)) {
        output.writeBytes(2, getAppIdBytes());
      }
      getUnknownFields().writeTo(output);
    }

    private int memoizedSerializedSize = -1;
    public int getSerializedSize() {
      int size = memoizedSerializedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) == 0x00000001)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(1, getSealTokenBytes());
      }
      if (((bitField0_ & 0x00000002) == 0x00000002)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(2, getAppIdBytes());
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSerializedSize = size;
      return size;
    }

    private static final long serialVersionUID = 0L;
    @java.lang.Override
    protected java.lang.Object writeReplace()
        throws java.io.ObjectStreamException {
      return super.writeReplace();
    }

    public static fm.lizhi.ocean.seal.protocol.SealTokenServiceProto.RequestGetUserInfoBySealToken parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static fm.lizhi.ocean.seal.protocol.SealTokenServiceProto.RequestGetUserInfoBySealToken parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static fm.lizhi.ocean.seal.protocol.SealTokenServiceProto.RequestGetUserInfoBySealToken parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static fm.lizhi.ocean.seal.protocol.SealTokenServiceProto.RequestGetUserInfoBySealToken parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static fm.lizhi.ocean.seal.protocol.SealTokenServiceProto.RequestGetUserInfoBySealToken parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return PARSER.parseFrom(input);
    }
    public static fm.lizhi.ocean.seal.protocol.SealTokenServiceProto.RequestGetUserInfoBySealToken parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseFrom(input, extensionRegistry);
    }
    public static fm.lizhi.ocean.seal.protocol.SealTokenServiceProto.RequestGetUserInfoBySealToken parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return PARSER.parseDelimitedFrom(input);
    }
    public static fm.lizhi.ocean.seal.protocol.SealTokenServiceProto.RequestGetUserInfoBySealToken parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseDelimitedFrom(input, extensionRegistry);
    }
    public static fm.lizhi.ocean.seal.protocol.SealTokenServiceProto.RequestGetUserInfoBySealToken parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return PARSER.parseFrom(input);
    }
    public static fm.lizhi.ocean.seal.protocol.SealTokenServiceProto.RequestGetUserInfoBySealToken parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseFrom(input, extensionRegistry);
    }

    public static Builder newBuilder() { return Builder.create(); }
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder(fm.lizhi.ocean.seal.protocol.SealTokenServiceProto.RequestGetUserInfoBySealToken prototype) {
      return newBuilder().mergeFrom(prototype);
    }
    public Builder toBuilder() { return newBuilder(this); }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code fm.lizhi.commons.template.datacenter.protocol.RequestGetUserInfoBySealToken}
     *
     * <pre>
     * SealTokenService.java
     * 根据用户SealToken获取用户信息
     * domain = 4302, op = 102
     * </pre>
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessage.Builder<Builder>
       implements fm.lizhi.ocean.seal.protocol.SealTokenServiceProto.RequestGetUserInfoBySealTokenOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return fm.lizhi.ocean.seal.protocol.SealTokenServiceProto.internal_static_fm_lizhi_commons_template_datacenter_protocol_RequestGetUserInfoBySealToken_descriptor;
      }

      protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return fm.lizhi.ocean.seal.protocol.SealTokenServiceProto.internal_static_fm_lizhi_commons_template_datacenter_protocol_RequestGetUserInfoBySealToken_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                fm.lizhi.ocean.seal.protocol.SealTokenServiceProto.RequestGetUserInfoBySealToken.class, fm.lizhi.ocean.seal.protocol.SealTokenServiceProto.RequestGetUserInfoBySealToken.Builder.class);
      }

      // Construct using fm.lizhi.ocean.seal.protocol.SealTokenServiceProto.RequestGetUserInfoBySealToken.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessage.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessage.alwaysUseFieldBuilders) {
        }
      }
      private static Builder create() {
        return new Builder();
      }

      public Builder clear() {
        super.clear();
        sealToken_ = "";
        bitField0_ = (bitField0_ & ~0x00000001);
        appId_ = "";
        bitField0_ = (bitField0_ & ~0x00000002);
        return this;
      }

      public Builder clone() {
        return create().mergeFrom(buildPartial());
      }

      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return fm.lizhi.ocean.seal.protocol.SealTokenServiceProto.internal_static_fm_lizhi_commons_template_datacenter_protocol_RequestGetUserInfoBySealToken_descriptor;
      }

      public fm.lizhi.ocean.seal.protocol.SealTokenServiceProto.RequestGetUserInfoBySealToken getDefaultInstanceForType() {
        return fm.lizhi.ocean.seal.protocol.SealTokenServiceProto.RequestGetUserInfoBySealToken.getDefaultInstance();
      }

      public fm.lizhi.ocean.seal.protocol.SealTokenServiceProto.RequestGetUserInfoBySealToken build() {
        fm.lizhi.ocean.seal.protocol.SealTokenServiceProto.RequestGetUserInfoBySealToken result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      public fm.lizhi.ocean.seal.protocol.SealTokenServiceProto.RequestGetUserInfoBySealToken buildPartial() {
        fm.lizhi.ocean.seal.protocol.SealTokenServiceProto.RequestGetUserInfoBySealToken result = new fm.lizhi.ocean.seal.protocol.SealTokenServiceProto.RequestGetUserInfoBySealToken(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) == 0x00000001)) {
          to_bitField0_ |= 0x00000001;
        }
        result.sealToken_ = sealToken_;
        if (((from_bitField0_ & 0x00000002) == 0x00000002)) {
          to_bitField0_ |= 0x00000002;
        }
        result.appId_ = appId_;
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof fm.lizhi.ocean.seal.protocol.SealTokenServiceProto.RequestGetUserInfoBySealToken) {
          return mergeFrom((fm.lizhi.ocean.seal.protocol.SealTokenServiceProto.RequestGetUserInfoBySealToken)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(fm.lizhi.ocean.seal.protocol.SealTokenServiceProto.RequestGetUserInfoBySealToken other) {
        if (other == fm.lizhi.ocean.seal.protocol.SealTokenServiceProto.RequestGetUserInfoBySealToken.getDefaultInstance()) return this;
        if (other.hasSealToken()) {
          bitField0_ |= 0x00000001;
          sealToken_ = other.sealToken_;
          onChanged();
        }
        if (other.hasAppId()) {
          bitField0_ |= 0x00000002;
          appId_ = other.appId_;
          onChanged();
        }
        this.mergeUnknownFields(other.getUnknownFields());
        return this;
      }

      public final boolean isInitialized() {
        return true;
      }

      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        fm.lizhi.ocean.seal.protocol.SealTokenServiceProto.RequestGetUserInfoBySealToken parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (fm.lizhi.ocean.seal.protocol.SealTokenServiceProto.RequestGetUserInfoBySealToken) e.getUnfinishedMessage();
          throw e;
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      // optional string sealToken = 1;
      private java.lang.Object sealToken_ = "";
      /**
       * <code>optional string sealToken = 1;</code>
       *
       * <pre>
       *sealToken获取用户信息
       * </pre>
       */
      public boolean hasSealToken() {
        return ((bitField0_ & 0x00000001) == 0x00000001);
      }
      /**
       * <code>optional string sealToken = 1;</code>
       *
       * <pre>
       *sealToken获取用户信息
       * </pre>
       */
      public java.lang.String getSealToken() {
        java.lang.Object ref = sealToken_;
        if (!(ref instanceof java.lang.String)) {
          java.lang.String s = ((com.google.protobuf.ByteString) ref)
              .toStringUtf8();
          sealToken_ = s;
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <code>optional string sealToken = 1;</code>
       *
       * <pre>
       *sealToken获取用户信息
       * </pre>
       */
      public com.google.protobuf.ByteString
          getSealTokenBytes() {
        java.lang.Object ref = sealToken_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          sealToken_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <code>optional string sealToken = 1;</code>
       *
       * <pre>
       *sealToken获取用户信息
       * </pre>
       */
      public Builder setSealToken(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000001;
        sealToken_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional string sealToken = 1;</code>
       *
       * <pre>
       *sealToken获取用户信息
       * </pre>
       */
      public Builder clearSealToken() {
        bitField0_ = (bitField0_ & ~0x00000001);
        sealToken_ = getDefaultInstance().getSealToken();
        onChanged();
        return this;
      }
      /**
       * <code>optional string sealToken = 1;</code>
       *
       * <pre>
       *sealToken获取用户信息
       * </pre>
       */
      public Builder setSealTokenBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000001;
        sealToken_ = value;
        onChanged();
        return this;
      }

      // optional string appId = 2;
      private java.lang.Object appId_ = "";
      /**
       * <code>optional string appId = 2;</code>
       */
      public boolean hasAppId() {
        return ((bitField0_ & 0x00000002) == 0x00000002);
      }
      /**
       * <code>optional string appId = 2;</code>
       */
      public java.lang.String getAppId() {
        java.lang.Object ref = appId_;
        if (!(ref instanceof java.lang.String)) {
          java.lang.String s = ((com.google.protobuf.ByteString) ref)
              .toStringUtf8();
          appId_ = s;
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <code>optional string appId = 2;</code>
       */
      public com.google.protobuf.ByteString
          getAppIdBytes() {
        java.lang.Object ref = appId_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          appId_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <code>optional string appId = 2;</code>
       */
      public Builder setAppId(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000002;
        appId_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional string appId = 2;</code>
       */
      public Builder clearAppId() {
        bitField0_ = (bitField0_ & ~0x00000002);
        appId_ = getDefaultInstance().getAppId();
        onChanged();
        return this;
      }
      /**
       * <code>optional string appId = 2;</code>
       */
      public Builder setAppIdBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000002;
        appId_ = value;
        onChanged();
        return this;
      }

      // @@protoc_insertion_point(builder_scope:fm.lizhi.commons.template.datacenter.protocol.RequestGetUserInfoBySealToken)
    }

    static {
      defaultInstance = new RequestGetUserInfoBySealToken(true);
      defaultInstance.initFields();
    }

    // @@protoc_insertion_point(class_scope:fm.lizhi.commons.template.datacenter.protocol.RequestGetUserInfoBySealToken)
  }

  public interface ResponseGetUserInfoBySealTokenOrBuilder
      extends com.google.protobuf.MessageOrBuilder {

    // optional .fm.lizhi.commons.template.datacenter.protocol.JwtUserInfo sealUser = 1;
    /**
     * <code>optional .fm.lizhi.commons.template.datacenter.protocol.JwtUserInfo sealUser = 1;</code>
     */
    boolean hasSealUser();
    /**
     * <code>optional .fm.lizhi.commons.template.datacenter.protocol.JwtUserInfo sealUser = 1;</code>
     */
    fm.lizhi.ocean.seal.protocol.SealTokenServiceProto.JwtUserInfo getSealUser();
    /**
     * <code>optional .fm.lizhi.commons.template.datacenter.protocol.JwtUserInfo sealUser = 1;</code>
     */
    fm.lizhi.ocean.seal.protocol.SealTokenServiceProto.JwtUserInfoOrBuilder getSealUserOrBuilder();
  }
  /**
   * Protobuf type {@code fm.lizhi.commons.template.datacenter.protocol.ResponseGetUserInfoBySealToken}
   *
   * <pre>
   * rcode == 0(SUCCESS) = 执行成功
   * rcode == 1(FAIL) = 无效Token
   * rcode == 2(ILLEGAL_PARAMS) = 参数非法
   * rcode == 3(ERROR) = 服务错误
   * </pre>
   */
  public static final class ResponseGetUserInfoBySealToken extends
      com.google.protobuf.GeneratedMessage
      implements ResponseGetUserInfoBySealTokenOrBuilder {
    // Use ResponseGetUserInfoBySealToken.newBuilder() to construct.
    private ResponseGetUserInfoBySealToken(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
      super(builder);
      this.unknownFields = builder.getUnknownFields();
    }
    private ResponseGetUserInfoBySealToken(boolean noInit) { this.unknownFields = com.google.protobuf.UnknownFieldSet.getDefaultInstance(); }

    private static final ResponseGetUserInfoBySealToken defaultInstance;
    public static ResponseGetUserInfoBySealToken getDefaultInstance() {
      return defaultInstance;
    }

    public ResponseGetUserInfoBySealToken getDefaultInstanceForType() {
      return defaultInstance;
    }

    private final com.google.protobuf.UnknownFieldSet unknownFields;
    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
        getUnknownFields() {
      return this.unknownFields;
    }
    private ResponseGetUserInfoBySealToken(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      initFields();
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            default: {
              if (!parseUnknownField(input, unknownFields,
                                     extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
            case 10: {
              fm.lizhi.ocean.seal.protocol.SealTokenServiceProto.JwtUserInfo.Builder subBuilder = null;
              if (((bitField0_ & 0x00000001) == 0x00000001)) {
                subBuilder = sealUser_.toBuilder();
              }
              sealUser_ = input.readMessage(fm.lizhi.ocean.seal.protocol.SealTokenServiceProto.JwtUserInfo.PARSER, extensionRegistry);
              if (subBuilder != null) {
                subBuilder.mergeFrom(sealUser_);
                sealUser_ = subBuilder.buildPartial();
              }
              bitField0_ |= 0x00000001;
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e.getMessage()).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return fm.lizhi.ocean.seal.protocol.SealTokenServiceProto.internal_static_fm_lizhi_commons_template_datacenter_protocol_ResponseGetUserInfoBySealToken_descriptor;
    }

    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return fm.lizhi.ocean.seal.protocol.SealTokenServiceProto.internal_static_fm_lizhi_commons_template_datacenter_protocol_ResponseGetUserInfoBySealToken_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              fm.lizhi.ocean.seal.protocol.SealTokenServiceProto.ResponseGetUserInfoBySealToken.class, fm.lizhi.ocean.seal.protocol.SealTokenServiceProto.ResponseGetUserInfoBySealToken.Builder.class);
    }

    public static com.google.protobuf.Parser<ResponseGetUserInfoBySealToken> PARSER =
        new com.google.protobuf.AbstractParser<ResponseGetUserInfoBySealToken>() {
      public ResponseGetUserInfoBySealToken parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new ResponseGetUserInfoBySealToken(input, extensionRegistry);
      }
    };

    @java.lang.Override
    public com.google.protobuf.Parser<ResponseGetUserInfoBySealToken> getParserForType() {
      return PARSER;
    }

    private int bitField0_;
    // optional .fm.lizhi.commons.template.datacenter.protocol.JwtUserInfo sealUser = 1;
    public static final int SEALUSER_FIELD_NUMBER = 1;
    private fm.lizhi.ocean.seal.protocol.SealTokenServiceProto.JwtUserInfo sealUser_;
    /**
     * <code>optional .fm.lizhi.commons.template.datacenter.protocol.JwtUserInfo sealUser = 1;</code>
     */
    public boolean hasSealUser() {
      return ((bitField0_ & 0x00000001) == 0x00000001);
    }
    /**
     * <code>optional .fm.lizhi.commons.template.datacenter.protocol.JwtUserInfo sealUser = 1;</code>
     */
    public fm.lizhi.ocean.seal.protocol.SealTokenServiceProto.JwtUserInfo getSealUser() {
      return sealUser_;
    }
    /**
     * <code>optional .fm.lizhi.commons.template.datacenter.protocol.JwtUserInfo sealUser = 1;</code>
     */
    public fm.lizhi.ocean.seal.protocol.SealTokenServiceProto.JwtUserInfoOrBuilder getSealUserOrBuilder() {
      return sealUser_;
    }

    private void initFields() {
      sealUser_ = fm.lizhi.ocean.seal.protocol.SealTokenServiceProto.JwtUserInfo.getDefaultInstance();
    }
    private byte memoizedIsInitialized = -1;
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized != -1) return isInitialized == 1;

      memoizedIsInitialized = 1;
      return true;
    }

    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      getSerializedSize();
      if (((bitField0_ & 0x00000001) == 0x00000001)) {
        output.writeMessage(1, sealUser_);
      }
      getUnknownFields().writeTo(output);
    }

    private int memoizedSerializedSize = -1;
    public int getSerializedSize() {
      int size = memoizedSerializedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) == 0x00000001)) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(1, sealUser_);
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSerializedSize = size;
      return size;
    }

    private static final long serialVersionUID = 0L;
    @java.lang.Override
    protected java.lang.Object writeReplace()
        throws java.io.ObjectStreamException {
      return super.writeReplace();
    }

    public static fm.lizhi.ocean.seal.protocol.SealTokenServiceProto.ResponseGetUserInfoBySealToken parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static fm.lizhi.ocean.seal.protocol.SealTokenServiceProto.ResponseGetUserInfoBySealToken parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static fm.lizhi.ocean.seal.protocol.SealTokenServiceProto.ResponseGetUserInfoBySealToken parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static fm.lizhi.ocean.seal.protocol.SealTokenServiceProto.ResponseGetUserInfoBySealToken parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static fm.lizhi.ocean.seal.protocol.SealTokenServiceProto.ResponseGetUserInfoBySealToken parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return PARSER.parseFrom(input);
    }
    public static fm.lizhi.ocean.seal.protocol.SealTokenServiceProto.ResponseGetUserInfoBySealToken parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseFrom(input, extensionRegistry);
    }
    public static fm.lizhi.ocean.seal.protocol.SealTokenServiceProto.ResponseGetUserInfoBySealToken parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return PARSER.parseDelimitedFrom(input);
    }
    public static fm.lizhi.ocean.seal.protocol.SealTokenServiceProto.ResponseGetUserInfoBySealToken parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseDelimitedFrom(input, extensionRegistry);
    }
    public static fm.lizhi.ocean.seal.protocol.SealTokenServiceProto.ResponseGetUserInfoBySealToken parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return PARSER.parseFrom(input);
    }
    public static fm.lizhi.ocean.seal.protocol.SealTokenServiceProto.ResponseGetUserInfoBySealToken parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseFrom(input, extensionRegistry);
    }

    public static Builder newBuilder() { return Builder.create(); }
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder(fm.lizhi.ocean.seal.protocol.SealTokenServiceProto.ResponseGetUserInfoBySealToken prototype) {
      return newBuilder().mergeFrom(prototype);
    }
    public Builder toBuilder() { return newBuilder(this); }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code fm.lizhi.commons.template.datacenter.protocol.ResponseGetUserInfoBySealToken}
     *
     * <pre>
     * rcode == 0(SUCCESS) = 执行成功
     * rcode == 1(FAIL) = 无效Token
     * rcode == 2(ILLEGAL_PARAMS) = 参数非法
     * rcode == 3(ERROR) = 服务错误
     * </pre>
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessage.Builder<Builder>
       implements fm.lizhi.ocean.seal.protocol.SealTokenServiceProto.ResponseGetUserInfoBySealTokenOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return fm.lizhi.ocean.seal.protocol.SealTokenServiceProto.internal_static_fm_lizhi_commons_template_datacenter_protocol_ResponseGetUserInfoBySealToken_descriptor;
      }

      protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return fm.lizhi.ocean.seal.protocol.SealTokenServiceProto.internal_static_fm_lizhi_commons_template_datacenter_protocol_ResponseGetUserInfoBySealToken_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                fm.lizhi.ocean.seal.protocol.SealTokenServiceProto.ResponseGetUserInfoBySealToken.class, fm.lizhi.ocean.seal.protocol.SealTokenServiceProto.ResponseGetUserInfoBySealToken.Builder.class);
      }

      // Construct using fm.lizhi.ocean.seal.protocol.SealTokenServiceProto.ResponseGetUserInfoBySealToken.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessage.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessage.alwaysUseFieldBuilders) {
          getSealUserFieldBuilder();
        }
      }
      private static Builder create() {
        return new Builder();
      }

      public Builder clear() {
        super.clear();
        if (sealUserBuilder_ == null) {
          sealUser_ = fm.lizhi.ocean.seal.protocol.SealTokenServiceProto.JwtUserInfo.getDefaultInstance();
        } else {
          sealUserBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000001);
        return this;
      }

      public Builder clone() {
        return create().mergeFrom(buildPartial());
      }

      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return fm.lizhi.ocean.seal.protocol.SealTokenServiceProto.internal_static_fm_lizhi_commons_template_datacenter_protocol_ResponseGetUserInfoBySealToken_descriptor;
      }

      public fm.lizhi.ocean.seal.protocol.SealTokenServiceProto.ResponseGetUserInfoBySealToken getDefaultInstanceForType() {
        return fm.lizhi.ocean.seal.protocol.SealTokenServiceProto.ResponseGetUserInfoBySealToken.getDefaultInstance();
      }

      public fm.lizhi.ocean.seal.protocol.SealTokenServiceProto.ResponseGetUserInfoBySealToken build() {
        fm.lizhi.ocean.seal.protocol.SealTokenServiceProto.ResponseGetUserInfoBySealToken result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      public fm.lizhi.ocean.seal.protocol.SealTokenServiceProto.ResponseGetUserInfoBySealToken buildPartial() {
        fm.lizhi.ocean.seal.protocol.SealTokenServiceProto.ResponseGetUserInfoBySealToken result = new fm.lizhi.ocean.seal.protocol.SealTokenServiceProto.ResponseGetUserInfoBySealToken(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) == 0x00000001)) {
          to_bitField0_ |= 0x00000001;
        }
        if (sealUserBuilder_ == null) {
          result.sealUser_ = sealUser_;
        } else {
          result.sealUser_ = sealUserBuilder_.build();
        }
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof fm.lizhi.ocean.seal.protocol.SealTokenServiceProto.ResponseGetUserInfoBySealToken) {
          return mergeFrom((fm.lizhi.ocean.seal.protocol.SealTokenServiceProto.ResponseGetUserInfoBySealToken)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(fm.lizhi.ocean.seal.protocol.SealTokenServiceProto.ResponseGetUserInfoBySealToken other) {
        if (other == fm.lizhi.ocean.seal.protocol.SealTokenServiceProto.ResponseGetUserInfoBySealToken.getDefaultInstance()) return this;
        if (other.hasSealUser()) {
          mergeSealUser(other.getSealUser());
        }
        this.mergeUnknownFields(other.getUnknownFields());
        return this;
      }

      public final boolean isInitialized() {
        return true;
      }

      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        fm.lizhi.ocean.seal.protocol.SealTokenServiceProto.ResponseGetUserInfoBySealToken parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (fm.lizhi.ocean.seal.protocol.SealTokenServiceProto.ResponseGetUserInfoBySealToken) e.getUnfinishedMessage();
          throw e;
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      // optional .fm.lizhi.commons.template.datacenter.protocol.JwtUserInfo sealUser = 1;
      private fm.lizhi.ocean.seal.protocol.SealTokenServiceProto.JwtUserInfo sealUser_ = fm.lizhi.ocean.seal.protocol.SealTokenServiceProto.JwtUserInfo.getDefaultInstance();
      private com.google.protobuf.SingleFieldBuilder<
          fm.lizhi.ocean.seal.protocol.SealTokenServiceProto.JwtUserInfo, fm.lizhi.ocean.seal.protocol.SealTokenServiceProto.JwtUserInfo.Builder, fm.lizhi.ocean.seal.protocol.SealTokenServiceProto.JwtUserInfoOrBuilder> sealUserBuilder_;
      /**
       * <code>optional .fm.lizhi.commons.template.datacenter.protocol.JwtUserInfo sealUser = 1;</code>
       */
      public boolean hasSealUser() {
        return ((bitField0_ & 0x00000001) == 0x00000001);
      }
      /**
       * <code>optional .fm.lizhi.commons.template.datacenter.protocol.JwtUserInfo sealUser = 1;</code>
       */
      public fm.lizhi.ocean.seal.protocol.SealTokenServiceProto.JwtUserInfo getSealUser() {
        if (sealUserBuilder_ == null) {
          return sealUser_;
        } else {
          return sealUserBuilder_.getMessage();
        }
      }
      /**
       * <code>optional .fm.lizhi.commons.template.datacenter.protocol.JwtUserInfo sealUser = 1;</code>
       */
      public Builder setSealUser(fm.lizhi.ocean.seal.protocol.SealTokenServiceProto.JwtUserInfo value) {
        if (sealUserBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          sealUser_ = value;
          onChanged();
        } else {
          sealUserBuilder_.setMessage(value);
        }
        bitField0_ |= 0x00000001;
        return this;
      }
      /**
       * <code>optional .fm.lizhi.commons.template.datacenter.protocol.JwtUserInfo sealUser = 1;</code>
       */
      public Builder setSealUser(
          fm.lizhi.ocean.seal.protocol.SealTokenServiceProto.JwtUserInfo.Builder builderForValue) {
        if (sealUserBuilder_ == null) {
          sealUser_ = builderForValue.build();
          onChanged();
        } else {
          sealUserBuilder_.setMessage(builderForValue.build());
        }
        bitField0_ |= 0x00000001;
        return this;
      }
      /**
       * <code>optional .fm.lizhi.commons.template.datacenter.protocol.JwtUserInfo sealUser = 1;</code>
       */
      public Builder mergeSealUser(fm.lizhi.ocean.seal.protocol.SealTokenServiceProto.JwtUserInfo value) {
        if (sealUserBuilder_ == null) {
          if (((bitField0_ & 0x00000001) == 0x00000001) &&
              sealUser_ != fm.lizhi.ocean.seal.protocol.SealTokenServiceProto.JwtUserInfo.getDefaultInstance()) {
            sealUser_ =
              fm.lizhi.ocean.seal.protocol.SealTokenServiceProto.JwtUserInfo.newBuilder(sealUser_).mergeFrom(value).buildPartial();
          } else {
            sealUser_ = value;
          }
          onChanged();
        } else {
          sealUserBuilder_.mergeFrom(value);
        }
        bitField0_ |= 0x00000001;
        return this;
      }
      /**
       * <code>optional .fm.lizhi.commons.template.datacenter.protocol.JwtUserInfo sealUser = 1;</code>
       */
      public Builder clearSealUser() {
        if (sealUserBuilder_ == null) {
          sealUser_ = fm.lizhi.ocean.seal.protocol.SealTokenServiceProto.JwtUserInfo.getDefaultInstance();
          onChanged();
        } else {
          sealUserBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000001);
        return this;
      }
      /**
       * <code>optional .fm.lizhi.commons.template.datacenter.protocol.JwtUserInfo sealUser = 1;</code>
       */
      public fm.lizhi.ocean.seal.protocol.SealTokenServiceProto.JwtUserInfo.Builder getSealUserBuilder() {
        bitField0_ |= 0x00000001;
        onChanged();
        return getSealUserFieldBuilder().getBuilder();
      }
      /**
       * <code>optional .fm.lizhi.commons.template.datacenter.protocol.JwtUserInfo sealUser = 1;</code>
       */
      public fm.lizhi.ocean.seal.protocol.SealTokenServiceProto.JwtUserInfoOrBuilder getSealUserOrBuilder() {
        if (sealUserBuilder_ != null) {
          return sealUserBuilder_.getMessageOrBuilder();
        } else {
          return sealUser_;
        }
      }
      /**
       * <code>optional .fm.lizhi.commons.template.datacenter.protocol.JwtUserInfo sealUser = 1;</code>
       */
      private com.google.protobuf.SingleFieldBuilder<
          fm.lizhi.ocean.seal.protocol.SealTokenServiceProto.JwtUserInfo, fm.lizhi.ocean.seal.protocol.SealTokenServiceProto.JwtUserInfo.Builder, fm.lizhi.ocean.seal.protocol.SealTokenServiceProto.JwtUserInfoOrBuilder> 
          getSealUserFieldBuilder() {
        if (sealUserBuilder_ == null) {
          sealUserBuilder_ = new com.google.protobuf.SingleFieldBuilder<
              fm.lizhi.ocean.seal.protocol.SealTokenServiceProto.JwtUserInfo, fm.lizhi.ocean.seal.protocol.SealTokenServiceProto.JwtUserInfo.Builder, fm.lizhi.ocean.seal.protocol.SealTokenServiceProto.JwtUserInfoOrBuilder>(
                  sealUser_,
                  getParentForChildren(),
                  isClean());
          sealUser_ = null;
        }
        return sealUserBuilder_;
      }

      // @@protoc_insertion_point(builder_scope:fm.lizhi.commons.template.datacenter.protocol.ResponseGetUserInfoBySealToken)
    }

    static {
      defaultInstance = new ResponseGetUserInfoBySealToken(true);
      defaultInstance.initFields();
    }

    // @@protoc_insertion_point(class_scope:fm.lizhi.commons.template.datacenter.protocol.ResponseGetUserInfoBySealToken)
  }

  private static com.google.protobuf.Descriptors.Descriptor
    internal_static_fm_lizhi_commons_template_datacenter_protocol_SealHashingTokenParam_descriptor;
  private static
    com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internal_static_fm_lizhi_commons_template_datacenter_protocol_SealHashingTokenParam_fieldAccessorTable;
  private static com.google.protobuf.Descriptors.Descriptor
    internal_static_fm_lizhi_commons_template_datacenter_protocol_JwtUserInfo_descriptor;
  private static
    com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internal_static_fm_lizhi_commons_template_datacenter_protocol_JwtUserInfo_fieldAccessorTable;
  private static com.google.protobuf.Descriptors.Descriptor
    internal_static_fm_lizhi_commons_template_datacenter_protocol_RequestGetSealHashingToken_descriptor;
  private static
    com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internal_static_fm_lizhi_commons_template_datacenter_protocol_RequestGetSealHashingToken_fieldAccessorTable;
  private static com.google.protobuf.Descriptors.Descriptor
    internal_static_fm_lizhi_commons_template_datacenter_protocol_ResponseGetSealHashingToken_descriptor;
  private static
    com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internal_static_fm_lizhi_commons_template_datacenter_protocol_ResponseGetSealHashingToken_fieldAccessorTable;
  private static com.google.protobuf.Descriptors.Descriptor
    internal_static_fm_lizhi_commons_template_datacenter_protocol_RequestVerifySealToken_descriptor;
  private static
    com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internal_static_fm_lizhi_commons_template_datacenter_protocol_RequestVerifySealToken_fieldAccessorTable;
  private static com.google.protobuf.Descriptors.Descriptor
    internal_static_fm_lizhi_commons_template_datacenter_protocol_ResponseVerifySealToken_descriptor;
  private static
    com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internal_static_fm_lizhi_commons_template_datacenter_protocol_ResponseVerifySealToken_fieldAccessorTable;
  private static com.google.protobuf.Descriptors.Descriptor
    internal_static_fm_lizhi_commons_template_datacenter_protocol_RequestGetUserInfoBySealToken_descriptor;
  private static
    com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internal_static_fm_lizhi_commons_template_datacenter_protocol_RequestGetUserInfoBySealToken_fieldAccessorTable;
  private static com.google.protobuf.Descriptors.Descriptor
    internal_static_fm_lizhi_commons_template_datacenter_protocol_ResponseGetUserInfoBySealToken_descriptor;
  private static
    com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internal_static_fm_lizhi_commons_template_datacenter_protocol_ResponseGetUserInfoBySealToken_fieldAccessorTable;

  public static com.google.protobuf.Descriptors.FileDescriptor
      getDescriptor() {
    return descriptor;
  }
  private static com.google.protobuf.Descriptors.FileDescriptor
      descriptor;
  static {
    java.lang.String[] descriptorData = {
      "\n\031protocol_seal_token.proto\022-fm.lizhi.co" +
      "mmons.template.datacenter.protocol\"~\n\025Se" +
      "alHashingTokenParam\022\016\n\006userId\030\001 \001(\003\022\r\n\005a" +
      "ppId\030\002 \001(\t\022\031\n\021currentTimeMillis\030\003 \001(\003\022\024\n" +
      "\014hashingToken\030\004 \001(\t\022\025\n\rnotVerifySign\030\005 \001" +
      "(\010\"=\n\013JwtUserInfo\022\016\n\006userId\030\001 \001(\003\022\020\n\010use" +
      "rName\030\002 \001(\t\022\014\n\004role\030\003 \001(\t\"\201\001\n\032RequestGet" +
      "SealHashingToken\022c\n\025sealHashingTokenPara" +
      "m\030\001 \001(\0132D.fm.lizhi.commons.template.data" +
      "center.protocol.SealHashingTokenParam\"D\n",
      "\033ResponseGetSealHashingToken\022\021\n\tsealToke" +
      "n\030\001 \001(\t\022\022\n\nexpireDate\030\002 \001(\003\":\n\026RequestVe" +
      "rifySealToken\022\021\n\tsealToken\030\001 \001(\t\022\r\n\005appI" +
      "d\030\002 \001(\t\"\031\n\027ResponseVerifySealToken\"A\n\035Re" +
      "questGetUserInfoBySealToken\022\021\n\tsealToken" +
      "\030\001 \001(\t\022\r\n\005appId\030\002 \001(\t\"n\n\036ResponseGetUser" +
      "InfoBySealToken\022L\n\010sealUser\030\001 \001(\0132:.fm.l" +
      "izhi.commons.template.datacenter.protoco" +
      "l.JwtUserInfoB7\n\034fm.lizhi.ocean.seal.pro" +
      "tocolB\025SealTokenServiceProtoH\001"
    };
    com.google.protobuf.Descriptors.FileDescriptor.InternalDescriptorAssigner assigner =
      new com.google.protobuf.Descriptors.FileDescriptor.InternalDescriptorAssigner() {
        public com.google.protobuf.ExtensionRegistry assignDescriptors(
            com.google.protobuf.Descriptors.FileDescriptor root) {
          descriptor = root;
          internal_static_fm_lizhi_commons_template_datacenter_protocol_SealHashingTokenParam_descriptor =
            getDescriptor().getMessageTypes().get(0);
          internal_static_fm_lizhi_commons_template_datacenter_protocol_SealHashingTokenParam_fieldAccessorTable = new
            com.google.protobuf.GeneratedMessage.FieldAccessorTable(
              internal_static_fm_lizhi_commons_template_datacenter_protocol_SealHashingTokenParam_descriptor,
              new java.lang.String[] { "UserId", "AppId", "CurrentTimeMillis", "HashingToken", "NotVerifySign", });
          internal_static_fm_lizhi_commons_template_datacenter_protocol_JwtUserInfo_descriptor =
            getDescriptor().getMessageTypes().get(1);
          internal_static_fm_lizhi_commons_template_datacenter_protocol_JwtUserInfo_fieldAccessorTable = new
            com.google.protobuf.GeneratedMessage.FieldAccessorTable(
              internal_static_fm_lizhi_commons_template_datacenter_protocol_JwtUserInfo_descriptor,
              new java.lang.String[] { "UserId", "UserName", "Role", });
          internal_static_fm_lizhi_commons_template_datacenter_protocol_RequestGetSealHashingToken_descriptor =
            getDescriptor().getMessageTypes().get(2);
          internal_static_fm_lizhi_commons_template_datacenter_protocol_RequestGetSealHashingToken_fieldAccessorTable = new
            com.google.protobuf.GeneratedMessage.FieldAccessorTable(
              internal_static_fm_lizhi_commons_template_datacenter_protocol_RequestGetSealHashingToken_descriptor,
              new java.lang.String[] { "SealHashingTokenParam", });
          internal_static_fm_lizhi_commons_template_datacenter_protocol_ResponseGetSealHashingToken_descriptor =
            getDescriptor().getMessageTypes().get(3);
          internal_static_fm_lizhi_commons_template_datacenter_protocol_ResponseGetSealHashingToken_fieldAccessorTable = new
            com.google.protobuf.GeneratedMessage.FieldAccessorTable(
              internal_static_fm_lizhi_commons_template_datacenter_protocol_ResponseGetSealHashingToken_descriptor,
              new java.lang.String[] { "SealToken", "ExpireDate", });
          internal_static_fm_lizhi_commons_template_datacenter_protocol_RequestVerifySealToken_descriptor =
            getDescriptor().getMessageTypes().get(4);
          internal_static_fm_lizhi_commons_template_datacenter_protocol_RequestVerifySealToken_fieldAccessorTable = new
            com.google.protobuf.GeneratedMessage.FieldAccessorTable(
              internal_static_fm_lizhi_commons_template_datacenter_protocol_RequestVerifySealToken_descriptor,
              new java.lang.String[] { "SealToken", "AppId", });
          internal_static_fm_lizhi_commons_template_datacenter_protocol_ResponseVerifySealToken_descriptor =
            getDescriptor().getMessageTypes().get(5);
          internal_static_fm_lizhi_commons_template_datacenter_protocol_ResponseVerifySealToken_fieldAccessorTable = new
            com.google.protobuf.GeneratedMessage.FieldAccessorTable(
              internal_static_fm_lizhi_commons_template_datacenter_protocol_ResponseVerifySealToken_descriptor,
              new java.lang.String[] { });
          internal_static_fm_lizhi_commons_template_datacenter_protocol_RequestGetUserInfoBySealToken_descriptor =
            getDescriptor().getMessageTypes().get(6);
          internal_static_fm_lizhi_commons_template_datacenter_protocol_RequestGetUserInfoBySealToken_fieldAccessorTable = new
            com.google.protobuf.GeneratedMessage.FieldAccessorTable(
              internal_static_fm_lizhi_commons_template_datacenter_protocol_RequestGetUserInfoBySealToken_descriptor,
              new java.lang.String[] { "SealToken", "AppId", });
          internal_static_fm_lizhi_commons_template_datacenter_protocol_ResponseGetUserInfoBySealToken_descriptor =
            getDescriptor().getMessageTypes().get(7);
          internal_static_fm_lizhi_commons_template_datacenter_protocol_ResponseGetUserInfoBySealToken_fieldAccessorTable = new
            com.google.protobuf.GeneratedMessage.FieldAccessorTable(
              internal_static_fm_lizhi_commons_template_datacenter_protocol_ResponseGetUserInfoBySealToken_descriptor,
              new java.lang.String[] { "SealUser", });
          return null;
        }
      };
    com.google.protobuf.Descriptors.FileDescriptor
      .internalBuildGeneratedFileFrom(descriptorData,
        new com.google.protobuf.Descriptors.FileDescriptor[] {
        }, assigner);
  }

  // @@protoc_insertion_point(outer_class_scope)
}
