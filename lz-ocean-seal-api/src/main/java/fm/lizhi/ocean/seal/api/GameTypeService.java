package fm.lizhi.ocean.seal.api;

import java.util.List;
import fm.lizhi.commons.service.client.annotation.Attribute;
import fm.lizhi.commons.service.client.annotation.Return;
import fm.lizhi.commons.service.client.annotation.Service;
import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.ocean.seal.protocol.GameTypeServiceProto.RequestGetAllTypes;
import fm.lizhi.ocean.seal.protocol.GameTypeServiceProto.ResponseGetAllTypes;
import fm.lizhi.ocean.seal.protocol.GameTypeServiceProto.GameType;

/**
 * this file generated by autoapi-maven-plugin
 * 
 * do not edit this file manually, because this file is covered when you run autoapi-maven-plugin
 * 
 */

public interface GameTypeService {
	
	
	/**
	 *  获取游戏类型列表
	 *
	 * @return 
	 *     //if rcode == 0 执行成功<br>
	 *     //if rcode == 1 错误<br>
	 */
	@Service(domain = 4302, op = 210, request = RequestGetAllTypes.class, response = ResponseGetAllTypes.class)
	@Return(resultType = ResponseGetAllTypes.class)
	Result<ResponseGetAllTypes> getAllTypes();
	
	
	public static final int GET_ALL_TYPES_SUCCESS = 0; // 执行成功
	public static final int GET_ALL_TYPES_FAIL = 1; // 错误


}