package fm.lizhi.ocean.seal.api;

import java.util.List;
import fm.lizhi.commons.service.client.annotation.Attribute;
import fm.lizhi.commons.service.client.annotation.Return;
import fm.lizhi.commons.service.client.annotation.Service;
import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.ocean.seal.protocol.GameReportServiceProto.RequestGameStartReport;
import fm.lizhi.ocean.seal.protocol.GameReportServiceProto.RequestGameSettleReport;
import fm.lizhi.ocean.seal.protocol.GameReportServiceProto.RequestGetRoomGameReportList;
import fm.lizhi.ocean.seal.protocol.GameReportServiceProto.ResponseGetRoomGameReportList;
import fm.lizhi.ocean.seal.protocol.GameReportServiceProto.RequestGetGameSettleReport;
import fm.lizhi.ocean.seal.protocol.GameReportServiceProto.ResponseGetGameSettleReport;
import fm.lizhi.ocean.seal.protocol.GameReportServiceProto.RequestRoomUsersChanged;
import fm.lizhi.ocean.seal.protocol.GameReportServiceProto.RequestSendUserHeart;
import fm.lizhi.ocean.seal.protocol.GameReportServiceProto.GamePlayerResult;
import fm.lizhi.ocean.seal.protocol.GameReportServiceProto.GamePlayerSettleResult;
import fm.lizhi.ocean.seal.protocol.GameReportServiceProto.GameStartResult;
import fm.lizhi.ocean.seal.protocol.GameReportServiceProto.GameSettleResult;
import fm.lizhi.ocean.seal.protocol.GameReportServiceProto.GameResult;
import fm.lizhi.ocean.seal.protocol.GameReportServiceProto.RoomUsersChangedResult;
import fm.lizhi.ocean.seal.protocol.GameReportServiceProto.RoomGameUserHeartBeat;

/**
 * this file generated by autoapi-maven-plugin
 * 
 * do not edit this file manually, because this file is covered when you run autoapi-maven-plugin
 * 
 */

public interface GameReportService {
	
	
	/**
	 *  上报游戏开始信息
	 *
	 * @param result
	 *            游戏开始信息
	 * @return 
	 */
	@Service(domain = 4302, op = 11, request = RequestGameStartReport.class)
	@Return(resultType = Void.class)
	Result<Void> gameStartReport(@Attribute(name = "result") GameStartResult result);
	
	
	/**
	 *  上报游戏结算信息
	 *
	 * @param result
	 *            游戏结算信息
	 * @return 
	 */
	@Service(domain = 4302, op = 12, request = RequestGameSettleReport.class)
	@Return(resultType = Void.class)
	Result<Void> gameSettleReport(@Attribute(name = "result") GameSettleResult result);
	
	
	/**
	 *  获取游戏上报数据
	 *
	 * @param roomId
	 *            房间ID
	 * @param gameChannel
	 *            游戏所属渠道
	 * @param pageNum
	 *            页码(1开始)
	 * @param pageCount
	 *            每页条数(最大不超过10)
	 * @param appId
	 *            所属appId
	 * @return 
	 *     //if rcode == 1 参数非法<br>
	 *     //if rcode == 2 失败<br>
	 *     //if rcode == 3 没有数据<br>
	 */
	@Service(domain = 4302, op = 13, request = RequestGetRoomGameReportList.class, response = ResponseGetRoomGameReportList.class)
	@Return(resultType = ResponseGetRoomGameReportList.class)
	Result<ResponseGetRoomGameReportList> getRoomGameReportList(@Attribute(name = "roomId") String roomId, @Attribute(name = "gameChannel") String gameChannel, @Attribute(name = "pageNum") int pageNum, @Attribute(name = "pageCount") int pageCount, @Attribute(name = "appId") String appId);
	
	
	/**
	 *  获取游戏开始上报信息
	 *
	 * @param gameRoundId
	 *            游戏开始信息
	 * @param gameChannel
	 *            游戏所属渠道
	 * @param appId
	 *            所属appId
	 * @return 
	 *     //if rcode == 1 参数非法<br>
	 *     //if rcode == 2 失败<br>
	 *     //if rcode == 3 没有数据<br>
	 */
	@Service(domain = 4302, op = 14, request = RequestGetGameSettleReport.class, response = ResponseGetGameSettleReport.class)
	@Return(resultType = ResponseGetGameSettleReport.class)
	Result<ResponseGetGameSettleReport> getGameSettleReport(@Attribute(name = "gameRoundId") String gameRoundId, @Attribute(name = "gameChannel") String gameChannel, @Attribute(name = "appId") String appId);
	
	
	/**
	 *  上报游戏房间用户人数变更
	 *
	 * @param result
	 *            房间用户人数变更数据
	 * @return 
	 */
	@Service(domain = 4302, op = 15, request = RequestRoomUsersChanged.class)
	@Return(resultType = Void.class)
	Result<Void> roomUsersChanged(@Attribute(name = "result") RoomUsersChangedResult result);
	
	
	/**
	 *  模拟业务发送用户心跳（demo）
	 *
	 * @param result
	 *            房间用户人数变更数据
	 * @return 
	 */
	@Service(domain = 4302, op = 10, request = RequestSendUserHeart.class)
	@Return(resultType = Void.class)
	Result<Void> sendUserHeart(@Attribute(name = "result") RoomGameUserHeartBeat result);
	
	
	public static final int GET_ROOM_GAME_REPORT_LIST_ILLEGAL_PARAMS = 1; // 参数非法
	public static final int GET_ROOM_GAME_REPORT_LIST_FAIL = 2; // 失败
	public static final int GET_ROOM_GAME_REPORT_LIST_NO_DATA = 3; // 没有数据

	public static final int GET_GAME_SETTLE_REPORT_ILLEGAL_PARAMS = 1; // 参数非法
	public static final int GET_GAME_SETTLE_REPORT_FAIL = 2; // 失败
	public static final int GET_GAME_SETTLE_REPORT_NO_DATA = 3; // 没有数据


}