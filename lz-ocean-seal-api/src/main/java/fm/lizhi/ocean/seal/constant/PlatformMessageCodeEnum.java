package fm.lizhi.ocean.seal.constant;

/**
 * 平台错误码
 */
public enum PlatformMessageCodeEnum {

    SUCCESS(0, "成功"),
    TOKEN_CREATE_FAILED(1001, "Token创建失败"),
    TOKEN_VERIFY_FAILED(1002, "Token校验失败"),
    TOKEN_DECODE_FAILED(1003, "Token解析失败"),
    TOKEN_INVALID(1004, "Token非法"),
    TOKEN_EXPIRED(1005, "Token过期"),
    NOT_FOUND(9998, "未找到有效信息"),
    UNDEFINE(9999, "未知错误");

    private int code;

    private String msg;

    PlatformMessageCodeEnum(int code, String msg) {
        this.code = code;
        this.msg = msg;
    }

    public int getCode() {
        return code;
    }

    public String getMsg() {
        return msg;
    }
}
