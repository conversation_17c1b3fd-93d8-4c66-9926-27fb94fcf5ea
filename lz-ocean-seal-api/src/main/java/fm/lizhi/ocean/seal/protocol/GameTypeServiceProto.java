// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: protocol_game_type.proto

package fm.lizhi.ocean.seal.protocol;

public final class GameTypeServiceProto {
  private GameTypeServiceProto() {}
  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistry registry) {
  }
  public interface GameTypeOrBuilder
      extends com.google.protobuf.MessageOrBuilder {

    // optional int64 id = 1;
    /**
     * <code>optional int64 id = 1;</code>
     */
    boolean hasId();
    /**
     * <code>optional int64 id = 1;</code>
     */
    long getId();

    // optional string typeName = 2;
    /**
     * <code>optional string typeName = 2;</code>
     *
     * <pre>
     *名称:竞技游戏 纸牌游戏
     * </pre>
     */
    boolean hasTypeName();
    /**
     * <code>optional string typeName = 2;</code>
     *
     * <pre>
     *名称:竞技游戏 纸牌游戏
     * </pre>
     */
    java.lang.String getTypeName();
    /**
     * <code>optional string typeName = 2;</code>
     *
     * <pre>
     *名称:竞技游戏 纸牌游戏
     * </pre>
     */
    com.google.protobuf.ByteString
        getTypeNameBytes();
  }
  /**
   * Protobuf type {@code fm.lizhi.commons.template.datacenter.protocol.GameType}
   */
  public static final class GameType extends
      com.google.protobuf.GeneratedMessage
      implements GameTypeOrBuilder {
    // Use GameType.newBuilder() to construct.
    private GameType(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
      super(builder);
      this.unknownFields = builder.getUnknownFields();
    }
    private GameType(boolean noInit) { this.unknownFields = com.google.protobuf.UnknownFieldSet.getDefaultInstance(); }

    private static final GameType defaultInstance;
    public static GameType getDefaultInstance() {
      return defaultInstance;
    }

    public GameType getDefaultInstanceForType() {
      return defaultInstance;
    }

    private final com.google.protobuf.UnknownFieldSet unknownFields;
    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
        getUnknownFields() {
      return this.unknownFields;
    }
    private GameType(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      initFields();
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            default: {
              if (!parseUnknownField(input, unknownFields,
                                     extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
            case 8: {
              bitField0_ |= 0x00000001;
              id_ = input.readInt64();
              break;
            }
            case 18: {
              bitField0_ |= 0x00000002;
              typeName_ = input.readBytes();
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e.getMessage()).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return fm.lizhi.ocean.seal.protocol.GameTypeServiceProto.internal_static_fm_lizhi_commons_template_datacenter_protocol_GameType_descriptor;
    }

    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return fm.lizhi.ocean.seal.protocol.GameTypeServiceProto.internal_static_fm_lizhi_commons_template_datacenter_protocol_GameType_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              fm.lizhi.ocean.seal.protocol.GameTypeServiceProto.GameType.class, fm.lizhi.ocean.seal.protocol.GameTypeServiceProto.GameType.Builder.class);
    }

    public static com.google.protobuf.Parser<GameType> PARSER =
        new com.google.protobuf.AbstractParser<GameType>() {
      public GameType parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new GameType(input, extensionRegistry);
      }
    };

    @java.lang.Override
    public com.google.protobuf.Parser<GameType> getParserForType() {
      return PARSER;
    }

    private int bitField0_;
    // optional int64 id = 1;
    public static final int ID_FIELD_NUMBER = 1;
    private long id_;
    /**
     * <code>optional int64 id = 1;</code>
     */
    public boolean hasId() {
      return ((bitField0_ & 0x00000001) == 0x00000001);
    }
    /**
     * <code>optional int64 id = 1;</code>
     */
    public long getId() {
      return id_;
    }

    // optional string typeName = 2;
    public static final int TYPENAME_FIELD_NUMBER = 2;
    private java.lang.Object typeName_;
    /**
     * <code>optional string typeName = 2;</code>
     *
     * <pre>
     *名称:竞技游戏 纸牌游戏
     * </pre>
     */
    public boolean hasTypeName() {
      return ((bitField0_ & 0x00000002) == 0x00000002);
    }
    /**
     * <code>optional string typeName = 2;</code>
     *
     * <pre>
     *名称:竞技游戏 纸牌游戏
     * </pre>
     */
    public java.lang.String getTypeName() {
      java.lang.Object ref = typeName_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          typeName_ = s;
        }
        return s;
      }
    }
    /**
     * <code>optional string typeName = 2;</code>
     *
     * <pre>
     *名称:竞技游戏 纸牌游戏
     * </pre>
     */
    public com.google.protobuf.ByteString
        getTypeNameBytes() {
      java.lang.Object ref = typeName_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        typeName_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    private void initFields() {
      id_ = 0L;
      typeName_ = "";
    }
    private byte memoizedIsInitialized = -1;
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized != -1) return isInitialized == 1;

      memoizedIsInitialized = 1;
      return true;
    }

    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      getSerializedSize();
      if (((bitField0_ & 0x00000001) == 0x00000001)) {
        output.writeInt64(1, id_);
      }
      if (((bitField0_ & 0x00000002) == 0x00000002)) {
        output.writeBytes(2, getTypeNameBytes());
      }
      getUnknownFields().writeTo(output);
    }

    private int memoizedSerializedSize = -1;
    public int getSerializedSize() {
      int size = memoizedSerializedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) == 0x00000001)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt64Size(1, id_);
      }
      if (((bitField0_ & 0x00000002) == 0x00000002)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(2, getTypeNameBytes());
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSerializedSize = size;
      return size;
    }

    private static final long serialVersionUID = 0L;
    @java.lang.Override
    protected java.lang.Object writeReplace()
        throws java.io.ObjectStreamException {
      return super.writeReplace();
    }

    public static fm.lizhi.ocean.seal.protocol.GameTypeServiceProto.GameType parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static fm.lizhi.ocean.seal.protocol.GameTypeServiceProto.GameType parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static fm.lizhi.ocean.seal.protocol.GameTypeServiceProto.GameType parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static fm.lizhi.ocean.seal.protocol.GameTypeServiceProto.GameType parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static fm.lizhi.ocean.seal.protocol.GameTypeServiceProto.GameType parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return PARSER.parseFrom(input);
    }
    public static fm.lizhi.ocean.seal.protocol.GameTypeServiceProto.GameType parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseFrom(input, extensionRegistry);
    }
    public static fm.lizhi.ocean.seal.protocol.GameTypeServiceProto.GameType parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return PARSER.parseDelimitedFrom(input);
    }
    public static fm.lizhi.ocean.seal.protocol.GameTypeServiceProto.GameType parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseDelimitedFrom(input, extensionRegistry);
    }
    public static fm.lizhi.ocean.seal.protocol.GameTypeServiceProto.GameType parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return PARSER.parseFrom(input);
    }
    public static fm.lizhi.ocean.seal.protocol.GameTypeServiceProto.GameType parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseFrom(input, extensionRegistry);
    }

    public static Builder newBuilder() { return Builder.create(); }
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder(fm.lizhi.ocean.seal.protocol.GameTypeServiceProto.GameType prototype) {
      return newBuilder().mergeFrom(prototype);
    }
    public Builder toBuilder() { return newBuilder(this); }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code fm.lizhi.commons.template.datacenter.protocol.GameType}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessage.Builder<Builder>
       implements fm.lizhi.ocean.seal.protocol.GameTypeServiceProto.GameTypeOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return fm.lizhi.ocean.seal.protocol.GameTypeServiceProto.internal_static_fm_lizhi_commons_template_datacenter_protocol_GameType_descriptor;
      }

      protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return fm.lizhi.ocean.seal.protocol.GameTypeServiceProto.internal_static_fm_lizhi_commons_template_datacenter_protocol_GameType_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                fm.lizhi.ocean.seal.protocol.GameTypeServiceProto.GameType.class, fm.lizhi.ocean.seal.protocol.GameTypeServiceProto.GameType.Builder.class);
      }

      // Construct using fm.lizhi.ocean.seal.protocol.GameTypeServiceProto.GameType.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessage.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessage.alwaysUseFieldBuilders) {
        }
      }
      private static Builder create() {
        return new Builder();
      }

      public Builder clear() {
        super.clear();
        id_ = 0L;
        bitField0_ = (bitField0_ & ~0x00000001);
        typeName_ = "";
        bitField0_ = (bitField0_ & ~0x00000002);
        return this;
      }

      public Builder clone() {
        return create().mergeFrom(buildPartial());
      }

      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return fm.lizhi.ocean.seal.protocol.GameTypeServiceProto.internal_static_fm_lizhi_commons_template_datacenter_protocol_GameType_descriptor;
      }

      public fm.lizhi.ocean.seal.protocol.GameTypeServiceProto.GameType getDefaultInstanceForType() {
        return fm.lizhi.ocean.seal.protocol.GameTypeServiceProto.GameType.getDefaultInstance();
      }

      public fm.lizhi.ocean.seal.protocol.GameTypeServiceProto.GameType build() {
        fm.lizhi.ocean.seal.protocol.GameTypeServiceProto.GameType result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      public fm.lizhi.ocean.seal.protocol.GameTypeServiceProto.GameType buildPartial() {
        fm.lizhi.ocean.seal.protocol.GameTypeServiceProto.GameType result = new fm.lizhi.ocean.seal.protocol.GameTypeServiceProto.GameType(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) == 0x00000001)) {
          to_bitField0_ |= 0x00000001;
        }
        result.id_ = id_;
        if (((from_bitField0_ & 0x00000002) == 0x00000002)) {
          to_bitField0_ |= 0x00000002;
        }
        result.typeName_ = typeName_;
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof fm.lizhi.ocean.seal.protocol.GameTypeServiceProto.GameType) {
          return mergeFrom((fm.lizhi.ocean.seal.protocol.GameTypeServiceProto.GameType)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(fm.lizhi.ocean.seal.protocol.GameTypeServiceProto.GameType other) {
        if (other == fm.lizhi.ocean.seal.protocol.GameTypeServiceProto.GameType.getDefaultInstance()) return this;
        if (other.hasId()) {
          setId(other.getId());
        }
        if (other.hasTypeName()) {
          bitField0_ |= 0x00000002;
          typeName_ = other.typeName_;
          onChanged();
        }
        this.mergeUnknownFields(other.getUnknownFields());
        return this;
      }

      public final boolean isInitialized() {
        return true;
      }

      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        fm.lizhi.ocean.seal.protocol.GameTypeServiceProto.GameType parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (fm.lizhi.ocean.seal.protocol.GameTypeServiceProto.GameType) e.getUnfinishedMessage();
          throw e;
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      // optional int64 id = 1;
      private long id_ ;
      /**
       * <code>optional int64 id = 1;</code>
       */
      public boolean hasId() {
        return ((bitField0_ & 0x00000001) == 0x00000001);
      }
      /**
       * <code>optional int64 id = 1;</code>
       */
      public long getId() {
        return id_;
      }
      /**
       * <code>optional int64 id = 1;</code>
       */
      public Builder setId(long value) {
        bitField0_ |= 0x00000001;
        id_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional int64 id = 1;</code>
       */
      public Builder clearId() {
        bitField0_ = (bitField0_ & ~0x00000001);
        id_ = 0L;
        onChanged();
        return this;
      }

      // optional string typeName = 2;
      private java.lang.Object typeName_ = "";
      /**
       * <code>optional string typeName = 2;</code>
       *
       * <pre>
       *名称:竞技游戏 纸牌游戏
       * </pre>
       */
      public boolean hasTypeName() {
        return ((bitField0_ & 0x00000002) == 0x00000002);
      }
      /**
       * <code>optional string typeName = 2;</code>
       *
       * <pre>
       *名称:竞技游戏 纸牌游戏
       * </pre>
       */
      public java.lang.String getTypeName() {
        java.lang.Object ref = typeName_;
        if (!(ref instanceof java.lang.String)) {
          java.lang.String s = ((com.google.protobuf.ByteString) ref)
              .toStringUtf8();
          typeName_ = s;
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <code>optional string typeName = 2;</code>
       *
       * <pre>
       *名称:竞技游戏 纸牌游戏
       * </pre>
       */
      public com.google.protobuf.ByteString
          getTypeNameBytes() {
        java.lang.Object ref = typeName_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          typeName_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <code>optional string typeName = 2;</code>
       *
       * <pre>
       *名称:竞技游戏 纸牌游戏
       * </pre>
       */
      public Builder setTypeName(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000002;
        typeName_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional string typeName = 2;</code>
       *
       * <pre>
       *名称:竞技游戏 纸牌游戏
       * </pre>
       */
      public Builder clearTypeName() {
        bitField0_ = (bitField0_ & ~0x00000002);
        typeName_ = getDefaultInstance().getTypeName();
        onChanged();
        return this;
      }
      /**
       * <code>optional string typeName = 2;</code>
       *
       * <pre>
       *名称:竞技游戏 纸牌游戏
       * </pre>
       */
      public Builder setTypeNameBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000002;
        typeName_ = value;
        onChanged();
        return this;
      }

      // @@protoc_insertion_point(builder_scope:fm.lizhi.commons.template.datacenter.protocol.GameType)
    }

    static {
      defaultInstance = new GameType(true);
      defaultInstance.initFields();
    }

    // @@protoc_insertion_point(class_scope:fm.lizhi.commons.template.datacenter.protocol.GameType)
  }

  public interface RequestGetAllTypesOrBuilder
      extends com.google.protobuf.MessageOrBuilder {
  }
  /**
   * Protobuf type {@code fm.lizhi.commons.template.datacenter.protocol.RequestGetAllTypes}
   *
   * <pre>
   * GameTypeService.java
   * 获取游戏类型列表
   * domain = 4302, op = 210
   * </pre>
   */
  public static final class RequestGetAllTypes extends
      com.google.protobuf.GeneratedMessage
      implements RequestGetAllTypesOrBuilder {
    // Use RequestGetAllTypes.newBuilder() to construct.
    private RequestGetAllTypes(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
      super(builder);
      this.unknownFields = builder.getUnknownFields();
    }
    private RequestGetAllTypes(boolean noInit) { this.unknownFields = com.google.protobuf.UnknownFieldSet.getDefaultInstance(); }

    private static final RequestGetAllTypes defaultInstance;
    public static RequestGetAllTypes getDefaultInstance() {
      return defaultInstance;
    }

    public RequestGetAllTypes getDefaultInstanceForType() {
      return defaultInstance;
    }

    private final com.google.protobuf.UnknownFieldSet unknownFields;
    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
        getUnknownFields() {
      return this.unknownFields;
    }
    private RequestGetAllTypes(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      initFields();
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            default: {
              if (!parseUnknownField(input, unknownFields,
                                     extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e.getMessage()).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return fm.lizhi.ocean.seal.protocol.GameTypeServiceProto.internal_static_fm_lizhi_commons_template_datacenter_protocol_RequestGetAllTypes_descriptor;
    }

    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return fm.lizhi.ocean.seal.protocol.GameTypeServiceProto.internal_static_fm_lizhi_commons_template_datacenter_protocol_RequestGetAllTypes_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              fm.lizhi.ocean.seal.protocol.GameTypeServiceProto.RequestGetAllTypes.class, fm.lizhi.ocean.seal.protocol.GameTypeServiceProto.RequestGetAllTypes.Builder.class);
    }

    public static com.google.protobuf.Parser<RequestGetAllTypes> PARSER =
        new com.google.protobuf.AbstractParser<RequestGetAllTypes>() {
      public RequestGetAllTypes parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new RequestGetAllTypes(input, extensionRegistry);
      }
    };

    @java.lang.Override
    public com.google.protobuf.Parser<RequestGetAllTypes> getParserForType() {
      return PARSER;
    }

    private void initFields() {
    }
    private byte memoizedIsInitialized = -1;
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized != -1) return isInitialized == 1;

      memoizedIsInitialized = 1;
      return true;
    }

    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      getSerializedSize();
      getUnknownFields().writeTo(output);
    }

    private int memoizedSerializedSize = -1;
    public int getSerializedSize() {
      int size = memoizedSerializedSize;
      if (size != -1) return size;

      size = 0;
      size += getUnknownFields().getSerializedSize();
      memoizedSerializedSize = size;
      return size;
    }

    private static final long serialVersionUID = 0L;
    @java.lang.Override
    protected java.lang.Object writeReplace()
        throws java.io.ObjectStreamException {
      return super.writeReplace();
    }

    public static fm.lizhi.ocean.seal.protocol.GameTypeServiceProto.RequestGetAllTypes parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static fm.lizhi.ocean.seal.protocol.GameTypeServiceProto.RequestGetAllTypes parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static fm.lizhi.ocean.seal.protocol.GameTypeServiceProto.RequestGetAllTypes parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static fm.lizhi.ocean.seal.protocol.GameTypeServiceProto.RequestGetAllTypes parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static fm.lizhi.ocean.seal.protocol.GameTypeServiceProto.RequestGetAllTypes parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return PARSER.parseFrom(input);
    }
    public static fm.lizhi.ocean.seal.protocol.GameTypeServiceProto.RequestGetAllTypes parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseFrom(input, extensionRegistry);
    }
    public static fm.lizhi.ocean.seal.protocol.GameTypeServiceProto.RequestGetAllTypes parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return PARSER.parseDelimitedFrom(input);
    }
    public static fm.lizhi.ocean.seal.protocol.GameTypeServiceProto.RequestGetAllTypes parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseDelimitedFrom(input, extensionRegistry);
    }
    public static fm.lizhi.ocean.seal.protocol.GameTypeServiceProto.RequestGetAllTypes parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return PARSER.parseFrom(input);
    }
    public static fm.lizhi.ocean.seal.protocol.GameTypeServiceProto.RequestGetAllTypes parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseFrom(input, extensionRegistry);
    }

    public static Builder newBuilder() { return Builder.create(); }
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder(fm.lizhi.ocean.seal.protocol.GameTypeServiceProto.RequestGetAllTypes prototype) {
      return newBuilder().mergeFrom(prototype);
    }
    public Builder toBuilder() { return newBuilder(this); }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code fm.lizhi.commons.template.datacenter.protocol.RequestGetAllTypes}
     *
     * <pre>
     * GameTypeService.java
     * 获取游戏类型列表
     * domain = 4302, op = 210
     * </pre>
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessage.Builder<Builder>
       implements fm.lizhi.ocean.seal.protocol.GameTypeServiceProto.RequestGetAllTypesOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return fm.lizhi.ocean.seal.protocol.GameTypeServiceProto.internal_static_fm_lizhi_commons_template_datacenter_protocol_RequestGetAllTypes_descriptor;
      }

      protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return fm.lizhi.ocean.seal.protocol.GameTypeServiceProto.internal_static_fm_lizhi_commons_template_datacenter_protocol_RequestGetAllTypes_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                fm.lizhi.ocean.seal.protocol.GameTypeServiceProto.RequestGetAllTypes.class, fm.lizhi.ocean.seal.protocol.GameTypeServiceProto.RequestGetAllTypes.Builder.class);
      }

      // Construct using fm.lizhi.ocean.seal.protocol.GameTypeServiceProto.RequestGetAllTypes.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessage.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessage.alwaysUseFieldBuilders) {
        }
      }
      private static Builder create() {
        return new Builder();
      }

      public Builder clear() {
        super.clear();
        return this;
      }

      public Builder clone() {
        return create().mergeFrom(buildPartial());
      }

      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return fm.lizhi.ocean.seal.protocol.GameTypeServiceProto.internal_static_fm_lizhi_commons_template_datacenter_protocol_RequestGetAllTypes_descriptor;
      }

      public fm.lizhi.ocean.seal.protocol.GameTypeServiceProto.RequestGetAllTypes getDefaultInstanceForType() {
        return fm.lizhi.ocean.seal.protocol.GameTypeServiceProto.RequestGetAllTypes.getDefaultInstance();
      }

      public fm.lizhi.ocean.seal.protocol.GameTypeServiceProto.RequestGetAllTypes build() {
        fm.lizhi.ocean.seal.protocol.GameTypeServiceProto.RequestGetAllTypes result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      public fm.lizhi.ocean.seal.protocol.GameTypeServiceProto.RequestGetAllTypes buildPartial() {
        fm.lizhi.ocean.seal.protocol.GameTypeServiceProto.RequestGetAllTypes result = new fm.lizhi.ocean.seal.protocol.GameTypeServiceProto.RequestGetAllTypes(this);
        onBuilt();
        return result;
      }

      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof fm.lizhi.ocean.seal.protocol.GameTypeServiceProto.RequestGetAllTypes) {
          return mergeFrom((fm.lizhi.ocean.seal.protocol.GameTypeServiceProto.RequestGetAllTypes)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(fm.lizhi.ocean.seal.protocol.GameTypeServiceProto.RequestGetAllTypes other) {
        if (other == fm.lizhi.ocean.seal.protocol.GameTypeServiceProto.RequestGetAllTypes.getDefaultInstance()) return this;
        this.mergeUnknownFields(other.getUnknownFields());
        return this;
      }

      public final boolean isInitialized() {
        return true;
      }

      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        fm.lizhi.ocean.seal.protocol.GameTypeServiceProto.RequestGetAllTypes parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (fm.lizhi.ocean.seal.protocol.GameTypeServiceProto.RequestGetAllTypes) e.getUnfinishedMessage();
          throw e;
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }

      // @@protoc_insertion_point(builder_scope:fm.lizhi.commons.template.datacenter.protocol.RequestGetAllTypes)
    }

    static {
      defaultInstance = new RequestGetAllTypes(true);
      defaultInstance.initFields();
    }

    // @@protoc_insertion_point(class_scope:fm.lizhi.commons.template.datacenter.protocol.RequestGetAllTypes)
  }

  public interface ResponseGetAllTypesOrBuilder
      extends com.google.protobuf.MessageOrBuilder {

    // repeated .fm.lizhi.commons.template.datacenter.protocol.GameType gameTypes = 1;
    /**
     * <code>repeated .fm.lizhi.commons.template.datacenter.protocol.GameType gameTypes = 1;</code>
     */
    java.util.List<fm.lizhi.ocean.seal.protocol.GameTypeServiceProto.GameType> 
        getGameTypesList();
    /**
     * <code>repeated .fm.lizhi.commons.template.datacenter.protocol.GameType gameTypes = 1;</code>
     */
    fm.lizhi.ocean.seal.protocol.GameTypeServiceProto.GameType getGameTypes(int index);
    /**
     * <code>repeated .fm.lizhi.commons.template.datacenter.protocol.GameType gameTypes = 1;</code>
     */
    int getGameTypesCount();
    /**
     * <code>repeated .fm.lizhi.commons.template.datacenter.protocol.GameType gameTypes = 1;</code>
     */
    java.util.List<? extends fm.lizhi.ocean.seal.protocol.GameTypeServiceProto.GameTypeOrBuilder> 
        getGameTypesOrBuilderList();
    /**
     * <code>repeated .fm.lizhi.commons.template.datacenter.protocol.GameType gameTypes = 1;</code>
     */
    fm.lizhi.ocean.seal.protocol.GameTypeServiceProto.GameTypeOrBuilder getGameTypesOrBuilder(
        int index);
  }
  /**
   * Protobuf type {@code fm.lizhi.commons.template.datacenter.protocol.ResponseGetAllTypes}
   *
   * <pre>
   * rcode == 0(SUCCESS) = 执行成功
   * rcode == 1(FAIL) = 错误
   * </pre>
   */
  public static final class ResponseGetAllTypes extends
      com.google.protobuf.GeneratedMessage
      implements ResponseGetAllTypesOrBuilder {
    // Use ResponseGetAllTypes.newBuilder() to construct.
    private ResponseGetAllTypes(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
      super(builder);
      this.unknownFields = builder.getUnknownFields();
    }
    private ResponseGetAllTypes(boolean noInit) { this.unknownFields = com.google.protobuf.UnknownFieldSet.getDefaultInstance(); }

    private static final ResponseGetAllTypes defaultInstance;
    public static ResponseGetAllTypes getDefaultInstance() {
      return defaultInstance;
    }

    public ResponseGetAllTypes getDefaultInstanceForType() {
      return defaultInstance;
    }

    private final com.google.protobuf.UnknownFieldSet unknownFields;
    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
        getUnknownFields() {
      return this.unknownFields;
    }
    private ResponseGetAllTypes(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      initFields();
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            default: {
              if (!parseUnknownField(input, unknownFields,
                                     extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
            case 10: {
              if (!((mutable_bitField0_ & 0x00000001) == 0x00000001)) {
                gameTypes_ = new java.util.ArrayList<fm.lizhi.ocean.seal.protocol.GameTypeServiceProto.GameType>();
                mutable_bitField0_ |= 0x00000001;
              }
              gameTypes_.add(input.readMessage(fm.lizhi.ocean.seal.protocol.GameTypeServiceProto.GameType.PARSER, extensionRegistry));
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e.getMessage()).setUnfinishedMessage(this);
      } finally {
        if (((mutable_bitField0_ & 0x00000001) == 0x00000001)) {
          gameTypes_ = java.util.Collections.unmodifiableList(gameTypes_);
        }
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return fm.lizhi.ocean.seal.protocol.GameTypeServiceProto.internal_static_fm_lizhi_commons_template_datacenter_protocol_ResponseGetAllTypes_descriptor;
    }

    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return fm.lizhi.ocean.seal.protocol.GameTypeServiceProto.internal_static_fm_lizhi_commons_template_datacenter_protocol_ResponseGetAllTypes_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              fm.lizhi.ocean.seal.protocol.GameTypeServiceProto.ResponseGetAllTypes.class, fm.lizhi.ocean.seal.protocol.GameTypeServiceProto.ResponseGetAllTypes.Builder.class);
    }

    public static com.google.protobuf.Parser<ResponseGetAllTypes> PARSER =
        new com.google.protobuf.AbstractParser<ResponseGetAllTypes>() {
      public ResponseGetAllTypes parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new ResponseGetAllTypes(input, extensionRegistry);
      }
    };

    @java.lang.Override
    public com.google.protobuf.Parser<ResponseGetAllTypes> getParserForType() {
      return PARSER;
    }

    // repeated .fm.lizhi.commons.template.datacenter.protocol.GameType gameTypes = 1;
    public static final int GAMETYPES_FIELD_NUMBER = 1;
    private java.util.List<fm.lizhi.ocean.seal.protocol.GameTypeServiceProto.GameType> gameTypes_;
    /**
     * <code>repeated .fm.lizhi.commons.template.datacenter.protocol.GameType gameTypes = 1;</code>
     */
    public java.util.List<fm.lizhi.ocean.seal.protocol.GameTypeServiceProto.GameType> getGameTypesList() {
      return gameTypes_;
    }
    /**
     * <code>repeated .fm.lizhi.commons.template.datacenter.protocol.GameType gameTypes = 1;</code>
     */
    public java.util.List<? extends fm.lizhi.ocean.seal.protocol.GameTypeServiceProto.GameTypeOrBuilder> 
        getGameTypesOrBuilderList() {
      return gameTypes_;
    }
    /**
     * <code>repeated .fm.lizhi.commons.template.datacenter.protocol.GameType gameTypes = 1;</code>
     */
    public int getGameTypesCount() {
      return gameTypes_.size();
    }
    /**
     * <code>repeated .fm.lizhi.commons.template.datacenter.protocol.GameType gameTypes = 1;</code>
     */
    public fm.lizhi.ocean.seal.protocol.GameTypeServiceProto.GameType getGameTypes(int index) {
      return gameTypes_.get(index);
    }
    /**
     * <code>repeated .fm.lizhi.commons.template.datacenter.protocol.GameType gameTypes = 1;</code>
     */
    public fm.lizhi.ocean.seal.protocol.GameTypeServiceProto.GameTypeOrBuilder getGameTypesOrBuilder(
        int index) {
      return gameTypes_.get(index);
    }

    private void initFields() {
      gameTypes_ = java.util.Collections.emptyList();
    }
    private byte memoizedIsInitialized = -1;
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized != -1) return isInitialized == 1;

      memoizedIsInitialized = 1;
      return true;
    }

    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      getSerializedSize();
      for (int i = 0; i < gameTypes_.size(); i++) {
        output.writeMessage(1, gameTypes_.get(i));
      }
      getUnknownFields().writeTo(output);
    }

    private int memoizedSerializedSize = -1;
    public int getSerializedSize() {
      int size = memoizedSerializedSize;
      if (size != -1) return size;

      size = 0;
      for (int i = 0; i < gameTypes_.size(); i++) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(1, gameTypes_.get(i));
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSerializedSize = size;
      return size;
    }

    private static final long serialVersionUID = 0L;
    @java.lang.Override
    protected java.lang.Object writeReplace()
        throws java.io.ObjectStreamException {
      return super.writeReplace();
    }

    public static fm.lizhi.ocean.seal.protocol.GameTypeServiceProto.ResponseGetAllTypes parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static fm.lizhi.ocean.seal.protocol.GameTypeServiceProto.ResponseGetAllTypes parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static fm.lizhi.ocean.seal.protocol.GameTypeServiceProto.ResponseGetAllTypes parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static fm.lizhi.ocean.seal.protocol.GameTypeServiceProto.ResponseGetAllTypes parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static fm.lizhi.ocean.seal.protocol.GameTypeServiceProto.ResponseGetAllTypes parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return PARSER.parseFrom(input);
    }
    public static fm.lizhi.ocean.seal.protocol.GameTypeServiceProto.ResponseGetAllTypes parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseFrom(input, extensionRegistry);
    }
    public static fm.lizhi.ocean.seal.protocol.GameTypeServiceProto.ResponseGetAllTypes parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return PARSER.parseDelimitedFrom(input);
    }
    public static fm.lizhi.ocean.seal.protocol.GameTypeServiceProto.ResponseGetAllTypes parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseDelimitedFrom(input, extensionRegistry);
    }
    public static fm.lizhi.ocean.seal.protocol.GameTypeServiceProto.ResponseGetAllTypes parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return PARSER.parseFrom(input);
    }
    public static fm.lizhi.ocean.seal.protocol.GameTypeServiceProto.ResponseGetAllTypes parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseFrom(input, extensionRegistry);
    }

    public static Builder newBuilder() { return Builder.create(); }
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder(fm.lizhi.ocean.seal.protocol.GameTypeServiceProto.ResponseGetAllTypes prototype) {
      return newBuilder().mergeFrom(prototype);
    }
    public Builder toBuilder() { return newBuilder(this); }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code fm.lizhi.commons.template.datacenter.protocol.ResponseGetAllTypes}
     *
     * <pre>
     * rcode == 0(SUCCESS) = 执行成功
     * rcode == 1(FAIL) = 错误
     * </pre>
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessage.Builder<Builder>
       implements fm.lizhi.ocean.seal.protocol.GameTypeServiceProto.ResponseGetAllTypesOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return fm.lizhi.ocean.seal.protocol.GameTypeServiceProto.internal_static_fm_lizhi_commons_template_datacenter_protocol_ResponseGetAllTypes_descriptor;
      }

      protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return fm.lizhi.ocean.seal.protocol.GameTypeServiceProto.internal_static_fm_lizhi_commons_template_datacenter_protocol_ResponseGetAllTypes_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                fm.lizhi.ocean.seal.protocol.GameTypeServiceProto.ResponseGetAllTypes.class, fm.lizhi.ocean.seal.protocol.GameTypeServiceProto.ResponseGetAllTypes.Builder.class);
      }

      // Construct using fm.lizhi.ocean.seal.protocol.GameTypeServiceProto.ResponseGetAllTypes.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessage.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessage.alwaysUseFieldBuilders) {
          getGameTypesFieldBuilder();
        }
      }
      private static Builder create() {
        return new Builder();
      }

      public Builder clear() {
        super.clear();
        if (gameTypesBuilder_ == null) {
          gameTypes_ = java.util.Collections.emptyList();
          bitField0_ = (bitField0_ & ~0x00000001);
        } else {
          gameTypesBuilder_.clear();
        }
        return this;
      }

      public Builder clone() {
        return create().mergeFrom(buildPartial());
      }

      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return fm.lizhi.ocean.seal.protocol.GameTypeServiceProto.internal_static_fm_lizhi_commons_template_datacenter_protocol_ResponseGetAllTypes_descriptor;
      }

      public fm.lizhi.ocean.seal.protocol.GameTypeServiceProto.ResponseGetAllTypes getDefaultInstanceForType() {
        return fm.lizhi.ocean.seal.protocol.GameTypeServiceProto.ResponseGetAllTypes.getDefaultInstance();
      }

      public fm.lizhi.ocean.seal.protocol.GameTypeServiceProto.ResponseGetAllTypes build() {
        fm.lizhi.ocean.seal.protocol.GameTypeServiceProto.ResponseGetAllTypes result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      public fm.lizhi.ocean.seal.protocol.GameTypeServiceProto.ResponseGetAllTypes buildPartial() {
        fm.lizhi.ocean.seal.protocol.GameTypeServiceProto.ResponseGetAllTypes result = new fm.lizhi.ocean.seal.protocol.GameTypeServiceProto.ResponseGetAllTypes(this);
        int from_bitField0_ = bitField0_;
        if (gameTypesBuilder_ == null) {
          if (((bitField0_ & 0x00000001) == 0x00000001)) {
            gameTypes_ = java.util.Collections.unmodifiableList(gameTypes_);
            bitField0_ = (bitField0_ & ~0x00000001);
          }
          result.gameTypes_ = gameTypes_;
        } else {
          result.gameTypes_ = gameTypesBuilder_.build();
        }
        onBuilt();
        return result;
      }

      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof fm.lizhi.ocean.seal.protocol.GameTypeServiceProto.ResponseGetAllTypes) {
          return mergeFrom((fm.lizhi.ocean.seal.protocol.GameTypeServiceProto.ResponseGetAllTypes)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(fm.lizhi.ocean.seal.protocol.GameTypeServiceProto.ResponseGetAllTypes other) {
        if (other == fm.lizhi.ocean.seal.protocol.GameTypeServiceProto.ResponseGetAllTypes.getDefaultInstance()) return this;
        if (gameTypesBuilder_ == null) {
          if (!other.gameTypes_.isEmpty()) {
            if (gameTypes_.isEmpty()) {
              gameTypes_ = other.gameTypes_;
              bitField0_ = (bitField0_ & ~0x00000001);
            } else {
              ensureGameTypesIsMutable();
              gameTypes_.addAll(other.gameTypes_);
            }
            onChanged();
          }
        } else {
          if (!other.gameTypes_.isEmpty()) {
            if (gameTypesBuilder_.isEmpty()) {
              gameTypesBuilder_.dispose();
              gameTypesBuilder_ = null;
              gameTypes_ = other.gameTypes_;
              bitField0_ = (bitField0_ & ~0x00000001);
              gameTypesBuilder_ = 
                com.google.protobuf.GeneratedMessage.alwaysUseFieldBuilders ?
                   getGameTypesFieldBuilder() : null;
            } else {
              gameTypesBuilder_.addAllMessages(other.gameTypes_);
            }
          }
        }
        this.mergeUnknownFields(other.getUnknownFields());
        return this;
      }

      public final boolean isInitialized() {
        return true;
      }

      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        fm.lizhi.ocean.seal.protocol.GameTypeServiceProto.ResponseGetAllTypes parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (fm.lizhi.ocean.seal.protocol.GameTypeServiceProto.ResponseGetAllTypes) e.getUnfinishedMessage();
          throw e;
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      // repeated .fm.lizhi.commons.template.datacenter.protocol.GameType gameTypes = 1;
      private java.util.List<fm.lizhi.ocean.seal.protocol.GameTypeServiceProto.GameType> gameTypes_ =
        java.util.Collections.emptyList();
      private void ensureGameTypesIsMutable() {
        if (!((bitField0_ & 0x00000001) == 0x00000001)) {
          gameTypes_ = new java.util.ArrayList<fm.lizhi.ocean.seal.protocol.GameTypeServiceProto.GameType>(gameTypes_);
          bitField0_ |= 0x00000001;
         }
      }

      private com.google.protobuf.RepeatedFieldBuilder<
          fm.lizhi.ocean.seal.protocol.GameTypeServiceProto.GameType, fm.lizhi.ocean.seal.protocol.GameTypeServiceProto.GameType.Builder, fm.lizhi.ocean.seal.protocol.GameTypeServiceProto.GameTypeOrBuilder> gameTypesBuilder_;

      /**
       * <code>repeated .fm.lizhi.commons.template.datacenter.protocol.GameType gameTypes = 1;</code>
       */
      public java.util.List<fm.lizhi.ocean.seal.protocol.GameTypeServiceProto.GameType> getGameTypesList() {
        if (gameTypesBuilder_ == null) {
          return java.util.Collections.unmodifiableList(gameTypes_);
        } else {
          return gameTypesBuilder_.getMessageList();
        }
      }
      /**
       * <code>repeated .fm.lizhi.commons.template.datacenter.protocol.GameType gameTypes = 1;</code>
       */
      public int getGameTypesCount() {
        if (gameTypesBuilder_ == null) {
          return gameTypes_.size();
        } else {
          return gameTypesBuilder_.getCount();
        }
      }
      /**
       * <code>repeated .fm.lizhi.commons.template.datacenter.protocol.GameType gameTypes = 1;</code>
       */
      public fm.lizhi.ocean.seal.protocol.GameTypeServiceProto.GameType getGameTypes(int index) {
        if (gameTypesBuilder_ == null) {
          return gameTypes_.get(index);
        } else {
          return gameTypesBuilder_.getMessage(index);
        }
      }
      /**
       * <code>repeated .fm.lizhi.commons.template.datacenter.protocol.GameType gameTypes = 1;</code>
       */
      public Builder setGameTypes(
          int index, fm.lizhi.ocean.seal.protocol.GameTypeServiceProto.GameType value) {
        if (gameTypesBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureGameTypesIsMutable();
          gameTypes_.set(index, value);
          onChanged();
        } else {
          gameTypesBuilder_.setMessage(index, value);
        }
        return this;
      }
      /**
       * <code>repeated .fm.lizhi.commons.template.datacenter.protocol.GameType gameTypes = 1;</code>
       */
      public Builder setGameTypes(
          int index, fm.lizhi.ocean.seal.protocol.GameTypeServiceProto.GameType.Builder builderForValue) {
        if (gameTypesBuilder_ == null) {
          ensureGameTypesIsMutable();
          gameTypes_.set(index, builderForValue.build());
          onChanged();
        } else {
          gameTypesBuilder_.setMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .fm.lizhi.commons.template.datacenter.protocol.GameType gameTypes = 1;</code>
       */
      public Builder addGameTypes(fm.lizhi.ocean.seal.protocol.GameTypeServiceProto.GameType value) {
        if (gameTypesBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureGameTypesIsMutable();
          gameTypes_.add(value);
          onChanged();
        } else {
          gameTypesBuilder_.addMessage(value);
        }
        return this;
      }
      /**
       * <code>repeated .fm.lizhi.commons.template.datacenter.protocol.GameType gameTypes = 1;</code>
       */
      public Builder addGameTypes(
          int index, fm.lizhi.ocean.seal.protocol.GameTypeServiceProto.GameType value) {
        if (gameTypesBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureGameTypesIsMutable();
          gameTypes_.add(index, value);
          onChanged();
        } else {
          gameTypesBuilder_.addMessage(index, value);
        }
        return this;
      }
      /**
       * <code>repeated .fm.lizhi.commons.template.datacenter.protocol.GameType gameTypes = 1;</code>
       */
      public Builder addGameTypes(
          fm.lizhi.ocean.seal.protocol.GameTypeServiceProto.GameType.Builder builderForValue) {
        if (gameTypesBuilder_ == null) {
          ensureGameTypesIsMutable();
          gameTypes_.add(builderForValue.build());
          onChanged();
        } else {
          gameTypesBuilder_.addMessage(builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .fm.lizhi.commons.template.datacenter.protocol.GameType gameTypes = 1;</code>
       */
      public Builder addGameTypes(
          int index, fm.lizhi.ocean.seal.protocol.GameTypeServiceProto.GameType.Builder builderForValue) {
        if (gameTypesBuilder_ == null) {
          ensureGameTypesIsMutable();
          gameTypes_.add(index, builderForValue.build());
          onChanged();
        } else {
          gameTypesBuilder_.addMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .fm.lizhi.commons.template.datacenter.protocol.GameType gameTypes = 1;</code>
       */
      public Builder addAllGameTypes(
          java.lang.Iterable<? extends fm.lizhi.ocean.seal.protocol.GameTypeServiceProto.GameType> values) {
        if (gameTypesBuilder_ == null) {
          ensureGameTypesIsMutable();
          super.addAll(values, gameTypes_);
          onChanged();
        } else {
          gameTypesBuilder_.addAllMessages(values);
        }
        return this;
      }
      /**
       * <code>repeated .fm.lizhi.commons.template.datacenter.protocol.GameType gameTypes = 1;</code>
       */
      public Builder clearGameTypes() {
        if (gameTypesBuilder_ == null) {
          gameTypes_ = java.util.Collections.emptyList();
          bitField0_ = (bitField0_ & ~0x00000001);
          onChanged();
        } else {
          gameTypesBuilder_.clear();
        }
        return this;
      }
      /**
       * <code>repeated .fm.lizhi.commons.template.datacenter.protocol.GameType gameTypes = 1;</code>
       */
      public Builder removeGameTypes(int index) {
        if (gameTypesBuilder_ == null) {
          ensureGameTypesIsMutable();
          gameTypes_.remove(index);
          onChanged();
        } else {
          gameTypesBuilder_.remove(index);
        }
        return this;
      }
      /**
       * <code>repeated .fm.lizhi.commons.template.datacenter.protocol.GameType gameTypes = 1;</code>
       */
      public fm.lizhi.ocean.seal.protocol.GameTypeServiceProto.GameType.Builder getGameTypesBuilder(
          int index) {
        return getGameTypesFieldBuilder().getBuilder(index);
      }
      /**
       * <code>repeated .fm.lizhi.commons.template.datacenter.protocol.GameType gameTypes = 1;</code>
       */
      public fm.lizhi.ocean.seal.protocol.GameTypeServiceProto.GameTypeOrBuilder getGameTypesOrBuilder(
          int index) {
        if (gameTypesBuilder_ == null) {
          return gameTypes_.get(index);  } else {
          return gameTypesBuilder_.getMessageOrBuilder(index);
        }
      }
      /**
       * <code>repeated .fm.lizhi.commons.template.datacenter.protocol.GameType gameTypes = 1;</code>
       */
      public java.util.List<? extends fm.lizhi.ocean.seal.protocol.GameTypeServiceProto.GameTypeOrBuilder> 
           getGameTypesOrBuilderList() {
        if (gameTypesBuilder_ != null) {
          return gameTypesBuilder_.getMessageOrBuilderList();
        } else {
          return java.util.Collections.unmodifiableList(gameTypes_);
        }
      }
      /**
       * <code>repeated .fm.lizhi.commons.template.datacenter.protocol.GameType gameTypes = 1;</code>
       */
      public fm.lizhi.ocean.seal.protocol.GameTypeServiceProto.GameType.Builder addGameTypesBuilder() {
        return getGameTypesFieldBuilder().addBuilder(
            fm.lizhi.ocean.seal.protocol.GameTypeServiceProto.GameType.getDefaultInstance());
      }
      /**
       * <code>repeated .fm.lizhi.commons.template.datacenter.protocol.GameType gameTypes = 1;</code>
       */
      public fm.lizhi.ocean.seal.protocol.GameTypeServiceProto.GameType.Builder addGameTypesBuilder(
          int index) {
        return getGameTypesFieldBuilder().addBuilder(
            index, fm.lizhi.ocean.seal.protocol.GameTypeServiceProto.GameType.getDefaultInstance());
      }
      /**
       * <code>repeated .fm.lizhi.commons.template.datacenter.protocol.GameType gameTypes = 1;</code>
       */
      public java.util.List<fm.lizhi.ocean.seal.protocol.GameTypeServiceProto.GameType.Builder> 
           getGameTypesBuilderList() {
        return getGameTypesFieldBuilder().getBuilderList();
      }
      private com.google.protobuf.RepeatedFieldBuilder<
          fm.lizhi.ocean.seal.protocol.GameTypeServiceProto.GameType, fm.lizhi.ocean.seal.protocol.GameTypeServiceProto.GameType.Builder, fm.lizhi.ocean.seal.protocol.GameTypeServiceProto.GameTypeOrBuilder> 
          getGameTypesFieldBuilder() {
        if (gameTypesBuilder_ == null) {
          gameTypesBuilder_ = new com.google.protobuf.RepeatedFieldBuilder<
              fm.lizhi.ocean.seal.protocol.GameTypeServiceProto.GameType, fm.lizhi.ocean.seal.protocol.GameTypeServiceProto.GameType.Builder, fm.lizhi.ocean.seal.protocol.GameTypeServiceProto.GameTypeOrBuilder>(
                  gameTypes_,
                  ((bitField0_ & 0x00000001) == 0x00000001),
                  getParentForChildren(),
                  isClean());
          gameTypes_ = null;
        }
        return gameTypesBuilder_;
      }

      // @@protoc_insertion_point(builder_scope:fm.lizhi.commons.template.datacenter.protocol.ResponseGetAllTypes)
    }

    static {
      defaultInstance = new ResponseGetAllTypes(true);
      defaultInstance.initFields();
    }

    // @@protoc_insertion_point(class_scope:fm.lizhi.commons.template.datacenter.protocol.ResponseGetAllTypes)
  }

  private static com.google.protobuf.Descriptors.Descriptor
    internal_static_fm_lizhi_commons_template_datacenter_protocol_GameType_descriptor;
  private static
    com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internal_static_fm_lizhi_commons_template_datacenter_protocol_GameType_fieldAccessorTable;
  private static com.google.protobuf.Descriptors.Descriptor
    internal_static_fm_lizhi_commons_template_datacenter_protocol_RequestGetAllTypes_descriptor;
  private static
    com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internal_static_fm_lizhi_commons_template_datacenter_protocol_RequestGetAllTypes_fieldAccessorTable;
  private static com.google.protobuf.Descriptors.Descriptor
    internal_static_fm_lizhi_commons_template_datacenter_protocol_ResponseGetAllTypes_descriptor;
  private static
    com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internal_static_fm_lizhi_commons_template_datacenter_protocol_ResponseGetAllTypes_fieldAccessorTable;

  public static com.google.protobuf.Descriptors.FileDescriptor
      getDescriptor() {
    return descriptor;
  }
  private static com.google.protobuf.Descriptors.FileDescriptor
      descriptor;
  static {
    java.lang.String[] descriptorData = {
      "\n\030protocol_game_type.proto\022-fm.lizhi.com" +
      "mons.template.datacenter.protocol\"(\n\010Gam" +
      "eType\022\n\n\002id\030\001 \001(\003\022\020\n\010typeName\030\002 \001(\t\"\024\n\022R" +
      "equestGetAllTypes\"a\n\023ResponseGetAllTypes" +
      "\022J\n\tgameTypes\030\001 \003(\01327.fm.lizhi.commons.t" +
      "emplate.datacenter.protocol.GameTypeB4\n\034" +
      "fm.lizhi.ocean.seal.protocolB\024GameTypeSe" +
      "rviceProto"
    };
    com.google.protobuf.Descriptors.FileDescriptor.InternalDescriptorAssigner assigner =
      new com.google.protobuf.Descriptors.FileDescriptor.InternalDescriptorAssigner() {
        public com.google.protobuf.ExtensionRegistry assignDescriptors(
            com.google.protobuf.Descriptors.FileDescriptor root) {
          descriptor = root;
          internal_static_fm_lizhi_commons_template_datacenter_protocol_GameType_descriptor =
            getDescriptor().getMessageTypes().get(0);
          internal_static_fm_lizhi_commons_template_datacenter_protocol_GameType_fieldAccessorTable = new
            com.google.protobuf.GeneratedMessage.FieldAccessorTable(
              internal_static_fm_lizhi_commons_template_datacenter_protocol_GameType_descriptor,
              new java.lang.String[] { "Id", "TypeName", });
          internal_static_fm_lizhi_commons_template_datacenter_protocol_RequestGetAllTypes_descriptor =
            getDescriptor().getMessageTypes().get(1);
          internal_static_fm_lizhi_commons_template_datacenter_protocol_RequestGetAllTypes_fieldAccessorTable = new
            com.google.protobuf.GeneratedMessage.FieldAccessorTable(
              internal_static_fm_lizhi_commons_template_datacenter_protocol_RequestGetAllTypes_descriptor,
              new java.lang.String[] { });
          internal_static_fm_lizhi_commons_template_datacenter_protocol_ResponseGetAllTypes_descriptor =
            getDescriptor().getMessageTypes().get(2);
          internal_static_fm_lizhi_commons_template_datacenter_protocol_ResponseGetAllTypes_fieldAccessorTable = new
            com.google.protobuf.GeneratedMessage.FieldAccessorTable(
              internal_static_fm_lizhi_commons_template_datacenter_protocol_ResponseGetAllTypes_descriptor,
              new java.lang.String[] { "GameTypes", });
          return null;
        }
      };
    com.google.protobuf.Descriptors.FileDescriptor
      .internalBuildGeneratedFileFrom(descriptorData,
        new com.google.protobuf.Descriptors.FileDescriptor[] {
        }, assigner);
  }

  // @@protoc_insertion_point(outer_class_scope)
}
