package fm.lizhi.ocean.seal.kafka;

/**
 * <AUTHOR>
 * @date 2025/4/16 下午6:31
 * @description
 */
public class RoomUsersChangedResult {

    /**
     * 房间id
     */
    private String roomId;
    /**
     * 游戏id
     */
    private String mgId;
    /**
     * 玩家总人数
     */
    private int playerTotal;
    /**
     * 观众总人数
     */
    private int obTotal;
    /**
     * 变更时间戳(毫秒)
     */
    private String changedTime;

    /**
     * 渠道原始信息
     */
    private String rawResult;

    public String getRoomId() {
        return roomId;
    }

    public void setRoomId(String roomId) {
        this.roomId = roomId;
    }

    public String getMgId() {
        return mgId;
    }

    public void setMgId(String mgId) {
        this.mgId = mgId;
    }

    public int getPlayerTotal() {
        return playerTotal;
    }

    public void setPlayerTotal(int playerTotal) {
        this.playerTotal = playerTotal;
    }

    public int getObTotal() {
        return obTotal;
    }

    public void setObTotal(int obTotal) {
        this.obTotal = obTotal;
    }

    public String getChangedTime() {
        return changedTime;
    }

    public void setChangedTime(String changedTime) {
        this.changedTime = changedTime;
    }

    public String getRawResult() {
        return rawResult;
    }

    public void setRawResult(String rawResult) {
        this.rawResult = rawResult;
    }

    @Override
    public String toString() {
        return "RoomUsersChangedResult{" +
                "roomId='" + roomId + '\'' +
                ", mgId='" + mgId + '\'' +
                ", playerTotal=" + playerTotal +
                ", obTotal=" + obTotal +
                ", changedTime='" + changedTime + '\'' +
                ", rawResult='" + rawResult + '\'' +
                '}';
    }
}
