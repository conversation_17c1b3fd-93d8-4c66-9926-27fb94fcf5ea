package fm.lizhi.ocean.seal.kafka;

/**
 * Created in 2022-01-26 19:40.
 *
 * <AUTHOR>
 */
public class GamePlayerSettleResult{
    /**
     * 用户ID
     */
    private long userId;
    /**
     * 是否是真实用户
     */
    private boolean realUser;
    /**
     * 是否逃跑
     */
    private boolean escaped;
    /**
     * 排名，从1开始，平局排名相同
     */
    private int rank;
    /**
     *   得分
     */
    private int score;
    /**
     * 游戏角色
     */
    private int role;
    /**
     * 胜负结果 0:表示没有信息，1:输，2:赢，3:平局 (接口待实现)
     */
    private int isWin;

    /**
     * 奖励
     */
    private int award;

    public GamePlayerSettleResult() {
    }

    public long getUserId() {
        return userId;
    }

    public void setUserId(long userId) {
        this.userId = userId;
    }

    public boolean isRealUser() {
        return realUser;
    }

    public void setRealUser(boolean realUser) {
        this.realUser = realUser;
    }

    public boolean isEscaped() {
        return escaped;
    }

    public void setEscaped(boolean escaped) {
        this.escaped = escaped;
    }

    public int getRank() {
        return rank;
    }

    public void setRank(int rank) {
        this.rank = rank;
    }

    public int getScore() {
        return score;
    }

    public void setScore(int score) {
        this.score = score;
    }

    public int getRole() {
        return role;
    }

    public void setRole(int role) {
        this.role = role;
    }

    public int getIsWin() {
        return isWin;
    }

    public void setIsWin(int isWin) {
        this.isWin = isWin;
    }

    public int getAward() {
        return award;
    }

    public void setAward(int award) {
        this.award = award;
    }

    @Override
    public String toString() {
        return "GamePlayerSettleResult{" +
                "userId=" + userId +
                ", realUser=" + realUser +
                ", escaped=" + escaped +
                ", rank=" + rank +
                ", score=" + score +
                ", role=" + role +
                ", isWin=" + isWin +
                ", award=" + award +
                '}';
    }
}
