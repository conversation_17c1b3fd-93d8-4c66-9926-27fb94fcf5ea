package fm.lizhi.ocean.seal.api;

import java.util.List;
import fm.lizhi.commons.service.client.annotation.Attribute;
import fm.lizhi.commons.service.client.annotation.Return;
import fm.lizhi.commons.service.client.annotation.Service;
import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.ocean.seal.protocol.GameRoundServiceProto.RequestRelationGameRound;
import fm.lizhi.ocean.seal.protocol.GameRoundServiceProto.ResponseRelationGameRound;
import fm.lizhi.ocean.seal.protocol.GameRoundServiceProto.RequestStartGameRound;
import fm.lizhi.ocean.seal.protocol.GameRoundServiceProto.ResponseStartGameRound;
import fm.lizhi.ocean.seal.protocol.GameRoundServiceProto.RequestEndGameRound;
import fm.lizhi.ocean.seal.protocol.GameRoundServiceProto.ResponseEndGameRound;
import fm.lizhi.ocean.seal.protocol.GameRoundServiceProto.RelationGameRoundParam;
import fm.lizhi.ocean.seal.protocol.GameRoundServiceProto.StartGameRoundParam;
import fm.lizhi.ocean.seal.protocol.GameRoundServiceProto.GameRound;
import fm.lizhi.ocean.seal.protocol.GameRoundServiceProto.EndGameRoundParam;

/**
 * this file generated by autoapi-maven-plugin
 * 
 * do not edit this file manually, because this file is covered when you run autoapi-maven-plugin
 * 
 */

public interface GameRoundService {
	
	
	/**
	 *  关联游戏局信息
	 *
	 * @param param
	 *            参数
	 * @return 
	 *     //if rcode == 1 参数非法<br>
	 *     //if rcode == 2 平台局ID不存在<br>
	 *     //if rcode == 3 内部错误<br>
	 */
	@Service(domain = 4302, op = 81, request = RequestRelationGameRound.class, response = ResponseRelationGameRound.class)
	@Return(resultType = ResponseRelationGameRound.class)
	Result<ResponseRelationGameRound> relationGameRound(@Attribute(name = "param") RelationGameRoundParam param);
	
	
	/**
	 *  开始平台游戏局，客户端在开始游戏的时候调用
	 *
	 * @param param
	 *            参数
	 * @return 
	 *     //if rcode == 1 参数非法<br>
	 *     //if rcode == 2 游戏ID不存在<br>
	 *     //if rcode == 3 内部错误<br>
	 */
	@Service(domain = 4302, op = 82, request = RequestStartGameRound.class, response = ResponseStartGameRound.class)
	@Return(resultType = ResponseStartGameRound.class)
	Result<ResponseStartGameRound> startGameRound(@Attribute(name = "param") StartGameRoundParam param);
	
	
	/**
	 *  开始平台游戏局，客户端在开始游戏的时候调用
	 *
	 * @param param
	 *            参数
	 * @return 
	 *     //if rcode == 1 参数非法<br>
	 *     //if rcode == 2 游戏ID不存在<br>
	 *     //if rcode == 3 内部错误<br>
	 */
	@Service(domain = 4302, op = 83, request = RequestEndGameRound.class, response = ResponseEndGameRound.class)
	@Return(resultType = ResponseEndGameRound.class)
	Result<ResponseEndGameRound> endGameRound(@Attribute(name = "param") EndGameRoundParam param);
	
	
	public static final int RELATION_GAME_ROUND_ILLEGAL_PARAMS = 1; // 参数非法
	public static final int RELATION_GAME_ROUND_NOT_EXISTS = 2; // 平台局ID不存在
	public static final int RELATION_GAME_ROUND_ERROR = 3; // 内部错误

	public static final int START_GAME_ROUND_ILLEGAL_PARAMS = 1; // 参数非法
	public static final int START_GAME_ROUND_NOT_EXISTS = 2; // 游戏ID不存在
	public static final int START_GAME_ROUND_ERROR = 3; // 内部错误

	public static final int END_GAME_ROUND_ILLEGAL_PARAMS = 1; // 参数非法
	public static final int END_GAME_ROUND_NOT_EXISTS = 2; // 游戏ID不存在
	public static final int END_GAME_ROUND_ERROR = 3; // 内部错误


}